import { getDb } from "@/lib/db";
import { contributeDatas, users } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";
import { cache } from "react";
import { calculateMultiDimensionScores } from "./github/score";

// 获取用户排名
export async function getUserRank(
  userId: string,
  db: Awaited<ReturnType<typeof getDb>>
): Promise<number | null> {
  try {
    // 构建一个排序的贡献数据列表 - 使用userId关联
    const rankedUsers = await db
      .select()
      .from(contributeDatas)
      .where(sql`${contributeDatas.userId} IS NOT NULL`)
      .orderBy(sql`${contributeDatas.contributionScore} DESC`);

    // 找到用户的排名
    const userRankIndex = rankedUsers.findIndex(
      (user) => user.userId === userId
    );

    // 如果找到了用户，返回排名（索引+1）
    return userRankIndex !== -1 ? userRankIndex + 1 : null;
  } catch (error) {
    console.error("获取用户排名失败:", error);
    return null;
  }
}

// 获取排行榜数据，使用React的cache函数缓存结果
export const getLeaderboard = cache(
  async (
    limit: number = 20,
    page: number = 1,
    db: Awaited<ReturnType<typeof getDb>>
  ) => {
    try {
      // 计算偏移量
      const offset = (page - 1) * limit;

      // 查询排行榜数据 - 使用userId关联
      const leaderboardData = await db
        .select()
        .from(contributeDatas)
        .innerJoin(users, eq(contributeDatas.userId, users.id))
        .orderBy(sql`${contributeDatas.contributionScore} DESC`) // 按贡献分数降序
        .limit(limit)
        .offset(offset);

      // 为结果添加排名和贡献等级
      const rankedResults = leaderboardData.map((item, index) => {
        const languageStats = JSON.parse(
          item.contribute_datas.languageStats || "{}"
        );
        const multiDimensionScore = calculateMultiDimensionScores({
          commits: item.contribute_datas.commits,
          contributedRepos: item.contribute_datas.contributedRepos,
          pullRequests: item.contribute_datas.pullRequests,
          reviews: item.contribute_datas.reviews,
          issues: item.contribute_datas.issues,
          totalStars: item.contribute_datas.totalStars,
          totalForks: item.contribute_datas.totalForks,
          followers: item.contribute_datas.followers,
          languageDiversity: languageStats.totalLanguages || 0,
          publicRepos: item.contribute_datas.publicRepos,
          following: item.contribute_datas.following,
          createdAt: item.contribute_datas.userCreatedAt,
        });

        return {
          rank: offset + index + 1,
          username: item.users.username,
          displayName: item.users.displayName,
          userId: item.users.id,
          avatarUrl: item.users.avatarUrl,
          contributionScore: item.contribute_datas.contributionScore,
          contributionGrade: multiDimensionScore.overallGrade,
        };
      });

      // 获取总用户数
      const totalUsersResult = await db.select().from(contributeDatas);

      const totalUsers = totalUsersResult.length;

      // 获取最后更新时间
      const lastUpdatedResult = await db
        .select()
        .from(contributeDatas)
        .orderBy(sql`${contributeDatas.lastUpdated} DESC`)
        .limit(1);

      const lastUpdatedTimestamp =
        lastUpdatedResult[0]?.lastUpdated || Date.now();
      const lastUpdated = new Date(lastUpdatedTimestamp).toISOString();

      return {
        leaderboard: rankedResults,
        totalUsers,
        lastUpdated,
      };
    } catch (error) {
      console.error("获取排行榜数据失败:", error);
      return {
        leaderboard: [],
        totalUsers: 0,
        lastUpdated: new Date().getTime(),
        error: "获取排行榜数据失败",
      };
    }
  }
);

// 获取完整的排行榜数据，包括当前用户信息
export async function getFullLeaderboard(
  limit: number = 20,
  page: number = 1,
  db: Awaited<ReturnType<typeof getDb>>,
  currentUserId?: string
) {
  try {
    // 获取排行榜数据
    const { leaderboard, totalUsers, lastUpdated } = await getLeaderboard(
      limit,
      page,
      db
    );

    // 如果提供了当前用户ID且该用户不在排行榜中，获取其排名
    let currentUser:
      | ({
          rank: number;
          contributionGrade: string;
        } & {
          username: string;
          displayName: string;
          userId: string;
          avatarUrl: string;
          contributionScore: number;
        })
      | undefined = undefined;

    if (currentUserId) {
      const userIds = leaderboard.map((item) => item.userId);

      if (!userIds.includes(currentUserId)) {
        // 获取用户排名
        const userRank = await getUserRank(currentUserId, db);

        if (userRank) {
          // 获取用户名
          const userResult = await db.query.users.findFirst({
            where: eq(users.id, currentUserId),
          });

          if (userResult && userResult.username) {
            // 获取用户的贡献数据 - 使用userId关联
            const userData = await db
              .select()
              .from(contributeDatas)
              .innerJoin(users, eq(contributeDatas.userId, users.id))
              .where(eq(contributeDatas.userId, currentUserId))
              .limit(1);

            if (userData.length > 0) {
              // 解析语言统计数据
              let languageStats = undefined;
              if (userData[0].contribute_datas.languageStats) {
                try {
                  languageStats = JSON.parse(
                    userData[0].contribute_datas.languageStats
                  );
                } catch (error) {
                  console.warn("Failed to parse language stats:", error);
                }
              }

              // 创建带排名的用户数据
              const multiDimensionScore = calculateMultiDimensionScores({
                commits: userData[0].contribute_datas.commits,
                contributedRepos: userData[0].contribute_datas.contributedRepos,
                pullRequests: userData[0].contribute_datas.pullRequests,
                reviews: userData[0].contribute_datas.reviews,
                issues: userData[0].contribute_datas.issues,
                totalStars: userData[0].contribute_datas.totalStars,
                totalForks: userData[0].contribute_datas.totalForks,
                followers: userData[0].contribute_datas.followers,
                languageDiversity: languageStats.totalLanguages || 0,
                publicRepos: userData[0].contribute_datas.publicRepos,
                following: userData[0].contribute_datas.following,
                createdAt: userData[0].contribute_datas.userCreatedAt,
              });

              currentUser = {
                rank: userRank,
                username: userData[0].users.username,
                displayName: userData[0].users.displayName || "",
                userId: userData[0].users.id,
                avatarUrl: userData[0].users.avatarUrl,
                contributionScore:
                  userData[0].contribute_datas.contributionScore,
                contributionGrade: multiDimensionScore.overallGrade,
              };
            }
          }
        }
      }
    }

    return {
      leaderboard,
      currentUser,
      totalUsers,
      lastUpdated,
      currentPage: page,
      totalPages: Math.ceil(totalUsers / limit),
    };
  } catch (error) {
    console.error("获取完整排行榜数据失败:", error);
    return {
      leaderboard: [],
      currentUser: undefined,
      totalUsers: 0,
      lastUpdated: new Date().getTime(),
      currentPage: page,
      totalPages: 0,
      error: "获取完整排行榜数据失败",
    };
  }
}
