// Cloudflare KV service client
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { MemoryKVStore } from "./kv-service-memory";

// Add global type declarations for Cloudflare Worker environment
declare global {
  interface GlobalThis {
    caches?: CacheStorage;
  }
}

interface KVNamespace {
  get(
    key: string,
    options?: { type: "json" }  
  ): Promise<any>;
  get<T>(key: string, options: { type: "json" }): Promise<T | null>;
  getWithMetadata<T>(key: string, options: { type: "json" }): Promise<{ value: T | null; metadata: KVMetadata } | null>;
  put(
    key: string,
    value: string | ReadableStream | ArrayBuffer | FormData,
    options?: KVSetOptions
  ): Promise<void>;
  delete(key: string): Promise<void>;
  list(options?: {
    prefix?: string;
    limit?: number;
    cursor?: string;
  }): Promise<{
    keys: { name: string }[];
    list_complete: boolean;
    cursor?: string;
  }>;
}

// KV set 方法的选项
export interface KVSetOptions {
  expiration?: number; // Unix 时间戳（秒）
  expirationTtl?: number; // 过期时间（秒）
  metadata?: KVMetadata; // 可选的元数据
}

// KV 元数据类型
export interface KVMetadata {
  [key: string]: any;    // Allow additional metadata fields
}

// KV 客户端接口定义
export interface KVClient {
  get<T = unknown>(key: string): Promise<T | null>;
  getWithMetadata<T = unknown>(key: string): Promise<{ value: T | null; metadata: KVMetadata | null } | null>;
  getAll<T = unknown>(): Promise<Record<string, T>>;
  has(key: string): Promise<boolean>;
  set<T>(key: string, value: T, options?: KVSetOptions): Promise<void>;
  delete(key: string): Promise<void>;
  list(options?: {
    prefix?: string;
    limit?: number;
    cursor?: string;
  }): Promise<{
    keys: { name: string }[];
    list_complete: boolean;
    cursor?: string;
  }>;
}

// 1. 添加全局类型声明
declare global {
  interface Window {
    GITHUB_CARD_KV?: KVNamespace;
  }
  // 在 Worker 环境中的类型声明
  const GITHUB_CARD_KV: KVNamespace | undefined;
}

// 2. 改进 KV 客户端实现
class CloudflareKVBindingClient implements KVClient {
  private namespace: KVNamespace;
  private readonly logger: Console;

  constructor(namespace: KVNamespace, logger: Console = console) {
    this.namespace = namespace;
    this.logger = logger;
  }

  async get<T = unknown>(key: string): Promise<T | null> {
    try {
      this.logger.debug(`[KV] Getting key: ${key}`);
      const value = await this.namespace.get(key, { type: "json" });

      if (value === null) {
        this.logger.debug(`[KV] Key not found: ${key}`);
        return null;
      }

      this.logger.debug(`[KV] Successfully got key: ${key}`);
      return value as T;
    } catch (error) {
      this.logger.error(`[KV] Error getting key ${key}:`, error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to get key ${key}: ${errorMessage}`);
    }
  }

  async getWithMetadata<T = unknown>(key: string): Promise<{ value: T | null; metadata: KVMetadata | null } | null> {
    try {
      this.logger.debug(`[KV] Getting key with metadata: ${key}`);
      const result = await (this.namespace.getWithMetadata as any)(key, { type: "json" });

      if (result === null) {
        this.logger.debug(`[KV] Key not found: ${key}`);
        return null;
      }

      const value = result.value as T;
      const metadata = result.metadata as KVMetadata;
      
      this.logger.debug(`[KV] Successfully got key with metadata: ${key}`, { metadata });
      return { 
        value, 
        metadata: metadata || null 
      };
    } catch (error) {
      this.logger.error(`[KV] Error getting key ${key} with metadata:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to get key ${key} with metadata: ${errorMessage}`);
    }
  }

  async set<T>(key: string, value: T, options: KVSetOptions = {}): Promise<void> {
    try {
      this.logger.debug(`[KV] Setting key: ${key}`, { options });
      await this.namespace.put(key, JSON.stringify(value), options);
      this.logger.debug(`[KV] Successfully set key: ${key}`, { options });
    } catch (error) {
      this.logger.error(`[KV] Error setting key ${key}:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to set key ${key}: ${errorMessage}`);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      this.logger.debug(`[KV] Deleting key: ${key}`);
      await this.namespace.delete(key);
      this.logger.debug(`[KV] Successfully deleted key: ${key}`);
    } catch (error) {
      this.logger.error(`[KV] Error deleting key ${key}:`, error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to delete key ${key}: ${errorMessage}`);
    }
  }

  async getAll<T = unknown>(): Promise<Record<string, T>> {
    try {
      this.logger.debug("[KV] Listing all keys");
      const result: Record<string, T> = {};
      let cursor: string | undefined;

      do {
        const list = await this.namespace.list({ cursor });
        await Promise.all(
          list.keys.map(async ({ name }) => {
            try {
              const value = await this.get<T>(name);
              if (value !== null) {
                result[name] = value;
              }
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : String(error);
              this.logger.error(
                `[KV] Error processing key ${name}: ${errorMessage}`
              );
            }
          })
        );
        cursor = list.list_complete ? undefined : list.cursor;
      } while (cursor);

      this.logger.debug(
        `[KV] Successfully listed ${Object.keys(result).length} keys`
      );
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`[KV] Error listing keys: ${errorMessage}`);
      throw new Error(`Failed to list keys: ${errorMessage}`);
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      this.logger.debug(`[KV] Checking if key exists: ${key}`);
      const value = await this.namespace.get(key);
      const exists = value !== null;
      this.logger.debug(
        `[KV] Key ${key} ${exists ? "exists" : "does not exist"}`
      );
      return exists;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `[KV] Error checking if key exists ${key}: ${errorMessage}`
      );
      throw new Error(`Failed to check if key exists ${key}: ${errorMessage}`);
    }
  }

  async list(options?: {
    prefix?: string;
    limit?: number;
    cursor?: string;
  }): Promise<{
    keys: { name: string }[];
    list_complete: boolean;
    cursor?: string;
  }> {
    try {
      this.logger.debug("[KV] Listing keys");
      const result = await this.namespace.list(options);
      this.logger.debug(
        `[KV] Successfully listed ${result.keys.length} keys`
      );
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`[KV] Error listing keys: ${errorMessage}`);
      throw new Error(`Failed to list keys: ${errorMessage}`);
    }
  }
}

export function createKVClient(
  kvBinding?: KVNamespace,
  options: { logger?: Console } = {}
): KVClient {
  const { logger = console } = options;

  // 如果提供了 KV 绑定，使用 Cloudflare KV
  if (kvBinding) {
    logger.info("[KV] Using provided KV binding");
    return new CloudflareKVBindingClient(kvBinding, logger);
  }

  // 回退到内存存储
  logger.warn("[KV] Without KV binding, Using in-memory KV store");
  return new MemoryKVStore();
}

// 导出默认的 KV 客户端实例
let _kvClient: KVClient | null = null;

export async function getKvClient(): Promise<KVClient> {
  if (_kvClient) return _kvClient;
  
  try {
    const context = await getCloudflareContext({ async: true });
    const env = context.env as { GITHUB_CARD_KV?: KVNamespace };
    _kvClient = createKVClient(env.GITHUB_CARD_KV);
    return _kvClient;
  } catch (error) {
    console.error("Failed to initialize KV client:", error);
    // 开发环境下回退到内存存储
    if (process.env.NODE_ENV !== "production") {
      console.warn("Falling back to in-memory KV store");
      _kvClient = new MemoryKVStore();
      return _kvClient;
    }
    throw error;
  }
}

// 向后兼容的导出 - 使用内存存储作为默认实现
const kvClient = new MemoryKVStore();

export { kvClient };

// 在支持的环境下初始化 KV 客户端
if (typeof window === 'undefined') {
  // 在服务器端初始化
  getKvClient().then(client => {
    // 将全局 kvClient 替换为实际的 KV 客户端
    Object.assign(kvClient, client);
  }).catch(error => {
    console.error('Failed to initialize KV client:', error);
  });
}
