import {
  getKvClient,
  KVSetOptions,
  KVMetadata,
} from "../cloudflare/kv-service";
import { CACHE_MANAGER_PREFIX } from "@/constants";

// Main cache manager that abstracts KV and memory cache
export class CacheManager {
  private prefix: string;
  private kvClient: Awaited<ReturnType<typeof getKvClient>> | null = null;
  private kvClientInitialized = false;

  constructor(prefix = CACHE_MANAGER_PREFIX) {
    this.prefix = prefix;
    this.initializeKvClient();
  }

  private async initializeKvClient() {
    if (this.kvClientInitialized || typeof window !== "undefined") return;

    try {
      this.kvClient = await getKvClient();
      this.kvClientInitialized = true;
    } catch (error) {
      console.error("Failed to initialize KV client in CacheManager:", error);
    }
  }

  private async getKvClient() {
    if (!this.kvClientInitialized) {
      await this.initializeKvClient();
    }
    return this.kvClient;
  }

  async set<T>(key: string, value: T, options?: KVSetOptions): Promise<void> {
    const fullKey = this.prefix + key;
    const kvClient = await this.getKvClient();
    if (!kvClient) return;

    try {
      await kvClient.set(fullKey, value, options);

      console.log(
        `Cache set in KV: ${fullKey} with options: ${JSON.stringify(options)} `
      );
    } catch (error) {
      console.error("KV set error:", error);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    const fullKey = this.prefix + key;
    const kvClient = await this.getKvClient();

    if (!kvClient) {
      return null;
    }

    try {
      const cacheItem = await kvClient.get(fullKey);

      if (!cacheItem) {
        return null;
      }

      return cacheItem as T;
    } catch (error) {
      console.error("KV get error:", error);
      return null;
    }
  }

  async getWithMetadata<T>(
    key: string
  ): Promise<{ value: T | null; metadata: KVMetadata | null }> {
    const fullKey = this.prefix + key;
    const kvClient = await this.getKvClient();

    if (!kvClient) {
      return { value: null, metadata: null };
    }

    try {
      const cacheItem = await kvClient.getWithMetadata(fullKey);

      if (!cacheItem) {
        return { value: null, metadata: null };
      }

      return { value: cacheItem.value as T, metadata: cacheItem.metadata };
    } catch (error) {
      console.error("KV get error:", error);
      return { value: null, metadata: null };
    }
  }

  async delete(key: string): Promise<boolean> {
    const fullKey = this.prefix + key;
    const kvClient = await this.getKvClient();

    if (!kvClient) {
      console.error("KV client not initialized");
      return false;
    }

    try {
      await kvClient.delete(fullKey);
      return true;
    } catch (error) {
      console.error("Error deleting cache item:", error);
      return false;
    }
  }

  /**
   * 批量删除缓存项 - Phase 4.3 优化
   * 并行删除多个KV项，提升清理效率
   */
  async batchDelete(keys: string[]): Promise<{
    success: boolean[];
    totalDeleted: number;
    errors: string[];
  }> {
    const result = {
      success: new Array(keys.length).fill(false),
      totalDeleted: 0,
      errors: [] as string[],
    };

    if (keys.length === 0) {
      return result;
    }

    const kvClient = await this.getKvClient();
    if (!kvClient) {
      result.errors.push("KV client not initialized");
      return result;
    }

    try {
      console.log(`🚀 Batch deleting ${keys.length} KV items`);
      const startTime = Date.now();

      // 并行删除所有KV项
      const deletePromises = keys.map(async (key, index) => {
        try {
          const fullKey = this.prefix + key;
          await kvClient.delete(fullKey);
          return { index, success: true, error: null };
        } catch (error) {
          return {
            index,
            success: false,
            error: error instanceof Error ? error.message : String(error),
          };
        }
      });

      const deleteResults = await Promise.allSettled(deletePromises);

      // 处理删除结果
      deleteResults.forEach((promiseResult, index) => {
        if (promiseResult.status === "fulfilled") {
          const deleteResult = promiseResult.value;
          result.success[deleteResult.index] = deleteResult.success;

          if (deleteResult.success) {
            result.totalDeleted++;
          } else if (deleteResult.error) {
            result.errors.push(
              `Failed to delete ${keys[deleteResult.index]}: ${
                deleteResult.error
              }`
            );
          }
        } else {
          result.errors.push(
            `Promise failed for ${keys[index]}: ${promiseResult.reason}`
          );
        }
      });

      const duration = Date.now() - startTime;
      console.log(
        `✅ KV batch delete completed in ${duration}ms: ${result.totalDeleted}/${keys.length} successful`
      );

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error("Error in KV batch delete:", error);
      result.errors.push(`Batch delete failed: ${errorMessage}`);
      return result;
    }
  }

  async listKeys(prefix: string): Promise<string[]> {
    const kvClient = await this.getKvClient();

    if (!kvClient) {
      console.error("KV client not initialized");
      return [];
    }

    try {
      const keys = await kvClient.list({ prefix: this.prefix + prefix });
      return keys.keys.map((key) => key.name);
    } catch (error) {
      console.error("Error listing keys:", error);
      return [];
    }
  }
}

// Create cache manager instance
export const cacheManager = new CacheManager();
