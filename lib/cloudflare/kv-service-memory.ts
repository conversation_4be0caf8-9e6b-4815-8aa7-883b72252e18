import { KVClient, KVMetadata, KVSetOptions } from "./kv-service";

// 内存缓存，用于本地开发或回退时
export class MemoryKVStore implements KVClient {
  private store: Map<string, { value: unknown; metadata?: KVMetadata }> = new Map();

  async get<T = unknown>(key: string): Promise<T | null> {
    const entry = this.store.get(key);
    if (!entry) return null;
    
    // For memory store, we still need to handle expiration since it's not automatic
    if (entry.metadata?.expiration && entry.metadata.expiration < Math.floor(Date.now() / 1000)) {
      await this.delete(key);
      return null;
    }
    
    return entry.value as T;
  }
  
  async getWithMetadata<T = unknown>(key: string): Promise<{ value: T | null; metadata: KVMetadata | null } | null> {
    const entry = this.store.get(key);
    if (!entry) return null;
    
    // For memory store, we still need to handle expiration since it's not automatic
    if (entry.metadata?.expiration && entry.metadata.expiration < Math.floor(Date.now() / 1000)) {
      await this.delete(key);
      return null;
    }
    
    return {
      value: entry.value as T,
      metadata: entry.metadata || null
    };
  }

  async getAll<T = unknown>(): Promise<Record<string, T>> {
    const result: Record<string, T> = {};
    this.store.forEach((value, key) => {
      result[key] = value as T;
    });
    return result;
  }

  async has(key: string): Promise<boolean> {
    return this.store.has(key);
  }

  async set<T>(key: string, value: T, options: KVSetOptions = {}): Promise<void> {
    const metadata: KVMetadata = {
      ...(options.metadata || {})
    };
    
    // Handle expiration
    if (options.expirationTtl) {
      metadata.expiration = Math.floor(Date.now() / 1000) + options.expirationTtl;
      metadata.expirationTtl = options.expirationTtl;
    } else if (options.expiration) {
      metadata.expiration = options.expiration;
    }
    
    this.store.set(key, { value, metadata });
  }

  async delete(key: string): Promise<void> {
    this.store.delete(key);
  }

  async list(options?: {
    prefix?: string;
    limit?: number;
    cursor?: string;
  }): Promise<{
    keys: { name: string }[];
    list_complete: boolean;
    cursor?: string;
  }> {
    try {
      const result = this.store.keys();
      return {
        keys: Array.from(result).map(key => ({ name: key })),
        list_complete: true,
        cursor: undefined
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to list keys: ${errorMessage}`);
    }
  }
}