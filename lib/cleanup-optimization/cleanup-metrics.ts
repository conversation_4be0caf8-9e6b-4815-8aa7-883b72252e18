// 清理性能监控模块 - Phase 4.3
// 提供详细的清理操作监控和分析

import { EnhancedCleanupResult } from "@/constants/cleanup-optimization";

/**
 * 清理性能监控器
 * 收集、分析和报告清理操作的性能指标
 */
export class CleanupMetricsCollector {
  private metricsHistory: EnhancedCleanupResult[] = [];
  private readonly maxHistorySize = 100; // 保留最近100次清理记录

  /**
   * 记录清理操作结果
   */
  recordCleanupResult(result: EnhancedCleanupResult): void {
    // 添加时间戳
    const timestampedResult = {
      ...result,
      timestamp: Date.now(),
    };

    this.metricsHistory.push(timestampedResult);

    // 保持历史记录大小限制
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory.shift();
    }

    // 实时性能分析
    this.analyzePerformance(result);
  }

  /**
   * 实时性能分析
   */
  private analyzePerformance(result: EnhancedCleanupResult): void {
    console.log(`📊 Cleanup Metrics Analysis:`);

    // 性能等级评估
    const performanceGrade = this.getPerformanceGrade(result.performanceGain);
    console.log(`   Performance Grade: ${performanceGrade}`);

    // 错误率分析
    const errorRate =
      result.errors.length / (result.removedFromR2 + result.errors.length);
    const errorGrade = this.getErrorGrade(errorRate);
    console.log(
      `   Error Rate: ${(errorRate * 100).toFixed(1)}% (${errorGrade})`
    );

    // 并发效率分析
    const concurrencyGrade = this.getConcurrencyGrade(
      result.concurrencyUtilization
    );
    console.log(`   Concurrency Efficiency: ${concurrencyGrade}`);

    // 异常检测
    this.detectAnomalies(result);
  }

  /**
   * 性能等级评估
   */
  private getPerformanceGrade(performanceGain: number): string {
    if (performanceGain >= 6) return "🏆 Excellent (6x+)";
    if (performanceGain >= 4) return "🥇 Great (4-6x)";
    if (performanceGain >= 2) return "🥈 Good (2-4x)";
    if (performanceGain >= 1.5) return "🥉 Fair (1.5-2x)";
    return "⚠️ Poor (<1.5x)";
  }

  /**
   * 错误率等级评估
   */
  private getErrorGrade(errorRate: number): string {
    if (errorRate <= 0.01) return "🟢 Excellent";
    if (errorRate <= 0.05) return "🟡 Good";
    if (errorRate <= 0.1) return "🟠 Fair";
    return "🔴 Poor";
  }

  /**
   * 并发效率等级评估
   */
  private getConcurrencyGrade(utilization: number): string {
    if (utilization >= 0.8) return "🚀 Optimal (80%+)";
    if (utilization >= 0.6) return "⚡ Good (60-80%)";
    if (utilization >= 0.4) return "📈 Fair (40-60%)";
    return "🐌 Poor (<40%)";
  }

  /**
   * 异常检测
   */
  private detectAnomalies(result: EnhancedCleanupResult): void {
    const anomalies: string[] = [];

    // 检测性能异常
    if (result.performanceGain < 2) {
      anomalies.push("⚠️ Performance below expected (target: 6x)");
    }

    // 检测超时异常
    if (result.timeoutErrors > result.removedFromR2 * 0.1) {
      anomalies.push("⏰ High timeout rate detected");
    }

    // 检测R2错误异常
    if (result.r2Errors > result.removedFromR2 * 0.05) {
      anomalies.push("☁️ High R2 error rate detected");
    }

    // 检测并发利用率异常
    if (result.concurrencyUtilization < 0.5) {
      anomalies.push("🔄 Low concurrency utilization");
    }

    if (anomalies.length > 0) {
      console.warn(`🚨 Performance Anomalies Detected:`);
      anomalies.forEach((anomaly) => console.warn(`   ${anomaly}`));
    }
  }

  /**
   * 获取性能统计摘要
   */
  getPerformanceSummary(): {
    totalCleanups: number;
    averagePerformanceGain: number;
    averageErrorRate: number;
    averageConcurrencyUtilization: number;
    totalImagesProcessed: number;
    totalTimesSaved: number;
  } {
    if (this.metricsHistory.length === 0) {
      return {
        totalCleanups: 0,
        averagePerformanceGain: 0,
        averageErrorRate: 0,
        averageConcurrencyUtilization: 0,
        totalImagesProcessed: 0,
        totalTimesSaved: 0,
      };
    }

    const totalCleanups = this.metricsHistory.length;
    const totalImagesProcessed = this.metricsHistory.reduce(
      (sum, result) => sum + result.removedFromQueue,
      0
    );

    const averagePerformanceGain =
      this.metricsHistory.reduce(
        (sum, result) => sum + result.performanceGain,
        0
      ) / totalCleanups;

    const averageErrorRate =
      this.metricsHistory.reduce(
        (sum, result) =>
          sum +
          result.errors.length / (result.removedFromR2 + result.errors.length),
        0
      ) / totalCleanups;

    const averageConcurrencyUtilization =
      this.metricsHistory.reduce(
        (sum, result) => sum + result.concurrencyUtilization,
        0
      ) / totalCleanups;

    // 计算总节省时间 (基于性能提升)
    const totalTimesSaved = this.metricsHistory.reduce((sum, result) => {
      const sequentialTime = result.removedFromQueue * 150; // 假设顺序执行150ms/图片
      const actualTime = result.totalDuration;
      return sum + (sequentialTime - actualTime);
    }, 0);

    return {
      totalCleanups,
      averagePerformanceGain,
      averageErrorRate,
      averageConcurrencyUtilization,
      totalImagesProcessed,
      totalTimesSaved,
    };
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport(): string {
    const summary = this.getPerformanceSummary();

    return `
📊 Cleanup Performance Report
============================
Total Cleanups: ${summary.totalCleanups}
Total Images Processed: ${summary.totalImagesProcessed}
Average Performance Gain: ${summary.averagePerformanceGain.toFixed(2)}x
Average Error Rate: ${(summary.averageErrorRate * 100).toFixed(2)}%
Average Concurrency Utilization: ${(
      summary.averageConcurrencyUtilization * 100
    ).toFixed(1)}%
Total Time Saved: ${(summary.totalTimesSaved / 1000).toFixed(1)} seconds

Recent Performance Trend:
${this.getRecentTrend()}
    `.trim();
  }

  /**
   * 获取最近性能趋势
   */
  private getRecentTrend(): string {
    if (this.metricsHistory.length < 3) {
      return "Insufficient data for trend analysis";
    }

    const recent = this.metricsHistory.slice(-3);
    const performanceGains = recent.map((r) => r.performanceGain);

    const isImproving = performanceGains[2] > performanceGains[0];
    const trend = isImproving ? "📈 Improving" : "📉 Declining";

    return `${trend} (${performanceGains
      .map((g) => g.toFixed(1) + "x")
      .join(" → ")})`;
  }

  /**
   * 清除历史记录
   */
  clearHistory(): void {
    this.metricsHistory = [];
  }

  /**
   * 获取历史记录
   */
  getHistory(): EnhancedCleanupResult[] {
    return [...this.metricsHistory];
  }
}

// 全局清理指标收集器实例
export const cleanupMetricsCollector = new CleanupMetricsCollector();
