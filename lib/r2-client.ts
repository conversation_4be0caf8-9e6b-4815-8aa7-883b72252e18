// Cloudflare R2 对象存储客户端 - Workers API 版本
// 参考: https://developers.cloudflare.com/r2/api/workers/workers-api-usage/

import type {
  R2Bucket,
  R2Object,
  R2ObjectBody,
  R2HTTPMetadata,
  R2Objects,
  R2Conditional,
  R2Range,
} from "@/types/cloudflare-kv";

export interface R2Config {
  publicUrl: string; // R2 公共访问域名
}

export interface ImageUploadResult {
  success: boolean;
  r2Key: string;
  publicUrl: string;
  contentType: string;
  error?: string;
}

export class R2ImageClient {
  private config: R2Config;

  constructor(config: R2Config) {
    this.config = config;
  }

  /**
   * 上传图片到R2存储 - 使用原生 R2 Workers API
   */
  async uploadImage(
    imageId: string,
    imageBuffer: ArrayBuffer,
    contentType: string
  ): Promise<ImageUploadResult> {
    try {
      const r2Key = `images/${imageId}`;

      // 获取 R2 绑定
      const r2Bucket = await this.getR2Bucket();
      if (!r2Bucket) {
        throw new Error("R2 bucket binding not available");
      }

      // 使用原生 R2 API 上传
      const result = await r2Bucket.put(r2Key, imageBuffer, {
        httpMetadata: {
          contentType: contentType,
          cacheControl: "public, max-age=31536000, immutable", // 1年缓存
        },
        customMetadata: {
          uploadedAt: new Date().toISOString(),
          imageId: imageId,
          source: "github-card-app",
        },
      });

      if (!result) {
        throw new Error("Failed to upload to R2 - no result returned");
      }

      const publicUrl = `${this.config.publicUrl}/${r2Key}`;

      console.log(`Successfully uploaded image to R2: ${imageId}`);

      return {
        success: true,
        r2Key,
        publicUrl,
        contentType,
      };
    } catch (error) {
      console.error("Error uploading to R2:", error);
      return {
        success: false,
        r2Key: `images/${imageId}`,
        publicUrl: "",
        contentType,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 检查图片是否存在
   */
  async imageExists(imageId: string): Promise<boolean> {
    try {
      const r2Key = `images/${imageId}`;
      const r2Bucket = await this.getR2Bucket();
      if (!r2Bucket) return false;

      const object = await r2Bucket.head(r2Key);
      return object !== null;
    } catch (error) {
      console.error(`Error checking image existence: ${imageId}`, error);
      return false;
    }
  }

  /**
   * 获取图片URL
   */
  getImageUrl(imageId: string): string {
    const r2Key = `images/${imageId}`;
    return `${this.config.publicUrl}/${r2Key}`;
  }

  /**
   * 删除R2中的图片
   */
  async deleteImage(r2Key: string): Promise<boolean> {
    try {
      const r2Bucket = await this.getR2Bucket();
      if (!r2Bucket) return false;

      await r2Bucket.delete(r2Key);
      console.log(`Successfully deleted from R2: ${r2Key}`);
      return true;
    } catch (error) {
      console.error(`Error deleting from R2: ${r2Key}`, error);
      return false;
    }
  }

  /**
   * 批量删除R2中的图片 - Phase 4.3 优化
   * 利用R2原生批量删除API，显著提升性能
   */
  async batchDeleteImages(imageIds: string[]): Promise<{
    success: boolean[];
    totalDeleted: number;
    errors: string[];
  }> {
    const result = {
      success: new Array(imageIds.length).fill(false),
      totalDeleted: 0,
      errors: [] as string[],
    };

    if (imageIds.length === 0) {
      return result;
    }

    try {
      const r2Bucket = await this.getR2Bucket();
      if (!r2Bucket) {
        result.errors.push("R2 bucket not available");
        return result;
      }

      // 转换为R2键名
      const r2Keys = imageIds.map((id) => `images/${id}`);

      console.log(`🚀 Batch deleting ${imageIds.length} images from R2`);
      const startTime = Date.now();

      // 使用R2原生批量删除API
      await r2Bucket.delete(r2Keys);

      const duration = Date.now() - startTime;
      console.log(`✅ Batch delete completed in ${duration}ms`);

      // 标记所有删除为成功 (R2批量删除不返回单个结果)
      result.success.fill(true);
      result.totalDeleted = imageIds.length;

      console.log(
        `Successfully batch deleted ${imageIds.length} images from R2`
      );
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Error in batch delete from R2:`, error);
      result.errors.push(`Batch delete failed: ${errorMessage}`);
      return result;
    }
  }

  /**
   * 批量上传图片（用于迁移）
   */
  async batchUpload(
    images: Array<{
      imageId: string;
      buffer: ArrayBuffer;
      contentType: string;
    }>
  ): Promise<ImageUploadResult[]> {
    const results: ImageUploadResult[] = [];

    // 并行上传，但限制并发数为3避免过载
    const chunks = this.chunkArray(images, 3);

    for (const chunk of chunks) {
      const chunkResults = await Promise.all(
        chunk.map((img) =>
          this.uploadImage(img.imageId, img.buffer, img.contentType)
        )
      );
      results.push(...chunkResults);
    }

    return results;
  }

  /**
   * 获取R2使用统计信息
   */
  async getStorageStats(): Promise<{
    totalImages: number;
    estimatedSize: string;
  }> {
    try {
      const r2Bucket = await this.getR2Bucket();
      if (!r2Bucket) {
        return {
          totalImages: 0,
          estimatedSize: "未知",
        };
      }

      // 列出所有图片
      const listResult = await r2Bucket.list({
        prefix: "images/",
        limit: 1000, // R2 最大限制
      });

      const totalImages = listResult.objects.length;
      const estimatedSize = listResult.objects.reduce(
        (total, obj) => total + obj.size,
        0
      );

      return {
        totalImages,
        estimatedSize: `${(estimatedSize / 1024 / 1024).toFixed(2)} MB`,
      };
    } catch (error) {
      console.error("Error getting storage stats:", error);
      return {
        totalImages: 0,
        estimatedSize: "未知",
      };
    }
  }

  /**
   * 获取 R2 Bucket 绑定 - Cloudflare Workers 环境
   */
  private async getR2Bucket(): Promise<R2Bucket | null> {
    try {
      // 在 Cloudflare Workers 环境中获取 R2 绑定
      if (typeof globalThis !== "undefined" && "GITHUB_CARD_R2" in globalThis) {
        return (globalThis as any).GITHUB_CARD_R2;
      }

      // 尝试通过 OpenNext 上下文获取
      if (typeof process !== "undefined" && process.env) {
        try {
          const { getCloudflareContext } = await import(
            "@opennextjs/cloudflare"
          );
          const context = await getCloudflareContext({ async: true });
          const env = context.env as any;

          if (env.GITHUB_CARD_R2) {
            console.log("Found R2 binding through OpenNext context");
            return env.GITHUB_CARD_R2;
          }
        } catch (contextError) {
          console.warn("Failed to get Cloudflare context:", contextError);
        }
      }

      console.warn(
        "R2 bucket binding not found. Make sure GITHUB_CARD_R2 is properly configured in wrangler.jsonc"
      );
      return null;
    } catch (error) {
      console.error("Failed to get R2 bucket binding:", error);
      return null;
    }
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

// 全局R2客户端实例
let r2Client: R2ImageClient | null = null;

export function getR2Client(): R2ImageClient {
  if (!r2Client) {
    const config: R2Config = {
      publicUrl: process.env.R2_PUBLIC_URL || "",
    };

    // 验证必要的环境变量
    const requiredVars = ["R2_PUBLIC_URL"];
    const missingVars = requiredVars.filter((varName) => !process.env[varName]);

    if (missingVars.length > 0) {
      console.warn(
        `Missing R2 environment variables: ${missingVars.join(
          ", "
        )}. R2 functionality will be limited.`
      );
    }

    r2Client = new R2ImageClient(config);
  }

  return r2Client;
}

// 便捷函数
export async function uploadImageToR2(
  imageId: string,
  imageBuffer: ArrayBuffer,
  contentType: string
): Promise<ImageUploadResult> {
  const client = getR2Client();
  return client.uploadImage(imageId, imageBuffer, contentType);
}

export async function checkImageInR2(imageId: string): Promise<boolean> {
  const client = getR2Client();
  return client.imageExists(imageId);
}

export function getR2ImageUrl(imageId: string): string {
  const client = getR2Client();
  return client.getImageUrl(imageId);
}
