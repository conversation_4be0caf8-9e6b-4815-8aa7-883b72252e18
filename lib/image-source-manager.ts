import { ImageSource } from "@/types/image-source";
import { IMAGE_SOURCES } from "@/constants";

export class ImageSourceManager {
  private sources: ImageSource[];

  constructor() {
    this.sources = IMAGE_SOURCES.filter((source) => source.enabled);
  }

  // 根据权重随机选择图片源
  selectRandomSource(): ImageSource {
    if (this.sources.length === 0) {
      throw new Error("No enabled image sources available");
    }

    const totalWeight = this.sources.reduce((sum, s) => sum + s.weight, 0);
    let random = Math.random() * totalWeight;

    for (const source of this.sources) {
      random -= source.weight;
      if (random <= 0) return source;
    }

    return this.sources[0]; // 兜底
  }

  // 获取指定类型的源
  getSourcesByType(
    type: "direct" | "pexels" | "pixabay" | "unsplash"
  ): ImageSource[] {
    return this.sources.filter((source) => source.type === type);
  }

  // 获取启用的源数量
  getEnabledSourceCount(): number {
    return this.sources.length;
  }

  // 获取所有启用的源
  getAllEnabledSources(): ImageSource[] {
    return [...this.sources];
  }
}
