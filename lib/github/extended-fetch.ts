/**
 * GitHub 扩展数据获取模块
 * V5 扩展 - 基于现有架构的良性扩展，获取原始数据用于后续分析
 *
 * 架构设计：
 * - 最小配置原则：提供合理默认值，开箱即用
 * - 类型安全：完整的TypeScript类型定义
 * - 复用优先：基于现有token管理和缓存机制
 * - 尽早报错：参数校验和错误处理
 */

import type {
  GitHubExtendedData,
  RepositoryDetailData,
  CommitDetailData,
  ReadmeDetailData,
  TechStackFileData,
  TechStackFileType,
  TimeStatsData,
  ExtendedDataFetchConfig,
} from "@/types/github-extended";
import { DEFAULT_EXTENDED_FETCH_CONFIG } from "@/types/github-extended";

// ==================== 工具函数 ====================

/**
 * 统一的GitHub API调用函数（复用现有实现）
 */
export async function fetchWithAuth(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  // 直接复用现有的token管理和认证逻辑
  const { getTokenManager } = await import("./token-manager");

  const tokenManager = getTokenManager();
  const token = tokenManager.getBestAvailableToken();

  if (!token) {
    throw new Error("No available GitHub tokens");
  }

  const headers = {
    Authorization: `Bearer ${token}`,
    Accept: "application/vnd.github.v3+json",
    "User-Agent": "github-card-app",
    ...options.headers,
  };

  try {
    const response = await fetch(url, { ...options, headers });

    // 记录成功调用
    if (response.ok) {
      tokenManager.recordTokenSuccess(token);
    } else if (response.status === 401 || response.status === 403) {
      // Token相关错误
      tokenManager.recordTokenError(
        token,
        new Error(`HTTP ${response.status}: ${response.statusText}`)
      );
    }

    return response;
  } catch (error) {
    // 网络或其他错误
    tokenManager.recordTokenError(token, error as Error);
    throw error;
  }
}

/**
 * 延迟函数，用于控制API调用频率
 */
function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// ==================== 仓库详细信息获取 ====================

export async function fetchRepositoryDetails(
  username: string,
  repos: any[], // GitHub API返回的完整仓库数据
  config: ExtendedDataFetchConfig = DEFAULT_EXTENDED_FETCH_CONFIG
): Promise<RepositoryDetailData[]> {
  console.log(
    `📦 Fetching repository details for ${username} (${repos.length} repos)`
  );

  // 按最近推送时间排序，取前N个仓库
  const targetRepos = repos
    .sort(
      (a, b) =>
        new Date(b.pushed_at).getTime() - new Date(a.pushed_at).getTime()
    )
    .slice(0, config.maxRepositories);

  const repositoryDetails: RepositoryDetailData[] = [];

  for (let i = 0; i < targetRepos.length; i++) {
    const repo = targetRepos[i];

    try {
      // 控制请求频率
      if (i > 0) {
        await delay(config.requestDelay);
      }

      console.log(
        `  📦 Processing ${repo.name} (${i + 1}/${targetRepos.length})`
      );

      // 获取仓库详细信息（已有数据，直接转换）
      const repoDetail: RepositoryDetailData = {
        name: repo.name,
        fullName: repo.full_name,
        description: repo.description,
        private: repo.private,
        language: repo.language,
        stargazersCount: repo.stargazers_count,
        forksCount: repo.forks_count,
        createdAt: repo.created_at,
        updatedAt: repo.updated_at,
        pushedAt: repo.pushed_at,
        size: repo.size,
        defaultBranch: repo.default_branch,
        topics: repo.topics || [],
        hasReadme: false, // Default value, will be updated if README is found
      };

      // 如果需要获取README
      if (config.includeReadme) {
        try {
          const readmeUrl = `https://api.github.com/repos/${repo.full_name}/readme`;
          const readmeResponse = await fetchWithAuth(readmeUrl, {
            next: { revalidate: 86400 * 7 }, // README缓存7天
          });

          if (readmeResponse.ok) {
            const readmeData = (await readmeResponse.json()) as any;
            repoDetail.hasReadme = true;
            repoDetail.readmeContent = Buffer.from(
              readmeData.content,
              "base64"
            ).toString("utf-8");
            repoDetail.readmeSize = readmeData.size;
          } else {
            repoDetail.hasReadme = false;
          }
        } catch (error) {
          console.warn(
            `    ⚠️ Failed to fetch README for ${repo.name}:`,
            error
          );
          repoDetail.hasReadme = false;
        }
      }

      repositoryDetails.push(repoDetail);
    } catch (error) {
      console.error(`❌ Error processing repository ${repo.name}:`, error);
    }
  }

  console.log(`✅ Fetched ${repositoryDetails.length} repository details`);
  return repositoryDetails;
}

// ==================== Commit信息获取 ====================

export async function fetchCommitDetails(
  username: string,
  repos: any[], // GitHub API返回的完整仓库数据
  config: ExtendedDataFetchConfig = DEFAULT_EXTENDED_FETCH_CONFIG
): Promise<CommitDetailData[]> {
  console.log(`💾 Fetching commit details for ${username}`);

  // 按最近更新时间排序，取前N个仓库
  const targetRepos = repos
    .sort(
      (a, b) =>
        new Date(b.pushed_at).getTime() - new Date(a.pushed_at).getTime()
    )
    .slice(0, config.maxRepositories);

  const commitDetails: CommitDetailData[] = [];

  for (let i = 0; i < targetRepos.length; i++) {
    const repo = targetRepos[i];

    try {
      // 控制请求频率
      if (i > 0) {
        await delay(config.requestDelay);
      }

      console.log(
        `  💾 Processing commits for ${repo.name} (${i + 1}/${
          targetRepos.length
        })`
      );

      // 获取仓库的commit历史
      const commitsUrl = `https://api.github.com/repos/${repo.full_name}/commits?per_page=${config.maxCommitsPerRepo}&author=${username}`;
      const commitsResponse = await fetchWithAuth(commitsUrl, {
        next: { revalidate: 3600 }, // commit数据缓存1小时
      });

      if (commitsResponse.ok) {
        const commits = (await commitsResponse.json()) as any[];

        for (const commit of commits) {
          const commitDetail: CommitDetailData = {
            sha: commit.sha,
            message: commit.commit.message,
            author: {
              name: commit.commit.author.name,
              email: commit.commit.author.email,
              date: commit.commit.author.date,
            },
            committer: {
              name: commit.commit.committer.name,
              email: commit.commit.committer.email,
              date: commit.commit.committer.date,
            },
            repository: repo.full_name,
          };

          // 如果有统计信息，添加进去
          if (commit.stats) {
            commitDetail.additions = commit.stats.additions;
            commitDetail.deletions = commit.stats.deletions;
            commitDetail.changedFiles = commit.files?.length || 0;
          }

          commitDetails.push(commitDetail);
        }

        console.log(
          `    ✅ Fetched ${commits.length} commits from ${repo.name}`
        );
      } else {
        console.warn(
          `    ⚠️ Failed to fetch commits for ${repo.name}: ${commitsResponse.status}`
        );
      }
    } catch (error) {
      console.error(`❌ Error fetching commits for ${repo.name}:`, error);
    }
  }

  console.log(`✅ Fetched ${commitDetails.length} total commits`);
  return commitDetails;
}

// ==================== README详细信息获取 ====================

export async function fetchReadmeDetails(
  username: string,
  repos: any[], // GitHub API返回的完整仓库数据
  config: ExtendedDataFetchConfig = DEFAULT_EXTENDED_FETCH_CONFIG
): Promise<ReadmeDetailData[]> {
  console.log(`📖 Fetching README details for ${username}`);

  const targetRepos = repos
    .sort(
      (a, b) =>
        new Date(b.pushed_at).getTime() - new Date(a.pushed_at).getTime()
    )
    .slice(0, config.maxRepositories);

  const readmeDetails: ReadmeDetailData[] = [];

  for (let i = 0; i < targetRepos.length; i++) {
    const repo = targetRepos[i];

    try {
      // 控制请求频率
      if (i > 0) {
        await delay(config.requestDelay);
      }

      console.log(
        `  📖 Processing README for ${repo.name} (${i + 1}/${
          targetRepos.length
        })`
      );

      const readmeUrl = `https://api.github.com/repos/${repo.full_name}/readme`;
      const readmeResponse = await fetchWithAuth(readmeUrl, {
        next: { revalidate: 86400 * 7 }, // README缓存7天
      });

      if (readmeResponse.ok) {
        const readmeData = (await readmeResponse.json()) as any;

        const readmeDetail: ReadmeDetailData = {
          repository: repo.full_name,
          name: readmeData.name,
          path: readmeData.path,
          content: readmeData.content, // 保持Base64编码
          size: readmeData.size,
          encoding: readmeData.encoding,
          sha: readmeData.sha,
          downloadUrl: readmeData.download_url,
        };

        readmeDetails.push(readmeDetail);
        console.log(
          `    ✅ Fetched README for ${repo.name} (${readmeData.size} bytes)`
        );
      } else {
        console.log(`    ℹ️ No README found for ${repo.name}`);
      }
    } catch (error) {
      console.error(`❌ Error fetching README for ${repo.name}:`, error);
    }
  }

  console.log(`✅ Fetched ${readmeDetails.length} README files`);
  return readmeDetails;
}

// ==================== 技术栈文件获取 ====================

/**
 * 技术栈文件类型映射
 */
const TECH_STACK_FILES: Record<string, TechStackFileType> = {
  "package.json": "package.json",
  "pyproject.toml": "pyproject.toml",
  "requirements.txt": "requirements.txt",
  Pipfile: "Pipfile",
  "Cargo.toml": "Cargo.toml",
  "go.mod": "go.mod",
  "composer.json": "composer.json",
  "pom.xml": "pom.xml",
  "build.gradle": "build.gradle",
  Gemfile: "Gemfile",
  "pubspec.yaml": "pubspec.yaml",
  "mix.exs": "mix.exs",
  "project.clj": "project.clj",
  "stack.yaml": "stack.yaml",
  "deno.json": "deno.json",
  "bun.lockb": "bun.lockb",
};

export async function fetchTechStackFiles(
  username: string,
  repos: any[], // GitHub API返回的完整仓库数据
  config: ExtendedDataFetchConfig = DEFAULT_EXTENDED_FETCH_CONFIG
): Promise<TechStackFileData[]> {
  console.log(`🔧 Fetching tech stack files for ${username}`);

  // 按最近推送时间排序，取前N个仓库
  const targetRepos = repos
    .sort(
      (a, b) =>
        new Date(b.pushed_at).getTime() - new Date(a.pushed_at).getTime()
    )
    .slice(0, config.maxRepositories);

  const techStackFiles: TechStackFileData[] = [];

  for (let i = 0; i < targetRepos.length; i++) {
    const repo = targetRepos[i];

    try {
      // 控制请求频率
      if (i > 0) {
        await delay(config.requestDelay);
      }

      console.log(
        `  🔧 Processing tech stack files for ${repo.name} (${i + 1}/${
          targetRepos.length
        })`
      );

      // 获取仓库根目录内容
      const contentsUrl = `https://api.github.com/repos/${repo.full_name}/contents`;
      const contentsResponse = await fetchWithAuth(contentsUrl, {
        next: { revalidate: 86400 * 30 }, // 技术栈文件缓存30天
      });

      if (!contentsResponse.ok) {
        console.warn(`    ⚠️ Failed to fetch contents for ${repo.name}`);
        continue;
      }

      const contents = (await contentsResponse.json()) as any[];

      // 查找技术栈文件
      for (const item of contents) {
        if (item.type === "file" && TECH_STACK_FILES[item.name]) {
          try {
            // 获取文件内容
            const fileResponse = await fetchWithAuth(item.url, {
              next: { revalidate: 86400 * 3 },
            });

            if (fileResponse.ok) {
              const fileData = (await fileResponse.json()) as any;

              const techStackFile: TechStackFileData = {
                repository: repo.full_name,
                name: item.name,
                fileType: TECH_STACK_FILES[item.name],
                path: item.path,
                content: fileData.content,
                size: fileData.size,
                encoding: fileData.encoding,
                sha: fileData.sha,
                downloadUrl: fileData.download_url,
              };

              techStackFiles.push(techStackFile);
              console.log(`    ✅ Fetched ${item.name} for ${repo.name}`);
            }
          } catch (error) {
            console.warn(
              `    ⚠️ Failed to fetch ${item.name} for ${repo.name}:`,
              error
            );
          }
        }
      }
    } catch (error) {
      console.error(
        `❌ Error processing tech stack files for ${repo.name}:`,
        error
      );
    }
  }

  console.log(`✅ Fetched ${techStackFiles.length} tech stack files`);
  return techStackFiles;
}

// ==================== 时间分布统计获取 ====================

export async function fetchTimeStats(
  username: string,
  repos: any[], // GitHub API返回的完整仓库数据
  config: ExtendedDataFetchConfig = DEFAULT_EXTENDED_FETCH_CONFIG
): Promise<TimeStatsData[]> {
  console.log(`⏰ Fetching time statistics for ${username}`);

  const targetRepos = repos
    .sort(
      (a, b) =>
        new Date(b.pushed_at).getTime() - new Date(a.pushed_at).getTime()
    )
    .slice(0, Math.min(5, config.maxRepositories)); // 时间统计只取前5个仓库，避免API调用过多

  const timeStatsData: TimeStatsData[] = [];

  for (let i = 0; i < targetRepos.length; i++) {
    const repo = targetRepos[i];

    try {
      // 控制请求频率
      if (i > 0) {
        await delay(config.requestDelay * 2); // 时间统计API调用间隔更长
      }

      console.log(
        `  ⏰ Processing time stats for ${repo.name} (${i + 1}/${
          targetRepos.length
        })`
      );

      // 获取punch card数据（每小时提交统计）
      const punchCardUrl = `https://api.github.com/repos/${repo.full_name}/stats/punch_card`;
      const punchCardResponse = await fetchWithAuth(punchCardUrl, {
        next: { revalidate: 86400 }, // 时间统计缓存1天
      });

      // 获取commit activity数据
      const commitActivityUrl = `https://api.github.com/repos/${repo.full_name}/stats/commit_activity`;
      const commitActivityResponse = await fetchWithAuth(commitActivityUrl, {
        next: { revalidate: 86400 },
      });

      // 获取contributors数据
      const contributorsUrl = `https://api.github.com/repos/${repo.full_name}/stats/contributors`;
      const contributorsResponse = await fetchWithAuth(contributorsUrl, {
        next: { revalidate: 86400 },
      });

      const timeStats: TimeStatsData = {
        repository: repo.full_name,
        punchCard: [],
        commitActivity: [],
        contributors: [],
      };

      // 处理punch card数据
      if (punchCardResponse.ok) {
        const punchCardData = (await punchCardResponse.json()) as Array<
          [number, number, number]
        >;
        timeStats.punchCard = punchCardData;
        console.log(`    ✅ Fetched punch card data for ${repo.name}`);
      }

      // 处理commit activity数据
      if (commitActivityResponse.ok) {
        const commitActivityData =
          (await commitActivityResponse.json()) as any[];
        timeStats.commitActivity = commitActivityData;
        console.log(`    ✅ Fetched commit activity data for ${repo.name}`);
      }

      // 处理contributors数据
      if (contributorsResponse.ok) {
        const contributorsData = (await contributorsResponse.json()) as any[];
        timeStats.contributors = contributorsData;
        console.log(`    ✅ Fetched contributors data for ${repo.name}`);
      }

      timeStatsData.push(timeStats);
    } catch (error) {
      console.error(`❌ Error fetching time stats for ${repo.name}:`, error);
    }
  }

  console.log(
    `✅ Fetched time statistics for ${timeStatsData.length} repositories`
  );
  return timeStatsData;
}

// ==================== 主要获取函数 ====================

export async function fetchExtendedGitHubData(
  username: string,
  repos: any[], // GitHub API返回的完整仓库数据
  config: ExtendedDataFetchConfig = DEFAULT_EXTENDED_FETCH_CONFIG
): Promise<GitHubExtendedData> {
  console.log(
    `🔧 [ExtendedFetch] Starting for ${username} with ${repos.length} repos`
  );
  const startTime = Date.now();

  const extendedData: GitHubExtendedData = {
    version: "1.0.0",
    fetchedAt: Date.now(),
    username,
    repositories: [],
    commits: [],
    readmes: [],
    techStackFiles: [],
    timeStats: [],
    fetchConfig: config,
  };

  try {
    // 1. 获取仓库详细信息
    console.log(`📦 [ExtendedFetch] Fetching repository details`);
    extendedData.repositories = await fetchRepositoryDetails(
      username,
      repos,
      config
    );
    console.log(
      `📦 [ExtendedFetch] Got ${extendedData.repositories.length} repository details`
    );

    // 2. 获取commit信息
    console.log(`💾 [ExtendedFetch] Fetching commit details`);
    extendedData.commits = await fetchCommitDetails(username, repos, config);
    console.log(
      `💾 [ExtendedFetch] Got ${extendedData.commits.length} commits`
    );

    // 3. 获取README信息（如果启用）
    if (config.includeReadme) {
      console.log(`📄 [ExtendedFetch] Fetching README files`);
      extendedData.readmes = await fetchReadmeDetails(username, repos, config);
      console.log(
        `📄 [ExtendedFetch] Got ${extendedData.readmes.length} README files`
      );
    }

    // 4. 获取技术栈文件（如果启用）
    if (config.includeTechStackFiles) {
      console.log(`🔧 [ExtendedFetch] Fetching tech stack files`);
      extendedData.techStackFiles = await fetchTechStackFiles(
        username,
        repos,
        config
      );
      console.log(
        `🔧 [ExtendedFetch] Got ${extendedData.techStackFiles.length} tech stack files`
      );
    }

    // 5. 获取时间统计（如果启用）
    if (config.includeTimeStats) {
      console.log(`📊 [ExtendedFetch] Fetching time stats`);
      extendedData.timeStats = await fetchTimeStats(username, repos, config);
      console.log(
        `📊 [ExtendedFetch] Got ${extendedData.timeStats.length} time stats`
      );
    }

    const processingTime = Date.now() - startTime;
    console.log(
      `✅ Extended GitHub data fetch completed in ${processingTime}ms`
    );
    console.log(`📊 Data summary:`);
    console.log(`   - Repositories: ${extendedData.repositories.length}`);
    console.log(`   - Commits: ${extendedData.commits.length}`);
    console.log(`   - READMEs: ${extendedData.readmes.length}`);
    console.log(`   - Time Stats: ${extendedData.timeStats.length}`);

    return extendedData;
  } catch (error) {
    console.error(`❌ Error during extended GitHub data fetch:`, error);
    throw error;
  }
}
