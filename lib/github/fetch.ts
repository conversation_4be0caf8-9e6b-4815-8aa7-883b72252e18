import {
  GitHubRepoResponse,
  GitHubContributionsResponse,
  GitHubUserData,
  GitHubContributionsData,
  LanguageStatsSummary,
  LanguageStatEntry,
} from "@/types/github";
import { calculateMultiDimensionScores } from "./score";
import { getTokenManager } from "./token-manager";

// V4 统一Token管理系统
let tokenManager: ReturnType<typeof getTokenManager> | null = null;

// 初始化Token管理器
function getGitHubTokenManager() {
  if (!tokenManager) {
    tokenManager = getTokenManager();
  }
  return tokenManager;
}

// 统一的GitHub API调用函数
async function fetchWithAuth(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const manager = getGitHubTokenManager();
  const token = manager.getBestAvailableToken();

  if (!token) {
    throw new Error("No available GitHub tokens");
  }

  const headers = {
    Authorization: `Bearer ${token}`,
    Accept: "application/vnd.github.v3+json",
    "User-Agent": "github-card-app",
    ...options.headers,
  };

  try {
    const response = await fetch(url, { ...options, headers });

    // 记录成功调用
    if (response.ok) {
      manager.recordTokenSuccess(token);
    } else if (response.status === 401 || response.status === 403) {
      // Token相关错误
      manager.recordTokenError(
        token,
        new Error(`HTTP ${response.status}: ${response.statusText}`)
      );
    }

    return response;
  } catch (error) {
    // 网络或其他错误
    manager.recordTokenError(token, error as Error);
    throw error;
  }
}

// V4 数据获取辅助函数

// 计算总Fork数
async function calculateTotalForks(
  reposData: GitHubRepoResponse[]
): Promise<number> {
  return reposData.reduce((sum, repo) => sum + (repo.forks_count || 0), 0);
}

// GraphQL查询辅助函数
async function fetchGraphQL(
  query: string,
  variables: Record<string, any> = {}
): Promise<any> {
  try {
    const tokenManager = getGitHubTokenManager();
    const token = tokenManager.getBestAvailableToken();

    if (!token) {
      throw new Error("No available GitHub tokens for GraphQL");
    }

    const response = await fetch("https://api.github.com/graphql", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "User-Agent": "github-card-app/1.0.0",
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    if (!response.ok) {
      // 记录Token错误
      if (response.status === 401 || response.status === 403) {
        tokenManager.recordTokenError(
          token,
          new Error(`GraphQL HTTP ${response.status}: ${response.statusText}`)
        );
      }
      throw new Error(`GraphQL API responded with status ${response.status}`);
    }

    const data = (await response.json()) as {
      data?: any;
      errors?: any[];
    };

    if (data.errors) {
      console.warn("GraphQL errors:", data.errors);
      throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
    }

    // 记录成功调用
    tokenManager.recordTokenSuccess(token);

    return data.data;
  } catch (error) {
    console.warn("GraphQL request failed:", error);
    throw error;
  }
}

// 获取贡献仓库数（使用 GraphQL API - 更准确的实现）
async function getContributedReposCount(username: string): Promise<number> {
  try {
    console.log(`🔍 [getContributedReposCount] Starting for user: ${username}`);

    // 🎯 使用GraphQL获取用户贡献的仓库总数（不受时间限制）
    // contributionsCollection 仅用于获取过去1年的活跃度数据
    const oneYearAgo = new Date(
      Date.now() - 365 * 24 * 60 * 60 * 1000
    ).toISOString();
    const now = new Date().toISOString();

    const query = `
      query($username: String!, $from: DateTime!, $to: DateTime!) {
        user(login: $username) {
          contributionsCollection(from: $from, to: $to) {
            contributionCalendar {
              totalContributions
            }
          }
          repositoriesContributedTo(first: 1) {
            totalCount
          }
        }
      }
    `;

    console.log(
      `📡 [getContributedReposCount] Calling GraphQL for ${username}`
    );

    const data = await fetchGraphQL(query, {
      username,
      from: oneYearAgo,
      to: now,
    });

    console.log(
      `📊 [getContributedReposCount] GraphQL response for ${username}:`,
      {
        hasUser: !!data?.user,
        hasRepositoriesContributedTo: !!data?.user?.repositoriesContributedTo,
        totalCount: data?.user?.repositoriesContributedTo?.totalCount,
        rawData: JSON.stringify(data, null, 2),
      }
    );

    if (!data?.user?.repositoriesContributedTo) {
      console.warn(
        `⚠️ [getContributedReposCount] No contribution data found for user: ${username}`
      );
      console.warn(`📊 Raw data structure:`, data);
      return 0;
    }

    const contributedReposCount =
      data.user.repositoriesContributedTo.totalCount;

    console.log(
      `✅ [getContributedReposCount] ${username} contributed to ${contributedReposCount} repositories (all time)`
    );

    return contributedReposCount || 0;
  } catch (error) {
    console.error(
      `❌ [getContributedReposCount] GraphQL failed for ${username}:`,
      {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        type: typeof error,
        username,
      }
    );

    // 在生产环境中，我们不应该静默返回0，而应该抛出错误或使用备用方案
    if (process.env.NODE_ENV === "production") {
      console.error(
        `🚨 [getContributedReposCount] Production error - contributed_repos will be 0 for ${username}`
      );
    }

    return 0;
  }
}

// V4.1 优化：获取编程语言基础统计（精简数据结构，减少存储压力）
async function getLanguageStatistics(
  username: string,
  reposData: GitHubRepoResponse[]
): Promise<LanguageStatsSummary> {
  // 检查是否启用语言多样性分析

  try {
    console.log(
      `🔍 [getLanguageStatistics] Starting optimized analysis for ${username}`
    );

    const maxRepos = parseInt(process.env.MAX_LANGUAGE_ANALYSIS_REPOS || "50");
    const analysisRepos = reposData.slice(0, maxRepos);
    const languageMap = new Map<string, LanguageStatEntry>();

    console.log(
      `📊 Analyzing ${analysisRepos.length} repositories (max: ${maxRepos})`
    );

    // 第一阶段：从仓库元数据获取主要语言（优化：不存储详细仓库信息）
    let basicLanguageCount = 0;
    for (const repo of analysisRepos) {
      if (repo.language) {
        const existing =
          languageMap.get(repo.language) ||
          createEmptyLanguageEntry(repo.language);
        existing.primaryRepoCount++;
        existing.repoCount++;
        // 优化：移除 repos 数组的详细信息存储，减少存储压力
        languageMap.set(repo.language, existing);
        basicLanguageCount++;
      }
    }

    console.log(
      `📝 Found ${languageMap.size} languages from basic repo metadata (${basicLanguageCount}/${analysisRepos.length} repos have primary language)`
    );

    // 第二阶段：详细语言分析（优化：减少存储，只统计语言数量）
    if (
      process.env.ENABLE_DETAILED_LANGUAGE_ANALYSIS === "true" &&
      analysisRepos.length <= maxRepos
    ) {
      console.log(
        `🔬 Performing optimized detailed language analysis on top ${Math.min(
          analysisRepos.length,
          15
        )} repositories`
      );

      const detailedAnalysisPromises = analysisRepos
        .slice(0, 15) // 限制详细分析的仓库数
        .map(async (repo, index) => {
          try {
            // 错开请求时间，避免限流
            await new Promise((resolve) => setTimeout(resolve, index * 60));

            const languagesUrl = `https://api.github.com/repos/${repo.full_name}/languages`;
            const languagesResponse = await fetchWithAuth(languagesUrl, {
              next: { revalidate: 86400 * 3 }, // 调整缓存时间为3天
            });

            if (languagesResponse.ok) {
              const languagesData: Record<string, number> =
                await languagesResponse.json();
              console.log(
                `  📁 ${repo.full_name}: ${
                  Object.keys(languagesData).length
                } languages`
              );

              return {
                repo,
                languages: languagesData,
              };
            } else {
              console.warn(
                `  ⚠️ Failed to fetch languages for ${repo.full_name}: HTTP ${languagesResponse.status}`
              );
              return null;
            }
          } catch (error) {
            console.warn(
              `  ❌ Error fetching languages for ${repo.full_name}:`,
              error instanceof Error ? error.message : String(error)
            );
            return null;
          }
        });

      try {
        const detailedResults = await Promise.all(detailedAnalysisPromises);
        let detailedLanguageCount = 0;

        detailedResults.forEach((result) => {
          if (!result) return;

          const { repo, languages } = result;

          Object.entries(languages).forEach(([langName, bytes]) => {
            const existing =
              languageMap.get(langName) || createEmptyLanguageEntry(langName);

            // 优化：只统计仓库数量，不存储字节数和详细仓库信息
            existing.repoCount = Math.max(existing.repoCount, 1); // 确保至少为1

            if (!languageMap.has(langName)) {
              detailedLanguageCount++;
            }
            languageMap.set(langName, existing);
          });
        });

        console.log(
          `🔬 Optimized detailed analysis added ${detailedLanguageCount} new languages`
        );
      } catch (error) {
        console.warn(
          `⚠️ Detailed language analysis failed, using basic data:`,
          error instanceof Error ? error.message : String(error)
        );
      }
    }

    // 第三阶段：计算统计数据（优化：精简数据结构）
    const languages = Array.from(languageMap.values());
    const totalRepos = analysisRepos.length;

    // 计算百分比（优化：只计算按仓库数的百分比，移除字节相关计算）
    languages.forEach((lang) => {
      lang.percentageByRepos =
        totalRepos > 0
          ? Math.round((lang.repoCount / totalRepos) * 10000) / 100
          : 0;
    });

    // 按仓库数排序（优化：改为按仓库数排序，因为字节数据质量差）
    languages.sort((a, b) => b.repoCount - a.repoCount);

    const primaryLanguage = languages.length > 0 ? languages[0] : undefined;

    // 优化：返回精简的数据结构
    const result: LanguageStatsSummary = {
      totalLanguages: languages.length,
      totalBytes: 0, // 优化：移除字节统计，设为0保持兼容性
      analyzedRepos: analysisRepos.length,
      languages: languages.map((lang) => ({
        language: lang.language,
        totalBytes: 0, // 优化：移除字节数据
        repoCount: lang.repoCount,
        primaryRepoCount: lang.primaryRepoCount,
        percentageByBytes: 0, // 优化：移除字节百分比
        percentageByRepos: lang.percentageByRepos,
        languageRank: 0, // 优化：移除排名，可实时计算
        repos: [], // 优化：移除详细仓库列表，大幅减少存储
      })),
      primaryLanguage: primaryLanguage
        ? {
            language: primaryLanguage.language,
            totalBytes: 0,
            repoCount: primaryLanguage.repoCount,
            primaryRepoCount: primaryLanguage.primaryRepoCount,
            percentageByBytes: 0,
            percentageByRepos: primaryLanguage.percentageByRepos,
            languageRank: 1,
            repos: [],
          }
        : undefined,
      metadata: {
        analysisVersion: "v4.1-optimized",
        lastUpdated: Date.now(),
        cacheExpiry: Date.now() + 86400 * 3 * 1000, // 3天后过期
      },
    };

    // 详细日志记录（优化版本）
    console.log(
      `✅ [getLanguageStatistics] Optimized analysis completed for ${username}:`
    );
    console.log(`   📊 Total languages: ${languages.length}`);
    console.log(
      `   📁 Repositories analyzed: ${analysisRepos.length}/${reposData.length}`
    );
    console.log(
      `   🏆 Primary language: ${primaryLanguage?.language || "None"} (${
        primaryLanguage?.percentageByRepos || 0
      }% of repos)`
    );
    console.log(
      `   🔝 Top 5 languages: ${languages
        .slice(0, 5)
        .map((l) => `${l.language}(${l.percentageByRepos}% repos)`)
        .join(", ")}`
    );
    console.log(
      `   💾 Storage optimized: removed repos details and byte counts`
    );

    return result;
  } catch (error) {
    console.error(
      `❌ [getLanguageStatistics] Critical error for ${username}:`,
      {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        reposCount: reposData.length,
      }
    );
    return createEmptyLanguageStats();
  }
}

// 辅助函数：创建空的语言统计（优化版本）
function createEmptyLanguageStats(): LanguageStatsSummary {
  return {
    totalLanguages: 0,
    totalBytes: 0, // 保持兼容性
    analyzedRepos: 0,
    languages: [],
    primaryLanguage: undefined,
    metadata: {
      analysisVersion: "v4.1-optimized",
      lastUpdated: Date.now(),
      cacheExpiry: Date.now() + 86400 * 3 * 1000,
    },
  };
}

// 辅助函数：创建空的语言条目（优化版本）
function createEmptyLanguageEntry(language: string): LanguageStatEntry {
  return {
    language,
    totalBytes: 0, // 保持兼容性，但不实际使用
    repoCount: 0,
    primaryRepoCount: 0,
    percentageByBytes: 0, // 保持兼容性，但不实际使用
    percentageByRepos: 0,
    languageRank: 0, // 保持兼容性，但不实际使用
    repos: [], // 优化：始终为空数组，减少存储
  };
}

// 保持向后兼容的包装函数（通过 languageStats 计算语言数量）
async function getLanguageDiversity(
  username: string,
  reposData: GitHubRepoResponse[]
): Promise<number> {
  const stats = await getLanguageStatistics(username, reposData);
  return stats.totalLanguages;
}

// GitHub API请求函数

export async function fetchGitHubUserData(
  username: string
): Promise<GitHubUserData> {
  try {
    const userResponse = await fetchWithAuth(
      `https://api.github.com/users/${username}`,
      {
        next: { revalidate: 86400 },
      }
    );

    if (!userResponse.ok) {
      if (userResponse.status === 404) {
        return {
          github_id: 0,
          login: username,
          name: username,
          avatar_url: "https://github.com/identicons/avatar.png",
          bio: "",
          blog: "",
          location: "",
          twitter_username: "",
          public_repos: 0,
          followers: 0,
          following: 0,
          created_at: new Date().getTime(),
        };
      }
      throw new Error(
        `GitHub API responded with status ${userResponse.status}`
      );
    }

    const userData = (await userResponse.json()) as any;
    console.log("1111 github userData", userData);
    return {
      github_id: userData.id,
      login: userData.login,
      name: userData.name,
      avatar_url: userData.avatar_url,
      bio: userData.bio,
      blog: userData.blog,
      location: userData.location,
      twitter_username: userData.twitter_username,
      public_repos: userData.public_repos,
      followers: userData.followers,
      following: userData.following,
      created_at: new Date(userData.created_at).getTime(), // GitHub API 返回字符串，转换为毫秒时间戳
    };
  } catch (error) {
    throw error;
  }
}

export async function fetchGitHubContributions(
  username: string
): Promise<GitHubContributionsData> {
  try {
    // 并行获取多个API数据，提高效率
    const [
      reposResponse,
      commitsResponse,
      prsResponse,
      issuesResponse,
      reviewsResponse,
      userResponse,
    ] = await Promise.all([
      // 获取用户仓库
      fetchWithAuth(
        `https://api.github.com/users/${username}/repos?per_page=100`,
        {
          next: { revalidate: 86400 },
        }
      ),
      // 获取最近10年的提交
      fetchWithAuth(
        `https://api.github.com/search/commits?q=author:${username}+author-date:>=${new Date(
          Date.now() - 10 * 365 * 24 * 60 * 60 * 1000
        ).toISOString()}`,
        {
          next: { revalidate: 86400 },
        }
      ),
      // 获取PR
      fetchWithAuth(
        `https://api.github.com/search/issues?q=author:${username}+type:pr`,
        {
          next: { revalidate: 86400 },
        }
      ),
      // 获取Issues
      fetchWithAuth(
        `https://api.github.com/search/issues?q=author:${username}+type:issue`,
        {
          next: { revalidate: 86400 },
        }
      ),
      // 获取Reviews
      fetchWithAuth(
        `https://api.github.com/search/issues?q=reviewed-by:${username}+type:pr`,
        {
          next: { revalidate: 86400 },
        }
      ),
      // 获取用户信息（补充followers等数据）
      fetchWithAuth(`https://api.github.com/users/${username}`, {
        next: { revalidate: 86400 },
      }),
    ]);

    // 处理仓库数据
    const reposData: GitHubRepoResponse[] = reposResponse.ok
      ? await reposResponse.json()
      : [];

    // 处理贡献数据
    const commitsData: GitHubContributionsResponse = commitsResponse.ok
      ? await commitsResponse.json()
      : { total_count: 0 };

    const prsData: GitHubContributionsResponse = prsResponse.ok
      ? await prsResponse.json()
      : { total_count: 0 };

    const issuesData: GitHubContributionsResponse = issuesResponse.ok
      ? await issuesResponse.json()
      : { total_count: 0 };

    const reviewsData: GitHubContributionsResponse = reviewsResponse.ok
      ? await reviewsResponse.json()
      : { total_count: 0 };

    // 处理用户数据
    let followers = 0;
    let following = 0;
    let created_at = new Date().getTime();
    if (userResponse.ok) {
      const userData = (await userResponse.json()) as any;
      followers = userData.followers;
      following = userData.following;
      created_at = new Date(userData.created_at).getTime(); // GitHub API 返回字符串，转换为毫秒时间戳
    }

    // 计算基础指标
    const totalStars = reposData.reduce(
      (sum, repo) => sum + repo.stargazers_count,
      0
    );

    // V4 新增数据获取
    const totalForks = await calculateTotalForks(reposData);
    const contributedRepos = await getContributedReposCount(username);
    const languageStats = await getLanguageStatistics(username, reposData);

    // Calculate contribution score (V4.2 增强版 - 包含新增数据)
    const { totalScore: contribution_score } = calculateMultiDimensionScores({
      commits: commitsData.total_count,
      contributedRepos,
      pullRequests: prsData.total_count,
      reviews: reviewsData.total_count,
      issues: issuesData.total_count,
      totalStars,
      totalForks,
      followers,
      languageDiversity: languageStats.totalLanguages,
      publicRepos: reposData.length,
      // V4.2 新增参数
      following,
      createdAt: created_at,
    });

    console.log(`Contribution score for ${username}:`, contribution_score);

    return {
      commits: commitsData.total_count,
      pull_requests: prsData.total_count,
      contribution_score, // 保留：用于排行榜性能优化
      total_stars: totalStars,
      public_repos: reposData.length,
      followers,
      following,
      issues: issuesData.total_count,
      reviews: reviewsData.total_count,
      // V4 新增字段 - 实际数据
      total_forks: totalForks,
      contributed_repos: contributedRepos,
      // V4.1 新增字段 - 语言统计（移除 language_diversity，使用 language_stats）
      language_stats: languageStats,
    };
  } catch (error) {
    throw error;
  }
}
