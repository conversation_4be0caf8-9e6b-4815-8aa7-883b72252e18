import { TokenInfo, RateLimitInfo } from "@/types/github";

/**
 * GitHub Token管理器
 * 负责Token池管理、健康检查和自动故障转移
 */
export class GitHubTokenManager {
  private tokens: Map<string, TokenInfo> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private readonly HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟
  private readonly MAX_ERROR_COUNT = 3;
  private readonly TOKEN_RECOVERY_TIME = 15 * 60 * 1000; // 15分钟

  constructor(tokens: string[]) {
    this.initializeTokens(tokens);
    this.startHealthCheck();
  }

  /**
   * 初始化Token池
   */
  private initializeTokens(tokens: string[]): void {
    tokens.forEach((token) => {
      if (token && token.trim()) {
        this.tokens.set(token, {
          token,
          isActive: true,
          lastUsed: 0,
          errorCount: 0,
        });
      }
    });

    if (this.tokens.size === 0) {
      console.warn("No valid GitHub tokens provided");
    } else {
      console.info(`Initialized ${this.tokens.size} GitHub tokens`);
    }
  }

  /**
   * 获取最佳可用Token
   * 优先选择错误次数少、最近使用时间较早的Token
   */
  getBestAvailableToken(): string | null {
    const activeTokens = Array.from(this.tokens.values()).filter(
      (tokenInfo) => tokenInfo.isActive
    );

    // 简化token日志

    const sortedTokens = activeTokens.sort((a, b) => {
      // 首先按错误次数排序
      if (a.errorCount !== b.errorCount) {
        return a.errorCount - b.errorCount;
      }
      // 然后按最后使用时间排序（越早越好）
      return a.lastUsed - b.lastUsed;
    });

    if (activeTokens.length === 0) {
      console.error("No active tokens available");
      return null;
    }

    const selectedToken = activeTokens[0];
    selectedToken.lastUsed = Date.now();

    return selectedToken.token;
  }

  /**
   * 获取所有可用Token
   */
  getAllAvailableTokens(): string[] {
    return Array.from(this.tokens.values())
      .filter((tokenInfo) => tokenInfo.isActive)
      .map((tokenInfo) => tokenInfo.token);
  }

  /**
   * 更新Token的限流信息
   */
  updateTokenRateLimit(token: string, rateLimitInfo: RateLimitInfo): void {
    const tokenInfo = this.tokens.get(token);
    if (!tokenInfo) {
      console.warn(`Token not found: ${this.maskToken(token)}`);
      return;
    }

    tokenInfo.rateLimitInfo = rateLimitInfo;

    // 如果剩余调用次数很少，暂时禁用Token
    if (rateLimitInfo.remaining < 50) {
      this.disableToken(
        token,
        `Low rate limit remaining: ${rateLimitInfo.remaining}`
      );

      // 计算重置时间并安排重新启用
      const resetTime = rateLimitInfo.reset * 1000;
      const now = Date.now();
      const waitTime = Math.max(0, resetTime - now);

      setTimeout(() => {
        this.enableToken(token, "Rate limit reset");
      }, waitTime);
    }
  }

  /**
   * 记录Token错误
   */
  recordTokenError(token: string, error: Error): void {
    const tokenInfo = this.tokens.get(token);
    if (!tokenInfo) {
      console.warn(`Token not found: ${this.maskToken(token)}`);
      return;
    }

    tokenInfo.errorCount++;
    console.warn(
      `Token error recorded for ${this.maskToken(token)}: ${
        error.message
      } (count: ${tokenInfo.errorCount})`
    );

    // 如果错误次数过多，禁用Token
    if (tokenInfo.errorCount >= this.MAX_ERROR_COUNT) {
      this.disableToken(token, `Too many errors: ${tokenInfo.errorCount}`);

      // 安排Token恢复
      setTimeout(() => {
        this.recoverToken(token);
      }, this.TOKEN_RECOVERY_TIME);
    }
  }

  /**
   * 记录成功的API调用
   */
  recordTokenSuccess(token: string): void {
    const tokenInfo = this.tokens.get(token);
    if (tokenInfo) {
      // 成功调用后减少错误计数
      if (tokenInfo.errorCount > 0) {
        tokenInfo.errorCount = Math.max(0, tokenInfo.errorCount - 1);
      }
      tokenInfo.lastUsed = Date.now();
    }
  }

  /**
   * 禁用Token
   */
  private disableToken(token: string, reason: string): void {
    const tokenInfo = this.tokens.get(token);
    if (tokenInfo && tokenInfo.isActive) {
      tokenInfo.isActive = false;
      console.warn(`Token disabled: ${this.maskToken(token)} - ${reason}`);
    }
  }

  /**
   * 启用Token
   */
  private enableToken(token: string, reason: string): void {
    const tokenInfo = this.tokens.get(token);
    if (tokenInfo && !tokenInfo.isActive) {
      tokenInfo.isActive = true;
      console.info(`Token enabled: ${this.maskToken(token)} - ${reason}`);
    }
  }

  /**
   * 恢复Token（重置错误计数并启用）
   */
  private recoverToken(token: string): void {
    const tokenInfo = this.tokens.get(token);
    if (tokenInfo) {
      tokenInfo.errorCount = 0;
      tokenInfo.isActive = true;
      console.info(`Token recovered: ${this.maskToken(token)}`);
    }
  }

  /**
   * 健康检查
   */
  private async performHealthCheck(): Promise<void> {
    console.info("Performing GitHub tokens health check...");

    const tokens = Array.from(this.tokens.keys());
    const healthCheckPromises = tokens.map((token) =>
      this.checkTokenHealth(token)
    );

    try {
      await Promise.allSettled(healthCheckPromises);
    } catch (error) {
      console.error("Health check failed:", error);
    }
  }

  /**
   * 检查单个Token的健康状态
   */
  private async checkTokenHealth(token: string): Promise<void> {
    try {
      const response = await fetch("https://api.github.com/rate_limit", {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/vnd.github.v3+json",
          "User-Agent": "GitHub-Card-App",
        },
      });

      if (response.ok) {
        const data = (await response.json()) as any;
        const rateLimitInfo: RateLimitInfo = {
          limit: data.rate.limit,
          remaining: data.rate.remaining,
          reset: data.rate.reset,
          used: data.rate.used,
        };

        this.updateTokenRateLimit(token, rateLimitInfo);
        this.recordTokenSuccess(token);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error(
        `Health check failed for token ${this.maskToken(token)}:`,
        error
      );
      this.recordTokenError(token, error as Error);
    }
  }

  /**
   * 开始定期健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.HEALTH_CHECK_INTERVAL);

    // 立即执行一次健康检查
    this.performHealthCheck();
  }

  /**
   * 停止健康检查
   */
  stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * 获取Token统计信息
   */
  getTokenStats() {
    const allTokens = Array.from(this.tokens.values());
    const activeTokens = allTokens.filter((t) => t.isActive);
    const inactiveTokens = allTokens.filter((t) => !t.isActive);

    return {
      total: allTokens.length,
      active: activeTokens.length,
      inactive: inactiveTokens.length,
      tokens: allTokens.map((token) => ({
        token: this.maskToken(token.token),
        isActive: token.isActive,
        errorCount: token.errorCount,
        lastUsed: token.lastUsed,
        rateLimitInfo: token.rateLimitInfo,
      })),
    };
  }

  /**
   * 重置所有Token状态
   */
  resetAllTokens(): void {
    this.tokens.forEach((tokenInfo) => {
      tokenInfo.isActive = true;
      tokenInfo.errorCount = 0;
      tokenInfo.rateLimitInfo = undefined;
      tokenInfo.lastUsed = 0;
    });
    console.info("All tokens have been reset");
  }

  /**
   * 添加新Token
   */
  addToken(token: string): boolean {
    if (!token || token.trim() === "") {
      return false;
    }

    if (this.tokens.has(token)) {
      console.warn(`Token already exists: ${this.maskToken(token)}`);
      return false;
    }

    this.tokens.set(token, {
      token,
      isActive: true,
      lastUsed: 0,
      errorCount: 0,
    });

    console.info(`New token added: ${this.maskToken(token)}`);

    // 立即检查新Token的健康状态
    this.checkTokenHealth(token);

    return true;
  }

  /**
   * 移除Token
   */
  removeToken(token: string): boolean {
    if (this.tokens.delete(token)) {
      console.info(`Token removed: ${this.maskToken(token)}`);
      return true;
    }
    return false;
  }

  /**
   * 掩码Token（用于日志记录）
   */
  private maskToken(token: string): string {
    if (token.length <= 8) {
      return "*".repeat(token.length);
    }
    return (
      token.substring(0, 4) +
      "*".repeat(token.length - 8) +
      token.substring(token.length - 4)
    );
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopHealthCheck();
    this.tokens.clear();
  }
}

// 单例实例
let tokenManager: GitHubTokenManager | null = null;

/**
 * 获取Token管理器实例 - V4 统一配置
 * 使用统一的 GITHUB_TOKENS 环境变量
 */
export function getTokenManager(): GitHubTokenManager {
  if (!tokenManager) {
    // 统一Token配置：仅使用 GITHUB_TOKENS
    let tokens: string[] = [];

    if (process.env.GITHUB_TOKENS) {
      tokens = process.env.GITHUB_TOKENS.split(",")
        .map((t) => t.trim())
        .filter(Boolean);
      console.log(`🔑 [TokenManager] Found ${tokens.length} tokens`);
    } else {
      console.error(
        `❌ [TokenManager] GITHUB_TOKENS environment variable not found`
      );
    }

    if (tokens.length === 0) {
      console.error(`❌ [TokenManager] No valid tokens found`);
      throw new Error(
        "GITHUB_TOKENS environment variable is required. Please set it with one or more comma-separated GitHub tokens."
      );
    }

    tokenManager = new GitHubTokenManager(tokens);
    console.log(`✅ [TokenManager] Initialized with ${tokens.length} token(s)`);
  }
  return tokenManager;
}

/**
 * 初始化Token管理器
 */
export function initializeTokenManager(tokens: string[]): GitHubTokenManager {
  if (tokenManager) {
    tokenManager.destroy();
  }
  tokenManager = new GitHubTokenManager(tokens);
  return tokenManager;
}
