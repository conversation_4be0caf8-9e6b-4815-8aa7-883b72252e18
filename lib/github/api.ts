import { cache } from "react";
import { cacheManager } from "../cloudflare/kv-cache-manager";
import { GitHubUserData, GitHubContributionsData } from "@/types/github";
import { fetchGitHubUserData, fetchGitHubContributions } from "./fetch";
import { getDb } from "../db";
import { aiDescriptions, shareLinks, contributeDatas } from "../db/schema";
import { eq, and, desc } from "drizzle-orm";

export const getGitHubUserData = cache(
  async (username: string): Promise<GitHubUserData> => {
    const cacheKey = `github:user:${username}`;
    try {
      const cachedData = await cacheManager.get<GitHubUserData>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const userData = await fetchGitHubUserData(username);
      await cacheManager.set(cacheKey, userData, { expirationTtl: 60 * 60 });
      return userData;
    } catch (error) {
      console.error(`Error in getGitHubUserData for ${username}:`, error);
      throw error;
    }
  }
);

export const getGitHubContributions = cache(
  async (username: string): Promise<GitHubContributionsData> => {
    const cacheKey = `github:contributions:${username}`;
    try {
      const cachedData = await cacheManager.get<GitHubContributionsData>(
        cacheKey
      );
      if (cachedData) {
        return cachedData;
      }
      const contributionsData = await fetchGitHubContributions(username);
      await cacheManager.set(cacheKey, contributionsData, {
        expirationTtl: 60 * 60,
      });
      return contributionsData;
    } catch (error) {
      console.error(`Error in getGitHubContributions for ${username}:`, error);
      throw error;
    }
  }
);

/**
 * Phase 2B: 用户描述缓存 - 扩展现有缓存模式
 * 复用统一的缓存基础设施
 */
export const getUserDescriptionCached = cache(
  async (userId: string): Promise<UserDescription | null> => {
    const cacheKey = `user:description:${userId}`;
    try {
      const cachedData = await cacheManager.get<UserDescription>(cacheKey);
      if (cachedData) {
        console.log(`🚀 Cache HIT: User description for ${userId}`);
        return cachedData;
      }

      // 查询数据库
      const db = await getDb();
      const result = await db
        .select()
        .from(aiDescriptions)
        .where(eq(aiDescriptions.userId, userId))
        .orderBy(desc(aiDescriptions.updatedAt))
        .limit(1);

      if (result.length === 0) {
        console.log(`📭 No description found for user ${userId}`);
        return null;
      }

      const record = result[0];
      const description: UserDescription = {
        id: record.id,
        userId: record.userId,
        description: record.isCustomApplied
          ? record.customDescription || record.generatedDescription
          : record.generatedDescription,
        style: record.descriptionStyle,
        isCustomApplied: record.isCustomApplied,
        generatedAt: record.generatedAt,
        updatedAt: record.updatedAt,
        expiresAt: record.expiresAt,
        isExpired: record.expiresAt ? Date.now() > record.expiresAt : false,
      };

      // 缓存30分钟
      await cacheManager.set(cacheKey, description, { expirationTtl: 1800 });
      console.log(`💾 Cached user description for ${userId}`);
      return description;
    } catch (error) {
      console.error(`Error in getUserDescriptionCached for ${userId}:`, error);
      throw error;
    }
  }
);

/**
 * Phase 2B: Dashboard 概览数据缓存
 */
export const getDashboardOverviewCached = cache(
  async (userId: string): Promise<DashboardOverviewData> => {
    const cacheKey = `dashboard:overview:${userId}`;
    try {
      const cachedData = await cacheManager.get<DashboardOverviewData>(
        cacheKey
      );
      if (cachedData) {
        console.log(`🚀 Cache HIT: Dashboard overview for ${userId}`);
        return cachedData;
      }

      const db = await getDb();

      // 并行获取所有Dashboard核心数据
      const [githubData, aiDescription, shareLinksData, userSubscription] =
        await Promise.all([
          db
            .select()
            .from(contributeDatas)
            .where(eq(contributeDatas.userId, userId))
            .get(),
          getUserDescriptionCached(userId),
          db
            .select()
            .from(shareLinks)
            .where(eq(shareLinks.userId, userId))
            .limit(10),
          // 这里可以添加订阅查询，暂时返回基础数据
          Promise.resolve(null),
        ]);

      const overview: DashboardOverviewData = {
        user: {
          id: userId,
          hasData: !!githubData,
        },
        github: githubData
          ? {
              login: githubData.login,
              totalStars: githubData.totalStars,
              totalRepos: githubData.publicRepos,
              totalCommits: githubData.commits,
              totalFollowers: githubData.followers,
              lastUpdated: githubData.lastUpdated,
              contributionScore: githubData.contributionScore,
              hasData: true,
            }
          : {
              hasData: false,
              message: "GitHub data not found",
            },
        ai: {
          hasDescription: !!aiDescription,
          lastGenerated: aiDescription?.generatedAt,
          isCustomized: aiDescription?.isCustomApplied || false,
          style: aiDescription?.style,
          expiresAt: aiDescription?.expiresAt,
          isExpired: aiDescription?.isExpired || false,
        },
        sharing: {
          totalLinks: shareLinksData.length,
          activeLinks: shareLinksData.filter(
            (link) => !link.expiresAt || link.expiresAt > Date.now()
          ).length,
          recentLinks: shareLinksData.slice(0, 3).map((link) => ({
            id: link.id,
            templateType: link.templateType,
            createdAt: link.createdAt,
            expiresAt: link.expiresAt,
            isActive: !link.expiresAt || link.expiresAt > Date.now(),
          })),
          hasActiveLinks: shareLinksData.some(
            (link) => !link.expiresAt || link.expiresAt > Date.now()
          ),
        },
        subscription: {
          isPro: false, // 暂时默认值，后续可扩展
          status: "free",
        },
        lastUpdated: Date.now(),
        cacheExpiry: Date.now() + 5 * 60 * 1000, // 5分钟缓存
      };

      // 缓存5分钟
      await cacheManager.set(cacheKey, overview, { expirationTtl: 300 });
      console.log(`💾 Cached dashboard overview for ${userId}`);
      return overview;
    } catch (error) {
      console.error(
        `Error in getDashboardOverviewCached for ${userId}:`,
        error
      );
      throw error;
    }
  }
);

/**
 * Phase 2B: 分享统计数据缓存
 */
export const getShareStatsCached = cache(
  async (userId: string): Promise<ShareStatsData> => {
    const cacheKey = `share:stats:${userId}`;
    try {
      const cachedData = await cacheManager.get<ShareStatsData>(cacheKey);
      if (cachedData) {
        console.log(`🚀 Cache HIT: Share stats for ${userId}`);
        return cachedData;
      }

      const db = await getDb();
      const shareLinksData = await db
        .select()
        .from(shareLinks)
        .where(eq(shareLinks.userId, userId));

      const now = Date.now();
      const last30Days = now - 30 * 24 * 60 * 60 * 1000;
      const last7Days = now - 7 * 24 * 60 * 60 * 1000;

      const stats: ShareStatsData = {
        totalLinks: shareLinksData.length,
        activeLinks: shareLinksData.filter(
          (link) => !link.expiresAt || link.expiresAt > now
        ).length,
        expiredLinks: shareLinksData.filter(
          (link) => link.expiresAt && link.expiresAt <= now
        ).length,
        recentLinks: shareLinksData.filter(
          (link) => link.createdAt > last30Days
        ).length,
        linksLast7Days: shareLinksData.filter(
          (link) => link.createdAt > last7Days
        ).length,
        templateBreakdown: shareLinksData.reduce((acc, link) => {
          acc[link.templateType] = (acc[link.templateType] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        lastCreated:
          shareLinksData.length > 0
            ? Math.max(...shareLinksData.map((link) => link.createdAt))
            : null,
      };

      // 缓存15分钟
      await cacheManager.set(cacheKey, stats, { expirationTtl: 900 });
      console.log(`💾 Cached share stats for ${userId}`);
      return stats;
    } catch (error) {
      console.error(`Error in getShareStatsCached for ${userId}:`, error);
      throw error;
    }
  }
);

/**
 * Phase 3.2: 多维度评分计算缓存
 * 缓存复杂的四维度评分计算结果，避免重复计算
 */
export const getMultiDimensionScoreCached = cache(
  async (userId: string): Promise<any> => {
    const cacheKey = `multidimension:score:${userId}`;
    try {
      const cachedScore = await cacheManager.get(cacheKey);
      if (cachedScore) {
        console.log(`🚀 Cache HIT: Multi-dimension score for ${userId}`);
        return cachedScore;
      }

      const db = await getDb();
      const githubData = await db
        .select()
        .from(contributeDatas)
        .where(eq(contributeDatas.userId, userId))
        .get();

      if (!githubData) {
        throw new Error(`GitHub data not found for user ${userId}`);
      }

      // 动态导入评分计算
      const { calculateMultiDimensionScores } = await import(
        "@/lib/github/score"
      );

      // 解析语言统计数据
      let languageStats = null;
      if (githubData.languageStats) {
        try {
          languageStats = JSON.parse(githubData.languageStats);
        } catch (error) {
          console.warn("Failed to parse language stats:", error);
        }
      }

      // 直接计算多维度评分
      const multiDimensionScore = calculateMultiDimensionScores({
        commits: githubData.commits,
        contributedRepos: githubData.contributedRepos,
        pullRequests: githubData.pullRequests,
        reviews: githubData.reviews,
        issues: githubData.issues,
        totalStars: githubData.totalStars,
        totalForks: githubData.totalForks,
        followers: githubData.followers,
        languageDiversity: languageStats?.totalLanguages || 0,
        publicRepos: githubData.publicRepos,
        following: githubData.following,
        createdAt: githubData.userCreatedAt,
      });

      // 缓存1小时
      await cacheManager.set(cacheKey, multiDimensionScore, {
        expirationTtl: 3600,
      });
      console.log(`💾 Cached multi-dimension score for ${userId}`);
      return multiDimensionScore;
    } catch (error) {
      console.error(
        `Error in getMultiDimensionScoreCached for ${userId}:`,
        error
      );
      throw error;
    }
  }
);

/**
 * Phase 3.2: GitHub基础数据获取缓存
 * 优化GitHub数据获取流程，减少重复的数据库查询
 */
export const getGitHubDataCached = cache(
  async (userId: string): Promise<any> => {
    const cacheKey = `github:data:${userId}`;
    try {
      const cachedData = await cacheManager.get(cacheKey);
      if (cachedData) {
        console.log(`🚀 Cache HIT: GitHub data for ${userId}`);
        return cachedData;
      }

      const db = await getDb();
      const githubData = await db
        .select()
        .from(contributeDatas)
        .where(eq(contributeDatas.userId, userId))
        .get();

      if (!githubData) {
        throw new Error(`GitHub data not found for user ${userId}`);
      }

      // 解析语言统计数据
      let languageStats = null;
      if (githubData.languageStats) {
        try {
          languageStats = JSON.parse(githubData.languageStats);
        } catch (error) {
          console.warn("Failed to parse language stats:", error);
        }
      }

      const result = {
        login: githubData.login,
        name: githubData.name || "",
        username: githubData.login,
        avatar_url: githubData.avatarUrl,
        bio: githubData.bio || "",
        blog: githubData.blog || "",
        location: githubData.location || "",
        twitter_username: githubData.twitterUsername || "",
        public_repos: githubData.publicRepos,
        followers: githubData.followers,
        following: githubData.following,
        created_at: githubData.userCreatedAt,
        total_stars: githubData.totalStars,
        contribution_score: githubData.contributionScore,
        commits: githubData.commits,
        pull_requests: githubData.pullRequests,
        issues: githubData.issues,
        reviews: githubData.reviews,
        total_forks: githubData.totalForks,
        contributed_repos: githubData.contributedRepos,
        language_stats: languageStats,
        lastUpdated: githubData.lastUpdated,
        dataVersion: githubData.dataVersion || 2,
      };

      // 缓存30分钟（GitHub数据变化不频繁）
      await cacheManager.set(cacheKey, result, { expirationTtl: 1800 });
      console.log(`💾 Cached GitHub data for ${userId}`);
      return result;
    } catch (error) {
      console.error(`Error in getGitHubDataCached for ${userId}:`, error);
      throw error;
    }
  }
);

/**
 * Phase 3.2: 扩展缓存失效机制
 */
export async function invalidateUserCaches(userId: string): Promise<void> {
  const cacheKeys = [
    `user:description:${userId}`,
    `dashboard:overview:${userId}`,
    `share:stats:${userId}`,
    `multidimension:score:${userId}`, // Phase 3.2: 新增多维度评分缓存失效
    `github:data:${userId}`, // Phase 3.2: 新增GitHub数据缓存失效
  ];

  const promises = cacheKeys.map((key) =>
    cacheManager
      .delete(key)
      .catch((error) =>
        console.warn(`Failed to invalidate cache key ${key}:`, error)
      )
  );

  await Promise.all(promises);
  console.log(`🗑️ Phase 3.2: Enhanced cache invalidation for user ${userId}`);
}

/**
 * Phase 3.5: 简化的AI描述批量预热
 * 直接并行调用现有缓存函数，保持简单高效
 */
export const batchWarmupUserDescriptions = async (
  userIds: string[]
): Promise<{ successful: number; total: number }> => {
  // 限制批量大小防止过载
  const limitedUserIds = userIds.slice(0, 15);

  console.log(`🔥 Batch warmup: ${limitedUserIds.length} users`);

  // 简单并行预热 - 直接调用现有缓存函数
  const results = await Promise.allSettled(
    limitedUserIds.map((userId) => getUserDescriptionCached(userId))
  );

  const successful = results.filter((r) => r.status === "fulfilled").length;
  console.log(`🔥 Warmup completed: ${successful}/${limitedUserIds.length}`);

  return { successful, total: limitedUserIds.length };
};

/**
 * Phase 3.5: 简化的智能缓存失效
 * 保持现有的invalidateUserCaches，只是增加reason参数用于日志
 */
export const smartInvalidateUserCaches = async (
  userId: string,
  reason: string = "manual"
): Promise<void> => {
  console.log(`🧠 Cache invalidation for ${userId}, reason: ${reason}`);

  // 直接调用现有的统一失效函数
  await invalidateUserCaches(userId);

  console.log(`🧠 Cache invalidation completed for ${userId}`);
};

/**
 * Phase 3.6: 简化的批量清理优化
 * 直接并行调用现有失效函数，保持简单高效
 */
export const batchCleanupUserCaches = async (
  userIds: string[]
): Promise<{ successful: number; total: number }> => {
  // 限制批量大小防止过载
  const limitedUserIds = userIds.slice(0, 20);

  console.log(`🧹 Batch cleanup: ${limitedUserIds.length} users`);

  // 简单并行清理 - 直接调用现有失效函数
  const results = await Promise.allSettled(
    limitedUserIds.map((userId) => invalidateUserCaches(userId))
  );

  const successful = results.filter((r) => r.status === "fulfilled").length;
  console.log(`🧹 Cleanup completed: ${successful}/${limitedUserIds.length}`);

  return { successful, total: limitedUserIds.length };
};

// 类型定义
interface UserDescription {
  id: string;
  userId: string;
  description: string;
  style: string;
  isCustomApplied: boolean;
  generatedAt: number;
  updatedAt: number;
  expiresAt?: number;
  isExpired?: boolean;
}

interface DashboardOverviewData {
  user: {
    id: string;
    hasData: boolean;
  };
  github:
    | {
        login: string;
        totalStars: number;
        totalRepos: number;
        totalCommits: number;
        totalFollowers: number;
        lastUpdated: number;
        contributionScore: number;
        hasData: true;
      }
    | {
        hasData: false;
        message: string;
      };
  ai: {
    hasDescription: boolean;
    lastGenerated?: number;
    isCustomized: boolean;
    style?: string;
    expiresAt?: number;
    isExpired?: boolean;
  };
  sharing: {
    totalLinks: number;
    activeLinks: number;
    recentLinks: Array<{
      id: string;
      templateType: string;
      createdAt: number;
      expiresAt: number;
      isActive: boolean;
    }>;
    hasActiveLinks: boolean;
  };
  subscription: {
    isPro: boolean;
    status: string;
  };
  lastUpdated: number;
  cacheExpiry: number;
}

interface ShareStatsData {
  totalLinks: number;
  activeLinks: number;
  expiredLinks: number;
  recentLinks: number;
  linksLast7Days: number;
  templateBreakdown: Record<string, number>;
  lastCreated: number | null;
}
