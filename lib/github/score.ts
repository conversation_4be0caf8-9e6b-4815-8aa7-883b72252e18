import { DimensionScores } from "@/types/score";
import { MultiDimensionScore } from "@/types/multi-dimension";
import { getAllDimensionConfigs, DIMENSION_CONFIGS } from "@/constants";
import { LanguageStatsSummary } from "@/types/github";

// 辅助函数：计算单维度直接得分（基于对数增长模型）
export function calculateDirectScore(
  value: number,
  baseValue: number,
  maxScore: number = 100
): number {
  if (value <= 0) return 0;

  // 使用对数增长模型：score = maxScore * log(1 + value/baseValue) / log(1 + maxMultiplier)
  // 当 value = baseValue 时，得分约为 maxScore * 0.5
  // 当 value = baseValue * 10 时，得分约为 maxScore * 0.85
  const maxMultiplier = 20; // 最大倍数，用于控制增长曲线
  const score =
    (maxScore * Math.log(1 + value / baseValue)) / Math.log(1 + maxMultiplier);

  return Math.min(maxScore, Math.max(0, score));
}

// 代码提交型评分计算 - V4.2 智能算法（增加账号成熟度因子）
export function calculateCommitScore(
  commits: number,
  contributedRepos: number,
  createdAt: number
): number {
  // 1. 💻 代码产出质量评分 (35分)
  const codeOutputScore = calculateCodeOutputScore(commits);

  // 2. 📈 贡献一致性评分 (30分) - 基于贡献仓库数评估
  const consistencyScore =
    calculateContributionConsistencyScore(contributedRepos);

  // 3. 🎯 技术影响力评分 (25分) - 基于仓库贡献广度
  const technicalImpactScore = calculateTechnicalImpactScore(contributedRepos);

  // 4. ⏱️ 效率与质量平衡评分 (10分)
  const efficiencyScore = calculateEfficiencyScore(commits, contributedRepos);

  // 5. 🎯 账号成熟度加成 (最多5分) - V4.2 新增
  const maturityBonus = calculateAccountMaturityBonus(createdAt, commits);

  const baseScore =
    codeOutputScore + consistencyScore + technicalImpactScore + efficiencyScore;

  return (
    Math.round(Math.min(Math.max(baseScore + maturityBonus, 0), 100) * 100) /
    100
  );
}

// 协作交流型评分计算 - V4.2 智能算法（增强社交网络评估）
export function calculateCollaborationScore(
  pullRequests: number,
  reviews: number,
  issues: number,
  following: number,
  followers: number
): number {
  // 1. 🤝 主动协作能力评分 (40分)
  const activeCollaborationScore =
    calculateActiveCollaborationScore(pullRequests);

  // 2. 🔍 代码审查专业度评分 (30分)
  const reviewProfessionalismScore = calculateReviewProfessionalismScore(
    reviews,
    pullRequests
  );

  // 3. 🌍 社区参与广度评分 (20分)
  const communityEngagementScore = calculateCommunityEngagementScore(issues);

  // 4. 🎯 协作效率与质量评分 (10分)
  const collaborationEfficiencyScore = calculateCollaborationEfficiencyScore(
    pullRequests,
    reviews,
    issues
  );

  // 5. 🌐 社交网络活跃度评分 (15分) - V4.2 新增
  const socialNetworkScore = calculateSocialNetworkScore(following, followers);

  const baseScore =
    activeCollaborationScore +
    reviewProfessionalismScore +
    communityEngagementScore +
    collaborationEfficiencyScore;

  // 调整基础分数权重为85%，社交网络分数占15%
  return (
    Math.round(
      Math.min(Math.max(baseScore * 0.85 + socialNetworkScore, 0), 100) * 100
    ) / 100
  );
}

// 开源影响型评分计算 - V4.2 智能算法（优化影响力深度评估）
export function calculateInfluenceScore(
  totalStars: number,
  totalForks: number,
  followers: number,
  following: number,
  createdAt: number
): number {
  // 1. 🌟 技术作品影响力评分 (40分)
  const technicalImpactScore = calculateTechnicalWorkImpactScore(totalStars);

  // 2. 👥 社区领导力评分 (30分)
  const communityLeadershipScore = calculateCommunityLeadershipScore(followers);

  // 3. 📈 影响力增长潜力评分 (20分)
  const influenceGrowthScore = calculateInfluenceGrowthScore(
    totalStars,
    followers
  );

  // 4. 🎯 影响力质量与深度评分 (10分)
  const influenceQualityScore = calculateInfluenceQualityScore(
    totalStars,
    totalForks
  );

  // 5. ⏳ 影响力持续性评分 (10分) - V4.2 新增
  const sustainabilityScore = calculateInfluenceSustainability(
    totalStars,
    followers,
    following,
    createdAt
  );

  const baseScore =
    technicalImpactScore +
    communityLeadershipScore +
    influenceGrowthScore +
    influenceQualityScore;

  // 调整基础分数权重为90%，持续性分数占10%
  return (
    Math.round(
      Math.min(Math.max(baseScore * 0.9 + sustainabilityScore, 0), 100) * 100
    ) / 100
  );
}

// 学习探索型评分计算 - V4.2 智能算法（增加学习网络评估）
export function calculateExplorationScore(
  languageDiversity: number,
  publicRepos: number,
  following: number,
  createdAt: number
): number {
  // 1. 📅 开发生涯深度评分 (25分) - 基于仓库数量推断经验
  const careerDepthScore = calculateCareerDepthScore(publicRepos);

  // 2. 🌈 技术多样性评分 (30分)
  const diversityScore = calculateTechnicalDiversityScore(
    languageDiversity,
    publicRepos
  );

  // 3. 🔄 学习活跃度评分 (25分)
  const learningActivityScore = calculateLearningActivityScore(publicRepos);

  // 4. 🌱 成长潜力评分 (20分)
  const growthPotentialScore = calculateGrowthPotentialScore(
    languageDiversity,
    publicRepos
  );

  // 5. 🎓 学习网络活跃度评分 (15分) - V4.2 新增
  const learningNetworkScore = calculateLearningNetworkScore(
    following,
    createdAt
  );

  const baseScore =
    careerDepthScore +
    diversityScore +
    learningActivityScore +
    growthPotentialScore;

  // 调整基础分数权重为85%，学习网络分数占15%
  return (
    Math.round(
      Math.min(Math.max(baseScore * 0.85 + learningNetworkScore, 0), 100) * 100
    ) / 100
  );
}

// V4 优化：基于领域突出度的等级判定算法
export function getOptimizedContributionGrade(scores: DimensionScores): string {
  const { commitScore, collaborationScore, influenceScore, explorationScore } =
    scores;
  const allScores = [
    commitScore,
    collaborationScore,
    influenceScore,
    explorationScore,
  ];

  // 定义突出标准
  const EXCELLENT_THRESHOLD = 80; // 突出领域标准
  const GOOD_THRESHOLD = 60; // 基础合格标准

  // 统计突出领域数量
  const excellentDomains = allScores.filter(
    (score) => score >= EXCELLENT_THRESHOLD
  ).length;
  const maxScore = Math.max(...allScores);

  // 基于领域突出度的等级判定
  if (maxScore < GOOD_THRESHOLD) {
    // 最高分连60分都达不到 -> D级
    return "D";
  }

  if (maxScore < EXCELLENT_THRESHOLD) {
    // 最突出领域未达到80分 -> C级
    return "C";
  }

  // 根据突出领域数量判定等级
  if (excellentDomains >= 3) {
    // 三个或四个领域突出 -> S级
    return "S";
  } else if (excellentDomains === 2) {
    // 两个领域突出 -> A级
    return "A";
  } else if (excellentDomains === 1) {
    // 一个领域突出 -> B级
    return "B";
  }

  // 理论上不会到达这里，但作为保险
  return "C";
}

// 获取用户的突出领域信息
export function getExcellentDomains(scores: DimensionScores): {
  count: number;
  domains: Array<{
    name: string;
    score: number;
    label: string;
  }>;
  maxScore: number;
} {
  const EXCELLENT_THRESHOLD = 80;

  const dimensions = getAllDimensionConfigs().map((config) => ({
    name: config.scoreKey,
    score: scores[config.scoreKey],
    label: config.label,
  }));

  const excellentDomains = dimensions.filter(
    (d) => d.score >= EXCELLENT_THRESHOLD
  );
  const maxScore = Math.max(...dimensions.map((d) => d.score));

  return {
    count: excellentDomains.length,
    domains: excellentDomains,
    maxScore,
  };
}

// 四维度综合评分计算
export function calculateMultiDimensionScores(data: {
  commits: number;
  contributedRepos: number;
  pullRequests: number;
  reviews: number;
  issues: number;
  totalStars: number;
  totalForks: number;
  followers: number;
  languageDiversity: number;
  publicRepos: number;
  // V4.2 新增参数
  following: number; // 关注数
  createdAt: number; // 账号创建时间（毫秒时间戳）
}): MultiDimensionScore {
  const commitScore = calculateCommitScore(
    data.commits,
    data.contributedRepos,
    data.createdAt
  );

  const collaborationScore = calculateCollaborationScore(
    data.pullRequests,
    data.reviews,
    data.issues,
    data.following,
    data.followers
  );

  const influenceScore = calculateInfluenceScore(
    data.totalStars,
    data.totalForks,
    data.followers,
    data.following,
    data.createdAt
  );

  const explorationScore = calculateExplorationScore(
    data.languageDiversity,
    data.publicRepos,
    data.following,
    data.createdAt
  );

  // V4 优化：使用基于领域突出度的等级判定算法
  const dimensionScores: DimensionScores = {
    commitScore,
    collaborationScore,
    influenceScore,
    explorationScore,
  };

  const overallGrade = getOptimizedContributionGrade(dimensionScores);
  // 计算最佳维度
  const bestDimension = getBestDimension(dimensionScores);
  // 🏆 综合得分计算：四维度均衡平均
  const totalScore =
    Math.floor(
      ((commitScore + collaborationScore + influenceScore + explorationScore) /
        4) *
        100
    ) / 100;

  return {
    commitScore,
    collaborationScore,
    influenceScore,
    explorationScore,
    overallGrade,
    totalScore,
    bestDimension: bestDimension.label,
    bestDimensionScore: bestDimension.score,
    dataVersion: "v4.2",
    calculatedAt: new Date().getTime(),
  };
}

// 根据多维度评分获取最优维度
export function getBestDimension(scores: DimensionScores): {
  dimension: string;
  score: number;
  label: string;
} {
  const dimensions = getAllDimensionConfigs().map((config) => ({
    key: config.scoreKey,
    score: scores[config.scoreKey],
    label: config.label,
  }));

  const best = dimensions.reduce((prev, current) =>
    current.score > prev.score ? current : prev
  );

  return {
    dimension: best.key,
    score: best.score,
    label: best.label,
  };
}

// ==================== 辅助函数 ====================

// 代码提交型维度的辅助函数
function calculateCodeOutputScore(commits: number): number {
  // 基础commit产出评分（非线性增长）
  let commitScore = 0;
  if (commits <= 100) {
    // 新手阶段：每个commit价值更高
    commitScore = (commits / 100) * 20;
  } else if (commits <= 500) {
    // 成长阶段：稳定增长
    commitScore = 20 + ((commits - 100) / 400) * 10;
  } else if (commits <= 2000) {
    // 熟练阶段：缓慢增长
    commitScore = 30 + ((commits - 500) / 1500) * 5;
  } else {
    // 专家阶段：接近满分
    commitScore = 35;
  }

  return Math.min(commitScore, 35);
}

function calculateContributionConsistencyScore(
  contributedRepos: number
): number {
  // 基于贡献仓库数评估持续贡献能力
  let consistencyScore = 0;
  if (contributedRepos <= 5) {
    consistencyScore = (contributedRepos / 5) * 15;
  } else if (contributedRepos <= 20) {
    consistencyScore = 15 + ((contributedRepos - 5) / 15) * 10;
  } else {
    consistencyScore = 25;
  }

  // 一致性加成：基于仓库贡献广度
  let consistencyBonus = 0;
  if (contributedRepos >= 30) {
    consistencyBonus = 5; // 广泛贡献者
  } else if (contributedRepos >= 15) {
    consistencyBonus = 3; // 稳定贡献者
  } else if (contributedRepos >= 8) {
    consistencyBonus = 2; // 活跃贡献者
  }

  return Math.min(consistencyScore + consistencyBonus, 30);
}

function calculateTechnicalImpactScore(contributedRepos: number): number {
  // 基于仓库贡献广度评估技术影响力
  const impactScore = Math.min((contributedRepos / 25) * 20, 20);

  // 质量加成：贡献仓库数量体现技术广度
  let qualityBonus = 0;
  if (contributedRepos >= 50) {
    qualityBonus = 5; // 技术专家级贡献
  } else if (contributedRepos >= 20) {
    qualityBonus = 3; // 高质量技术贡献
  } else if (contributedRepos >= 10) {
    qualityBonus = 2; // 良好技术贡献
  }

  return Math.min(impactScore + qualityBonus, 25);
}

function calculateEfficiencyScore(
  commits: number,
  contributedRepos: number
): number {
  // 防止除零错误
  if (contributedRepos <= 0) return 5; // 基础分

  // 平均每个仓库的commit数（效率指标）
  const commitsPerRepo = commits / contributedRepos;

  // 效率评分：每个仓库5-50个commit为最佳区间
  let efficiencyScore = 0;
  if (commitsPerRepo >= 5 && commitsPerRepo <= 50) {
    efficiencyScore = 7; // 高效且稳定的贡献
  } else if (commitsPerRepo >= 2 && commitsPerRepo <= 100) {
    efficiencyScore = 5; // 合理的贡献效率
  } else if (commitsPerRepo >= 1) {
    efficiencyScore = 3; // 基础贡献效率
  } else {
    efficiencyScore = 1; // 较低贡献效率
  }

  // 质量加成：基于总体贡献量
  const qualityBonus = Math.min(commits / 1000, 3);

  return Math.min(efficiencyScore + qualityBonus, 10);
}

// 协作交流型维度的辅助函数
function calculateActiveCollaborationScore(pullRequests: number): number {
  // Pull Request 主动协作评分（非线性增长）
  let prScore = 0;
  if (pullRequests <= 20) {
    // 初学协作阶段：每个PR价值高
    prScore = (pullRequests / 20) * 25;
  } else if (pullRequests <= 80) {
    // 熟练协作阶段：稳定增长
    prScore = 25 + ((pullRequests - 20) / 60) * 10;
  } else {
    // 专家协作阶段：接近满分
    prScore = 35;
  }

  // 协作持续性加成
  let sustainabilityBonus = 0;
  if (pullRequests >= 100) {
    sustainabilityBonus = 5; // 高频协作者
  } else if (pullRequests >= 50) {
    sustainabilityBonus = 3; // 稳定协作者
  } else if (pullRequests >= 25) {
    sustainabilityBonus = 2; // 活跃协作者
  }

  return Math.min(prScore + sustainabilityBonus, 40);
}

function calculateReviewProfessionalismScore(
  reviews: number,
  pullRequests: number
): number {
  // 基础审查能力评分
  let reviewScore = 0;
  if (reviews <= 15) {
    // 审查新手阶段
    reviewScore = (reviews / 15) * 15;
  } else if (reviews <= 60) {
    // 审查熟练阶段
    reviewScore = 15 + ((reviews - 15) / 45) * 10;
  } else {
    // 审查专家阶段
    reviewScore = 25;
  }

  // 审查质量推断：基于reviews与PR的比例
  let qualityBonus = 0;
  if (pullRequests > 0) {
    const reviewToPrRatio = reviews / pullRequests;
    if (reviewToPrRatio >= 0.5) {
      // 高质量审查者：给出的审查比自己的PR还多
      qualityBonus = 5;
    } else if (reviewToPrRatio >= 0.2) {
      // 积极审查者：有一定的审查参与度
      qualityBonus = 3;
    }
  } else if (reviews > 10) {
    // 纯审查贡献者
    qualityBonus = 4;
  }

  return Math.min(reviewScore + qualityBonus, 30);
}

function calculateCommunityEngagementScore(issues: number): number {
  // Issue 参与度评分
  const issueEngagement = Math.min((issues / 100) * 12, 12);

  // 社区参与度基础分
  let engagementBonus = 0;
  if (issues >= 50) {
    engagementBonus = 8; // 高度参与社区
  } else if (issues >= 20) {
    engagementBonus = 5; // 积极参与社区
  } else if (issues >= 10) {
    engagementBonus = 3; // 基础参与社区
  }

  return Math.min(issueEngagement + engagementBonus, 20);
}

function calculateCollaborationEfficiencyScore(
  pullRequests: number,
  reviews: number,
  issues: number
): number {
  // 协作活动总量
  const totalCollaboration = pullRequests + reviews + issues;

  if (totalCollaboration === 0) return 0;

  // 协作多样性评分
  const activities = [pullRequests, reviews, issues];
  const nonZeroActivities = activities.filter((count) => count > 0).length;

  let diversityScore = 0;
  switch (nonZeroActivities) {
    case 3:
      // 全面协作者：PR、Review、Issue 都有参与
      diversityScore = 6;
      break;
    case 2:
      // 双向协作者：参与两种协作方式
      diversityScore = 4;
      break;
    case 1:
      // 单向协作者：只参与一种协作方式
      diversityScore = 2;
      break;
    default:
      diversityScore = 0;
  }

  // 协作规模加成
  const scaleBonus = Math.min(totalCollaboration / 100, 4);

  return Math.min(diversityScore + scaleBonus, 10);
}

// 开源影响型维度的辅助函数
function calculateTechnicalWorkImpactScore(totalStars: number): number {
  // Stars 技术认可度评分（非线性增长）
  let starScore = 0;
  if (totalStars <= 50) {
    // 初期影响阶段：每个star价值较高
    starScore = (totalStars / 50) * 20;
  } else if (totalStars <= 200) {
    // 成长影响阶段：稳定增长
    starScore = 20 + ((totalStars - 50) / 150) * 10;
  } else if (totalStars <= 1000) {
    // 显著影响阶段：持续增长
    starScore = 30 + ((totalStars - 200) / 800) * 8;
  } else {
    // 顶级影响阶段：接近满分
    starScore = 38;
  }

  // 影响力质量加成
  let qualityBonus = 0;
  if (totalStars >= 500) {
    qualityBonus = 2; // 高影响力项目
  } else if (totalStars >= 100) {
    qualityBonus = 1; // 有影响力项目
  }

  return Math.min(starScore + qualityBonus, 40);
}

function calculateCommunityLeadershipScore(followers: number): number {
  // 基础社区影响力评分
  let leadershipScore = 0;
  if (followers <= 50) {
    // 新兴影响者阶段
    leadershipScore = (followers / 50) * 15;
  } else if (followers <= 200) {
    // 稳定影响者阶段
    leadershipScore = 15 + ((followers - 50) / 150) * 10;
  } else if (followers <= 500) {
    // 知名影响者阶段
    leadershipScore = 25 + ((followers - 200) / 300) * 3;
  } else {
    // 顶级影响者阶段
    leadershipScore = 28;
  }

  // 领导力质量加成
  let qualityBonus = 0;
  if (followers >= 200) {
    qualityBonus = 2; // 高影响力领导者
  } else if (followers >= 50) {
    qualityBonus = 1; // 有影响力领导者
  }

  return Math.min(leadershipScore + qualityBonus, 30);
}

function calculateInfluenceGrowthScore(
  totalStars: number,
  followers: number
): number {
  // Stars 增长潜力评分
  let starGrowthScore = 0;
  if (totalStars >= 100) {
    starGrowthScore = 12; // 快速增长的技术影响力
  } else if (totalStars >= 20) {
    starGrowthScore = 8; // 稳定增长的技术影响力
  } else if (totalStars >= 5) {
    starGrowthScore = 5; // 基础技术影响力增长
  } else {
    starGrowthScore = 2; // 缓慢技术影响力增长
  }

  // Followers 增长潜力评分
  let followerGrowthScore = 0;
  if (followers >= 50) {
    followerGrowthScore = 8; // 快速增长的社区影响力
  } else if (followers >= 10) {
    followerGrowthScore = 5; // 稳定增长的社区影响力
  } else if (followers >= 2) {
    followerGrowthScore = 3; // 基础社区影响力增长
  } else {
    followerGrowthScore = 1; // 缓慢社区影响力增长
  }

  return Math.min(starGrowthScore + followerGrowthScore, 20);
}

function calculateInfluenceQualityScore(
  totalStars: number,
  totalForks: number
): number {
  // 影响力集中度：基于stars和forks的关系
  let concentrationScore = 0;
  if (totalStars > 0 && totalForks > 0) {
    const starToForkRatio = totalStars / totalForks;

    // 理想比例：3-10个star对应1个fork
    if (starToForkRatio >= 3 && starToForkRatio <= 10) {
      concentrationScore = 5; // 优秀的影响力质量
    } else if (starToForkRatio >= 1 && starToForkRatio <= 20) {
      concentrationScore = 3; // 良好的影响力质量
    } else {
      concentrationScore = 2; // 基础影响力质量
    }
  } else if (totalStars >= 10) {
    concentrationScore = 3; // 有一定影响力但缺乏实用性
  }

  // 影响力真实性：避免纯粹的数字游戏
  let authenticityScore = 0;
  const hasRealImpact = totalStars >= 10 || totalForks >= 5;

  if (hasRealImpact) {
    authenticityScore = 3; // 真实的技术影响力
  } else if (totalStars >= 5) {
    authenticityScore = 2; // 一定的真实性
  }

  // 影响力可持续性
  let sustainabilityScore = 0;
  if (totalStars >= 50 && totalForks >= 5) {
    sustainabilityScore = 2; // 可持续的影响力
  } else if (totalStars >= 20) {
    sustainabilityScore = 1; // 基础可持续性
  }

  return Math.min(
    concentrationScore + authenticityScore + sustainabilityScore,
    10
  );
}

// 学习探索型维度的辅助函数
function calculateCareerDepthScore(publicRepos: number): number {
  // 基于仓库数量推断开发经验深度
  let baseScore = 0;
  if (publicRepos <= 5) {
    // 新手期：0-5个仓库
    baseScore = (publicRepos / 5) * 15;
  } else if (publicRepos <= 15) {
    // 成长期：5-15个仓库
    baseScore = 15 + ((publicRepos - 5) / 10) * 6;
  } else if (publicRepos <= 30) {
    // 熟练期：15-30个仓库
    baseScore = 21 + ((publicRepos - 15) / 15) * 3;
  } else {
    // 专家期：30+个仓库
    baseScore = 24;
  }

  // 经验深度加成
  let depthBonus = 0;
  if (publicRepos >= 50) {
    depthBonus = 1; // 丰富经验
  }

  return Math.min(baseScore + depthBonus, 25);
}

function calculateTechnicalDiversityScore(
  languageDiversity: number,
  publicRepos: number
): number {
  // 基础多样性评分：基于语言多样性
  const languageScore = Math.min((languageDiversity / 8) * 20, 20); // 最高20分

  // 项目多样性：基于仓库数量
  const projectDiversityScore = Math.min((publicRepos / 20) * 8, 8); // 最高8分

  // 探索广度加成
  let explorationBonus = 0;
  if (languageDiversity >= 5 && publicRepos >= 10) {
    explorationBonus = 2; // 全面探索者
  } else if (languageDiversity >= 3 || publicRepos >= 8) {
    explorationBonus = 1; // 积极探索者
  }

  return Math.min(languageScore + projectDiversityScore + explorationBonus, 30);
}

function calculateLearningActivityScore(publicRepos: number): number {
  // 基于仓库创建活跃度评估学习活跃度
  let activityScore = 0;
  if (publicRepos <= 10) {
    activityScore = (publicRepos / 10) * 15;
  } else if (publicRepos <= 30) {
    activityScore = 15 + ((publicRepos - 10) / 20) * 8;
  } else {
    activityScore = 23;
  }

  // 学习持续性加成
  let sustainabilityBonus = 0;
  if (publicRepos >= 25) {
    sustainabilityBonus = 2; // 持续学习者
  } else if (publicRepos >= 15) {
    sustainabilityBonus = 1; // 稳定学习者
  }

  return Math.min(activityScore + sustainabilityBonus, 25);
}

function calculateGrowthPotentialScore(
  languageDiversity: number,
  publicRepos: number
): number {
  // 技术广度增长潜力
  const diversityGrowthScore = Math.min((languageDiversity / 6) * 10, 10);

  // 项目创造力增长潜力
  const creativityScore = Math.min((publicRepos / 25) * 8, 8);

  // 综合成长潜力加成
  let potentialBonus = 0;
  if (languageDiversity >= 4 && publicRepos >= 15) {
    potentialBonus = 2; // 高成长潜力
  } else if (languageDiversity >= 2 && publicRepos >= 8) {
    potentialBonus = 1; // 良好成长潜力
  }

  return Math.min(diversityGrowthScore + creativityScore + potentialBonus, 20);
}

// V4.2 新增：账号成熟度加成计算
function calculateAccountMaturityBonus(
  createdAt: number,
  commits: number
): number {
  const accountAgeYears =
    (Date.now() - createdAt) / (365 * 24 * 60 * 60 * 1000);

  // 账号年龄越长，且有持续贡献，加成越高
  if (accountAgeYears >= 5 && commits > 1000) return 5; // 资深开发者
  if (accountAgeYears >= 3 && commits > 500) return 3; // 经验丰富开发者
  if (accountAgeYears >= 2 && commits > 200) return 2; // 稳定开发者
  if (accountAgeYears >= 1 && commits > 50) return 1; // 新兴开发者

  return 0; // 新手或不活跃账号
}

// V4.2 新增：社交网络活跃度计算
function calculateSocialNetworkScore(
  following: number,
  followers: number
): number {
  // 关注/被关注比例分析
  const followRatio = followers > 0 ? following / followers : following / 1;

  // 理想的社交比例：既有输出(followers)也有学习(following)
  let socialBalance = 0;
  if (followRatio >= 0.1 && followRatio <= 2.0) {
    socialBalance = 8; // 平衡的社交网络
  } else if (followRatio >= 0.05 && followRatio <= 5.0) {
    socialBalance = 5; // 较为平衡
  } else {
    socialBalance = 2; // 不够平衡
  }

  // 社交规模加成
  const socialScale = Math.min((following + followers) / 100, 7);

  return Math.min(socialBalance + socialScale, 15);
}

// V4.2 新增：影响力持续性计算
function calculateInfluenceSustainability(
  totalStars: number,
  followers: number,
  following: number,
  createdAt: number
): number {
  const accountAgeYears =
    (Date.now() - createdAt) / (365 * 24 * 60 * 60 * 1000);

  // 年均影响力增长
  const annualStarsGrowth =
    accountAgeYears > 0 ? totalStars / accountAgeYears : 0;
  const annualFollowersGrowth =
    accountAgeYears > 0 ? followers / accountAgeYears : 0;

  // 持续性评分
  let sustainabilityScore = 0;

  // 基于年均增长的持续性评估
  if (annualStarsGrowth >= 100 && annualFollowersGrowth >= 20) {
    sustainabilityScore = 8; // 高持续增长
  } else if (annualStarsGrowth >= 50 && annualFollowersGrowth >= 10) {
    sustainabilityScore = 6; // 稳定增长
  } else if (annualStarsGrowth >= 10 && annualFollowersGrowth >= 5) {
    sustainabilityScore = 4; // 缓慢增长
  } else {
    sustainabilityScore = 2; // 基础分
  }

  // 学习态度加成：关注他人体现学习精神
  const learningBonus = Math.min(following / 50, 2);

  return Math.min(sustainabilityScore + learningBonus, 10);
}

// V4.2 新增：学习网络活跃度计算
function calculateLearningNetworkScore(
  following: number,
  createdAt: number
): number {
  const accountAgeYears =
    (Date.now() - createdAt) / (365 * 24 * 60 * 60 * 1000);

  // 年均关注增长（体现学习积极性）
  const annualFollowingGrowth =
    accountAgeYears > 0 ? following / accountAgeYears : following;

  // 学习网络规模评分
  let networkScore = 0;
  if (following >= 200) {
    networkScore = 8; // 广泛的学习网络
  } else if (following >= 100) {
    networkScore = 6; // 良好的学习网络
  } else if (following >= 50) {
    networkScore = 4; // 基础学习网络
  } else if (following >= 20) {
    networkScore = 2; // 有限学习网络
  } else {
    networkScore = 1; // 较少学习网络
  }

  // 学习持续性加成
  const consistencyBonus = Math.min(annualFollowingGrowth / 10, 7);

  return Math.min(networkScore + consistencyBonus, 15);
}
