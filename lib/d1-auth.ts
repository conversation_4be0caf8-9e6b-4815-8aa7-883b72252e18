import { AuthConfig } from "@auth/core";
import GitHub from "@auth/core/providers/github";

// 添加无数据库处理函数
export function handleAuthWithoutDB(env: any) {
  console.log("创建无数据库配置，环境:", {
    NEXTAUTH_URL: env.NEXTAUTH_URL,
    NODE_ENV: env.NODE_ENV,
  });

  const config: AuthConfig = {
    providers: [
      GitHub({
        clientId: env.GITHUB_ID as string,
        clientSecret: env.GITHUB_SECRET as string,
        profile(profile) {
          return {
            id: profile.id.toString(),
            name: profile.name ?? profile.login,
            email: profile.email,
            image: profile.avatar_url,
            username: profile.login,
            githubId: profile.id.toString(),
            avatarUrl: profile.avatar_url,
          };
        },
      }),
    ],
    // 不使用任何数据库适配器
    adapter: undefined,
    session: {
      strategy: "jwt",
      maxAge: 3 * 24 * 60 * 60, // 3天
    },
    cookies: {
      sessionToken: {
        name: "auth-session",
        options: {
          httpOnly: true,
          sameSite: "lax",
          path: "/",
          secure: env.NODE_ENV === "production",
        },
      },
    },
    callbacks: {
      async session({ session, token }) {
        console.log("无DB模式 - session回调", { token });
        // 从JWT token中获取用户信息
        if (session.user) {
          session.user.id = token.sub as string;
          // 添加更多用户信息
          if (token.name) session.user.name = token.name as string;
          if (token.email) session.user.email = token.email as string;
          if (token.picture) session.user.image = token.picture as string;
          if (token.username) session.user.username = token.username as string;
          if (token.githubId) session.user.githubId = token.githubId as string;
          if (token.avatarUrl)
            session.user.avatarUrl = token.avatarUrl as string;
        }
        return session;
      },
      async jwt({ token, user, account, profile }) {
        console.log("无DB模式 - jwt回调", { token, user, profile });
        try {
          // 首次登录时将用户信息保存到token
          if (user) {
            token.userId = user.id;
            token.sub = user.id;
            token.username = (user as any).username;
            token.githubId = (user as any).githubId;
            token.avatarUrl = (user as any).avatarUrl;
          }
          // 如果有profile信息(GitHub API返回)，也添加到token
          if (profile) {
            token.username = token.username || (profile as any).login;
            token.githubId = token.githubId || (profile as any).id?.toString();
            token.avatarUrl = token.avatarUrl || (profile as any).avatar_url;
          }
          return token;
        } catch (error) {
          console.error("无DB模式 - jwt回调出错:", error);
          return token;
        }
      },
    },
    debug: true,
    trustHost: true,
  };

  return { config };
}

// 原始处理函数保持不变
export function createAuthHandler(env: any) {
  // 检查是否为开发环境
  const isDev =
    env.NEXTAUTH_URL?.startsWith("http://localhost") ||
    env.NEXTAUTH_URL?.includes("127.0.0.1");

  console.log("createAuthHandler - 环境:", {
    isDev,
    NEXTAUTH_URL: env.NEXTAUTH_URL,
    NODE_ENV: env.NODE_ENV,
  });

  // 直接使用D1Adapter而非DrizzleAdapter
  const config: AuthConfig = {
    providers: [
      GitHub({
        clientId: env.GITHUB_ID as string,
        clientSecret: env.GITHUB_SECRET as string,
        profile(profile) {
          return {
            id: profile.id.toString(),
            name: profile.name ?? profile.login,
            email: profile.email,
            image: profile.avatar_url,
            username: profile.login,
            githubId: profile.id.toString(),
            avatarUrl: profile.avatar_url,
          };
        },
      }),
    ],
    // 完全不使用数据库适配器，仅使用JWT
    adapter: undefined,
    session: {
      strategy: "jwt", // 使用JWT策略避免数据库会话问题
      maxAge: 3 * 24 * 60 * 60, // 3天
    },
    cookies: {
      sessionToken: {
        name: "auth-session",
        options: {
          httpOnly: true,
          sameSite: "none",
          path: "/",
          secure: true,
        },
      },
    },
    callbacks: {
      async session({ session, token }) {
        // 从JWT token中获取用户信息
        if (session.user) {
          session.user.id = token.sub as string;
          // 添加更多用户信息
          if (token.username) {
            session.user.username = token.username as string;
          }
          if (token.githubId) {
            session.user.githubId = token.githubId as string;
          }
          if (token.avatarUrl) {
            session.user.avatarUrl = token.avatarUrl as string;
          }
        }
        return session;
      },
      async jwt({ token, user, account, profile }) {
        // 首次登录时将用户信息保存到token
        console.log("d1-auth.ts jwt callback 执行", { token, user, profile });
        try {
          if (user) {
            token.sub = user.id;
            token.username = (user as any).username;
            token.githubId = (user as any).githubId;
            token.avatarUrl = (user as any).avatarUrl;
          }
          // 如果有profile信息(GitHub API返回)，也添加到token
          if (profile) {
            if (!token.username) token.username = (profile as any).login;
            if (!token.githubId)
              token.githubId = (profile as any).id?.toString();
            if (!token.avatarUrl) token.avatarUrl = (profile as any).avatar_url;
          }
          return token;
        } catch (error) {
          console.error("d1-auth.ts jwt回调出错:", error);
          // 返回原始token，不阻断认证流程
          return token;
        }
      },
    },
    debug: env.NODE_ENV !== "production",
    trustHost: true,
  };

  return { config };
}
