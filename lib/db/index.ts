import { getCloudflareContext } from "@opennextjs/cloudflare";
import { drizzle } from "drizzle-orm/d1";
import { drizzle as drizzleBetter } from "drizzle-orm/better-sqlite3";
import * as schema from "./schema";
import type { D1Database } from "@cloudflare/workers-types";
import { createMockD1Database } from "./mock-db";
import path from "path";
import { createRequire } from "module";

let _db:
  | ReturnType<typeof drizzle<typeof schema>>
  | ReturnType<typeof drizzleBetter<typeof schema>>
  | null = null;

// 用于防止并发初始化的 Promise
let _initPromise: Promise<any> | null = null;

// 检测是否在Edge Runtime环境中
function isEdgeRuntime() {
  return (
    process.env.NEXT_RUNTIME === "edge" ||
    (typeof window === "undefined" &&
      typeof global === "undefined" &&
      typeof process !== "undefined")
  );
}

// 开发环境下使用真实的SQLite数据库
function createDevDb() {
  if (process.env.NODE_ENV !== "production") {
    // 在Edge Runtime中不能使用better-sqlite3
    if (isEdgeRuntime()) {
      console.log("Edge Runtime detected, using mock database");
      const mockD1 = createMockD1Database() as unknown as D1Database;
      return drizzle(mockD1, { schema });
    }

    try {
      // 使用createRequire来在ESM中导入CommonJS模块
      const require = createRequire(import.meta.url);
      const Database = require("better-sqlite3");

      // 使用lib/db/dev目录下的SQLite数据库文件
      const dbPath = path.join(process.cwd(), "lib/db/dev/dev-database.db");
      console.log("Using development SQLite database at:", dbPath);

      const sqlite = new Database(dbPath);
      sqlite.pragma("journal_mode = WAL");

      return drizzleBetter(sqlite, { schema });
    } catch (error) {
      console.error("Failed to create development database:", error);
      console.log("Falling back to mock database");

      // 如果创建真实数据库失败，回退到模拟数据库
      const mockD1 = createMockD1Database() as unknown as D1Database;
      return drizzle(mockD1, { schema });
    }
  }
  return null;
}

export async function getDb() {
  // 如果已经有数据库实例，直接返回
  if (_db) return _db;

  // 如果正在初始化，等待初始化完成
  if (_initPromise) {
    await _initPromise;
    if (_db) return _db;
  }

  // 创建初始化 Promise，防止并发初始化
  _initPromise = initializeDatabase();

  try {
    await _initPromise;
    if (_db) return _db;
    throw new Error("Database initialization failed");
  } finally {
    // 清理初始化 Promise
    _initPromise = null;
  }
}

async function initializeDatabase() {
  // 开发环境下使用真实的SQLite数据库
  if (process.env.NODE_ENV !== "production") {
    console.log("🔧 Development environment detected, using SQLite database");
    _db = createDevDb();
    if (_db) return _db;
  }

  try {
    console.log(
      "🚀 Production environment detected, using Cloudflare D1 database"
    );
    // 使用异步模式获取 Cloudflare 上下文
    const context = await getCloudflareContext({ async: true });
    const env = (context as unknown as { env: { DB: D1Database } }).env;
    
    if (!env.DB) {
      throw new Error("D1 database binding not found in environment");
    }
    
    _db = drizzle(env.DB, { schema });
    console.log("✅ Cloudflare D1 database connection established");
    return _db;
  } catch (error) {
    console.error("❌ Failed to initialize database:", error);
    
    // 重置数据库实例和初始化 Promise
    _db = null;
    _initPromise = null;
    
    if (process.env.NODE_ENV === "production") {
      throw error;
    }

    // 开发环境如果初始化失败，抛出错误
    throw new Error(
      "Failed to initialize database and no fallback available"
    );
  }
}

// 优雅关闭数据库连接的函数
export async function closeDb() {
  if (_db && process.env.NODE_ENV !== "production") {
    try {
      // 仅在开发环境中关闭SQLite连接
      if (typeof (_db as any).close === 'function') {
        await (_db as any).close();
        console.log("✅ Database connection closed");
      }
    } catch (error) {
      console.error("❌ Error closing database:", error);
    }
  }
  _db = null;
  _initPromise = null;
}

/**
 * Helper function to get a Unix timestamp 3 days from now (for share links expiration)
 */
export function getExpirationDate(): number {
  const now = new Date();
  now.setDate(now.getDate() + 3);
  return Math.floor(now.getTime() / 1000);
}

// Export schema for use in other modules
export { schema };
