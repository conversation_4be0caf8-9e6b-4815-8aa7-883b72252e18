import {
  sqliteTable,
  text,
  integer,
  blob,
  real,
} from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { sql } from "drizzle-orm";

// 用户表
export const users = sqliteTable("users", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  name: text("name"),
  email: text("email"),
  emailVerified: integer("email_verified"),
  image: text("image"),
  // 以下是自定义字段
  githubId: text("github_id").notNull().unique(),
  username: text("username").notNull(),
  displayName: text("display_name"),
  avatarUrl: text("avatar_url").notNull(),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch())`),
  updatedAt: integer("updated_at")
    .notNull()
    .default(sql`(unixepoch())`),
});

export const usersRelations = relations(users, ({ one, many }) => ({
  userBehaviors: many(userBehaviors),
  shareLinks: many(shareLinks),
  contributeDatas: many(contributeDatas),
  subscriptions: one(userSubscriptions, {
    fields: [users.id],
    references: [userSubscriptions.userId],
  }),
  payments: many(payments),
  aiDescriptions: one(aiDescriptions, {
    fields: [users.id],
    references: [aiDescriptions.userId],
  }),
  descriptionHistory: many(descriptionHistory),
}));

// 添加GitHub贡献数据表
export const contributeDatas = sqliteTable("contribute_datas", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id")
    .notNull()
    .unique()
    .references(() => users.id, { onDelete: "cascade" }),
  // 基本用户数据
  login: text("login").notNull(),
  name: text("name"),
  avatarUrl: text("avatar_url").notNull(),
  bio: text("bio"),
  blog: text("blog"),
  location: text("location"),
  twitterUsername: text("twitter_username"),
  publicRepos: integer("public_repos").notNull(),
  followers: integer("followers").notNull(),
  following: integer("following").notNull(),
  userCreatedAt: integer("user_created_at").notNull(),
  // 贡献数据
  totalStars: integer("total_stars").notNull(),
  contributionScore: integer("contribution_score").notNull(),
  commits: integer("commits").notNull(),
  pullRequests: integer("pull_requests").notNull(),
  issues: integer("issues").notNull(),
  reviews: integer("reviews").notNull(),
  // V4 新增字段 - 多维度数据
  totalForks: integer("total_forks").notNull().default(0),
  contributedRepos: integer("contributed_repos").notNull().default(0),
  // V4.1 新增字段 - 语言统计数据（移除 languageDiversity，统一使用 languageStats）
  languageStats: text("language_stats"), // JSON格式存储详细语言统计
  dataVersion: integer("data_version").notNull().default(1),
  lastFullUpdate: integer("last_full_update").notNull().default(0),
  updateStatus: text("update_status").notNull().default("completed"), // pending/updating/completed/failed
  // 元数据
  lastUpdated: integer("last_updated")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  recordCreatedAt: integer("record_created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

export const userBehaviors = sqliteTable("user_behaviors", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  actionType: text("action_type").notNull(),
  actionData: blob("action_data", { mode: "json" }),
  performedAt: integer("performed_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

export const userBehaviorsRelations = relations(userBehaviors, ({ one }) => ({
  users: one(users, {
    fields: [userBehaviors.userId],
    references: [users.id],
  }),
}));

export const shareLinks = sqliteTable("share_links", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  linkToken: text("link_token").notNull().unique(),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  expiresAt: integer("expires_at").notNull(),
  isActive: integer("is_active", { mode: "boolean" }).notNull().default(true),
  templateType: text("template_type").notNull().default("contribute"),
  backgroundId: text("background_image_id"),
});

export const shareLinksRelations = relations(shareLinks, ({ one }) => ({
  users: one(users, {
    fields: [shareLinks.userId],
    references: [users.id],
  }),
}));

// Types for TypeScript
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type UserBehavior = typeof userBehaviors.$inferSelect;
export type NewUserBehavior = typeof userBehaviors.$inferInsert;

export type ShareLink = typeof shareLinks.$inferSelect;
export type NewShareLink = typeof shareLinks.$inferInsert;

// 使用 Drizzle 推断类型替换手动定义
export type ContributeData = typeof contributeDatas.$inferSelect;
export type NewContributeData = typeof contributeDatas.$inferInsert;

// 订阅计划表
export const subscriptionPlans = sqliteTable("subscription_plans", {
  id: text("id").primaryKey().notNull(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: integer("price").notNull(), // 单位：分
  currency: text("currency").notNull().default("USD"),
  interval: text("interval").notNull(), // 'month' or 'year'
  features: text("features").notNull(), // JSON array
  isActive: integer("is_active", { mode: "boolean" }).notNull().default(true),
  stripePriceId: text("stripe_price_id"),
  stripeProductId: text("stripe_product_id"),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  updatedAt: integer("updated_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

// 用户订阅表
export const userSubscriptions = sqliteTable("user_subscriptions", {
  id: text("id").primaryKey().notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  planId: text("plan_id")
    .notNull()
    .references(() => subscriptionPlans.id, { onDelete: "restrict" }),
  status: text("status").notNull(),
  priceId: text("price_id").notNull(),
  currentPeriodStart: integer("current_period_start").notNull(),
  currentPeriodEnd: integer("current_period_end").notNull(),
  cancelAt: integer("cancel_at"),
  canceledAt: integer("canceled_at"),
  cancelAtPeriodEnd: integer("cancel_at_period_end", { mode: "boolean" })
    .notNull()
    .default(true),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  updatedAt: integer("updated_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

// 支付记录表
export const payments = sqliteTable("payments", {
  id: text("id").primaryKey().notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  subscriptionId: text("subscription_id").references(
    () => userSubscriptions.id,
    {
      onDelete: "set null",
    }
  ),
  amount: integer("amount").notNull(),
  currency: text("currency").notNull().default("USD"),
  status: text("status").notNull(),
  paymentMethod: text("payment_method"),
  receiptUrl: text("receipt_url"),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

// 订阅日志表
export const subscriptionLogs = sqliteTable("subscription_logs", {
  id: text("id").primaryKey().notNull(),
  subscriptionId: text("subscription_id")
    .notNull()
    .references(() => userSubscriptions.id, { onDelete: "cascade" }),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  fromStatus: text("from_status").notNull(),
  toStatus: text("to_status").notNull(),
  reason: text("reason"),
  metadata: text("metadata", { mode: "json" }).$type<Record<string, unknown>>(),
  timestamp: integer("timestamp").notNull(),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

// 订阅计划关系
export const subscriptionPlansRelations = relations(
  subscriptionPlans,
  ({ many }) => ({
    userSubscriptions: many(userSubscriptions),
  })
);

// 用户订阅关系
export const userSubscriptionsRelations = relations(
  userSubscriptions,
  ({ one, many }) => ({
    user: one(users, {
      fields: [userSubscriptions.userId],
      references: [users.id],
    }),
    plan: one(subscriptionPlans, {
      fields: [userSubscriptions.planId],
      references: [subscriptionPlans.id],
    }),
    payments: many(payments),
  })
);

// 支付记录关系
export const paymentsRelations = relations(payments, ({ one }) => ({
  user: one(users, {
    fields: [payments.userId],
    references: [users.id],
  }),
  subscription: one(userSubscriptions, {
    fields: [payments.subscriptionId],
    references: [userSubscriptions.id],
  }),
}));

// 类型定义
export type SubscriptionPlan = typeof subscriptionPlans.$inferSelect;
export type NewSubscriptionPlan = typeof subscriptionPlans.$inferInsert;

export type UserSubscription = typeof userSubscriptions.$inferSelect;
export type NewUserSubscription = typeof userSubscriptions.$inferInsert;

export type Payment = typeof payments.$inferSelect;
export type NewPayment = typeof payments.$inferInsert;

// 更新 contributeDatas 关系定义
export const contributeDatasRelations = relations(
  contributeDatas,
  ({ one }) => ({
    user: one(users, {
      fields: [contributeDatas.userId],
      references: [users.id],
    }),
  })
);

// AI描述表 - V5新增
export const aiDescriptions = sqliteTable("ai_descriptions", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id")
    .notNull()
    .unique()
    .references(() => users.id, { onDelete: "cascade" }),

  // AI生成的描述内容
  generatedDescription: text("generated_description").notNull(),
  descriptionStyle: text("description_style").notNull(), // technical-expert, community-builder, innovation-pioneer, learning-enthusiast

  // 生成时的数据快照
  dataSnapshot: text("data_snapshot").notNull(), // JSON格式的GitHub数据
  promptUsed: text("prompt_used").notNull(),
  aiModelVersion: text("ai_model_version").notNull().default("doubao-pro-128k"),

  // 用户自定义内容
  customDescription: text("custom_description"), // 用户编辑后的版本
  isCustomApplied: integer("is_custom_applied", { mode: "boolean" })
    .notNull()
    .default(false),

  // 元数据
  generatedAt: integer("generated_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  updatedAt: integer("updated_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  expiresAt: integer("expires_at").notNull(), // 描述过期时间，需要重新生成
});

// 描述生成历史表 - V5新增
export const descriptionHistory = sqliteTable("description_history", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  descriptionContent: text("description_content").notNull(),
  generationType: text("generation_type").notNull(), // 'ai-generated', 'user-customized', 'fallback'
  style: text("style"),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

export const aiDescriptionsRelations = relations(aiDescriptions, ({ one }) => ({
  user: one(users, {
    fields: [aiDescriptions.userId],
    references: [users.id],
  }),
}));

export const descriptionHistoryRelations = relations(
  descriptionHistory,
  ({ one }) => ({
    user: one(users, {
      fields: [descriptionHistory.userId],
      references: [users.id],
    }),
  })
);

// V5 AI描述相关类型定义
export type AIDescription = typeof aiDescriptions.$inferSelect;
export type NewAIDescription = typeof aiDescriptions.$inferInsert;

export type DescriptionHistory = typeof descriptionHistory.$inferSelect;
export type NewDescriptionHistory = typeof descriptionHistory.$inferInsert;

// =================================================================
// V5 AI 描述生成系统 - 新增表
// =================================================================

// 1. AI描述生成请求表
export const aiGenerationRequests = sqliteTable("ai_generation_requests", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  preferredStyle: text("preferred_style"),
  contextType: text("context_type"),
  streamResponse: integer("stream_response", { mode: "boolean" }).default(
    false
  ),
  forceRegenerate: integer("force_regenerate", { mode: "boolean" }).default(
    false
  ),
  status: text("status").notNull().default("pending"), // pending, processing, completed, failed
  currentStep: text("current_step"), // analyzer, strategist, writer, critic
  progressPercentage: integer("progress_percentage").default(0),
  stepTimings: text("step_timings", { mode: "json" }), // JSON: {analyzer: 1200, ...}
  startedAt: integer("started_at").notNull(),
  completedAt: integer("completed_at"),
  errorMessage: text("error_message"),
  retryCount: integer("retry_count").default(0),
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

// 2. 模块处理日志表
export const aiModuleLogs = sqliteTable("ai_module_logs", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  requestId: text("request_id")
    .notNull()
    .references(() => aiGenerationRequests.id, { onDelete: "cascade" }),
  moduleName: text("module_name").notNull(), // analyzer, strategist, writer, critic
  inputData: text("input_data", { mode: "json" }).notNull(),
  outputData: text("output_data", { mode: "json" }),
  promptUsed: text("prompt_used"),
  status: text("status").notNull(), // success, failed, retrying
  processingTime: integer("processing_time"),
  tokenUsage: text("token_usage", { mode: "json" }),
  modelVersion: text("model_version"),
  errorCode: text("error_code"),
  errorMessage: text("error_message"),
  qualityScore: real("quality_score"), // 0-1分数
  confidenceScore: real("confidence_score"), // 0-1分数
  createdAt: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .$defaultFn(() => new Date()),
});

// Relational schema for ai_module_logs
export const aiModuleLogsRelations = relations(aiModuleLogs, ({ one }) => ({
  request: one(aiGenerationRequests, {
    fields: [aiModuleLogs.requestId],
    references: [aiGenerationRequests.id],
  }),
}));

// ==================== V5 扩展数据表 ====================

/**
 * GitHub扩展数据表
 * 存储用于AI分析的扩展GitHub数据（仓库详情、commit信息、README、时间统计）
 * 与常规贡献数据分离，按需获取和更新
 */
export const githubExtendedDatas = sqliteTable("github_extended_datas", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),

  // 扩展数据内容（JSON格式存储）
  repositoriesData: text("repositories_data"), // 仓库详细信息
  commitsData: text("commits_data"), // commit信息
  readmeData: text("readme_data"), // README内容
  techStackFilesData: text("tech_stack_files_data"), // 技术栈文件内容
  timeStatsData: text("time_stats_data"), // 时间分布统计

  // 获取配置
  fetchConfig: text("fetch_config"), // JSON格式存储获取配置

  // 数据元信息
  dataVersion: text("data_version").notNull().default("1.0.0"),
  fetchedAt: integer("fetched_at").notNull(), // 数据获取时间戳
  expiresAt: integer("expires_at").notNull(), // 数据过期时间戳

  // 状态管理
  updateStatus: text("update_status").notNull().default("completed"), // pending/updating/completed/failed
  errorMessage: text("error_message"), // 错误信息

  // 统计信息
  repositoriesCount: integer("repositories_count").notNull().default(0),
  commitsCount: integer("commits_count").notNull().default(0),
  readmesCount: integer("readmes_count").notNull().default(0),
  techStackFilesCount: integer("tech_stack_files_count").notNull().default(0),
  timeStatsCount: integer("time_stats_count").notNull().default(0),

  // 时间戳
  createdAt: integer("created_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  updatedAt: integer("updated_at")
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

// 扩展数据表关系
export const githubExtendedDatasRelations = relations(
  githubExtendedDatas,
  ({ one }) => ({
    user: one(users, {
      fields: [githubExtendedDatas.userId],
      references: [users.id],
    }),
  })
);
