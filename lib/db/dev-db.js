// CommonJS module for development database connection
const Database = require("better-sqlite3");
const path = require("path");

function createDevDatabase() {
  try {
    const dbPath = path.join(process.cwd(), "lib/db/dev/dev-database.db");
    console.log("Creating development SQLite database at:", dbPath);
    
    const sqlite = new Database(dbPath);
    sqlite.pragma("journal_mode = WAL");
    
    return sqlite;
  } catch (error) {
    console.error("Failed to create development database:", error);
    return null;
  }
}

module.exports = { createDevDatabase };
