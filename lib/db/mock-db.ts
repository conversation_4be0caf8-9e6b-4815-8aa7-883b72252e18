import type { D1Database } from "@cloudflare/workers-types";

/**
 * Creates a mock D1 database for development and testing purposes.
 * This implementation provides minimal functionality needed for Drizzle ORM.
 */
export function createMockD1Database(): D1Database {
  // Create a simple mock that implements only what <PERSON><PERSON><PERSON> needs
  const mockDb: any = {
    prepare: (query: string) => {
      return {
        bind: (...values: any[]) => mockDb.prepare(query),
        first: async () => null,
        all: async () => ({
          results: [],
          success: true,
          meta: {
            duration: 0,
            size_after: 0,
            rows_read: 0,
            rows_written: 0,
            changed_db: false,
            changes: 0,
            last_row_id: 0,
          },
        }),
        raw: async () => [],
        run: async () => ({
          success: true,
          meta: {
            duration: 0,
            size_after: 0,
            rows_read: 0,
            rows_written: 0,
            changed_db: false,
            changes: 0,
            last_row_id: 0,
          },
        }),
      };
    },
    batch: async (statements: any[]) => {
      return statements.map(() => ({
        results: [],
        success: true,
        meta: {
          duration: 0,
          size_after: 0,
          rows_read: 0,
          rows_written: 0,
          changed_db: false,
          changes: 0,
          last_row_id: 0,
        },
      }));
    },
    exec: async (query: string) => ({
      count: 0,
      duration: 0,
    }),
    dump: async () => new ArrayBuffer(0),
    withSession: () => mockDb,
  };

  return mockDb as D1Database;
}
