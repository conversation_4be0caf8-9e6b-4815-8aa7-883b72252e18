import type { SupportedModuleType } from "@/types/debug-api";

// 🎯 工具函数1: 自描述数据检测 (15行)
export function detectSelfDescribingData(chunk: string, message: string) {
  let isSelfDescribing = false;
  let hasStructuredContent = false;

  try {
    if (chunk && typeof chunk === "string") {
      const parsed = JSON.parse(chunk);
      isSelfDescribing = !!(parsed.data && parsed.displayHints);
      hasStructuredContent = true;
    }
  } catch {
    // 不是JSON，可能是普通字符串
    hasStructuredContent = !!chunk && chunk !== message;
  }

  return {
    isSelfDescribing,
    hasStructuredContent,
    contentLength: chunk?.length || 0,
    messageLength: message?.length || 0,
  };
}

// 🎯 工具函数2: 事件对象构建 (20行)
export function createProgressEvent(
  moduleType: SupportedModuleType,
  stage: string,
  message: string,
  chunk: string,
  isComplete: boolean,
  sessionId: string,
  extraMetadata?: Record<string, any>
): object {
  const selfDescribingData = detectSelfDescribingData(chunk, message);

  return {
    type: "content_chunk",
    module: moduleType,
    stage,
    message,
    content: chunk || "",
    isComplete,
    timestamp: Date.now(),
    sessionId,
    metadata: {
      streamingMode: true,
      hasStructuredData: !!chunk,
      stageType: isComplete ? "completed" : "in_progress",
      ...selfDescribingData,
      ...extraMetadata,
    },
  };
}

// 🎯 工具函数3: onProgress回调工厂 (15行)
export function createOnProgress(
  moduleType: SupportedModuleType,
  sessionId: string,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder,
  extraMetadata?: (chunk: string, stage: string) => Record<string, any>
): (
  message: string,
  chunk: string,
  isComplete: boolean,
  stage: string
) => void {
  return (
    message: string,
    chunk: string,
    isComplete: boolean,
    stage: string
  ) => {
    const enhancedMetadata = extraMetadata?.(chunk, stage) || {};
    const event = createProgressEvent(
      moduleType,
      stage,
      message,
      chunk,
      isComplete,
      sessionId,
      enhancedMetadata
    );
    const eventData = `data: ${JSON.stringify(event)}\n\n`;
    controller.enqueue(encoder.encode(eventData));
  };
}
