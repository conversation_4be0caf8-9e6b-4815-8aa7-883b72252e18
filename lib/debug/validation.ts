import { z } from "zod";
import type { UserData } from "@/types/user-data";
import type { AnalyzerConfig, UserPreferences } from "@/lib/ai/types";
import type {
  SupportedModuleType,
  WriterAIParams,
  CriticAIParams,
  DebugApiError,
} from "@/types/debug-api";
import { DebugErrorCodes } from "@/types/debug-api";

// =================================================================
// 🚀 统一验证器系统 (V2.0) - 简洁版本
// =================================================================

// 基础验证Schema
export const userDataSchema = z.object({
  login: z.string().min(1, "GitHub用户名不能为空"),
  name: z.string().min(1, "显示名称不能为空"),
  username: z.string().min(1, "用户名不能为空"),
  avatarUrl: z.string().url("头像URL格式无效"),
  publicRepos: z.number().min(0, "公开仓库数不能为负数"),
  followers: z.number().min(0, "关注者数不能为负数"),
  following: z.number().min(0, "关注数不能为负数"),
  createdAt: z.number().positive("创建时间必须为正数"),
  totalStars: z.number().min(0, "总星标数不能为负数"),
  contributionScore: z.number().min(0, "贡献分数不能为负数"),
  commits: z.number().min(0, "提交数不能为负数"),
  pullRequests: z.number().min(0, "PR数不能为负数"),
  issues: z.number().min(0, "Issue数不能为负数"),
  reviews: z.number().min(0, "代码审查数不能为负数"),
  totalForks: z.number().min(0, "总Fork数不能为负数"),
  contributedRepos: z.number().min(0, "贡献仓库数不能为负数"),
  languageStats: z.object({
    totalLanguages: z.number().min(0),
    totalBytes: z.number().min(0),
    analyzedRepos: z.number().min(0),
    languages: z.array(z.any()),
    primaryLanguage: z.any(),
    metadata: z.object({
      analysisVersion: z.string(),
      lastUpdated: z.number().positive(),
      cacheExpiry: z.number().positive(),
    }),
  }),
  updatedAt: z.number().positive("更新时间必须为正数"),
});

export const sessionIdSchema = z
  .string()
  .min(1, "会话ID不能为空")
  .regex(/^[a-zA-Z0-9_-]+$/, "会话ID只能包含字母、数字、下划线和连字符");

export const moduleTypeSchema = z.enum(
  ["analyzer", "strategist", "writer", "critic"],
  {
    errorMap: () => ({
      message: "模块类型必须是 analyzer、strategist、writer 或 critic 之一",
    }),
  }
);

// GitHub扩展数据验证Schema
export const githubExtendedDataSchema = z.object({
  version: z.string(),
  fetchedAt: z.number(),
  username: z.string(),
  repositories: z.array(z.any()),
  commits: z.array(z.any()),
  readmes: z.array(z.any()),
  techStackFiles: z.array(z.any()),
  timeStats: z.array(z.any()),
  fetchConfig: z.object({
    maxRepositories: z.number(),
    maxCommitsPerRepo: z.number(),
    includeReadme: z.boolean(),
    includeTechStackFiles: z.boolean(),
    includeTimeStats: z.boolean(),
  }),
});

// 统一执行选项验证Schema
export const unifiedExecutionOptionsSchema = z.object({
  enableStreaming: z.boolean().optional(),
  timeout: z.number().min(1000).max(300000).optional(),
  retryCount: z.number().min(1).max(10).optional(),
});

// 统一执行上下文验证Schema
export const unifiedExecutionContextSchema = z.object({
  githubData: userDataSchema,
  extendedData: githubExtendedDataSchema.optional(),
});

// 模块输出验证Schema - 简化版本
export const analyzerOutputSchema = z.object({
  status: z.enum(["success", "error"]),
  content: z.array(z.string()), // 语义标签数组
  processing_time: z.number().optional(),
  confidence: z.number().optional(),
  metadata: z.any().optional(),
});

export const strategistOutputSchema = z.object({
  status: z.enum(["success", "error"]),
  content: z.object({
    narrative_scaffold: z.object({
      A: z.string(),
      B: z.string(),
      C_hint: z.string(),
    }),
    insight_pool: z.object({
      behavior_summary: z.string(),
      persona: z.string(),
      humor_angle: z.string(),
      analogy_pool: z.array(z.string()),
    }),
    punchline_focus: z.object({
      punchline_candidates: z.array(z.string()),
      tone: z.string(),
      stylistic_suggestion: z.string(),
    }),
    writing_instruction: z.object({
      focus_on: z.string(),
      overall_tone: z.string(),
    }),
  }),
  processing_time: z.number().optional(),
  confidence: z.number().optional(),
  metadata: z.any().optional(),
});

export const writerOutputSchema = z.object({
  status: z.enum(["success", "error"]),
  content: z
    .object({
      generatedText: z.string(),
    })
    .passthrough(), // 允许额外字段
  processing_time: z.number().optional(),
  confidence: z.number().optional(),
  metadata: z.any().optional(),
});

// 统一请求基础验证Schema
export const unifiedDebugRequestBaseSchema = z.object({
  sessionId: sessionIdSchema,
  context: unifiedExecutionContextSchema,
  options: unifiedExecutionOptionsSchema.optional(),
});

/**
 * 统一验证器类 - 处理新的统一请求格式
 */
export class UnifiedDebugValidator {
  /**
   * 验证模块类型
   */
  static validateModuleType(moduleType: string): SupportedModuleType {
    const result = moduleTypeSchema.safeParse(moduleType);
    if (!result.success) {
      throw this.createValidationError(
        DebugErrorCodes.INVALID_MODULE,
        `无效的模块类型: ${moduleType}`,
        result.error.errors
      );
    }
    return result.data;
  }

  /**
   * 验证统一Analyzer请求
   */
  static validateAnalyzerRequest(requestBody: any) {
    const schema = unifiedDebugRequestBaseSchema.extend({
      context: z.object({
        githubData: userDataSchema,
        extendedData: githubExtendedDataSchema, // Analyzer必需
      }),
      input: z.undefined(), // Analyzer无输入
      config: z.any().optional(), // 简化配置验证
    });

    const result = schema.safeParse(requestBody);
    if (!result.success) {
      throw this.createValidationError(
        DebugErrorCodes.VALIDATION_ERROR,
        "Analyzer请求参数验证失败",
        result.error.errors
      );
    }
    return result.data;
  }

  /**
   * 验证统一Strategist请求
   */
  static validateStrategistRequest(requestBody: any) {
    const schema = unifiedDebugRequestBaseSchema.extend({
      context: z.object({
        githubData: userDataSchema,
        extendedData: z.undefined(), // Strategist不需要
      }),
      input: analyzerOutputSchema, // 必需Analyzer输出
      config: z.any().optional(), // 简化配置验证
    });

    const result = schema.safeParse(requestBody);
    if (!result.success) {
      throw this.createValidationError(
        DebugErrorCodes.VALIDATION_ERROR,
        "Strategist请求参数验证失败",
        result.error.errors
      );
    }
    return result.data;
  }

  /**
   * 验证统一Writer请求
   */
  static validateWriterRequest(requestBody: any) {
    const schema = unifiedDebugRequestBaseSchema.extend({
      context: z.object({
        githubData: userDataSchema,
        extendedData: z.undefined(),
      }),
      input: z.object({
        analyzerOutput: analyzerOutputSchema,
        strategistOutput: strategistOutputSchema,
      }),
      config: z.any().optional(), // 简化配置验证
    });

    const result = schema.safeParse(requestBody);
    if (!result.success) {
      throw this.createValidationError(
        DebugErrorCodes.VALIDATION_ERROR,
        "Writer请求参数验证失败",
        result.error.errors
      );
    }
    return result.data;
  }

  /**
   * 验证统一Critic请求
   */
  static validateCriticRequest(requestBody: any) {
    const schema = unifiedDebugRequestBaseSchema.extend({
      context: z.object({
        githubData: userDataSchema,
        extendedData: z.undefined(),
      }),
      input: z.object({
        writerOutput: writerOutputSchema,
        strategistOutput: strategistOutputSchema,
      }),
      config: z.any().optional(), // 简化配置验证
    });

    const result = schema.safeParse(requestBody);
    if (!result.success) {
      throw this.createValidationError(
        DebugErrorCodes.VALIDATION_ERROR,
        "Critic请求参数验证失败",
        result.error.errors
      );
    }
    return result.data;
  }

  /**
   * 统一请求验证入口
   */
  static validateUnifiedRequest(
    moduleType: SupportedModuleType,
    requestBody: any
  ) {
    switch (moduleType) {
      case "analyzer":
        return this.validateAnalyzerRequest(requestBody);
      case "strategist":
        return this.validateStrategistRequest(requestBody);
      case "writer":
        return this.validateWriterRequest(requestBody);
      case "critic":
        return this.validateCriticRequest(requestBody);
      default:
        throw this.createValidationError(
          DebugErrorCodes.INVALID_MODULE,
          `不支持的模块类型: ${moduleType}`,
          []
        );
    }
  }

  /**
   * 创建验证错误
   */
  static createValidationError(
    code: DebugErrorCodes,
    message: string,
    details?: any
  ): DebugApiError {
    return {
      code,
      message,
      details,
      timestamp: Date.now(),
    };
  }
}

// =================================================================
// 错误处理工具类 - 保留现有的错误处理逻辑
// =================================================================

export class DebugErrorHandler {
  /**
   * 处理Zod验证错误
   */
  static handleZodError(error: z.ZodError): DebugApiError {
    const formattedErrors = error.errors.map((err) => ({
      path: err.path.join("."),
      message: err.message,
      code: err.code,
    }));

    return UnifiedDebugValidator.createValidationError(
      DebugErrorCodes.VALIDATION_ERROR,
      "参数验证失败",
      formattedErrors
    );
  }

  /**
   * 处理模块错误
   */
  static handleModuleError(
    error: Error,
    moduleType: SupportedModuleType
  ): DebugApiError {
    let errorCode = DebugErrorCodes.MODULE_EXECUTION_ERROR;
    let message = `${moduleType}模块执行失败: ${error.message}`;

    // 特殊错误类型处理
    if (error.message.includes("API key not configured")) {
      errorCode = DebugErrorCodes.INTERNAL_SERVER_ERROR;
      message = "AI服务配置错误，请联系管理员";
    } else if (error.message.includes("timeout")) {
      message = `${moduleType}模块执行超时，请稍后重试`;
    } else if (
      error.message.includes("requires") &&
      error.message.includes("input")
    ) {
      errorCode = DebugErrorCodes.MISSING_INPUT_DATA;
    }

    return UnifiedDebugValidator.createValidationError(errorCode, message, {
      originalError: error.message,
      moduleType,
      stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  }

  /**
   * 处理认证错误
   */
  static handleAuthError(): DebugApiError {
    return UnifiedDebugValidator.createValidationError(
      DebugErrorCodes.UNAUTHORIZED,
      "未授权访问，请先登录",
      { requiresAuth: true }
    );
  }

  /**
   * 处理通用错误
   */
  static handleGenericError(error: unknown): DebugApiError {
    if (error instanceof Error) {
      return UnifiedDebugValidator.createValidationError(
        DebugErrorCodes.INTERNAL_SERVER_ERROR,
        "服务器内部错误",
        {
          originalError: error.message,
          stack:
            process.env.NODE_ENV === "development" ? error.stack : undefined,
        }
      );
    }

    return UnifiedDebugValidator.createValidationError(
      DebugErrorCodes.INTERNAL_SERVER_ERROR,
      "未知错误",
      { originalError: String(error) }
    );
  }
}

// 请求限流验证 - 保留现有逻辑
export class RateLimitValidator {
  private static requestCounts = new Map<
    string,
    { count: number; resetTime: number }
  >();
  private static readonly RATE_LIMIT = 60; // 每小时60次请求
  private static readonly WINDOW_MS = 60 * 60 * 1000; // 1小时

  /**
   * 检查请求频率
   */
  static checkRateLimit(userId: string): boolean {
    const now = Date.now();
    const userLimit = this.requestCounts.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // 重置计数
      this.requestCounts.set(userId, {
        count: 1,
        resetTime: now + this.WINDOW_MS,
      });
      return true;
    }

    if (userLimit.count >= this.RATE_LIMIT) {
      return false;
    }

    userLimit.count++;
    return true;
  }

  /**
   * 获取剩余请求次数
   */
  static getRemainingRequests(userId: string): number {
    const userLimit = this.requestCounts.get(userId);
    if (!userLimit || Date.now() > userLimit.resetTime) {
      return this.RATE_LIMIT;
    }
    return Math.max(0, this.RATE_LIMIT - userLimit.count);
  }

  /**
   * 获取重置时间
   */
  static getResetTime(userId: string): number {
    const userLimit = this.requestCounts.get(userId);
    if (!userLimit || Date.now() > userLimit.resetTime) {
      return Date.now() + this.WINDOW_MS;
    }
    return userLimit.resetTime;
  }
}
