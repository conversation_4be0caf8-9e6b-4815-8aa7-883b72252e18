import type { UserData } from "@/types/user-data";
import type {
  SupportedModuleType,
  DebugRequest,
  DebugResponse,
  AnalyzerDebugRequest,
  StrategistDebugRequest,
  WriterDebugRequest,
  CriticDebugRequest,
  AnalyzerDebugResponse,
  StrategistDebugResponse,
  WriterDebugResponse,
  CriticDebugResponse,
  WriterAIParams,
  CriticAIParams,
} from "@/types/debug-api";
import type { AnalyzerConfig, UserPreferences } from "@/lib/ai/types";
import type {
  DebugAnalyzerOutput,
  DebugStrategistOutput,
  DebugWriterOutput,
  DebugCriticOutput,
} from "@/lib/ai/types/debug-adapters";

interface DebugClientConfig {
  baseUrl?: string;
  timeout?: number;
}

const DEFAULT_CONFIG: Required<DebugClientConfig> = {
  baseUrl: "/api/debug",
  timeout: 30000,
};

/**
 * 简化的调试API客户端
 */
export class DebugApiClient {
  private config: Required<DebugClientConfig>;

  constructor(config: DebugClientConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
        ...options,
        signal: controller.signal,
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async executeModule<T extends DebugResponse>(
    moduleType: SupportedModuleType,
    request: DebugRequest
  ): Promise<T> {
    const endpoint = `/ai-modules?module=${moduleType}`;
    return this.request<T>(endpoint, {
      method: "POST",
      body: JSON.stringify(request),
    });
  }

  async executeAnalyzer(
    sessionId: string,
    githubData: UserData,
    config: AnalyzerConfig
  ): Promise<AnalyzerDebugResponse> {
    const request: AnalyzerDebugRequest = {
      sessionId,
      githubData,
      moduleConfig: config,
    };
    return this.executeModule<AnalyzerDebugResponse>("analyzer", request);
  }

  async executeStrategist(
    sessionId: string,
    githubData: UserData,
    config: UserPreferences,
    analyzerOutput: DebugAnalyzerOutput
  ): Promise<StrategistDebugResponse> {
    const request: StrategistDebugRequest = {
      sessionId,
      githubData,
      moduleConfig: config,
      inputData: analyzerOutput,
    };
    return this.executeModule<StrategistDebugResponse>("strategist", request);
  }

  async executeWriter(
    sessionId: string,
    githubData: UserData,
    config: WriterAIParams,
    analyzerOutput: DebugAnalyzerOutput,
    strategistOutput: DebugStrategistOutput
  ): Promise<WriterDebugResponse> {
    const request: WriterDebugRequest = {
      sessionId,
      githubData,
      moduleConfig: config,
      inputData: {
        analyzerOutput,
        strategistOutput,
      },
    };
    return this.executeModule<WriterDebugResponse>("writer", request);
  }

  async executeCritic(
    sessionId: string,
    githubData: UserData,
    config: CriticAIParams,
    writerOutput: DebugWriterOutput,
    strategistOutput: DebugStrategistOutput
  ): Promise<CriticDebugResponse> {
    const request: CriticDebugRequest = {
      sessionId,
      githubData,
      moduleConfig: config,
      inputData: {
        writerOutput,
        strategistOutput,
      },
    };
    return this.executeModule<CriticDebugResponse>("critic", request);
  }

  async healthCheck(): Promise<{ status: string }> {
    return this.request<{ status: string }>("/health");
  }
}

// 简化的会话管理
export class DebugSessionManager {
  private static sessions = new Map<string, any>();

  static createSession(githubData: UserData): { id: string } {
    const id = Date.now().toString(36) + Math.random().toString(36).substr(2);
    const session = {
      id,
      githubData,
      createdAt: Date.now(),
    };
    this.sessions.set(id, session);
    return { id };
  }

  static getSession(sessionId: string) {
    return this.sessions.get(sessionId);
  }

  static deleteSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }
}
