/**
 * 统一数据服务层 - UserDataService
 *
 * 职责：为四个API端点提供统一的数据获取、缓存、转换服务
 * 架构：单例模式，确保缓存策略一致性
 * 优化：零重复代码，统一缓存配置
 */

import { cacheManager } from "@/lib/cloudflare/kv-cache-manager";
import { getDb } from "@/lib/db";
import {
  contributeDatas,
  shareLinks,
  users,
  type NewContributeData,
} from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import {
  UserData,
  CompleteUserResponse,
  SharedCardResponse,
} from "@/types/user-data";
import { calculateMultiDimensionScores } from "@/lib/github/score";
import {
  fetchGitHubUserData,
  fetchGitHubContributions,
} from "@/lib/github/fetch";

import type { ContributeData } from "@/lib/db/schema";

// ==================== 统一缓存配置 ====================

/**
 * 统一缓存配置
 * 所有API使用相同的TTL和键规范
 */
export const UNIFIED_CACHE_CONFIG = {
  TTL: {
    COMPLETE_DATA: 1800, // 30分钟：完整用户数据
    SHARE_TOKEN: 3600, // 1小时：分享链接数据
    GITHUB_RAW: 1800, // 30分钟：GitHub原始数据
    EXTENDED_DATA: 86400, // 24小时：扩展GitHub数据
  },
  KEY_PREFIX: {
    COMPLETE: "unified:complete",
    SHARE: "unified:share",
    GITHUB_RAW: "unified:github_raw",
    EXTENDED: "unified:extended",
  },
} as const;

/**
 * 生成统一的缓存键
 */
export function generateUnifiedCacheKey(
  type: keyof typeof UNIFIED_CACHE_CONFIG.KEY_PREFIX,
  identifier: string
): string {
  const prefix = UNIFIED_CACHE_CONFIG.KEY_PREFIX[type];
  return `${prefix}:${identifier}`;
}

// ==================== 类型定义 ====================

/**
 * 分享元数据
 */
export interface ShareMetadata {
  templateType: string;
  backgroundId?: string;
  expiresAt: number;
}

// ==================== 统一数据服务类 ====================

/**
 * 统一数据服务
 * 为所有API端点提供一致的数据获取和缓存策略
 */
export class UserDataService {
  private constructor() {}

  // 单例实例
  private static instance: UserDataService;
  public static getInstance(): UserDataService {
    if (!UserDataService.instance) {
      UserDataService.instance = new UserDataService();
    }
    return UserDataService.instance;
  }

  // ==================== 核心数据获取方法 ====================

  /**
   * 统一缓存失效
   */
  async invalidateUserCaches(userId: string): Promise<void> {
    const cacheKeys = [
      generateUnifiedCacheKey("COMPLETE", userId),
      generateUnifiedCacheKey("GITHUB_RAW", userId),
      generateUnifiedCacheKey("EXTENDED", userId),
    ];

    const promises = cacheKeys.map((key) =>
      cacheManager
        .delete(key)
        .catch((error) =>
          console.warn(`Failed to invalidate cache key ${key}:`, error)
        )
    );

    await Promise.all(promises);
    console.log(`🗑️ Unified cache invalidation for user ${userId}`);
  }

  /**
   * 获取完整用户数据（用于 /api/github-data）
   */
  async getUserCompleteData(
    userId: string,
    options: {
      forceRefresh?: boolean;
      waitUntil?: (promise: Promise<any>) => void;
    } = {}
  ): Promise<{
    success: boolean;
    data?: CompleteUserResponse;
    error?: string;
  }> {
    const cacheKey = generateUnifiedCacheKey("COMPLETE", userId);

    if (options.forceRefresh) {
      await this.invalidateUserCaches(userId);
      console.log(`🔄 Force cache refresh for user ${userId}`);
    }

    try {
      // 1. 尝试从缓存获取
      if (!options.forceRefresh) {
        try {
          const cached = await cacheManager.get<CompleteUserResponse>(cacheKey);
          if (cached) {
            console.log(`🚀 Cache HIT: Complete data for ${userId}`);

            // 扩展数据获取已移至独立的 /api/github-extended 接口
            // 不在主请求中处理，避免Worker超时

            return {
              success: true,
              data: {
                ...cached,
                metadata: { ...cached.metadata, cached: true },
              },
            };
          }
        } catch (error) {
          console.warn(`Cache get failed for complete data ${userId}:`, error);
        }
      }

      // 2. 获取原始数据并转换
      const userData = await this.getUserDataFromDB(userId, {
        forceRefresh: options.forceRefresh,
      });

      // 扩展数据获取已移至独立的 /api/github-extended 接口
      // 不在主请求中处理，避免Worker超时

      // 4. 计算多维度评分
      const multiDimensionScore = calculateMultiDimensionScores({
        commits: userData.commits,
        contributedRepos: userData.contributedRepos,
        pullRequests: userData.pullRequests,
        reviews: userData.reviews,
        issues: userData.issues,
        totalStars: userData.totalStars,
        totalForks: userData.totalForks,
        followers: userData.followers,
        languageDiversity: userData.languageStats?.totalLanguages || 0,
        publicRepos: userData.publicRepos,
        following: userData.following,
        createdAt: userData.createdAt,
      });

      // 4. 构建完整响应数据
      const completeData: CompleteUserResponse = {
        userData,
        multiDimensionScore,
        activityScores: {
          stars: userData.totalStars || 0,
          forks: userData.totalForks || 0,
          commits: userData.commits || 0,
          pullRequests: userData.pullRequests || 0,
          issues: userData.issues || 0,
          reviews: userData.reviews || 0,
        },
        metadata: {
          cached: false,
          version: "unified-v2.0",
          responseTime: 0,
          lastCalculated: Date.now(),
          dataVersion: "v4.2",
          timestamp: Date.now(),
        },
      };

      // 5. 缓存结果
      try {
        await cacheManager.set(cacheKey, completeData, {
          expirationTtl: UNIFIED_CACHE_CONFIG.TTL.COMPLETE_DATA,
        });
        console.log(`💾 Cached complete data for ${userId}`);
      } catch (error) {
        console.warn(`Cache set failed for complete data ${userId}:`, error);
      }

      return { success: true, data: completeData };
    } catch (error: any) {
      console.error(`Error in getUserCompleteData for ${userId}:`, error);
      return {
        success: false,
        error: error.message || "Failed to fetch complete user data",
      };
    }
  }

  /**
   * 通过分享token获取用户数据（用于 /api/share-links/[token]）
   */
  async getUserDataByShareToken(token: string): Promise<{
    success: boolean;
    data?: SharedCardResponse;
    error?: string;
  }> {
    const cacheKey = generateUnifiedCacheKey("SHARE", token);

    try {
      // 1. 尝试从缓存获取
      try {
        const cached = await cacheManager.get<SharedCardResponse>(cacheKey);
        if (cached) {
          console.log(`🚀 Cache HIT: Share data for token ${token}`);
          return { success: true, data: cached };
        }
      } catch (error) {
        console.warn(`Cache get failed for share token ${token}:`, error);
      }

      // 2. 从数据库获取分享链接信息
      const db = await getDb();
      const shareLink = await db
        .select()
        .from(shareLinks)
        .where(eq(shareLinks.linkToken, token))
        .get();

      if (!shareLink || new Date() > new Date(shareLink.expiresAt)) {
        return {
          success: false,
          error: shareLink ? "Share link has expired" : "Share link not found",
        };
      }

      // 3. 获取完整用户数据
      const completeDataResponse = await this.getUserCompleteData(
        shareLink.userId
      );

      if (!completeDataResponse.success || !completeDataResponse.data) {
        throw new Error(
          completeDataResponse.error || "Failed to fetch complete user data"
        );
      }

      // 4. 构建分享响应数据
      const shareData = {
        ...completeDataResponse.data,
        templateType: shareLink.templateType,
        backgroundId: shareLink.backgroundId || undefined,
        expiresAt: shareLink.expiresAt,
      };

      // 5. 缓存结果
      try {
        await cacheManager.set(cacheKey, shareData, {
          expirationTtl: UNIFIED_CACHE_CONFIG.TTL.SHARE_TOKEN,
        });
        console.log(`💾 Cached share data for token ${token}`);
      } catch (error) {
        console.warn(`Cache set failed for share token ${token}:`, error);
      }

      return { success: true, data: shareData };
    } catch (error: any) {
      console.error(`Error in getUserDataByShareToken for ${token}:`, error);
      return {
        success: false,
        error: error.message || "Failed to fetch share data",
      };
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 从数据库获取用户数据并转换为UserData格式
   */
  private async getUserDataFromDB(
    userId: string,
    options: { forceRefresh?: boolean } = {}
  ): Promise<UserData> {
    const cacheKey = generateUnifiedCacheKey("GITHUB_RAW", userId);

    if (!options.forceRefresh) {
      try {
        const cached = await cacheManager.get<UserData>(cacheKey);
        if (cached) {
          console.log(`🚀 Cache HIT: User data for ${userId}`);
          return cached;
        }
      } catch (error) {
        console.warn(`Cache get failed for user data ${userId}:`, error);
      }
    }

    // 从数据库获取原始数据
    const db = await getDb();
    let githubData: ContributeData | undefined = await db
      .select()
      .from(contributeDatas)
      .where(eq(contributeDatas.userId, userId))
      .get();

    if (!githubData) {
      // 如果数据不存在，则从 GitHub 获取，并保存到数据库
      const user = await db
        .select()
        .from(users)
        .where(eq(users.id, userId))
        .get();
      if (!user) {
        throw new Error(`User with id ${userId} not found.`);
      }
      const username = user.username;

      console.log(
        `[UserDataService] DB miss for ${username}, fetching from GitHub.`
      );

      const [userDataFromGithub, contributionsDataFromGithub] =
        await Promise.all([
          fetchGitHubUserData(username),
          fetchGitHubContributions(username),
        ]);

      const dataToUpsert: Omit<
        NewContributeData,
        "id" | "recordCreatedAt" | "lastUpdated"
      > = {
        userId: userId,
        login: userDataFromGithub.login,
        name: userDataFromGithub.name,
        avatarUrl: userDataFromGithub.avatar_url,
        bio: userDataFromGithub.bio,
        blog: userDataFromGithub.blog,
        location: userDataFromGithub.location,
        twitterUsername: userDataFromGithub.twitter_username,
        publicRepos: userDataFromGithub.public_repos,
        followers: userDataFromGithub.followers,
        following: userDataFromGithub.following,
        userCreatedAt: userDataFromGithub.created_at,
        totalStars: contributionsDataFromGithub.total_stars,
        contributionScore: contributionsDataFromGithub.contribution_score,
        commits: contributionsDataFromGithub.commits,
        pullRequests: contributionsDataFromGithub.pull_requests,
        issues: contributionsDataFromGithub.issues,
        reviews: contributionsDataFromGithub.reviews,
        totalForks: contributionsDataFromGithub.total_forks,
        contributedRepos: contributionsDataFromGithub.contributed_repos,
        languageStats: JSON.stringify(
          contributionsDataFromGithub.language_stats
        ),
        lastFullUpdate: Date.now(),
        updateStatus: "completed",
      };

      await db.insert(contributeDatas).values(dataToUpsert).onConflictDoUpdate({
        target: contributeDatas.userId,
        set: dataToUpsert,
      });

      githubData = await db
        .select()
        .from(contributeDatas)
        .where(eq(contributeDatas.userId, userId))
        .get();

      if (!githubData) {
        throw new Error("Failed to fetch and save data from GitHub.");
      }
    }

    // 解析语言统计数据
    let languageStats = null;
    if (githubData.languageStats) {
      try {
        languageStats = JSON.parse(githubData.languageStats);
      } catch (error) {
        console.warn("Failed to parse language stats:", error);
      }
    }

    // 直接构建UserData对象（统一格式）
    const userData: UserData = {
      login: githubData.login,
      name: githubData.name || "",
      username: githubData.login,
      avatarUrl: githubData.avatarUrl,
      bio: githubData.bio || "",
      blog: githubData.blog || "",
      location: githubData.location || "",
      twitterUsername: githubData.twitterUsername || "",
      publicRepos: githubData.publicRepos,
      followers: githubData.followers,
      following: githubData.following,
      totalStars: githubData.totalStars,
      totalForks: githubData.totalForks,
      contributionScore: githubData.contributionScore,
      commits: githubData.commits,
      pullRequests: githubData.pullRequests,
      issues: githubData.issues,
      reviews: githubData.reviews,
      contributedRepos: githubData.contributedRepos,
      languageStats: languageStats,
      createdAt: githubData.userCreatedAt,
      updatedAt: Date.now(),
    };

    // 缓存结果
    try {
      await cacheManager.set(cacheKey, userData, {
        expirationTtl: UNIFIED_CACHE_CONFIG.TTL.GITHUB_RAW,
      });
      console.log(`💾 Cached user data for ${userId}`);
    } catch (error) {
      console.warn(`Cache set failed for user data ${userId}:`, error);
    }

    return userData;
  }
}

// ==================== 导出单例实例 ====================

/**
 * 导出统一数据服务实例
 * 确保整个应用使用同一个实例和缓存策略
 */
export const userDataService = UserDataService.getInstance();
