/**
 * GitHub扩展数据服务
 * V5 重构 - 独立存储和管理扩展数据，实现静默更新机制
 *
 * 设计原则：
 * - 数据分离：扩展数据独立存储，不影响常规GitHub数据
 * - 静默更新：后台自动检查和更新，对用户透明
 * - 按需获取：仅在AI功能需要时才获取扩展数据
 * - 性能优先：不影响现有API响应速度
 */

import { getDb } from "@/lib/db";
import { githubExtendedDatas, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { fetchExtendedGitHubData } from "@/lib/github/extended-fetch";
import type {
  GitHubExtendedData,
  ExtendedDataStatusInfo,
} from "@/types/github-extended";
import { DEFAULT_EXTENDED_FETCH_CONFIG } from "@/types/github-extended";

// ==================== 配置常量 ====================

/**
 * 扩展数据默认配置
 */
const EXTENDED_DATA_CONFIG = {
  DEFAULT_EXPIRY_HOURS: 720, // 默认30天过期 (30 * 24 = 720小时)
  MAX_UPDATE_ATTEMPTS: 3, // 最大更新尝试次数
  UPDATE_TIMEOUT_MS: 30000, // 更新超时时间30秒
  BACKGROUND_UPDATE_DELAY: 1000, // 后台更新延迟1秒
} as const;

// ==================== 扩展数据服务类 ====================

/**
 * GitHub扩展数据服务
 * 负责扩展数据的存储、检查、更新和获取
 */
export class GitHubExtendedService {
  private constructor() {}

  // 单例实例
  private static instance: GitHubExtendedService;
  public static getInstance(): GitHubExtendedService {
    if (!GitHubExtendedService.instance) {
      GitHubExtendedService.instance = new GitHubExtendedService();
    }
    return GitHubExtendedService.instance;
  }

  // ==================== 数据状态检查 ====================

  /**
   * 检查用户扩展数据状态
   */
  async checkExtendedDataStatus(
    userId: string
  ): Promise<ExtendedDataStatusInfo> {
    try {
      const db = await getDb();
      let record: any = null;

      try {
        // 使用超时保护的查询
        const queryPromise = db
          .select()
          .from(githubExtendedDatas)
          .where(eq(githubExtendedDatas.userId, userId))
          .get();

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(
            () => reject(new Error("Query timeout after 10 seconds")),
            10000
          );
        });

        record = (await Promise.race([queryPromise, timeoutPromise])) as any;
      } catch (queryError) {
        // 备用查询方式
        try {
          const allRecords = await db
            .select()
            .from(githubExtendedDatas)
            .where(eq(githubExtendedDatas.userId, userId));

          record = allRecords[0] || null;
        } catch (altError) {
          // 降级方案 - 假设数据不存在
          return {
            exists: false,
            expired: true,
            needsUpdate: true,
          };
        }
      }

      if (!record) {
        return {
          exists: false,
          expired: true,
          needsUpdate: true,
        };
      }

      const now = Date.now();
      const expired = now > record.expiresAt;
      const needsUpdate = expired || record.updateStatus === "failed";

      return {
        exists: true,
        expired,
        needsUpdate,
        lastFetchedAt: record.fetchedAt,
        expiresAt: record.expiresAt,
        updateStatus: record.updateStatus as
          | "pending"
          | "updating"
          | "completed"
          | "failed",
      };
    } catch (error) {
      console.error(
        `❌ Error checking extended data status for ${userId}:`,
        error
      );
      return {
        exists: false,
        expired: true,
        needsUpdate: true,
      };
    }
  }

  // ==================== 静默更新机制 ====================

  /**
   * 静默检查并更新扩展数据（在常规数据获取时调用）
   * 这个方法不会阻塞主流程，在后台异步执行
   */
  async silentCheckAndUpdate(userId: string): Promise<void> {
    try {
      console.log(`🔍 [SilentUpdate] Starting for user ${userId}`);

      // 步骤1: 检查数据状态
      console.log(
        `🔍 [SilentUpdate] Step 1: Checking data status for ${userId}`
      );
      const status = await this.checkExtendedDataStatus(userId);
      console.log(`📊 [SilentUpdate] Status check result for ${userId}:`, {
        exists: status.exists,
        expired: status.expired,
        needsUpdate: status.needsUpdate,
        updateStatus: status.updateStatus,
      });

      if (!status.needsUpdate) {
        console.log(`✅ [SilentUpdate] No update needed for user ${userId}`);
        return;
      }

      console.log(
        `🔄 [SilentUpdate] Update needed for user ${userId}, starting background update`
      );

      // 步骤2: 执行后台更新
      try {
        console.log(
          `🚀 [SilentUpdate] Step 2: Starting background update for ${userId}`
        );
        await this.updateExtendedDataInBackground(userId);
        console.log(
          `✅ [SilentUpdate] Background update completed successfully for ${userId}`
        );
      } catch (error) {
        console.error(
          `❌ [SilentUpdate] Background update failed for ${userId}:`,
          error
        );
        console.error(`❌ [SilentUpdate] Error details:`, {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : "No stack trace",
          userId,
        });

        // 重新抛出错误，让调用者知道更新失败
        throw error;
      }
    } catch (error) {
      console.error(
        `❌ [SilentUpdate] Fatal error during silent check for ${userId}:`,
        error
      );
      console.error(`❌ [SilentUpdate] Fatal error details:`, {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : "No stack trace",
        userId,
      });

      // 重新抛出错误
      throw error;
    }
  }

  /**
   * 后台更新扩展数据
   */
  private async updateExtendedDataInBackground(userId: string): Promise<void> {
    try {
      console.log(
        `🚀 [BackgroundUpdate] Starting extended data fetch for user ${userId}`
      );

      // 步骤1: 获取用户信息
      console.log(
        `👤 [BackgroundUpdate] Step 1: Fetching user info for ${userId}`
      );
      const db = await getDb();
      const user = await db.query.users.findFirst({
        where: eq(users.id, userId),
      });

      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      console.log(
        `👤 [BackgroundUpdate] Found user: ${user.username} (${userId})`
      );

      // 步骤2: 验证 GitHub Token
      console.log(`🔑 [BackgroundUpdate] Step 2: Validating GitHub token`);
      try {
        const { getTokenManager } = await import("@/lib/github/token-manager");
        const tokenManager = getTokenManager();
        const testToken = tokenManager.getBestAvailableToken();

        if (!testToken) {
          throw new Error("No GitHub token available");
        }

        console.log(
          `🔑 [BackgroundUpdate] GitHub token validated: ${testToken.substring(
            0,
            8
          )}...`
        );
      } catch (tokenError) {
        console.error(
          `❌ [BackgroundUpdate] Token validation failed:`,
          tokenError
        );
        throw new Error(
          `Token manager failed: ${
            tokenError instanceof Error
              ? tokenError.message
              : String(tokenError)
          }`
        );
      }

      // 步骤3: 获取仓库列表
      console.log(
        `📦 [BackgroundUpdate] Step 3: Fetching repositories for ${user.username}`
      );
      const { fetchWithAuth } = await import("@/lib/github/extended-fetch");
      const reposUrl = `https://api.github.com/user/repos?affiliation=owner,organization_member&sort=pushed&per_page=50`;

      console.log(`📡 [BackgroundUpdate] Making API request to: ${reposUrl}`);
      const reposResponse = await fetchWithAuth(reposUrl, {
        next: { revalidate: 3600 },
      });

      console.log(
        `📡 [BackgroundUpdate] API response status: ${reposResponse.status}`
      );

      if (!reposResponse.ok) {
        const errorText = await reposResponse.text();
        console.error(`❌ [BackgroundUpdate] GitHub API error:`, {
          status: reposResponse.status,
          statusText: reposResponse.statusText,
          body: errorText,
          url: reposUrl,
        });
        throw new Error(
          `Failed to fetch repositories: ${reposResponse.status} - ${errorText}`
        );
      }

      const repos = (await reposResponse.json()) as any[];
      console.log(
        `📦 [BackgroundUpdate] Found ${repos.length} repositories for ${user.username}`
      );

      // 步骤4: 获取扩展数据
      console.log(
        `🔧 [BackgroundUpdate] Step 4: Fetching extended data for ${user.username}`
      );
      const extendedData = await fetchExtendedGitHubData(
        user.username,
        repos,
        DEFAULT_EXTENDED_FETCH_CONFIG
      );

      console.log(`🔧 [BackgroundUpdate] Extended data fetched successfully:`, {
        repositories: extendedData.repositories.length,
        commits: extendedData.commits.length,
        readmes: extendedData.readmes.length,
        techStackFiles: extendedData.techStackFiles.length,
        timeStats: extendedData.timeStats.length,
      });

      // 步骤5: 保存到数据库
      console.log(
        `💾 [BackgroundUpdate] Step 5: Saving extended data to database for ${userId}`
      );
      await this.saveExtendedData(userId, extendedData);

      console.log(
        `✅ [BackgroundUpdate] Extended data fetch completed successfully for user ${userId}`
      );
    } catch (error) {
      console.error(`❌ [BackgroundUpdate] Failed for user ${userId}:`, error);
      console.error(`❌ [BackgroundUpdate] Error details:`, {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : "No stack trace",
        userId,
      });

      // 标记为失败状态
      await this.markAsFailed(
        userId,
        error instanceof Error ? error.message : "Unknown error"
      );

      // 重新抛出错误
      throw error;
    }
  }

  // ==================== 数据存储管理 ====================

  /**
   * 保存扩展数据到数据库
   */
  private async saveExtendedData(
    userId: string,
    extendedData: GitHubExtendedData
  ): Promise<void> {
    try {
      const db = await getDb();
      const now = Date.now();
      const expiresAt =
        now + EXTENDED_DATA_CONFIG.DEFAULT_EXPIRY_HOURS * 60 * 60 * 1000;

      const dataToSave = {
        userId,
        repositoriesData: JSON.stringify(extendedData.repositories),
        commitsData: JSON.stringify(extendedData.commits),
        readmeData: JSON.stringify(extendedData.readmes),
        techStackFilesData: JSON.stringify(extendedData.techStackFiles),
        timeStatsData: JSON.stringify(extendedData.timeStats),
        fetchConfig: JSON.stringify(extendedData.fetchConfig),
        dataVersion: extendedData.version,
        fetchedAt: now,
        expiresAt,
        updateStatus: "completed" as const,
        errorMessage: null,
        repositoriesCount: extendedData.repositories.length,
        commitsCount: extendedData.commits.length,
        readmesCount: extendedData.readmes.length,
        techStackFilesCount: extendedData.techStackFiles.length,
        timeStatsCount: extendedData.timeStats.length,
        updatedAt: now,
      };

      // 检查是否已存在记录 - 使用超时保护
      let existingRecord: any = null;

      try {
        const queryPromise = db
          .select()
          .from(githubExtendedDatas)
          .where(eq(githubExtendedDatas.userId, userId))
          .get();

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Query timeout")), 5000);
        });

        existingRecord = (await Promise.race([
          queryPromise,
          timeoutPromise,
        ])) as any;
      } catch (queryError) {
        existingRecord = null;
      }

      if (existingRecord) {
        // 更新现有记录
        await db
          .update(githubExtendedDatas)
          .set(dataToSave)
          .where(eq(githubExtendedDatas.userId, userId));
      } else {
        // 插入新记录
        await db.insert(githubExtendedDatas).values(dataToSave);
      }
    } catch (error) {
      console.error(
        `❌ [Extended Data] Save failed for user ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 标记数据为失败状态
   */
  private async markAsFailed(
    userId: string,
    errorMessage: string
  ): Promise<void> {
    try {
      const db = await getDb();
      const now = Date.now();

      // 检查是否已存在记录 - 使用超时保护
      let existingRecord: any = null;

      try {
        const queryPromise = db
          .select()
          .from(githubExtendedDatas)
          .where(eq(githubExtendedDatas.userId, userId))
          .get();

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Query timeout")), 5000);
        });

        existingRecord = (await Promise.race([
          queryPromise,
          timeoutPromise,
        ])) as any;
      } catch (queryError) {
        existingRecord = null;
      }

      if (existingRecord) {
        // 更新现有记录
        await db
          .update(githubExtendedDatas)
          .set({
            updateStatus: "failed",
            errorMessage,
            updatedAt: now,
          })
          .where(eq(githubExtendedDatas.userId, userId));
      } else {
        // 插入新记录
        await db.insert(githubExtendedDatas).values({
          userId,
          updateStatus: "failed",
          errorMessage,
          updatedAt: now,
          fetchedAt: now,
          expiresAt:
            now + EXTENDED_DATA_CONFIG.DEFAULT_EXPIRY_HOURS * 60 * 60 * 1000,
          dataVersion: "1.0.0",
          repositoriesCount: 0,
          commitsCount: 0,
          readmesCount: 0,
          techStackFilesCount: 0,
          timeStatsCount: 0,
        });
      }
    } catch (error) {
      console.error(
        `❌ [Extended Data] Mark as failed error for ${userId}:`,
        error
      );
    }
  }

  // ==================== 数据获取 ====================

  /**
   * 获取用户的扩展数据（用于AI功能）
   * 支持等待数据获取完成，避免在数据获取中返回错误
   */
  async getExtendedData(userId: string): Promise<GitHubExtendedData | null> {
    try {
      console.log(`🔍 [ExtendedData] Getting data for user ${userId}`);

      // 第一步：检查当前数据状态
      const status = await this.checkExtendedDataStatus(userId);
      console.log(`📊 [ExtendedData] Current status for ${userId}:`, status);

      // 如果数据存在且有效，直接返回
      if (status.exists && !status.expired && !status.needsUpdate) {
        console.log(
          `✅ [ExtendedData] Valid data exists for ${userId}, returning directly`
        );
        return await this.getExtendedDataFromDb(userId);
      }

      // 如果数据正在更新中，等待完成
      if (status.updateStatus === "updating") {
        console.log(
          `⏳ [ExtendedData] Data is updating for ${userId}, waiting for completion`
        );
        return await this.waitForUpdateCompletion(userId);
      }

      // 如果数据不存在或过期，触发获取并等待完成
      console.log(
        `🚀 [ExtendedData] Need to fetch data for ${userId}, starting update`
      );

      // 先设置状态为 updating，避免并发请求
      await this.markAsUpdating(userId);

      try {
        // 执行数据获取
        await this.updateExtendedDataInBackground(userId);

        // 获取完成后返回数据
        console.log(
          `✅ [ExtendedData] Update completed for ${userId}, returning data`
        );
        return await this.getExtendedDataFromDb(userId);
      } catch (error) {
        console.error(`❌ [ExtendedData] Update failed for ${userId}:`, error);
        throw error;
      }
    } catch (error) {
      console.error(
        `❌ [ExtendedData] Error getting data for ${userId}:`,
        error
      );
      return null;
    }
  }

  /**
   * 等待数据更新完成
   */
  private async waitForUpdateCompletion(
    userId: string,
    maxWaitMs: number = 60000
  ): Promise<GitHubExtendedData | null> {
    const startTime = Date.now();
    const pollInterval = 2000; // 每2秒检查一次

    console.log(
      `⏳ [ExtendedData] Waiting for update completion for ${userId}, max wait: ${maxWaitMs}ms`
    );

    while (Date.now() - startTime < maxWaitMs) {
      // 检查状态
      const status = await this.checkExtendedDataStatus(userId);

      if (status.updateStatus === "completed") {
        console.log(`✅ [ExtendedData] Update completed for ${userId}`);
        return await this.getExtendedDataFromDb(userId);
      }

      if (status.updateStatus === "failed") {
        console.error(`❌ [ExtendedData] Update failed for ${userId}`);
        return null;
      }

      // 等待一段时间后再检查
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    }

    console.error(
      `⏰ [ExtendedData] Wait timeout for ${userId} after ${maxWaitMs}ms`
    );
    return null;
  }

  /**
   * 标记数据为更新中状态
   */
  private async markAsUpdating(userId: string): Promise<void> {
    try {
      const db = await getDb();
      const now = Date.now();

      // 检查是否已存在记录
      let existingRecord: any = null;

      try {
        const queryPromise = db
          .select()
          .from(githubExtendedDatas)
          .where(eq(githubExtendedDatas.userId, userId))
          .get();

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Query timeout")), 5000);
        });

        existingRecord = (await Promise.race([
          queryPromise,
          timeoutPromise,
        ])) as any;
      } catch (queryError) {
        existingRecord = null;
      }

      if (existingRecord) {
        // 更新现有记录状态
        await db
          .update(githubExtendedDatas)
          .set({
            updateStatus: "updating",
            errorMessage: null,
            updatedAt: now,
          })
          .where(eq(githubExtendedDatas.userId, userId));
      } else {
        // 插入新记录，状态为 updating
        await db.insert(githubExtendedDatas).values({
          userId,
          updateStatus: "updating",
          updatedAt: now,
          fetchedAt: now,
          expiresAt:
            now + EXTENDED_DATA_CONFIG.DEFAULT_EXPIRY_HOURS * 60 * 60 * 1000,
          dataVersion: "1.0.0",
          repositoriesCount: 0,
          commitsCount: 0,
          readmesCount: 0,
          techStackFilesCount: 0,
          timeStatsCount: 0,
        });
      }

      console.log(`🔄 [ExtendedData] Marked as updating for ${userId}`);
    } catch (error) {
      console.error(
        `❌ [ExtendedData] Failed to mark as updating for ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 从数据库获取扩展数据（内部方法）
   */
  private async getExtendedDataFromDb(
    userId: string
  ): Promise<GitHubExtendedData | null> {
    try {
      const db = await getDb();

      // 使用超时保护的查询
      let record: any = null;

      try {
        const queryPromise = db
          .select()
          .from(githubExtendedDatas)
          .where(eq(githubExtendedDatas.userId, userId))
          .get();

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Query timeout")), 5000);
        });

        record = (await Promise.race([queryPromise, timeoutPromise])) as any;
      } catch (queryError) {
        return null;
      }

      if (!record || record.updateStatus !== "completed") {
        return null;
      }

      // 检查是否过期
      if (Date.now() > record.expiresAt) {
        return null;
      }

      // 解析JSON数据
      const extendedData: GitHubExtendedData = {
        version: record.dataVersion,
        fetchedAt: record.fetchedAt,
        username: "", // 需要从用户表获取
        repositories: JSON.parse(record.repositoriesData || "[]"),
        commits: JSON.parse(record.commitsData || "[]"),
        readmes: JSON.parse(record.readmeData || "[]"),
        techStackFiles: JSON.parse(record.techStackFilesData || "[]"),
        timeStats: JSON.parse(record.timeStatsData || "[]"),
        fetchConfig: JSON.parse(record.fetchConfig || "{}"),
      };

      return extendedData;
    } catch (error) {
      return null;
    }
  }
}

// ==================== 导出单例实例 ====================

export const githubExtendedService = GitHubExtendedService.getInstance();
