import { ImageSource, FetchedImage } from "@/types/image-source";

export class DirectFetcher {
  async fetchImage(source: ImageSource): Promise<FetchedImage | null> {
    try {
      console.debug("Fetching direct image from:", source.url);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000);

      const response = await fetch(source.url, {
        signal: controller.signal,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const imageData = await response.arrayBuffer();
      const contentType = response.headers.get("content-type") || "image/jpeg";

      return {
        imageUrl: response.url, // 使用重定向后的最终URL
        imageData: Buffer.from(imageData).toString("base64"),
        contentType,
        sourceId: source.id,
        metadata: {
          sourceType: "direct",
        },
      };
    } catch (error) {
      console.error(`Direct fetch error for source ${source.id}:`, error);
      return null;
    }
  }
}
