import { ImageSource, FetchedImage } from "@/types/image-source";

export class UnsplashFetcher {
  private accessKey: string;
  private baseUrl = "https://api.unsplash.com";

  constructor() {
    this.accessKey = process.env.UNSPLASH_ACCESS_KEY || "";
    if (!this.accessKey) {
      console.warn(
        "Unsplash Access Key not configured, Unsplash source will be disabled"
      );
    }
  }

  async fetchImage(source: ImageSource): Promise<FetchedImage | null> {
    if (!this.accessKey) {
      console.warn("Unsplash Access Key not available");
      return null;
    }

    try {
      const photos = await this.searchPhotos(source);
      if (!photos || photos.length === 0) {
        throw new Error("No photos returned from Unsplash");
      }

      // 过滤自然主题内容
      const naturalPhotos = this.filterNaturalContent(
        photos,
        source.config?.exclude_keywords || []
      );

      if (naturalPhotos.length === 0) {
        console.warn(
          "No natural content photos found after filtering, using original photos"
        );
      }

      const photosToUse = naturalPhotos.length > 0 ? naturalPhotos : photos;
      const randomPhoto =
        photosToUse[Math.floor(Math.random() * photosToUse.length)];

      // 选择合适尺寸的图片URL
      const imageUrl = this.selectImageUrl(
        randomPhoto,
        source.config?.size || "large"
      );

      const imageData = await this.downloadImage(imageUrl);

      return {
        imageUrl,
        imageData: imageData.data,
        contentType: imageData.contentType,
        sourceId: source.id,
        metadata: {
          photographer: randomPhoto.user.name,
          photographerUrl: randomPhoto.user.links.html,
          unsplashId: randomPhoto.id,
          sourceType: "unsplash",
        },
      };
    } catch (error) {
      console.error(`Unsplash fetch error for source ${source.id}:`, error);
      return null;
    }
  }

  private async searchPhotos(source: ImageSource): Promise<any[]> {
    const config = source.config || {};

    // 构建自然主题的搜索关键词
    const query = this.buildNaturalQuery(config.keywords || []);

    const params = new URLSearchParams({
      query,
      per_page: String(config.per_page || 25),
      orientation: config.orientation || "portrait",
      order_by: config.order || "relevant",
      content_filter: "high", // 高质量内容过滤
    });

    console.log(`Fetching from Unsplash with query: ${query}`);

    const response = await fetch(`${this.baseUrl}/search/photos?${params}`, {
      headers: {
        Authorization: `Client-ID ${this.accessKey}`,
        "Accept-Version": "v1",
        "User-Agent": "github-card-app/1.0",
      },
      signal: AbortSignal.timeout(8000),
    });

    if (!response.ok) {
      throw new Error(
        `Unsplash API error: ${response.status} ${response.statusText}`
      );
    }

    const data = (await response.json()) as { results: any[] };
    return data.results || [];
  }

  private buildNaturalQuery(baseKeywords: string[]): string {
    // 自然主题关键词库
    const naturalKeywords = [
      // 植物类
      "nature",
      "forest",
      "trees",
      "flowers",
      "leaves",
      "garden",
      "botanical",
      // 风光类
      "landscape",
      "mountain",
      "ocean",
      "lake",
      "waterfall",
      "sunset",
      "sunrise",
      // 宇宙类
      "galaxy",
      "stars",
      "nebula",
      "space",
      "night sky",
      "aurora",
      "cosmos",
      // 动物类
      "wildlife",
      "birds",
      "butterfly",
      "animals",
      // 抽象自然
      "natural patterns",
      "textures",
      "minimalist nature",
    ];

    // 合并关键词
    const allKeywords =
      baseKeywords.length > 0 ? baseKeywords : naturalKeywords;

    // 随机选择1-2个关键词
    const selectedKeywords = this.selectRandomKeywords(allKeywords, 1, 2);

    return selectedKeywords.join(" ");
  }

  private selectRandomKeywords(
    keywords: string[],
    min: number,
    max: number
  ): string[] {
    const count = Math.floor(Math.random() * (max - min + 1)) + min;
    const shuffled = [...keywords].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  private filterNaturalContent(
    photos: any[],
    excludeKeywords: string[]
  ): any[] {
    // 默认排除关键词（人物相关）
    const defaultExcludeKeywords = [
      "person",
      "people",
      "human",
      "face",
      "portrait",
      "man",
      "woman",
      "child",
      "family",
      "crowd",
      "business",
      "fashion",
      "sport",
      "city",
      "building",
      "architecture",
      "urban",
    ];

    const allExcludeKeywords = [...defaultExcludeKeywords, ...excludeKeywords];

    return photos.filter((photo) => {
      const description = (photo.description || "").toLowerCase();
      const altDescription = (photo.alt_description || "").toLowerCase();
      const tags = (photo.tags || []).map((tag: any) =>
        tag.title.toLowerCase()
      );

      const allText = [description, altDescription, ...tags].join(" ");

      // 检查是否包含排除的关键词
      const hasExcludedContent = allExcludeKeywords.some((keyword) =>
        allText.includes(keyword)
      );

      return !hasExcludedContent;
    });
  }

  private selectImageUrl(photo: any, size: string): string {
    const urls = photo.urls;

    switch (size) {
      case "large":
        return urls.full || urls.regular;
      case "medium":
        return urls.regular || urls.small;
      case "small":
        return urls.small || urls.thumb;
      default:
        return urls.regular;
    }
  }

  private async downloadImage(
    url: string
  ): Promise<{ data: string; contentType: string }> {
    const response = await fetch(url, {
      signal: AbortSignal.timeout(10000),
      headers: {
        "User-Agent": "github-card-app/1.0",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status}`);
    }

    const imageData = await response.arrayBuffer();
    const contentType = response.headers.get("content-type") || "image/jpeg";

    return {
      data: Buffer.from(imageData).toString("base64"),
      contentType,
    };
  }
}
