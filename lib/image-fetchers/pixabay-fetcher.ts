import { ImageSource, FetchedImage } from "@/types/image-source";

export class PixabayFetcher {
  private apiKey: string;
  private rateLimiter: PixabayRateLimiter;

  constructor() {
    this.apiKey = process.env.PIXABAY_API_KEY || "";
    if (!this.apiKey) {
      console.warn(
        "Pixabay API key not configured, Pixabay source will be disabled"
      );
    }

    // 初始化限流器：每60秒100个请求
    this.rateLimiter = new PixabayRateLimiter({
      maxRequests: 100,
      windowMs: 60000, // 60秒
    });
  }

  async fetchImage(source: ImageSource): Promise<FetchedImage | null> {
    if (!this.apiKey) {
      console.warn("Pixabay API key not available");
      return null;
    }

    try {
      console.log(
        `[Pixabay] Rate limiter status:`,
        this.rateLimiter.getStatus()
      );

      const photos = await this.fetchPhotos(source);
      if (!photos || photos.length === 0) {
        throw new Error("No photos returned from Pixabay");
      }

      // 过滤自然主题内容
      const naturalPhotos = this.filterNaturalContent(photos);

      if (naturalPhotos.length === 0) {
        console.warn(
          "No natural content photos found after filtering, using original photos"
        );
        // 如果过滤后没有图片，使用原始结果但记录警告
      }

      const photosToUse = naturalPhotos.length > 0 ? naturalPhotos : photos;
      const randomPhoto =
        photosToUse[Math.floor(Math.random() * photosToUse.length)];

      // 选择合适的图片尺寸
      const imageUrl = this.selectImageUrl(
        randomPhoto,
        source.config?.size || "large"
      );

      console.log(`[Pixabay] Downloading image: ${imageUrl}`);
      const imageData = await this.downloadImage(imageUrl);

      return {
        imageUrl,
        imageData: Buffer.from(imageData).toString("base64"),
        contentType: "image/jpeg",
        sourceId: source.id,
        metadata: {
          photographer: randomPhoto.user,
          photographerUrl: `https://pixabay.com/users/${randomPhoto.user}-${randomPhoto.user_id}/`,
          sourceType: "pixabay",
        },
      };
    } catch (error) {
      console.error("[Pixabay] Error fetching image:", error);
      console.log(
        `[Pixabay] Final rate limiter status:`,
        this.rateLimiter.getStatus()
      );
      return null;
    }
  }

  private async fetchPhotos(source: ImageSource): Promise<any[]> {
    const config = source.config || {};

    // 简化搜索关键词构建
    const searchQuery = this.buildSearchQuery(config.keywords || []);

    const params = new URLSearchParams({
      key: this.apiKey,
      q: searchQuery,
      image_type: config.image_type || "photo",
      orientation: this.mapOrientation(config.orientation || "portrait"),
      category: this.selectNaturalCategory(config.category),
      min_width: String(config.min_width || 1080),
      min_height: String(config.min_height || 1920),
      safesearch: String(config.safesearch !== false), // 默认开启安全搜索
      order: config.order || "popular",
      per_page: String(Math.min(config.per_page || 20, 200)),
      page: "1",
    });

    console.log(`Fetching from Pixabay with query: ${searchQuery}`);

    const response = await fetch(`${source.url}?${params.toString()}`, {
      headers: {
        "User-Agent": "Mozilla/5.0 (compatible; GitHub-Card-Bot/1.0)",
      },
      signal: AbortSignal.timeout(8000), // 添加超时控制
    });

    if (!response.ok) {
      throw new Error(
        `Pixabay API error: ${response.status} ${response.statusText}`
      );
    }

    const data = (await response.json()) as { hits: any[] };

    // 改进错误处理
    if (!data.hits) {
      console.warn("Pixabay API returned no hits field");
      return [];
    }

    if (data.hits.length === 0) {
      console.warn(`Pixabay API returned 0 hits for query: ${searchQuery}`);
      return [];
    }

    console.log(
      `Pixabay API returned ${data.hits.length} hits for query: ${searchQuery}`
    );
    return data.hits;
  }

  // 简化搜索查询构建
  private buildSearchQuery(baseKeywords: string[]): string {
    // 如果有配置的关键词，优先使用
    if (baseKeywords.length > 0) {
      // 随机选择1-2个关键词，避免过于复杂
      const selectedKeywords = this.selectRandomKeywords(baseKeywords, 1, 2);
      return selectedKeywords.join(" ");
    }

    // 默认的简单自然主题关键词
    const defaultKeywords = [
      "nature",
      "landscape",
      "forest",
      "mountain",
      "ocean",
      "flowers",
      "trees",
      "sunset",
      "galaxy",
      "stars",
    ];

    // 随机选择一个简单关键词
    return defaultKeywords[Math.floor(Math.random() * defaultKeywords.length)];
  }

  private selectRandomKeywords(
    keywords: string[],
    min: number,
    max: number
  ): string[] {
    const count = Math.floor(Math.random() * (max - min + 1)) + min;
    const shuffled = [...keywords].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  private selectNaturalCategory(category?: string): string {
    // 自然主题分类
    const naturalCategories = ["nature", "animals", "backgrounds", "places"];

    if (category && naturalCategories.includes(category)) {
      return category;
    }

    // 随机选择一个自然分类
    return naturalCategories[
      Math.floor(Math.random() * naturalCategories.length)
    ];
  }

  private filterNaturalContent(hits: any[]): any[] {
    // 优化过滤逻辑，更宽松的排除条件
    const excludeKeywords = [
      "people",
      "person",
      "human",
      "face",
      "portrait",
      "business",
      "fashion",
    ];

    const filtered = hits.filter((hit) => {
      const tags = (hit.tags || "").toLowerCase();

      // 只有当标签明确包含排除词时才过滤
      const hasExcludedContent = excludeKeywords.some((keyword) =>
        tags.split(",").some((tag: string) => tag.trim() === keyword)
      );

      return !hasExcludedContent;
    });

    console.log(
      `Filtered ${hits.length} hits down to ${filtered.length} natural content photos`
    );
    return filtered;
  }

  private mapOrientation(orientation: string): string {
    switch (orientation) {
      case "landscape":
        return "horizontal";
      case "portrait":
        return "vertical";
      case "square":
        return "all";
      default:
        return "vertical"; // 默认纵向，移动优先
    }
  }

  private selectImageUrl(photo: any, size: string): string {
    // 根据尺寸选择合适的图片URL
    switch (size) {
      case "large":
        return photo.largeImageURL || photo.webformatURL;
      case "medium":
        return photo.webformatURL;
      case "small":
        return photo.previewURL;
      default:
        return photo.largeImageURL || photo.webformatURL;
    }
  }

  private async downloadImage(imageUrl: string): Promise<ArrayBuffer> {
    return this.rateLimiter.executeWithRetry(async () => {
      const response = await fetch(imageUrl, {
        signal: AbortSignal.timeout(10000), // 10秒超时
        headers: {
          "User-Agent": "Mozilla/5.0 (compatible; GitHub-Card-Bot/1.0)",
        },
      });

      if (!response.ok) {
        // 对于 429 错误，创建一个包含响应信息的错误
        if (response.status === 429) {
          const retryAfter = response.headers.get("retry-after");
          const error = new Error(
            `Failed to download image: ${response.status}`
          ) as any;
          error.status = response.status;
          error.retryAfter = retryAfter;
          throw error;
        }
        throw new Error(`Failed to download image: ${response.status}`);
      }

      return await response.arrayBuffer();
    });
  }
}

/**
 * Pixabay API 专用限流器
 * 实现令牌桶算法 + 智能重试机制
 */
class PixabayRateLimiter {
  private tokens: number;
  private lastRefill: number;
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(config: { maxRequests: number; windowMs: number }) {
    this.maxRequests = config.maxRequests;
    this.windowMs = config.windowMs;
    this.tokens = this.maxRequests;
    this.lastRefill = Date.now();
  }

  /**
   * 带重试的执行函数
   */
  async executeWithRetry<T>(fn: () => Promise<T>, maxRetries = 3): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // 等待令牌可用
        await this.acquireToken();

        // 执行请求
        const result = await fn();
        return result;
      } catch (error) {
        lastError = error as Error;

        // 检查是否是 429 错误
        if (this.is429Error(error)) {
          const waitTime =
            this.parseRetryAfter(error) || this.calculateBackoffDelay(attempt);
          console.warn(
            `Rate limit hit, waiting ${waitTime}ms before retry ${
              attempt + 1
            }/${maxRetries}`
          );

          if (attempt < maxRetries) {
            await this.sleep(waitTime);
            continue;
          }
        }

        // 非 429 错误或达到最大重试次数，直接抛出
        throw error;
      }
    }

    throw lastError || new Error("Max retries exceeded");
  }

  /**
   * 获取令牌（非阻塞）
   */
  private async acquireToken(): Promise<void> {
    this.refillTokens();

    if (this.tokens <= 0) {
      // 计算需要等待的时间
      const waitTime = this.windowMs - (Date.now() - this.lastRefill);
      if (waitTime > 0) {
        console.log(`Rate limit preventive wait: ${waitTime}ms`);
        await this.sleep(waitTime);
        this.refillTokens();
      }
    }

    this.tokens = Math.max(0, this.tokens - 1);
  }

  /**
   * 补充令牌
   */
  private refillTokens(): void {
    const now = Date.now();
    const timePassed = now - this.lastRefill;

    if (timePassed >= this.windowMs) {
      this.tokens = this.maxRequests;
      this.lastRefill = now;
    }
  }

  /**
   * 检查是否是 429 错误
   */
  private is429Error(error: unknown): boolean {
    if (error instanceof Error) {
      return (
        error.message.includes("429") ||
        error.message.includes("Too Many Requests")
      );
    }
    return false;
  }

  /**
   * 解析 Retry-After 头
   */
  private parseRetryAfter(error: unknown): number | null {
    if (error && typeof error === "object" && "retryAfter" in error) {
      const retryAfter = (error as any).retryAfter;
      if (!retryAfter) return null;

      // 支持两种格式：秒数和 HTTP 日期
      let millisToSleep = Math.round(parseFloat(retryAfter) * 1000);
      if (isNaN(millisToSleep)) {
        // 尝试解析 HTTP 日期格式
        const resetTime = new Date(retryAfter).getTime();
        const now = Date.now();
        millisToSleep = Math.max(0, resetTime - now);
      }

      return millisToSleep > 0 ? millisToSleep : null;
    }
    return null;
  }

  /**
   * 计算指数退避延迟
   */
  private calculateBackoffDelay(attempt: number): number {
    // 指数退避：1秒、2秒、4秒...
    const baseDelay = 1000;
    const exponentialDelay = baseDelay * Math.pow(2, attempt);

    // 添加随机抖动，避免重试风暴
    const jitter = Math.random() * 1000;

    return Math.min(exponentialDelay + jitter, 30000); // 最大30秒
  }

  /**
   * 休眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      tokens: this.tokens,
      maxRequests: this.maxRequests,
      windowMs: this.windowMs,
      lastRefill: this.lastRefill,
    };
  }
}
