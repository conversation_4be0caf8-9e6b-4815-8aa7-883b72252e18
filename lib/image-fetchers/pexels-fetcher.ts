import { ImageSource, FetchedImage } from "@/types/image-source";

export class PexelsFetcher {
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.PEXELS_API_KEY || "";
    if (!this.apiKey) {
      console.warn(
        "Pexels API key not configured, Pexels source will be disabled"
      );
    }
  }

  async fetchImage(source: ImageSource): Promise<FetchedImage | null> {
    if (!this.apiKey) {
      console.warn("Pexels API key not available");
      return null;
    }

    try {
      const photos = await this.fetchPhotos(source);
      if (!photos || photos.length === 0) {
        throw new Error("No photos returned from Pexels");
      }

      // 过滤自然主题内容
      const naturalPhotos = this.filterNaturalContent(
        photos,
        source.config?.exclude_keywords || []
      );

      if (naturalPhotos.length === 0) {
        console.warn(
          "No natural content photos found after filtering, using original photos"
        );
        // 如果过滤后没有图片，使用原始结果但记录警告
      }

      const photosToUse = naturalPhotos.length > 0 ? naturalPhotos : photos;
      const randomPhoto =
        photosToUse[Math.floor(Math.random() * photosToUse.length)];
      const imageUrl = randomPhoto.src[source.config?.size || "large"];

      const imageData = await this.downloadImage(imageUrl);

      return {
        imageUrl,
        imageData: imageData.data,
        contentType: imageData.contentType,
        sourceId: source.id,
        metadata: {
          photographer: randomPhoto.photographer,
          photographerUrl: randomPhoto.photographer_url,
          pexelsId: randomPhoto.id,
          sourceType: "pexels",
        },
      };
    } catch (error) {
      console.error(`Pexels fetch error for source ${source.id}:`, error);
      return null;
    }
  }

  private async fetchPhotos(source: ImageSource) {
    const params = new URLSearchParams({
      per_page: String(source.config?.per_page || 25),
      orientation: source.config?.orientation || "landscape",
    });

    const response = await fetch(`${source.url}?${params}`, {
      headers: {
        Authorization: this.apiKey,
        "User-Agent": "github-card-app/1.0",
      },
      signal: AbortSignal.timeout(8000),
    });

    if (!response.ok) {
      throw new Error(
        `Pexels API error: ${response.status} ${response.statusText}`
      );
    }

    const data = (await response.json()) as { photos: any[] };
    return data.photos;
  }

  private filterNaturalContent(
    photos: any[],
    excludeKeywords: string[]
  ): any[] {
    // 默认排除关键词（人物相关）
    const defaultExcludeKeywords = [
      "person",
      "people",
      "human",
      "face",
      "portrait",
      "man",
      "woman",
      "child",
      "family",
      "crowd",
      "business",
      "fashion",
      "sport",
    ];

    const allExcludeKeywords = [...defaultExcludeKeywords, ...excludeKeywords];

    return photos.filter((photo) => {
      const alt = (photo.alt || "").toLowerCase();

      // 检查是否包含排除的关键词
      const hasExcludedContent = allExcludeKeywords.some((keyword) =>
        alt.includes(keyword)
      );

      return !hasExcludedContent;
    });
  }

  private async downloadImage(
    url: string
  ): Promise<{ data: string; contentType: string }> {
    const response = await fetch(url, {
      signal: AbortSignal.timeout(8000),
    });

    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status}`);
    }

    const imageData = await response.arrayBuffer();
    const contentType = response.headers.get("content-type") || "image/jpeg";

    return {
      data: Buffer.from(imageData).toString("base64"),
      contentType,
    };
  }
}
