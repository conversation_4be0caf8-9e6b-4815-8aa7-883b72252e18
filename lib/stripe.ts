import Stripe from "stripe";

// 验证环境变量
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY 未设置");
}

// 初始化 Stripe 客户端
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-05-28.basil", // Latest API version compatible with Stripe v18.1.1
  typescript: true,
  // 添加应用信息，有助于 Stripe 支持团队识别请求来源
  appInfo: {
    name: "GitHub Card",
    version: "1.0.0",
    url: "https://github-card.refined-x.workers.dev",
  },
  // 配置 HTTP 客户端
  httpClient: Stripe.createFetchHttpClient(),
  // 设置超时
  timeout: 30000, // 30秒超时
  // 启用自动重试
  maxNetworkRetries: 2,
});

// 获取 Stripe 价格 ID 的辅助函数
export const getStripePriceId = (
  planId: string,
  isYearly: boolean = false
): string => {
  const priceIds: Record<string, string> = {
    pro: isYearly ? "price_yearly_pro" : "price_monthly_pro",
    premium: isYearly ? "price_yearly_premium" : "price_monthly_premium",
  };

  const priceId = priceIds[planId];
  if (!priceId) {
    console.warn(`未找到对应 ${planId} 的价格 ID`);
  }
  return priceId || "";
};
