"use server";

import { cache } from "react";
import { userDataService } from "@/lib/services/user-data-service";

/**
 * 简化的GitHub数据获取函数
 * 现在直接使用UserDataService，移除所有重复逻辑
 */
export const getUserGitHubData = cache(async (userId: string) => {
  if (!userId) {
    console.error("getUserGitHubData called with empty userId");
    return {
      success: false,
      error: "UserId is required",
    };
  }

  console.log(
    `Server action: getUserGitHubData for userId ${userId} (via UserDataService)`
  );

  try {
    const response = await userDataService.getUserCompleteData(userId);

    if (!response.success || !response.data) {
      throw new Error(response.error || "Failed to fetch user data");
    }

    return {
      success: true,
      data: response.data.userData,
    };
  } catch (error: any) {
    console.error(`Error in getUserGitHubData for ${userId}:`, error);
    return {
      success: false,
      error: error.message || "Failed to fetch GitHub data",
    };
  }
});
