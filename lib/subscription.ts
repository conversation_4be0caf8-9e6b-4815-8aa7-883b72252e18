import { getDb } from "./db";
import { userSubscriptions, subscriptionPlans } from "./db/schema";
import { eq, and, gte } from "drizzle-orm";

export async function getUserSubscription(userId: string) {
  const db = await getDb();
  
  const now = Date.now();
  
  const subscription = await db
    .select()
    .from(userSubscriptions)
    .where(
      and(
        eq(userSubscriptions.userId, userId),
        eq(userSubscriptions.status, "active"),
        gte(userSubscriptions.currentPeriodEnd, now)
      )
    )
    .get();

  return subscription || null;
}

export async function getSubscriptionPlan(planId: string) {
  const db = await getDb();
  
  const plan = await db
    .select()
    .from(subscriptionPlans)
    .where(eq(subscriptionPlans.id, planId))
    .get();
    
  return plan;
}

export async function getActiveSubscriptionPlans() {
  const db = await getDb();
  
  const plans = await db
    .select()
    .from(subscriptionPlans)
    .where(eq(subscriptionPlans.isActive, true));
    
  return plans;
}

export async function isUserSubscribed(userId: string): Promise<boolean> {
  const subscription = await getUserSubscription(userId);
  return !!subscription;
}

export async function getUserSubscriptionWithPlan(userId: string) {
  const subscription = await getUserSubscription(userId);
  if (!subscription) return null;
  
  const plan = await getSubscriptionPlan(subscription.planId);
  
  return {
    ...subscription,
    plan,
  };
}
