import { loadStripe } from "@stripe/stripe-js";

// 确保你有设置 NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY 环境变量
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || ""
);

export async function createCheckoutSession({
  priceId,
  successUrl,
  cancelUrl,
}: {
  priceId: string;
  successUrl: string;
  cancelUrl: string;
}) {
  try {
    const response = await fetch("/api/stripe/create-checkout", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        priceId,
        successUrl: window.location.origin + successUrl,
        cancelUrl: window.location.origin + cancelUrl,
      }),
    });

    const session = (await response.json()) as any;

    if (session.error) {
      throw new Error(session.error);
    }

    const stripe = await stripePromise;
    if (!stripe) {
      throw new Error("<PERSON>e failed to initialize");
    }

    const { error } = await stripe.redirectToCheckout({
      sessionId: session.id,
    });

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error("Checkout error:", error);
    throw error;
  }
}

export async function redirectToBillingPortal(
  returnUrl: string = "/dashboard"
) {
  try {
    const response = await fetch("/api/stripe/create-portal-session", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        returnUrl: window.location.origin + returnUrl,
      }),
    });

    const { url } = (await response.json()) as any;

    if (!url) {
      throw new Error("Failed to create billing portal session");
    }

    window.location.href = url;
  } catch (error) {
    console.error("Billing portal error:", error);
    throw error;
  }
}
