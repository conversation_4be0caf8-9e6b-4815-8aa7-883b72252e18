// R2图片队列管理器 - Phase 4.1
// 简化架构：零CPU消耗的图片服务

import { cacheManager } from "@/lib/cloudflare/kv-cache-manager";
import { r2StorageManager } from "@/lib/r2-storage-manager";
import { generateImageId } from "@/utils/img-util";
import {
  QueuedImage,
  ImageCacheQueue,
  QueueStats,
  CleanupResult,
  QueueOperationResult,
  ImageSource,
} from "@/types/image-queue";
import { QUEUE_CONSTANTS } from "@/constants";

export class ImageQueueManager {
  private stats: QueueStats = {
    totalImages: 0,
    activeImages: 0,
    expiredImages: 0,
    avgAccessCount: 0,
    lastCleanup: 0,
    r2StorageUsed: 0,
  };

  /**
   * 获取当前队列状态
   */
  async getQueue(): Promise<ImageCacheQueue> {
    try {
      const queue = await cacheManager.get<ImageCacheQueue>(
        QUEUE_CONSTANTS.QUEUE_KEY
      );

      if (!queue) {
        return this.createEmptyQueue();
      }

      // 自动清理过期图片
      const now = Date.now();
      const validImages = queue.images.filter((img) => img.expiry > now);

      if (validImages.length !== queue.images.length) {
        console.log(
          `Cleaned ${
            queue.images.length - validImages.length
          } expired images from queue`
        );
        queue.images = validImages;
        queue.lastUpdated = now;
        await this.saveQueue(queue);
      }

      return queue;
    } catch (error) {
      console.error("Error getting queue:", error);
      return this.createEmptyQueue();
    }
  }

  /**
   * 检查队列是否已满
   */
  async isQueueFull(): Promise<boolean> {
    const queue = await this.getQueue();
    return queue.images.length >= QUEUE_CONSTANTS.MAX_QUEUE_SIZE;
  }

  /**
   * 添加新图片到队列
   */
  async addToQueue(
    imageUrl: string,
    contentType: string,
    source: ImageSource = "direct"
  ): Promise<QueueOperationResult> {
    try {
      const imageId = generateImageId(imageUrl);
      const queue = await this.getQueue();

      // 检查是否已存在
      if (queue.images.some((img) => img.id === imageId)) {
        console.log(`Image already in queue: ${imageId}`);
        const existingImage = queue.images.find((img) => img.id === imageId)!;
        return {
          success: true,
          imageId,
          r2Url: existingImage.r2Url,
          queueSize: queue.images.length,
        };
      }

      // 上传到R2
      const r2Result = await r2StorageManager.storeImage(
        imageUrl,
        contentType,
        source
      );

      if (!r2Result.success) {
        return {
          success: false,
          error: `R2 upload failed: ${r2Result.error}`,
        };
      }

      // 创建队列项
      const now = Date.now();
      const queuedImage: QueuedImage = {
        id: imageId,
        r2Url: r2Result.r2Url!,
        contentType,
        addedAt: now,
        expiry: now + QUEUE_CONSTANTS.QUEUE_TTL,
        sourceUrl: imageUrl,
        accessCount: 0,
      };

      // 添加到队列
      queue.images.push(queuedImage);
      queue.lastUpdated = now;
      queue.totalServed++;

      // 如果超过容量，移除最旧的图片
      if (queue.images.length > QUEUE_CONSTANTS.MAX_QUEUE_SIZE) {
        await this.removeOldestImages(queue, 1);
      }

      await this.saveQueue(queue);

      console.log(
        `Added image to queue: ${imageId} (${queue.images.length}/${QUEUE_CONSTANTS.MAX_QUEUE_SIZE})`
      );

      return {
        success: true,
        imageId,
        r2Url: r2Result.r2Url!,
        queueSize: queue.images.length,
      };
    } catch (error) {
      console.error("Error adding to queue:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 从队列中随机选择图片
   */
  async selectRandomImage(): Promise<QueuedImage | null> {
    const queue = await this.getQueue();

    if (queue.images.length === 0) {
      return null;
    }

    // 随机选择
    const randomIndex = Math.floor(Math.random() * queue.images.length);
    const selectedImage = queue.images[randomIndex];

    // 更新访问计数
    selectedImage.accessCount++;
    queue.lastUpdated = Date.now();
    await this.saveQueue(queue);

    return selectedImage;
  }

  /**
   * 根据ID查询图片
   */
  async queryImage(imageId: string): Promise<QueuedImage | null> {
    const queue = await this.getQueue();
    const image = queue.images.find((img) => img.id === imageId);

    if (image && image.expiry > Date.now()) {
      // 更新访问计数
      image.accessCount++;
      queue.lastUpdated = Date.now();
      await this.saveQueue(queue);
      return image;
    }

    return null;
  }

  /**
   * 获取队列统计信息
   */
  async getStats(): Promise<QueueStats> {
    const queue = await this.getQueue();
    const now = Date.now();

    const activeImages = queue.images.filter((img) => img.expiry > now);
    const expiredImages = queue.images.filter((img) => img.expiry <= now);

    const totalAccessCount = activeImages.reduce(
      (sum, img) => sum + img.accessCount,
      0
    );

    return {
      totalImages: queue.images.length,
      activeImages: activeImages.length,
      expiredImages: expiredImages.length,
      avgAccessCount:
        activeImages.length > 0 ? totalAccessCount / activeImages.length : 0,
      lastCleanup: this.stats.lastCleanup,
      r2StorageUsed: this.stats.r2StorageUsed,
    };
  }

  /**
   * 清理过期和未使用的图片 - 优化版本
   * Phase 4.3: 集成并行清理，实现6倍性能提升
   */
  async cleanup(): Promise<CleanupResult> {
    const result: CleanupResult = {
      removedFromQueue: 0,
      removedFromR2: 0,
      freedSpace: 0,
      errors: [],
    };

    try {
      const queue = await this.getQueue();
      const now = Date.now();

      // 分离有效和过期图片
      const validImages = queue.images.filter((img) => img.expiry > now);
      const expiredImages = queue.images.filter((img) => img.expiry <= now);

      // 更新队列
      if (expiredImages.length > 0) {
        queue.images = validImages;
        queue.lastUpdated = now;
        await this.saveQueue(queue);
        result.removedFromQueue = expiredImages.length;
      }

      // 🚀 优化: 使用并行清理替换顺序删除
      if (expiredImages.length > 0) {
        // 动态导入并行清理管理器 (避免循环依赖)
        const { parallelCleanupManager } = await import(
          "@/lib/cleanup-optimization/parallel-cleanup"
        );

        console.log(
          `🔄 Switching to parallel cleanup for ${expiredImages.length} images`
        );
        const enhancedResult =
          await parallelCleanupManager.deleteImagesInParallel(expiredImages);

        // 将增强结果映射到标准结果格式
        result.removedFromR2 = enhancedResult.removedFromR2;
        result.freedSpace = enhancedResult.freedSpace;
        result.errors = enhancedResult.errors;

        // 记录性能提升信息
        if (enhancedResult.performanceGain > 1) {
          console.log(
            `⚡ Performance improvement: ${enhancedResult.performanceGain.toFixed(
              2
            )}x faster`
          );
        }
      }

      this.stats.lastCleanup = now;
      console.log(
        `Cleanup completed: ${result.removedFromQueue} from queue, ${result.removedFromR2} from R2`
      );

      return result;
    } catch (error) {
      result.errors.push(`Cleanup failed: ${error}`);
      return result;
    }
  }

  // 私有辅助方法
  private createEmptyQueue(): ImageCacheQueue {
    return {
      images: [],
      lastUpdated: Date.now(),
      version: QUEUE_CONSTANTS.CURRENT_VERSION,
      totalServed: 0,
    };
  }

  private async saveQueue(queue: ImageCacheQueue): Promise<void> {
    await cacheManager.set(QUEUE_CONSTANTS.QUEUE_KEY, queue, {
      expirationTtl: Math.ceil(QUEUE_CONSTANTS.QUEUE_TTL / 1000),
    });
  }

  private async removeOldestImages(
    queue: ImageCacheQueue,
    count: number
  ): Promise<void> {
    // 按添加时间排序，移除最旧的
    queue.images.sort((a, b) => a.addedAt - b.addedAt);
    const removed = queue.images.splice(0, count);

    // 异步清理R2中的旧图片
    for (const removedImage of removed) {
      r2StorageManager.deleteImage(removedImage.id).catch((error) => {
        console.error(`Failed to delete old image ${removedImage.id}:`, error);
      });
    }

    console.log(`Removed ${count} oldest images from queue`);
  }
}

// 全局实例
export const imageQueueManager = new ImageQueueManager();
