"use server";

import { getDb, schema } from "./db";
import { eq } from "drizzle-orm";

/**
 * 用户类型定义
 */
export type User = {
  id: string;
  name?: string | null;
  email?: string | null;
  username: string;
  githubId: string;
  avatarUrl?: string;
  image?: string;
};

/**
 * 同步用户数据到数据库
 * 这个函数应该在用户成功登录后调用
 */
export async function syncUserToDB(user: User) {
  try {
    const db = await getDb();
    const now = Date.now();
    const SYNC_INTERVAL = 24 * 60 * 60 * 1000; // 24小时同步一次

    // 检查用户是否已存在
    const existingUser = await db.query.users.findFirst({
      where: eq(schema.users.githubId, user.githubId),
    });

    if (!existingUser) {
      // 创建新用户
      console.log("[user-management] 创建新用户", user);
      await db
        .insert(schema.users)
        .values({
          id: user.id,
          name: user.name || null,
          email: user.email || null,
          githubId: user.githubId,
          username: user.username,
          avatarUrl: user.avatarUrl || user.image || "",
          displayName: user.name || user.username,
          updatedAt: now,
          createdAt: now,
        })
        .catch((err) => {
          console.error("[user-management] 创建用户失败", err);
          // 不抛出错误，允许认证流程继续
        });

      // 记录用户行为
      await db
        .insert(schema.userBehaviors)
        .values({
          userId: user.id,
          actionType: "signup",
          performedAt: now,
        })
        .catch((err) => {
          console.error("[user-management] 记录用户行为失败", err);
        });
    } else {
      // 检查是否需要更新（距离上次更新超过24小时）
      const lastUpdated = existingUser.updatedAt
        ? new Date(existingUser.updatedAt * 1000).getTime()
        : 0;

      if (now - lastUpdated > SYNC_INTERVAL) {
        // 准备更新字段
        const updateFields: Record<string, any> = {
          updatedAt: Math.floor(now / 1000), // 转换为秒
        };

        // 只更新有变化的字段
        if (user.name !== undefined && user.name !== existingUser.name) {
          updateFields.name = user.name;
          updateFields.displayName =
            user.name || existingUser.displayName || user.username;
        }

        if (user.email !== undefined && user.email !== existingUser.email) {
          updateFields.email = user.email;
        }

        if (
          user.username !== undefined &&
          user.username !== existingUser.username
        ) {
          updateFields.username = user.username;
        }

        const newAvatarUrl = user.avatarUrl || user.image;
        if (newAvatarUrl && newAvatarUrl !== existingUser.avatarUrl) {
          updateFields.avatarUrl = newAvatarUrl;
        }

        // 如果有字段需要更新
        if (Object.keys(updateFields).length > 1) {
          // 大于1因为有 updatedAt
          console.log("[user-management] 更新用户信息", {
            userId: existingUser.id,
            updatedFields: Object.keys(updateFields),
          });

          await db
            .update(schema.users)
            .set(updateFields)
            .where(eq(schema.users.id, existingUser.id))
            .catch((err) => {
              console.error("[user-management] 更新用户失败", err);
              // 不抛出错误，允许认证流程继续
            });
        } else {
          // 只更新同步时间
          await db
            .update(schema.users)
            .set({ updatedAt: now })
            .where(eq(schema.users.id, existingUser.id));
        }
      }

      // 记录登录行为（如果距离上次登录超过1小时）
      const lastLogin = await db.query.userBehaviors.findFirst({
        where: (behaviors, { and, eq, gt }) =>
          and(
            eq(behaviors.userId, existingUser.id),
            eq(behaviors.actionType, "login"),
            gt(behaviors.performedAt, now - 60 * 60 * 1000) // 1小时内
          ),
        orderBy: (behaviors, { desc }) => [desc(behaviors.performedAt)],
      });

      if (!lastLogin) {
        await db
          .insert(schema.userBehaviors)
          .values({
            userId: existingUser.id,
            actionType: "login",
            performedAt: now,
          })
          .catch((err) => {
            console.error("[user-management] 记录用户行为失败", err);
          });
      }
    }

    return user;
  } catch (error) {
    console.error("[user-management] 同步用户数据失败", error);
    // 不抛出错误，返回原始用户对象
    return user;
  }
}
