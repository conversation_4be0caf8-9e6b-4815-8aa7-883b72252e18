import { UserData } from "@/types/user-data";
import { ActivityScores } from "@/components/charts/ActivityRadarChart";

/**
 * 将UserData转换为开发活跃度评分数据
 * @param data 统一用户数据
 * @returns 活跃度评分数据
 */
export function adaptUserDataToActivity(data: UserData): ActivityScores {
  return {
    stars: data.totalStars || 0,
    forks: data.totalForks || 0,
    commits: data.commits || 0,
    pullRequests: data.pullRequests || 0,
    issues: data.issues || 0,
    reviews: data.reviews || 0,
  };
}

/**
 * 计算活跃度综合评分
 * @param scores 活跃度评分数据
 * @returns 0-100的综合评分
 */
export function calculateActivityScore(scores: ActivityScores): number {
  // 对各维度进行权重加权计算
  const weights = {
    stars: 0.2, // 项目影响力 20%
    forks: 0.15, // 项目被复用程度 15%
    commits: 0.25, // 代码提交活跃度 25%
    pullRequests: 0.2, // PR贡献 20%
    issues: 0.1, // 问题发现参与度 10%
    reviews: 0.1, // 代码审查活跃度 10%
  };

  // 使用对数标准化，避免极值影响
  const normalizedScores = {
    stars: Math.min(100, Math.log10(scores.stars + 1) * 20),
    forks: Math.min(100, Math.log10(scores.forks + 1) * 25),
    commits: Math.min(100, Math.log10(scores.commits + 1) * 15),
    pullRequests: Math.min(100, Math.log10(scores.pullRequests + 1) * 22),
    issues: Math.min(100, Math.log10(scores.issues + 1) * 30),
    reviews: Math.min(100, Math.log10(scores.reviews + 1) * 28),
  };

  // 加权平均计算综合评分
  const weightedScore = Object.entries(weights).reduce(
    (total, [key, weight]) => {
      return (
        total + normalizedScores[key as keyof typeof normalizedScores] * weight
      );
    },
    0
  );

  return Math.round(weightedScore);
}

/**
 * 获取活跃度等级描述
 * @param score 综合评分
 * @returns 等级信息
 */
export function getActivityGrade(score: number): {
  grade: string;
  title: string;
  description: string;
  color: string;
} {
  if (score >= 85) {
    return {
      grade: "S",
      title: "Extremely Active",
      description:
        "Exceptional developer with outstanding performance across multiple dimensions",
      color: "#10B981",
    };
  }
  if (score >= 70) {
    return {
      grade: "A",
      title: "Highly Active",
      description:
        "Active developer with excellent performance in most dimensions",
      color: "#3B82F6",
    };
  }
  if (score >= 55) {
    return {
      grade: "B",
      title: "Moderately Active",
      description: "Stable developer with strong performance in specific areas",
      color: "#F59E0B",
    };
  }
  if (score >= 40) {
    return {
      grade: "C",
      title: "Generally Active",
      description: "Growing developer with solid potential",
      color: "#EF4444",
    };
  }
  return {
    grade: "D",
    title: "Emerging Developer",
    description: "Early-stage developer with untapped potential",
    color: "#6B7280",
  };
}
