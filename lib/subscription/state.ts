// 定义所有可能的状态
export const SUBSCRIPTION_STATUS = {
  INCOMPLETE: 'incomplete',
  INCOMPLETE_EXPIRED: 'incomplete_expired',
  TRIALING: 'trialing',
  ACTIVE: 'active',
  PAST_DUE: 'past_due',
  CANCELED: 'canceled',
  UNPAID: 'unpaid',
} as const;

export type SubscriptionStatus = typeof SUBSCRIPTION_STATUS[keyof typeof SUBSCRIPTION_STATUS];

// 状态转换规则
const STATUS_TRANSITIONS: Record<SubscriptionStatus, SubscriptionStatus[]> = {
  [SUBSCRIPTION_STATUS.INCOMPLETE]: [
    SUBSCRIPTION_STATUS.INCOMPLETE_EXPIRED,
    SUBSCRIPTION_STATUS.TRIALING,
    SUBSCRIPTION_STATUS.ACTIVE,
  ],
  [SUBSCRIPTION_STATUS.INCOMPLETE_EXPIRED]: [],
  [SUBSCRIPTION_STATUS.TRIALING]: [
    SUBSCRIPTION_STATUS.ACTIVE,
    SUBSCRIPTION_STATUS.CANCELED,
  ],
  [SUBSCRIPTION_STATUS.ACTIVE]: [
    SUBSCRIPTION_STATUS.PAST_DUE,
    SUBSCRIPTION_STATUS.CANCELED,
    SUBSCRIPTION_STATUS.UNPAID,
  ],
  [SUBSCRIPTION_STATUS.PAST_DUE]: [
    SUBSCRIPTION_STATUS.ACTIVE,
    SUBSCRIPTION_STATUS.CANCELED,
    SUBSCRIPTION_STATUS.UNPAID,
  ],
  [SUBSCRIPTION_STATUS.CANCELED]: [],
  [SUBSCRIPTION_STATUS.UNPAID]: [
    SUBSCRIPTION_STATUS.ACTIVE,
    SUBSCRIPTION_STATUS.CANCELED,
  ],
};

// 状态转换验证
export function isValidTransition(
  currentStatus: SubscriptionStatus,
  newStatus: SubscriptionStatus
): boolean {
  return STATUS_TRANSITIONS[currentStatus].includes(newStatus);
}

// 状态变更日志记录
export interface StatusChangeLog {
  subscriptionId: string;
  userId: string;
  fromStatus: SubscriptionStatus;
  toStatus: SubscriptionStatus;
  reason?: string;
  metadata?: Record<string, unknown>;
  timestamp: number;
}

// 状态变更上下文
export interface StatusChangeContext {
  userId: string;
  subscriptionId: string;
  reason?: string;
  metadata?: Record<string, unknown>;
  timestamp?: number;
}
