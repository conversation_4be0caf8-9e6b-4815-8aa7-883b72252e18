import { getDb } from "../db";
import { subscriptionLogs } from "../db/schema";
import {
  StatusChangeContext,
  StatusChangeLog,
  SUBSCRIPTION_STATUS,
  SubscriptionStatus,
} from "./state";
import { eq, desc } from "drizzle-orm";
import { NewSubscriptionLog } from "../db/types";

// 类型守卫，验证状态是否有效
function isValidStatus(status: string): status is SubscriptionStatus {
  return Object.values(SUBSCRIPTION_STATUS).includes(
    status as SubscriptionStatus
  );
}

// 安全转换状态类型
function toSubscriptionStatus(status: string): SubscriptionStatus {
  return isValidStatus(status) ? status : SUBSCRIPTION_STATUS.INCOMPLETE;
}

class SubscriptionLogger {
  private static instance: SubscriptionLogger;

  private constructor() {}

  public static getInstance(): SubscriptionLogger {
    if (!SubscriptionLogger.instance) {
      SubscriptionLogger.instance = new SubscriptionLogger();
    }
    return SubscriptionLogger.instance;
  }

  public async logStatusChange(
    ctx: StatusChangeContext,
    fromStatus: string,
    toStatus: string
  ): Promise<void> {
    try {
      const db = await getDb();
      const now = Date.now();

      // 确保状态值有效
      const validFromStatus = toSubscriptionStatus(fromStatus);
      const validToStatus = toSubscriptionStatus(toStatus);

      const logEntry: NewSubscriptionLog = {
        id: `log_${now}_${Math.random().toString(36).substring(2, 9)}`,
        subscriptionId: ctx.subscriptionId,
        userId: ctx.userId,
        fromStatus: validFromStatus,
        toStatus: validToStatus,
        reason: ctx.reason || "status_changed",
        metadata: ctx.metadata ? { ...ctx.metadata } : null,
        timestamp: ctx.timestamp || now,
        createdAt: now,
      };

      await db.insert(subscriptionLogs).values(logEntry);

      // 同时输出到控制台，方便调试
      console.log(`[Subscription Status Change]`, {
        subscriptionId: ctx.subscriptionId,
        fromStatus: validFromStatus,
        toStatus: validToStatus,
        reason: ctx.reason,
        timestamp: new Date(now).toISOString(),
      });
    } catch (error) {
      console.error("Failed to log subscription status change:", error);
      // 即使日志记录失败，也不应该影响主流程
    }
  }

  public async getStatusHistory(
    subscriptionId: string,
    limit = 50
  ): Promise<StatusChangeLog[]> {
    try {
      const db = await getDb();
      const result = await db
        .select()
        .from(subscriptionLogs)
        .where(eq(subscriptionLogs.subscriptionId, subscriptionId))
        .orderBy(desc(subscriptionLogs.timestamp))
        .limit(limit);

      return result.map((log) => ({
        subscriptionId: log.subscriptionId,
        userId: log.userId,
        fromStatus: toSubscriptionStatus(log.fromStatus),
        toStatus: toSubscriptionStatus(log.toStatus),
        reason: log.reason || undefined,
        metadata: log.metadata ? { ...log.metadata } : undefined,
        timestamp: log.timestamp,
      }));
    } catch (error) {
      console.error("Failed to fetch subscription status history:", error);
      return [];
    }
  }
}

export const subscriptionLogger = SubscriptionLogger.getInstance();
