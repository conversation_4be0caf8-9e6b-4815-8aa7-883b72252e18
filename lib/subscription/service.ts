import { getDb } from "../db";
import { userSubscriptions } from "../db/schema";
import { eq } from "drizzle-orm";
import {
  SUBSCRIPTION_STATUS,
  SubscriptionStatus,
  isValidTransition,
  StatusChangeContext,
} from "./state";
import { subscriptionLogger } from "./logger";
import { payments } from "../db/schema";

export class SubscriptionService {
  /**
   * 更新订阅状态
   * @param subscriptionId 订阅ID
   * @param status 新状态
   * @param ctx 状态变更上下文
   */
  static async updateSubscriptionStatus(
    subscriptionId: string,
    status: SubscriptionStatus,
    ctx: StatusChangeContext
  ) {
    const db = await getDb();

    // 获取当前订阅状态
    const currentSubscription = await db
      .select()
      .from(userSubscriptions)
      .where(eq(userSubscriptions.id, subscriptionId))
      .get();

    if (!currentSubscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    const currentStatus = currentSubscription.status as SubscriptionStatus;

    // 验证状态转换是否有效
    if (currentStatus !== status && !isValidTransition(currentStatus, status)) {
      throw new Error(
        `Invalid status transition from ${currentStatus} to ${status}`
      );
    }

    // 记录状态变更日志
    await subscriptionLogger.logStatusChange(
      {
        ...ctx,
        subscriptionId,
        userId: currentSubscription.userId,
      },
      currentStatus,
      status
    );

    // 更新订阅状态
    const now = Date.now();
    const updateData = {
      status,
      updatedAt: now,
      // 如果状态变为已取消，设置取消时间
      ...(status === SUBSCRIPTION_STATUS.CANCELED && {
        canceledAt: now,
        cancelAtPeriodEnd: true,
      }),
      // 如果状态变为活跃，清除取消标记
      ...(status === SUBSCRIPTION_STATUS.ACTIVE && {
        canceledAt: null,
        cancelAtPeriodEnd: false,
      }),
    };

    await db
      .update(userSubscriptions)
      .set(updateData)
      .where(eq(userSubscriptions.id, subscriptionId));

    return {
      ...currentSubscription,
      ...updateData,
    };
  }

  /**
   * 处理支付成功事件
   */
  static async handlePaymentSucceeded(
    subscriptionId: string,
    paymentData: {
      amount: number;
      currency: string;
      paymentMethod: string;
      receiptUrl?: string;
    },
    ctx: Omit<StatusChangeContext, "subscriptionId" | "userId">
  ) {
    const db = await getDb();

    // 获取订阅信息
    const subscription = await db
      .select()
      .from(userSubscriptions)
      .where(eq(userSubscriptions.id, subscriptionId))
      .get();

    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    // 更新订阅状态为活跃
    await this.updateSubscriptionStatus(
      subscriptionId,
      SUBSCRIPTION_STATUS.ACTIVE,
      {
        ...ctx,
        subscriptionId,
        userId: subscription.userId,
        reason: "payment_succeeded",
        metadata: {
          paymentAmount: paymentData.amount,
          currency: paymentData.currency,
        },
      }
    );

    // 记录支付信息
    await db.insert(payments).values({
      id: `pay_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      userId: subscription.userId,
      subscriptionId,
      amount: paymentData.amount,
      currency: paymentData.currency,
      status: "succeeded",
      paymentMethod: paymentData.paymentMethod,
      receiptUrl: paymentData.receiptUrl,
      createdAt: Date.now(),
    });
  }

  /**
   * 处理支付失败事件
   */
  static async handlePaymentFailed(
    subscriptionId: string,
    failureData: {
      code?: string;
      message: string;
      paymentMethod?: string;
    },
    ctx: Omit<StatusChangeContext, "subscriptionId" | "userId">
  ) {
    const db = await getDb();

    // 获取订阅信息
    const subscription = await db
      .select()
      .from(userSubscriptions)
      .where(eq(userSubscriptions.id, subscriptionId))
      .get();

    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    // 更新订阅状态为逾期
    await this.updateSubscriptionStatus(
      subscriptionId,
      SUBSCRIPTION_STATUS.PAST_DUE,
      {
        ...ctx,
        subscriptionId,
        userId: subscription.userId,
        reason: "payment_failed",
        metadata: {
          failureCode: failureData.code,
          failureMessage: failureData.message,
        },
      }
    );

    // 记录失败的支付尝试
    if (failureData.paymentMethod) {
      await db.insert(payments).values({
        id: `pay_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        userId: subscription.userId,
        subscriptionId,
        amount: 0, // 未知金额
        currency: "USD", // 默认货币
        status: `failed: ${failureData.code || 'unknown'}`,
        paymentMethod: failureData.paymentMethod,
        receiptUrl: undefined,
        createdAt: Date.now(),
      });
    }
  }
}
