/**
 * 调试平台适配器类型定义
 *
 * 用于调试平台的模块输出类型适配
 * 提供调试界面所需的标准化输出格式
 */

// 基础模块输出接口
export interface ModuleOutput {
  status: "success" | "error";
  content: any;
  error_details?: string;
  processing_time?: number;
  confidence?: number;
  metadata?: Record<string, any>;
}

// 质量维度接口
export interface QualityDimensions {
  humor: number;
  compliance: number;
  originality: number;
  naturalness: number;
  relevance: number;
}

// Debug版本的模块输出接口
export interface DebugAnalyzerOutput extends ModuleOutput {
  content: {
    metrics: Array<{
      metric: string;
      value: number;
      valence:
        | "highly_positive"
        | "positive"
        | "neutral"
        | "negative"
        | "highly_negative";
      analysis: string;
      talkingPoint?: string;
      confidence?: number;
    }>;
    overallPattern: string;
    insights: string[];
    processingTime: number;
    dataPattern?: {
      type: string;
      confidence: number;
      keyIndicators: string[];
      strategy: string;
    };
  };
}

export interface DebugStrategistOutput extends ModuleOutput {
  content: {
    // 基于tags-to-strategist.md模板的创意简报结构
    // 与主类型定义保持一致，遵循"共享类型定义"原则
    narrative_scaffold: {
      A: string; // 概述型铺垫
      B: string; // 反差型过渡
      C_hint: string; // C段写作方向
    };
    insight_pool: {
      behavior_summary: string; // 行为特征总结
      persona: string; // 人物设定
      humor_angle: string; // 幽默角度
      analogy_pool: string[]; // 类比句池
    };
    punchline_focus: {
      punchline_candidates: string[]; // 收尾候选句
      tone: string; // 语言气质
      stylistic_suggestion: string; // 结构建议
    };
    writing_instruction: {
      focus_on: string; // 重点打磨指导
      overall_tone: string; // 整体语气要求
    };
  };
}

export interface DebugWriterOutput extends ModuleOutput {
  content: {
    generatedText: string;
    confidence: number;
    usedTemplate: string; // V6.0新增：使用的模板路径
    iterations?: number;
    qualityScore?: number;
    // V6.0移除的复杂字段（保持与主类型定义一致）：
    // - usedExamples (FewShot示例，已简化移除)
    // - reasoningEnhanced (推理增强模式，已简化移除)
    // - creativityMetrics (复杂的创意指标，已简化移除)
  };
}

export interface DebugCriticOutput extends ModuleOutput {
  content: {
    overallScore: number;
    dimensions: QualityDimensions;
    assessment: string;
    suggestions: string[];
    confidence: number;
    needsOptimization: boolean;
    standardizedGrade: "A" | "B" | "C" | "D" | "F";
    hybridDetails?: {
      algorithmicContribution: number;
      reasoningContribution: number;
      algorithmicScore: number;
      reasoningInsights: string[];
      detailedAssessment: string;
    };
  };
}

// 调试会话类型
export interface DebugModuleConfig {
  timeout: number;
  retries: number;
  enableLogging: boolean;
}

// 调试平台工具类型
export interface DebugValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 导出适配器函数类型
export type DebugAdapterFunction<T, R> = (
  input: T,
  config?: DebugModuleConfig
) => R;
