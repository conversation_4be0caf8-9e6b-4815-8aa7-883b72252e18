/**
 * V6 流式日志系统统一类型定义
 *
 * 特性：
 * - 事件驱动的状态管理
 * - Stage名称完全自由化（支持中文、emoji、特殊字符）
 * - 统一的四模块支持（analyzer/strategist/writer/critic）
 * - 单一真相源架构
 */

// =================================================================
// 模块类型定义
// =================================================================

export type SupportedModuleType =
  | "analyzer"
  | "strategist"
  | "writer"
  | "critic";

// =================================================================
// 统一事件类型系统
// =================================================================

/**
 * 核心事件类型 - 明确的状态表达，不依赖stage后缀
 */
export type StreamEventType =
  | "stage_start" // 阶段开始
  | "stage_progress" // 阶段进行中（带进度）
  | "stage_complete" // 阶段完成
  | "stage_error" // 阶段错误
  | "debug_start" // 调试开始
  | "debug_complete" // 调试完成
  | "debug_error" // 调试错误
  | "content_chunk"; // 实时内容块

// =================================================================
// V6.1 新增：非阻塞流式事件系统
// =================================================================

/**
 * 流式事件监听器类型
 */
export type StreamEventListener = (event: StreamEvent) => void;

/**
 * 流式错误处理器类型
 */
export type StreamErrorHandler = (error: Error, context?: any) => void;

/**
 * 流式完成处理器类型
 */
export type StreamCompleteHandler = (result: any, metadata?: any) => void;

/**
 * 非阻塞流式事件发射器接口
 *
 * 核心设计理念：
 * - 替代 await 阻塞的流式方法
 * - 基于事件驱动的非阻塞架构
 * - 保持向后兼容性
 * - 支持实时流式输出传播
 */
export interface StreamingEventEmitter {
  /**
   * 发射内容块事件 - 替代 await 阻塞
   * @param content 流式内容
   * @param stage 阶段名称
   * @param metadata 可选元数据
   */
  emitChunk(content: string, stage: string, metadata?: any): void;

  /**
   * 发射进度事件
   * @param progress 进度百分比 (0-100)
   * @param message 进度描述
   * @param stage 阶段名称
   */
  emitProgress(progress: number, message: string, stage: string): void;

  /**
   * 发射阶段开始事件
   * @param stage 阶段名称
   * @param context 阶段上下文
   */
  emitStageStart(stage: string, context?: any): void;

  /**
   * 发射阶段完成事件
   * @param stage 阶段名称
   * @param result 阶段结果
   * @param metadata 结果元数据
   */
  emitStageComplete(stage: string, result: any, metadata?: any): void;

  /**
   * 发射错误事件
   * @param error 错误对象
   * @param stage 发生错误的阶段
   * @param context 错误上下文
   */
  emitError(error: Error, stage: string, context?: any): void;

  /**
   * 添加事件监听器
   * @param listener 事件监听器
   */
  onEvent(listener: StreamEventListener): void;

  /**
   * 移除事件监听器
   * @param listener 要移除的监听器
   */
  offEvent(listener: StreamEventListener): void;

  /**
   * 添加错误监听器
   * @param handler 错误处理器
   */
  onError(handler: StreamErrorHandler): void;

  /**
   * 添加完成监听器
   * @param handler 完成处理器
   */
  onComplete(handler: StreamCompleteHandler): void;

  /**
   * 获取会话ID
   */
  getSessionId(): string;

  /**
   * 获取模块类型
   */
  getModuleType(): SupportedModuleType;

  /**
   * 清理资源
   */
  cleanup(): void;
}

/**
 * 流式方法执行器接口
 *
 * 用于包装原有的 await 阻塞方法，转换为事件驱动
 */
export interface StreamingExecutor<T> {
  /**
   * 执行流式方法 - 非阻塞版本
   * @param emitter 事件发射器
   * @param args 方法参数
   * @returns Promise<T> 仅用于兼容性，实际结果通过事件传递
   */
  executeNonBlocking(
    emitter: StreamingEventEmitter,
    ...args: any[]
  ): Promise<T>;
}

// =================================================================
// 统一流式事件接口
// =================================================================

/**
 * 流式事件元数据
 */
export interface StreamEventMetadata {
  // 阶段输出数据
  stageOutput?: any;

  // 性能指标
  processingMetrics?: {
    duration: number;
    itemsProcessed: number;
    confidence: number;
    algorithmSteps?: number;
  };

  // 上下文数据
  contextData?: Record<string, any>;

  // 流式内容相关
  streamingContent?: {
    stage: string;
    isStreaming: boolean;
    accumulatedContent: string;
    chunkCount: number;
    startTime: number;
    lastUpdateTime?: number;
    completedAt?: number;
  };

  // 向后兼容字段
  hasStructuredData?: boolean;
  stageType?: "completed" | "in_progress";
  streamingMode?: boolean;
}

/**
 * 统一流式事件接口
 *
 * 支持所有AI模块，stage名称完全自由
 */
export interface StreamEvent {
  // 核心标识
  sessionId: string;
  moduleType: SupportedModuleType;

  // 事件信息
  eventType: StreamEventType;
  stage: string; // 完全自由命名：可以是中文、emoji、特殊字符等

  // 内容
  content: string;

  // 状态
  isComplete?: boolean;
  progress?: number; // 0-100

  // 元数据
  metadata?: StreamEventMetadata;

  // 时间戳
  timestamp: number;
}

// =================================================================
// 模块特定事件类型
// =================================================================

/**
 * Analyzer模块事件
 */
export interface AnalyzerStreamEvent extends StreamEvent {
  moduleType: "analyzer";
}

/**
 * Strategist模块事件
 */
export interface StrategistStreamEvent extends StreamEvent {
  moduleType: "strategist";
}

/**
 * Writer模块事件
 */
export interface WriterStreamEvent extends StreamEvent {
  moduleType: "writer";
}

/**
 * Critic模块事件
 */
export interface CriticStreamEvent extends StreamEvent {
  moduleType: "critic";
}

// =================================================================
// 阶段生命周期管理
// =================================================================

/**
 * 阶段上下文
 */
export interface StageContext {
  stage: string;
  startTime: number;
  endTime?: number;
  status: "pending" | "running" | "completed" | "error";
  progress?: number;
  result?: any;
  error?: Error;
  context?: Record<string, any>;
}

/**
 * 生命周期事件监听器
 */
export type LifecycleEventListener = (
  eventType: StreamEventType,
  stage: string,
  data: any
) => void;

// =================================================================
// 工具函数
// =================================================================

/**
 * 创建标准流式事件
 */
export function createStreamEvent(
  sessionId: string,
  moduleType: SupportedModuleType,
  eventType: StreamEventType,
  stage: string,
  content: string,
  options: Partial<
    Omit<
      StreamEvent,
      | "sessionId"
      | "moduleType"
      | "eventType"
      | "stage"
      | "content"
      | "timestamp"
    >
  > = {}
): StreamEvent {
  return {
    sessionId,
    moduleType,
    eventType,
    stage,
    content,
    timestamp: Date.now(),
    ...options,
  };
}

/**
 * 验证流式事件
 */
export function validateStreamEvent(
  event: Partial<StreamEvent>
): event is StreamEvent {
  return !!(
    event.sessionId &&
    event.moduleType &&
    event.eventType &&
    event.stage &&
    typeof event.content === "string" &&
    typeof event.timestamp === "number"
  );
}

/**
 * 格式化阶段名称
 *
 * 支持完全自由的命名，包括中文、emoji、特殊字符
 */
export function formatStageName(stage: string): string {
  return stage.trim();
}

// =================================================================
// 向后兼容
// =================================================================

/**
 * @deprecated 使用 ProgressReporter 替代
 */
export type StreamingProgressCallback = (
  message: string,
  chunk: string,
  isComplete: boolean,
  stage: string
) => void;

/**
 * @deprecated 使用 StreamEventType 替代
 */
export type StreamEventData = StreamEventMetadata;
