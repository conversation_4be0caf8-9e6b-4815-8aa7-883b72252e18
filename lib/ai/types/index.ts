/**
 * AI描述生成系统 - 统一类型定义
 * 整合所有模块的类型定义，消除重复
 *
 * 包含：
 * - 核心AI模块类型（统一的content包装结构）
 * - 调试平台类型
 * - 流式日志系统类型

 */
import { UserData } from "@/types/user-data";
import type { GitHubExtendedData } from "@/types/github-extended";

// 导入新的分析器类型
import type {
  AnalysisItem,
  AnalyzerInput,
  AnalyzerConfig,
  AnalysisItemName,
  AnalysisItemValue,
  AnalyzerMapConfig,
  AnalyzerFunction,
} from "@/types/analyzer";

// 导入分析器常量
export {
  DEFAULT_ANALYZER_CONFIG,
  LLM_REQUIRED_ITEMS,
  ALGORITHM_ITEMS,
} from "@/types/analyzer";

// 重新导出分析器类型
export type {
  AnalysisItem,
  AnalyzerInput,
  AnalyzerConfig,
  AnalysisItemName,
  AnalysisItemValue,
  AnalyzerMapConfig,
  AnalyzerFunction,
};

// 导出V6流式日志系统类型
export type {
  // 核心类型
  SupportedModuleType,
  StreamEventType,
  StreamEvent,
  StreamEventMetadata,

  // 模块特定事件
  AnalyzerStreamEvent,
  StrategistStreamEvent,
  WriterStreamEvent,
  CriticStreamEvent,

  // 生命周期管理
  StageContext,
  LifecycleEventListener,

  // V6.1 新增：非阻塞流式事件系统
  StreamEventListener,
  StreamErrorHandler,
  StreamCompleteHandler,
  StreamingEventEmitter,
  StreamingExecutor,

  // 进度报告
  StreamingProgressCallback,
  StreamEventData,
} from "./streaming";

// 导入自描述数据类型
import type { SelfDescribingData } from "../core/utils/AdaptiveMetadataDisplayManager";

// =================================================================
// 重载的 onProgress 类型定义
// =================================================================

/**
 * 重载的进度回调函数类型
 * 支持传统的字符串格式和新的自描述数据格式
 */
export interface ProgressCallback {
  // 传统格式：字符串内容
  (message: string, chunk: string, isComplete: boolean, stage: string): void;

  // 新格式：支持自描述数据
  (
    message: string,
    data: SelfDescribingData,
    isComplete: boolean,
    stage: string
  ): void;
}

// 导出工具函数
export { createStreamEvent, validateStreamEvent } from "./streaming";

// =================================================================
// 核心模块输出类型（统一结构）
// =================================================================

export interface ModuleOutput {
  status: "success" | "error";
  content: any;
  error_details?: string;
  processing_time?: number;
  confidence?: number;
  metadata?: Record<string, any>;
}

export interface DebugInfo {
  analyzerOutput: AnalyzerOutput;
  strategistOutput: StrategistOutput;
  writerOutput: WriterOutput;
  criticOutput: CriticOutput;
}

export interface GenerationResult {
  finalText: string;
  confidence: number;
  metadata: {
    strategy: string;
    iterations: number;
    executionTime: number;
    moduleHealth: Record<string, boolean>;
  };
  debugInfo?: DebugInfo;
}

export interface AnalyzerOutput extends ModuleOutput {
  content: string[];
}

export interface StrategistOutput extends ModuleOutput {
  content: {
    // 基于tags-to-strategist.md模板的创意简报结构
    // 遵循开发规范：类型安全优先，避免any类型
    narrative_scaffold: {
      A: string; // 概述型铺垫
      B: string; // 反差型过渡
      C_hint: string; // C段写作方向
    };
    insight_pool: {
      behavior_summary: string; // 行为特征总结
      persona: string; // 人物设定
      humor_angle: string; // 幽默角度
      analogy_pool: string[]; // 类比句池
    };
    punchline_focus: {
      punchline_candidates: string[]; // 收尾候选句
      tone: string; // 语言气质
      stylistic_suggestion: string; // 结构建议
    };
    writing_instruction: {
      focus_on: string; // 重点打磨指导
      overall_tone: string; // 整体语气要求
    };
  };
}

export interface WriterOutput extends ModuleOutput {
  content: {
    generatedText: string;
    confidence: number;
    usedTemplate: string; // V6.0新增：使用的模板路径
    // V6.0保留的可选字段
    iterations?: number;
    qualityScore?: number;
    // V6.0移除的复杂字段：
    // - usedExamples (FewShot示例，已简化移除)
    // - reasoningEnhanced (推理增强模式，已简化移除)
    // - creativityMetrics (复杂的创意指标，已简化移除)
  };
}

export interface CriticOutput extends ModuleOutput {
  content: {
    overallScore: number;
    dimensions: QualityDimensions;
    assessment: string;
    suggestions: string[];
    confidence: number;
    needsOptimization: boolean;
    standardizedGrade: "A" | "B" | "C" | "D" | "F";
  };
}

// =================================================================
// 调试平台专用类型
// =================================================================

export interface ModuleExecutionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  processingTime: number;
  timestamp: number;
  metadata?: {
    tokensUsed?: number;
    apiCalls?: number;
    cacheHit?: boolean;
    confidence?: number;
  };
}

export interface SessionError {
  module: ModuleType;
  error_type: "validation" | "execution" | "timeout" | "api_error" | "unknown";
  message: string;
  timestamp: number;
  stack_trace?: string;
  recovery_attempted: boolean;
}

export interface SessionComparison {
  baseline_session: string;
  comparison_sessions: string[];
  comparison_results: {
    performance_improvements: { [module: string]: number };
    quality_improvements: { [metric: string]: number };
    parameter_impact_analysis: ParameterImpactAnalysis[];
    recommendations: string[];
  };
}

export interface ParameterImpactAnalysis {
  module: ModuleType;
  parameter: string;
  impact_score: number;
  trend: "positive" | "negative" | "neutral";
  optimal_range?: { min: number; max: number };
  examples: {
    low_value: { value: number; result_quality: number };
    high_value: { value: number; result_quality: number };
    optimal_value: { value: number; result_quality: number };
  };
}

export interface DebugConfiguration {
  auto_save_sessions: boolean;
  max_concurrent_sessions: number;
  default_timeout: number;
  retry_policy: {
    max_retries: number;
    backoff_strategy: "linear" | "exponential";
    retry_on_errors: string[];
  };
  performance_monitoring: {
    track_token_usage: boolean;
    track_processing_time: boolean;
    alert_thresholds: {
      max_processing_time: number;
      max_token_usage: number;
    };
  };
}

// =================================================================
// 调试平台类型定义
// =================================================================

// 调试平台专用的模块输出类型通过 debug-adapters.ts 文件直接导入

export interface AnalyzedMetric {
  metric: string;
  value: number;
  valence:
    | "highly_positive"
    | "positive"
    | "neutral"
    | "negative"
    | "highly_negative";
  analysis: string;
  confidence: number;
  talking_points?: string[];
}

// =================================================================
// 更新的调试会话类型
// =================================================================

// 更新 DebugSession 接口，使用实际模块的参数类型
export interface DebugSession {
  id: string;
  status: "idle" | "running" | "completed" | "failed" | "paused";
  created_at: number;
  started_at?: number;
  completed_at?: number;

  // Input configuration - 使用实际模块的参数类型
  input_data: UserData;
  module_configurations: {
    analyzer: AnalyzerConfig;
    strategist: UserPreferences;
    // Writer 和 Critic 模块不需要单独的配置参数
  };

  // Execution results
  execution_results: {
    analyzer?: ModuleExecutionResult<AnalyzerOutput>;
    strategist?: ModuleExecutionResult<StrategistOutput>;
    writer?: ModuleExecutionResult<WriterOutput>;
    critic?: ModuleExecutionResult<CriticOutput>;
  };

  // Session metadata
  current_module?: ModuleType;
  total_processing_time?: number;
  error_details?: SessionError[];
  retry_count: number;
  tags?: string[];
}

// =================================================================
// 集合类型定义
// =================================================================

// 更新 AllModuleInputs 类型
export type AllModuleInputs =
  | { githubData: UserData; config: AnalyzerConfig }
  | { analyzerOutput: AnalyzerOutput; userPreferences: UserPreferences }
  | {
      githubData: UserData;
      analyzerOutput: AnalyzerOutput;
      strategistOutput: StrategistOutput;
      userPreferences: UserPreferences;
    }
  | {
      generatedText: string;
      strategistOutput: StrategistOutput;
      githubData: UserData;
    };

export type AllModuleOutputs =
  | AnalyzerOutput
  | StrategistOutput
  | WriterOutput
  | CriticOutput;

// 更新 AllModuleParameters 类型
export type AllModuleParameters =
  | AnalyzerConfig
  | UserPreferences
  | WriterAIParams
  | CriticAIParams;

// =================================================================
// 共享接口定义
// =================================================================

export interface MetricAnalysis {
  metric: string;
  value: number;
  valence:
    | "highly_positive"
    | "positive"
    | "neutral"
    | "negative"
    | "highly_negative";
  analysis: string;
  talkingPoint?: string;
  confidence?: number;
  contextualWeight?: number;
  patternRelevance?: number;
}

export interface QualityDimensions {
  humor: number;
  compliance: number;
  originality: number;
  naturalness: number;
  relevance: number;
}

export interface GenerationRequest {
  githubData: UserData;
  githubExtendedData: GitHubExtendedData;
  options?: GenerationOptions;
}

// =================================================================
// 新增的AI调用参数类型（Debug Platform专用）
// =================================================================

/**
 * Writer模块AI调用参数配置 - V6.0模板化版本
 * 用于Debug Platform中Writer模块的独立调试
 */
export interface WriterAIParams {
  /** 温度参数(0-1)：控制创意性和随机性，值越高越有创意，默认0.8 */
  temperature?: number;
  /** 最大Token数：控制生成文本的最大长度，默认800 */
  max_tokens?: number;
  /** 重试次数：AI调用失败后的重试次数，默认1 */
  retry_count?: number;
  // V6.0移除：few_shot_examples (FewShot示例已简化移除，改用模板化生成)
}

/**
 * Critic模块AI调用参数配置
 * 用于Debug Platform中Critic模块的独立调试
 */
export interface CriticAIParams {
  /** 温度参数(0-1)：控制评估一致性，值越低越一致，默认0.2 */
  temperature?: number;
  /** 最大Token数：控制评估详细程度，值越大越详细，默认600 */
  max_tokens?: number;
  /** 重试次数：AI调用失败后的重试次数，默认3 */
  retry_count?: number;
  /** 评分严格度(0-1)：控制评分标准的严格程度，默认0.5 */
  scoring_strictness?: number;
}

/**
 * Writer模块的默认AI参数配置 - V6.0模板化版本
 */
export const DEFAULT_WRITER_AI_PARAMS: WriterAIParams = {
  temperature: 0.8, // 适中创意性，适合模板化文本生成
  max_tokens: 800, // 模板化生成通常更简洁
  retry_count: 1, // 模板化生成更稳定，减少重试次数
  // V6.0移除：few_shot_examples (改用模板化生成)
};

/**
 * Critic模块的默认AI参数配置
 */
export const DEFAULT_CRITIC_AI_PARAMS: CriticAIParams = {
  temperature: 0.2, // 低温度确保评估一致性
  max_tokens: 600, // 足够生成详细的评估报告
  retry_count: 3, // 标准重试次数
  scoring_strictness: 0.5, // 中等严格度，平衡标准
};

export interface UserPreferences {
  perspective: "first_person" | "second_person" | "third_person";
  targetEmotion:
    | "self_deprecating"
    | "witty"
    | "philosophical"
    | "confident"
    | "playful";
  comedyStrategy?: string;
  humorIntensity?: number;
  formalityLevel?: number;
  style?: string;
  includeWords?: string[];
  excludeWords?: string[];
}

export interface GenerationOptions {
  forceRefresh?: boolean;
  quickMode?: boolean;
  optimizationEnabled?: boolean;
  enableQualityAssessment?: boolean;
}

// =================================================================
// 策略相关类型
// =================================================================

export interface ComedyStrategy {
  id: string;
  strategyName: string;
  strategyType: string;
  description: string;
  targetEmotions: string[];
  suitableDataPatterns: Record<string, any>;
  templateExamples: string[];
  isActive: boolean;
  difficultyLevel: number;
}

// =================================================================
// 通用类型定义
// =================================================================

export type ModuleType = "analyzer" | "strategist" | "writer" | "critic";

// =================================================================
// 模块配置类型（AI系统主要参数）
// =================================================================

// AnalyzerConfig 现在从 @/types/analyzer 导入

// DEFAULT_ANALYZER_CONFIG 现在从 @/types/analyzer 导入
