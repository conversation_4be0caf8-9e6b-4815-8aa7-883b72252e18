import { DoubaoJsonSchema } from "../core/clients/DoubaoClient";

/**
 * StrategistOutput JSON Schema 定义
 * 用于 Doubao 官方结构化输出 API
 *
 * 基于 tags-to-strategist.md 模板的创意简报结构
 * 遵循开发规范：约定大于配置，类型安全优先
 */
export const StrategistOutputSchema: DoubaoJsonSchema = {
  type: "object",
  properties: {
    narrative_scaffold: {
      type: "object",
      description: "三段式叙事架构",
      properties: {
        A: {
          type: "string",
          description: "概述型铺垫：描述该用户持续进行的行为习惯",
        },
        B: {
          type: "string",
          description: "反差型过渡：揭示这些投入与产出间的落差或荒诞",
        },
        C_hint: {
          type: "string",
          description: "点明C段写作方向，例如暗喻式、自黑式、哲思式等",
        },
      },
      required: ["A", "B", "C_hint"],
      additionalProperties: false,
    },
    insight_pool: {
      type: "object",
      description: "洞察资源池",
      properties: {
        behavior_summary: {
          type: "string",
          description: "简洁复述该用户整体行为特征",
        },
        persona: {
          type: "string",
          description: "建议的人物设定，用一句有品位的短语概括",
        },
        humor_angle: {
          type: "string",
          description: "本次幽默的落点方式，如认知错位、自我反转、社交隐喻等",
        },
        analogy_pool: {
          type: "array",
          description: "类比句池",
          items: {
            type: "string",
            description: "用文学或生活化意象强化人物特征的类比句",
          },
          minItems: 2,
          maxItems: 5,
        },
      },
      required: ["behavior_summary", "persona", "humor_angle", "analogy_pool"],
      additionalProperties: false,
    },
    punchline_focus: {
      type: "object",
      description: "收尾焦点设计",
      properties: {
        punchline_candidates: {
          type: "array",
          description: "收尾候选句",
          items: {
            type: "string",
            description: "应具备哲思感、传播力或冷幽默调性的收尾句",
          },
          minItems: 2,
          maxItems: 4,
        },
        tone: {
          type: "string",
          description: "语言气质描述，如'冷幽默 + 文艺讽刺'",
        },
        stylistic_suggestion: {
          type: "string",
          description: "结构建议，如'短句收尾'、'反转+暗喻'",
        },
      },
      required: ["punchline_candidates", "tone", "stylistic_suggestion"],
      additionalProperties: false,
    },
    writing_instruction: {
      type: "object",
      description: "写作指导",
      properties: {
        focus_on: {
          type: "string",
          description: "重点打磨指导",
        },
        overall_tone: {
          type: "string",
          description: "整体语气要求",
        },
      },
      required: ["focus_on", "overall_tone"],
      additionalProperties: false,
    },
  },
  required: [
    "narrative_scaffold",
    "insight_pool",
    "punchline_focus",
    "writing_instruction",
  ],
  additionalProperties: false,
};

/**
 * StrategistOutput 响应格式配置
 * 遵循约定大于配置原则，提供合理默认值
 */
export const StrategistResponseFormat = {
  type: "json_object" as const,
  json_schema: {
    name: "creative_brief_output",
    schema: StrategistOutputSchema,
    strict: true,
  },
};

/**
 * 创意简报结果类型定义
 * 类型安全优先：避免any类型，使用具体类型
 */
export interface CreativeBriefResult {
  narrative_scaffold: {
    A: string;
    B: string;
    C_hint: string;
  };
  insight_pool: {
    behavior_summary: string;
    persona: string;
    humor_angle: string;
    analogy_pool: string[];
  };
  punchline_focus: {
    punchline_candidates: string[];
    tone: string;
    stylistic_suggestion: string;
  };
  writing_instruction: {
    focus_on: string;
    overall_tone: string;
  };
}
