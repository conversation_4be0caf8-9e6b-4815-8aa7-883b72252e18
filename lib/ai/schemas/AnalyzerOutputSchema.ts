import { DoubaoJsonSchema } from "../core/clients/DoubaoClient";

/**
 * AnalyzerOutput JSON Schema 定义
 * 用于 Doubao 官方结构化输出 API
 */
export const AnalyzerOutputSchema: DoubaoJsonSchema = {
  type: "object",
  properties: {
    metrics: {
      type: "array",
      description: "分析指标数组",
      items: {
        type: "object",
        properties: {
          metric: {
            type: "string",
            description: "指标名称",
          },
          value: {
            type: "number",
            description: "指标数值",
          },
          valence: {
            type: "string",
            description: "指标情感倾向",
            enum: ["positive", "negative", "neutral"],
          },
          analysis: {
            type: "string",
            description: "指标分析说明",
          },
          talkingPoint: {
            type: "string",
            description: "相关话题点",
          },
          confidence: {
            type: "number",
            description: "置信度 (0-1)",
            minimum: 0,
            maximum: 1,
          },
        },
        required: ["metric", "value", "valence", "analysis"],
        additionalProperties: false,
      },
    },
    overallPattern: {
      type: "string",
      description: "整体模式分析",
    },
    insights: {
      type: "array",
      description: "洞察数组",
      items: {
        type: "string",
      },
    },
  },
  required: ["metrics", "overallPattern", "insights"],
  additionalProperties: false,
};

/**
 * AnalyzerOutput 响应格式配置
 */
export const AnalyzerResponseFormat = {
  type: "json_object" as const,
  json_schema: {
    name: "analyzer_output",
    schema: AnalyzerOutputSchema,
    strict: true,
  },
};
