import {
  DoubaoResponseFormat,
  DoubaoJsonSchema,
} from "../core/clients/DoubaoClient";
import { AnalyzerResponseFormat } from "./AnalyzerOutputSchema";
import { StrategistResponseFormat } from "./StrategistOutputSchema";
import { CriticResponseFormat } from "./CriticOutputSchema";
import {
  SemanticTagsResponseFormat,
  SemanticTagsOpenAIResponseFormat,
} from "./SemanticTagsSchema";
import {
  SimplifiedEnrichedNarrativeResponseFormat,
  SimplifiedEnrichedNarrativeOpenAIResponseFormat,
} from "./EnrichedNarrativeSchema";

// OpenAI 结构化输出格式
export interface OpenAIResponseFormat {
  type: "json_schema";
  json_schema: {
    name: string;
    schema: OpenAIJsonSchema;
    strict?: boolean;
  };
}

// OpenAI JSON Schema 格式（与 Doubao 基本兼容，但有细微差异）
export interface OpenAIJsonSchema {
  type: "object";
  properties: Record<string, any>;
  required?: string[];
  additionalProperties?: boolean;
}

// 通用结构化输出格式
export type UniversalResponseFormat =
  | DoubaoResponseFormat
  | OpenAIResponseFormat;

// 应用层使用的简化配置对象
export interface ResponseFormatConfig {
  name: string;
  schema: DoubaoJsonSchema;
  strict?: boolean;
}

/**
 * AI 模块类型枚举
 */
export enum AIModuleType {
  ANALYZER = "analyzer",
  STRATEGIST = "strategist",
  CRITIC = "critic",
  WRITER = "writer",
}

/**
 * Schema 管理器
 * 提供统一的 JSON Schema 获取和验证功能
 * 支持多 provider 的结构化输出格式
 */
export class SchemaManager {
  private static instance: SchemaManager;
  private schemaCache = new Map<AIModuleType, DoubaoResponseFormat>();

  private constructor() {
    this.initializeSchemas();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): SchemaManager {
    if (!SchemaManager.instance) {
      SchemaManager.instance = new SchemaManager();
    }
    return SchemaManager.instance;
  }

  /**
   * 根据模型名称判断应该使用哪种结构化输出格式
   */
  private getModelFamily(
    model: string
  ): "doubao" | "openai" | "anthropic" | "meta" | "unknown" {
    const modelLower = model.toLowerCase();

    // Doubao 模型
    if (modelLower.includes("doubao") || modelLower.includes("ep-")) {
      return "doubao";
    }

    // OpenAI 模型
    if (modelLower.includes("gpt") || modelLower.includes("openai/")) {
      return "openai";
    }

    // Anthropic 模型
    if (modelLower.includes("claude") || modelLower.includes("anthropic/")) {
      return "anthropic";
    }

    // Meta 模型
    if (modelLower.includes("llama") || modelLower.includes("meta-llama/")) {
      return "meta";
    }

    return "unknown";
  }

  /**
   * 根据模型创建适配的结构化输出格式
   */
  public createResponseFormat(
    model: string,
    moduleType: AIModuleType
  ): UniversalResponseFormat | null {
    const doubaoFormat = this.getResponseFormat(moduleType);
    if (!doubaoFormat || !doubaoFormat.json_schema) {
      return null;
    }

    const { name, schema, strict } = doubaoFormat.json_schema;
    const modelFamily = this.getModelFamily(model);

    switch (modelFamily) {
      case "doubao":
        return doubaoFormat;

      case "openai":
      case "anthropic":
      case "meta":
        // OpenAI 兼容格式（大多数模型都支持这种格式）
        return {
          type: "json_schema",
          json_schema: {
            name: name,
            schema: this.convertToOpenAISchema(schema),
            strict: strict,
          },
        };

      default:
        // 默认使用 OpenAI 兼容格式
        return {
          type: "json_schema",
          json_schema: {
            name: name,
            schema: this.convertToOpenAISchema(schema),
            strict: strict,
          },
        };
    }
  }

  /**
   * 将 Doubao Schema 转换为 OpenAI 兼容的 Schema
   */
  private convertToOpenAISchema(
    doubaoSchema: DoubaoJsonSchema
  ): OpenAIJsonSchema {
    return {
      type: "object",
      properties: doubaoSchema.properties,
      required: doubaoSchema.required,
      additionalProperties: doubaoSchema.additionalProperties ?? false,
    };
  }

  /**
   * 静态方法：根据模型自动转换 ResponseFormatConfig 为对应的格式
   * 供 BaseModule.callLLMStream 内部使用
   */
  public static convertResponseFormat(
    config: ResponseFormatConfig,
    model: string
  ): UniversalResponseFormat {
    const instance = SchemaManager.getInstance();
    const modelFamily = instance.getModelFamily(model);

    switch (modelFamily) {
      case "doubao":
        return {
          type: "json_object",
          json_schema: {
            name: config.name,
            schema: config.schema,
            strict: config.strict ?? true,
          },
        };

      case "openai":
      case "anthropic":
      case "meta":
      default:
        return {
          type: "json_schema",
          json_schema: {
            name: config.name,
            schema: instance.convertToOpenAISchema(config.schema),
            strict: config.strict ?? true,
          },
        };
    }
  }

  /**
   * 获取语义标签提取的响应格式
   */
  public getSemanticTagsResponseFormat(model: string): UniversalResponseFormat {
    const modelFamily = this.getModelFamily(model);

    switch (modelFamily) {
      case "doubao":
        return SemanticTagsResponseFormat;
      case "openai":
      case "anthropic":
      case "meta":
        return SemanticTagsOpenAIResponseFormat;
      default:
        return SemanticTagsOpenAIResponseFormat; // 默认使用 OpenAI 兼容格式
    }
  }

  /**
   * 获取叙述增强的响应格式
   */
  public getEnrichedNarrativeResponseFormat(
    model: string
  ): UniversalResponseFormat {
    const modelFamily = this.getModelFamily(model);

    switch (modelFamily) {
      case "doubao":
        return SimplifiedEnrichedNarrativeResponseFormat;
      case "openai":
      case "anthropic":
      case "meta":
        return SimplifiedEnrichedNarrativeOpenAIResponseFormat;
      default:
        return SimplifiedEnrichedNarrativeOpenAIResponseFormat; // 默认使用 OpenAI 兼容格式
    }
  }

  /**
   * 初始化 Schema 缓存
   */
  private initializeSchemas(): void {
    this.schemaCache.set(AIModuleType.ANALYZER, AnalyzerResponseFormat);
    this.schemaCache.set(AIModuleType.STRATEGIST, StrategistResponseFormat);
    this.schemaCache.set(AIModuleType.CRITIC, CriticResponseFormat);
    // WRITER 模块不需要结构化输出
  }

  /**
   * 获取指定模块的响应格式配置
   */
  public getResponseFormat(
    moduleType: AIModuleType
  ): DoubaoResponseFormat | null {
    return this.schemaCache.get(moduleType) || null;
  }

  /**
   * 检查模块是否支持结构化输出
   */
  public supportsStructuredOutput(moduleType: AIModuleType): boolean {
    return this.schemaCache.has(moduleType);
  }

  /**
   * 验证 JSON 数据是否符合指定模块的 Schema
   */
  public validateData(moduleType: AIModuleType, data: any): boolean {
    const responseFormat = this.getResponseFormat(moduleType);
    if (!responseFormat) {
      return false;
    }

    try {
      // 基础类型检查
      if (!data || typeof data !== "object") {
        return false;
      }

      const schema = responseFormat.json_schema?.schema;
      if (!schema || !schema.required) {
        return true; // 没有必需字段要求
      }

      // 检查必需字段
      for (const field of schema.required) {
        if (!(field in data)) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.warn(`Schema validation failed for ${moduleType}:`, error);
      return false;
    }
  }

  /**
   * 获取所有支持结构化输出的模块类型
   */
  public getSupportedModules(): AIModuleType[] {
    return Array.from(this.schemaCache.keys());
  }

  /**
   * 清除 Schema 缓存（用于测试）
   */
  public clearCache(): void {
    this.schemaCache.clear();
  }

  /**
   * 重新初始化 Schema（用于热更新）
   */
  public reinitialize(): void {
    this.clearCache();
    this.initializeSchemas();
  }
}

/**
 * 导出单例实例
 */
export const schemaManager = SchemaManager.getInstance();
