/**
 * AI Schemas 统一导出
 * 提供所有 JSON Schema 定义和管理工具
 */

// 导入分析器输出 Schema
import {
  AnalyzerOutputSchema,
  AnalyzerResponseFormat,
} from "./AnalyzerOutputSchema";

// 导入语义标签 Schema
import {
  SemanticTagsSchema,
  SemanticTagsResponseFormat,
  SemanticTagsConfig,
  SemanticTagsResult,
} from "./SemanticTagsSchema";

// 导入 enrichedNarrative Schema
import {
  SimplifiedEnrichedNarrativeSchema,
  SimplifiedEnrichedNarrativeResponseFormat,
  SimplifiedEnrichedNarrativeConfig,
  SimplifiedEnrichedNarrativeResult,
} from "./EnrichedNarrativeSchema";

// 导入策略师输出 Schema
import {
  StrategistOutputSchema,
  StrategistResponseFormat,
  CreativeBriefResult,
} from "./StrategistOutputSchema";

// 导入评论家输出 Schema
import { CriticOutputSchema, CriticResponseFormat } from "./CriticOutputSchema";

// 导入 Schema 管理器
export { SchemaManager, schemaManager, AIModuleType } from "./SchemaManager";

// =================================================================
// 统一导出所有 Schema 定义
// =================================================================

export {
  // 分析器相关
  AnalyzerOutputSchema,
  AnalyzerResponseFormat,

  // 语义标签相关
  SemanticTagsSchema,
  SemanticTagsResponseFormat,
  SemanticTagsConfig,
  type SemanticTagsResult,

  // enrichedNarrative 相关 - 只保留优化版本
  SimplifiedEnrichedNarrativeSchema,
  SimplifiedEnrichedNarrativeResponseFormat,
  SimplifiedEnrichedNarrativeConfig,
  type SimplifiedEnrichedNarrativeResult,

  // 策略师相关
  StrategistOutputSchema,
  StrategistResponseFormat,
  type CreativeBriefResult,

  // 评论家相关
  CriticOutputSchema,
  CriticResponseFormat,
};

// 类型定义
export type {
  DoubaoJsonSchema,
  DoubaoResponseFormat,
} from "../core/clients/DoubaoClient";
