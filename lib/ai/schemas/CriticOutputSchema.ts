import { DoubaoJsonSchema } from "../core/clients/DoubaoClient";

/**
 * CriticOutput JSON Schema 定义
 * 用于 Doubao 官方结构化输出 API
 */
export const CriticOutputSchema: DoubaoJsonSchema = {
  type: "object",
  properties: {
    dimensions: {
      type: "object",
      description: "五维质量评分",
      properties: {
        humor: {
          type: "number",
          description: "幽默性评分 (0-100)",
          minimum: 0,
          maximum: 100,
        },
        compliance: {
          type: "number",
          description: "合规性评分 (0-100)",
          minimum: 0,
          maximum: 100,
        },
        originality: {
          type: "number",
          description: "原创性评分 (0-100)",
          minimum: 0,
          maximum: 100,
        },
        naturalness: {
          type: "number",
          description: "自然度评分 (0-100)",
          minimum: 0,
          maximum: 100,
        },
        relevance: {
          type: "number",
          description: "相关性评分 (0-100)",
          minimum: 0,
          maximum: 100,
        },
      },
      required: [
        "humor",
        "compliance",
        "originality",
        "naturalness",
        "relevance",
      ],
      additionalProperties: false,
    },
    detailedAssessment: {
      type: "string",
      description: "详细评估分析",
    },
    confidence: {
      type: "number",
      description: "评估置信度 (0-1)",
      minimum: 0,
      maximum: 1,
    },
    improvementSuggestions: {
      type: "array",
      description: "改进建议数组",
      items: {
        type: "string",
      },
      minItems: 0,
      maxItems: 5,
    },
    qualityInsights: {
      type: "array",
      description: "质量洞察数组（可选）",
      items: {
        type: "string",
      },
    },
  },
  required: ["dimensions", "detailedAssessment", "confidence"],
  additionalProperties: false,
};

/**
 * CriticOutput 响应格式配置
 */
export const CriticResponseFormat = {
  type: "json_object" as const,
  json_schema: {
    name: "critic_output",
    schema: CriticOutputSchema,
    strict: true,
  },
};
