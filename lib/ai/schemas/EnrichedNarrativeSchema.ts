import { DoubaoJsonSchema } from "../core/clients/DoubaoClient";

/**
 * 简化版 enrichedNarrative 补全 JSON Schema
 * 🚀 Token 优化版本：只输出 name 和 enrichedNarrative 字段
 * 减少约 60% 的输出 token，提升生成速度和降低成本
 */
export const SimplifiedEnrichedNarrativeSchema: DoubaoJsonSchema = {
  type: "object",
  properties: {
    enrichedNarratives: {
      type: "array",
      description: "简化的 enrichedNarrative 数组，只包含关键字段",
      items: {
        type: "object",
        properties: {
          name: {
            type: "string",
            description: "分析项名称，用于与原始数据匹配",
          },
          value: {
            type: "string",
            description: "分析项值，用于与原始数据匹配",
          },
          enrichedNarrative: {
            type: "string",
            description:
              "对标签的创造性解读，有幽默感或文艺感，暗示积极侧面，风格一致",
            minLength: 10,
            maxLength: 100,
          },
        },
        required: ["name", "enrichedNarrative"],
        additionalProperties: false,
      },
    },
  },
  required: ["enrichedNarratives"],
  additionalProperties: false,
};

/**
 * 简化版 enrichedNarrative 补全响应格式配置 - Doubao 格式
 * 🚀 Token 优化版本
 */
export const SimplifiedEnrichedNarrativeResponseFormat = {
  type: "json_object" as const,
  json_schema: {
    name: "simplified_enriched_narrative_completion",
    schema: SimplifiedEnrichedNarrativeSchema,
    strict: true,
  },
};

/**
 * 简化版 enrichedNarrative 补全响应格式配置 - OpenAI 格式
 * 🚀 Token 优化版本
 */
export const SimplifiedEnrichedNarrativeOpenAIResponseFormat = {
  type: "json_schema" as const,
  json_schema: {
    name: "simplified_enriched_narrative_completion",
    schema: {
      type: "object" as const,
      properties: SimplifiedEnrichedNarrativeSchema.properties,
      required: SimplifiedEnrichedNarrativeSchema.required,
      additionalProperties: false,
    },
    strict: true,
  },
};

/**
 * 简化版 enrichedNarrative 补全 - 简化配置对象（应用层使用）
 * 🚀 Token 优化版本
 */
export const SimplifiedEnrichedNarrativeConfig = {
  name: "simplified_enriched_narrative_completion",
  schema: SimplifiedEnrichedNarrativeSchema,
  strict: true,
};

/**
 * 简化版 enrichedNarrative 补全结果类型
 * 🚀 Token 优化版本
 */
export interface SimplifiedEnrichedNarrativeResult {
  enrichedNarratives: Array<{
    name: string;
    enrichedNarrative: string;
  }>;
}
