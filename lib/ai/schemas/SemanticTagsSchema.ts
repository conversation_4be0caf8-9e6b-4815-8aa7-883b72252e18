import { DoubaoJsonSchema } from "../core/clients/DoubaoClient";

/**
 * 语义标签提取 JSON Schema 定义
 * 用于 Doubao 官方结构化输出 API
 * 按照json-to-tags模板要求输出标签数组
 */
export const SemanticTagsSchema: DoubaoJsonSchema = {
  type: "object",
  properties: {
    tags: {
      type: "array",
      description: "语义标签数组，5-8个具有画面感和风格化的标签",
      items: {
        type: "string",
        description: "单个语义标签，具有画面感和性格特征，控制在20字以内",
      },
      minItems: 5,
      maxItems: 8,
    },
  },
  required: ["tags"],
  additionalProperties: false,
};

/**
 * 语义标签提取响应格式配置 - Doubao 格式
 */
export const SemanticTagsResponseFormat = {
  type: "json_object" as const,
  json_schema: {
    name: "semantic_tags_extraction",
    schema: SemanticTagsSchema,
    strict: true,
  },
};

/**
 * 语义标签提取响应格式配置 - OpenAI 格式
 */
export const SemanticTagsOpenAIResponseFormat = {
  type: "json_schema" as const,
  json_schema: {
    name: "semantic_tags_extraction",
    schema: {
      type: "object" as const,
      properties: SemanticTagsSchema.properties,
      required: SemanticTagsSchema.required,
      additionalProperties: false,
    },
    strict: true,
  },
};

/**
 * 语义标签提取 - 简化配置对象（应用层使用）
 */
export const SemanticTagsConfig = {
  name: "semantic_tags_extraction",
  schema: SemanticTagsSchema,
  strict: true,
};

/**
 * 语义标签提取结果类型
 */
export interface SemanticTagsResult {
  tags: string[];
}
