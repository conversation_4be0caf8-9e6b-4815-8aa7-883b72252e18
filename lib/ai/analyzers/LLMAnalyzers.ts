/**
 * LLM分析器集合
 * 实现需要LLM的分析项
 *
 * 职责：
 * - 实现基于LLM的语义分析
 * - 处理情感、风格、语义类分析
 * - 提供智能化的分析结果
 *
 * 设计原则：
 * - LLM驱动：利用大模型的语义理解能力
 * - 结构化输出：使用Doubao的结构化输出功能
 * - 错误处理：优雅处理LLM调用失败
 */

import type {
  AnalyzerInput,
  AnalysisItem,
  AnalysisItemName,
  AnalyzerFunction,
} from "@/lib/ai/types";
import { DoubaoClient } from "@/lib/ai/core/clients/DoubaoClient";

// 导入分析器映射配置
import analyzerMapConfig from "@/docs/v6/analyzer-map.json";

/**
 * LLM分析器类
 */
export class LLMAnalyzers {
  private doubaoClient: DoubaoClient;

  constructor(doubaoClient: DoubaoClient) {
    this.doubaoClient = doubaoClient;
  }

  /**
   * 获取LLM分析器
   * @param itemName 分析项名称
   * @returns 分析器函数
   */
  getAnalyzer(itemName: AnalysisItemName): AnalyzerFunction {
    const analyzers: Record<string, AnalyzerFunction> = {
      commit_sentiment: this.analyzeCommitSentiment.bind(this),
      repo_naming_style: this.analyzeRepoNamingStyle.bind(this),
    };

    const analyzer = analyzers[itemName];
    if (!analyzer) {
      throw new Error(`不支持的LLM分析项: ${itemName}`);
    }

    return analyzer;
  }

  /**
   * 分析提交情感
   */
  private async analyzeCommitSentiment(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as any)[itemName];

    try {
      const { extendedData } = input;
      const commitsData = extendedData.commits;

      if (!commitsData || commitsData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少提交数据");
      }

      // 提取提交消息样本（最多50条）
      const commitMessages = commitsData
        .slice(0, 50)
        .map((commit: any) => commit.message)
        .filter((message: string) => message && message.trim().length > 0);

      if (commitMessages.length === 0) {
        return this.createErrorResult(
          itemName,
          itemConfig,
          "没有有效的提交消息"
        );
      }

      // 构建LLM提示
      const prompt = this.buildCommitSentimentPrompt(
        commitMessages,
        itemConfig.values
      );

      // 调用LLM
      const response = await this.doubaoClient.chat({
        messages: [{ role: "user", content: prompt }],
        temperature: 0.1, // 低温度确保一致性
        max_tokens: 100,
      });

      // 解析响应
      const result = this.parseCommitSentimentResponse(
        response.choices[0]?.message?.content || "",
        itemConfig.values
      );

      return {
        name: itemName,
        value: result.value,
        source: itemConfig.source,
        usedLLM: true,
        enrichedNarrative: "",
        rawData: {
          sampleMessages: commitMessages.slice(0, 5), // 只保留前5条用于调试
          llmResponse: response.choices[0]?.message?.content || "",
        },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: Math.min(commitMessages.length / 20, 1.0), // 20条消息为完整数据
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析仓库命名风格
   */
  private async analyzeRepoNamingStyle(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as any)[itemName];

    try {
      const { extendedData } = input;
      const repositoriesData = extendedData.repositories;

      if (!repositoriesData || repositoriesData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少仓库数据");
      }

      // 提取仓库名称
      const repoNames = repositoriesData
        .map((repo: any) => repo.name)
        .filter((name: string) => name && name.trim().length > 0);

      if (repoNames.length === 0) {
        return this.createErrorResult(
          itemName,
          itemConfig,
          "没有有效的仓库名称"
        );
      }

      // 构建LLM提示
      const prompt = this.buildRepoNamingStylePrompt(
        repoNames,
        itemConfig.values
      );

      // 调用LLM
      const response = await this.doubaoClient.chat({
        messages: [{ role: "user", content: prompt }],
        temperature: 0.1, // 低温度确保一致性
        max_tokens: 100,
      });

      // 解析响应
      const result = this.parseRepoNamingStyleResponse(
        response.choices[0]?.message?.content || "",
        itemConfig.values
      );

      return {
        name: itemName,
        value: result.value,
        source: itemConfig.source,
        usedLLM: true,
        enrichedNarrative: "",
        rawData: {
          repoNames: repoNames.slice(0, 10), // 只保留前10个用于调试
          llmResponse: response.choices[0]?.message?.content || "",
        },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: Math.min(repoNames.length / 10, 1.0), // 10个仓库为完整数据
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 构建提交情感分析提示
   */
  private buildCommitSentimentPrompt(
    messages: string[],
    validValues: string[]
  ): string {
    return `请分析以下Git提交消息的整体情感风格，从这些选项中选择最合适的一个：${validValues.join(
      ", "
    )}

提交消息样本：
${messages
  .slice(0, 10)
  .map((msg, i) => `${i + 1}. ${msg}`)
  .join("\n")}

请只返回选项中的一个词，不要添加其他解释。`;
  }

  /**
   * 构建仓库命名风格分析提示
   */
  private buildRepoNamingStylePrompt(
    repoNames: string[],
    validValues: string[]
  ): string {
    return `请分析以下GitHub仓库名称的命名风格，从这些选项中选择最合适的一个：${validValues.join(
      ", "
    )}

仓库名称：
${repoNames
  .slice(0, 15)
  .map((name, i) => `${i + 1}. ${name}`)
  .join("\n")}

请只返回选项中的一个词，不要添加其他解释。`;
  }

  /**
   * 解析提交情感分析响应
   */
  private parseCommitSentimentResponse(
    response: string,
    validValues: string[]
  ): { value: string; confidence: number } {
    const cleanResponse = response.trim().toLowerCase();

    // 查找匹配的值
    for (const value of validValues) {
      if (cleanResponse.includes(value.toLowerCase())) {
        return { value, confidence: 0.8 };
      }
    }

    // 如果没有找到匹配，返回默认值
    return { value: validValues[0] || "neutral", confidence: 0.3 };
  }

  /**
   * 解析仓库命名风格分析响应
   */
  private parseRepoNamingStyleResponse(
    response: string,
    validValues: string[]
  ): { value: string; confidence: number } {
    const cleanResponse = response.trim().toLowerCase();

    // 查找匹配的值
    for (const value of validValues) {
      if (cleanResponse.includes(value.toLowerCase())) {
        return { value, confidence: 0.8 };
      }
    }

    // 如果没有找到匹配，返回默认值
    return { value: validValues[0] || "random", confidence: 0.3 };
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(
    itemName: AnalysisItemName,
    itemConfig: any,
    error: any
  ): AnalysisItem {
    return {
      name: itemName,
      value: "error",
      source: itemConfig.source,
      usedLLM: true,
      enrichedNarrative: "分析失败，无法生成描述性内容",
      metadata: {
        processingTime: 0,
        dataCompleteness: 0,
        error: error instanceof Error ? error.message : String(error),
      },
    };
  }

  /**
   * 健康检查
   */
  healthCheck(): boolean {
    try {
      // 简单检查DoubaoClient是否可用
      return !!this.doubaoClient;
    } catch {
      return false;
    }
  }
}
