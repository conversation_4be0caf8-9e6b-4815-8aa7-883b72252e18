/**
 * 分析器工厂
 * 根据 analyzer-map.json 配置创建和管理分析器
 *
 * 职责：
 * - 根据 requiresLLM 字段选择算法分析或LLM分析
 * - 统一管理所有分析器
 * - 提供分析器的创建和执行接口
 *
 * 设计原则：
 * - 工厂模式：统一创建分析器实例
 * - 策略模式：根据配置选择不同的分析策略
 * - 单一职责：每个分析器只负责一个分析项
 */

import type {
  AnalyzerInput,
  AnalysisItem,
  AnalysisItemName,
  AnalyzerFunction,
} from "@/lib/ai/types";
import { DoubaoClient } from "@/lib/ai/core/clients/DoubaoClient";

// 导入分析器映射配置
import analyzerMapConfig from "@/docs/v6/analyzer-map.json";

// 导入具体的分析器实现
import { AlgorithmAnalyzers } from "./AlgorithmAnalyzers";
import { LLMAnalyzers } from "./LLMAnalyzers";

/**
 * 分析器工厂类
 */
export class AnalyzerFactory {
  private doubaoClient: DoubaoClient;
  private algorithmAnalyzers: AlgorithmAnalyzers;
  private llmAnalyzers: LLMAnalyzers;

  constructor() {
    this.doubaoClient = new DoubaoClient();
    this.algorithmAnalyzers = new AlgorithmAnalyzers();
    this.llmAnalyzers = new LLMAnalyzers(this.doubaoClient);
  }

  /**
   * 获取分析器函数
   * @param itemName 分析项名称
   * @returns 分析器函数
   */
  getAnalyzer(itemName: AnalysisItemName): AnalyzerFunction {
    const itemConfig = (analyzerMapConfig as any)[itemName];

    if (!itemConfig) {
      throw new Error(`未找到分析项配置: ${itemName}`);
    }

    if (itemConfig.requiresLLM) {
      return this.llmAnalyzers.getAnalyzer(itemName);
    } else {
      return this.algorithmAnalyzers.getAnalyzer(itemName);
    }
  }

  /**
   * 执行单个分析项
   * @param input 输入数据
   * @param itemName 分析项名称
   * @returns 分析结果
   */
  async analyze(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const analyzer = this.getAnalyzer(itemName);
    return await analyzer(input, itemName);
  }

  /**
   * 批量执行分析项
   * @param input 输入数据
   * @param itemNames 分析项名称列表
   * @returns 分析结果列表
   */
  async analyzeMultiple(
    input: AnalyzerInput,
    itemNames: AnalysisItemName[],
    callback?: (result: AnalysisItem) => void
  ): Promise<AnalysisItem[]> {
    const results: AnalysisItem[] = [];

    for (const itemName of itemNames) {
      try {
        const result = await this.analyze(input, itemName);
        results.push(result);
        callback?.(result);
      } catch (error) {
        console.error(`分析项 ${itemName} 执行失败:`, error);
        // 创建错误结果
        results.push({
          name: itemName,
          value: "error",
          source: "error",
          usedLLM: false,
          enrichedNarrative: "分析失败，无法生成描述性内容",
          metadata: {
            processingTime: 0,
            dataCompleteness: 0,
            error: error instanceof Error ? error.message : "未知错误",
          },
        });
      }
    }

    return results;
  }

  /**
   * 获取所有支持的分析项
   * @returns 分析项名称列表
   */
  getSupportedItems(): AnalysisItemName[] {
    return Object.keys(analyzerMapConfig) as AnalysisItemName[];
  }

  /**
   * 获取需要LLM的分析项
   * @returns 分析项名称列表
   */
  getLLMRequiredItems(): AnalysisItemName[] {
    return Object.entries(analyzerMapConfig)
      .filter(([_, config]) => (config as any).requiresLLM)
      .map(([name, _]) => name as AnalysisItemName);
  }

  /**
   * 获取算法分析项
   * @returns 分析项名称列表
   */
  getAlgorithmItems(): AnalysisItemName[] {
    return Object.entries(analyzerMapConfig)
      .filter(([_, config]) => !(config as any).requiresLLM)
      .map(([name, _]) => name as AnalysisItemName);
  }

  /**
   * 获取分析项配置
   * @param itemName 分析项名称
   * @returns 分析项配置
   */
  getItemConfig(itemName: AnalysisItemName): any {
    const config = (analyzerMapConfig as any)[itemName];
    if (!config) {
      throw new Error(`未找到分析项配置: ${itemName}`);
    }
    return config;
  }

  /**
   * 验证分析项是否支持
   * @param itemName 分析项名称
   * @returns 是否支持
   */
  isItemSupported(itemName: string): itemName is AnalysisItemName {
    return itemName in analyzerMapConfig;
  }

  /**
   * 健康检查
   * @returns 是否健康
   */
  async healthCheck(): Promise<boolean> {
    try {
      // 检查LLM客户端
      const llmHealthy = await this.doubaoClient.healthCheck();

      // 检查算法分析器
      const algorithmHealthy = this.algorithmAnalyzers.healthCheck();

      // 检查LLM分析器
      const llmAnalyzersHealthy = this.llmAnalyzers.healthCheck();

      return llmHealthy && algorithmHealthy && llmAnalyzersHealthy;
    } catch (error) {
      console.error("分析器工厂健康检查失败:", error);
      return false;
    }
  }

  /**
   * 获取统计信息
   * @returns 统计信息
   */
  getStats(): {
    totalItems: number;
    algorithmItems: number;
    llmItems: number;
    supportedItems: AnalysisItemName[];
  } {
    const supportedItems = this.getSupportedItems();
    const algorithmItems = this.getAlgorithmItems();
    const llmItems = this.getLLMRequiredItems();

    return {
      totalItems: supportedItems.length,
      algorithmItems: algorithmItems.length,
      llmItems: llmItems.length,
      supportedItems,
    };
  }
}

/**
 * 默认分析器工厂实例
 */
export const analyzerFactory = new AnalyzerFactory();
