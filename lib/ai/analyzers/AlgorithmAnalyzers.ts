/**
 * 算法分析器集合
 * 实现不需要LLM的分析项
 *
 * 职责：
 * - 实现基于算法的分析逻辑
 * - 处理时间、数量、比例类分析
 * - 提供快速、确定性的分析结果
 *
 * 设计原则：
 * - 纯算法实现：不依赖外部AI服务
 * - 高性能：毫秒级响应时间
 * - 确定性：相同输入产生相同输出
 */

import type {
  AnalyzerInput,
  AnalysisItem,
  AnalysisItemName,
  AnalyzerFunction,
} from "@/lib/ai/types";
import type {
  TechStackFileData,
  RepositoryDetailData,
  CommitDetailData,
} from "@/types/github-extended";

// 定义分析器映射配置类型
interface AnalyzerMapItem {
  source: string;
  extractor: string;
  requiresLLM: boolean;
  values: string[];
}

// 导入分析器映射配置
import analyzerMapConfig from "@/docs/v6/analyzer-map.json";

/**
 * 算法分析器类
 */
export class AlgorithmAnalyzers {
  /**
   * 获取算法分析器
   * @param itemName 分析项名称
   * @returns 分析器函数
   */
  getAnalyzer(itemName: AnalysisItemName): AnalyzerFunction {
    const analyzers: Record<string, AnalyzerFunction> = {
      commit_density: this.analyzeCommitDensity.bind(this),
      commit_consistency: this.analyzeCommitConsistency.bind(this),
      commit_length: this.analyzeCommitLength.bind(this),
      active_hours: this.analyzeActiveHours.bind(this),
      weekend_dev: this.analyzeWeekendDev.bind(this),
      long_pause_gaps: this.analyzeLongPauseGaps.bind(this),
      language_diversity: this.analyzeLanguageDiversity.bind(this),
      framework_focus: this.analyzeFrameworkFocus.bind(this),
      experimental_ratio: this.analyzeExperimentalRatio.bind(this),
      star_to_follower_ratio: this.analyzeStarToFollowerRatio.bind(this),
    };

    const analyzer = analyzers[itemName];
    if (!analyzer) {
      throw new Error(`不支持的算法分析项: ${itemName}`);
    }

    return analyzer;
  }

  /**
   * 分析提交密度
   */
  private async analyzeCommitDensity(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const commitsData = extendedData.commits;

      if (!commitsData || commitsData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少提交数据");
      }

      // 计算提交密度（每月平均提交数）
      const totalCommits = commitsData.length;
      const accountAgeMonths = this.calculateAccountAgeMonths(
        input.userData.createdAt
      );
      const commitsPerMonth = totalCommits / Math.max(accountAgeMonths, 1);

      let value: string;

      if (commitsPerMonth < 10) {
        value = "low";
      } else if (commitsPerMonth < 50) {
        value = "medium";
      } else {
        value = "high";
      }

      return {
        name: itemName,
        value,
        source: itemConfig.source,
        usedLLM: false,
        enrichedNarrative: "",
        rawData: { totalCommits, accountAgeMonths, commitsPerMonth },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析提交一致性
   */
  private async analyzeCommitConsistency(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const commitsData = extendedData.commits;

      if (!commitsData || commitsData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少提交数据");
      }

      // 分析提交时间间隔的一致性
      const commitDates = (commitsData as CommitDetailData[])
        .map((commit) => new Date(commit.author.date))
        .sort((a, b) => a.getTime() - b.getTime());

      const intervals = [];
      for (let i = 1; i < commitDates.length; i++) {
        const interval =
          commitDates[i].getTime() - commitDates[i - 1].getTime();
        intervals.push(interval / (1000 * 60 * 60 * 24)); // 转换为天数
      }

      let value: string;

      if (intervals.length === 0) {
        value = "random";
      } else {
        const avgInterval =
          intervals.reduce((sum, interval) => sum + interval, 0) /
          intervals.length;
        const variance =
          intervals.reduce(
            (sum, interval) => sum + Math.pow(interval - avgInterval, 2),
            0
          ) / intervals.length;
        const stdDev = Math.sqrt(variance);
        const coefficientOfVariation = stdDev / avgInterval;

        if (coefficientOfVariation < 0.5) {
          // 寻找连续提交的最长天数
          const maxStreak = this.findMaxCommitStreak(commitDates);
          value = maxStreak > 7 ? `streak: ${maxStreak} days` : "seasonal";
        } else {
          value = "random";
        }
      }

      return {
        name: itemName,
        value,
        source: itemConfig.source,
        usedLLM: false,
        enrichedNarrative: "",
        rawData: { intervals: intervals.slice(0, 10) }, // 只保留前10个间隔用于调试
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析提交长度
   */
  private async analyzeCommitLength(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const commitsData = extendedData.commits;

      if (!commitsData || commitsData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少提交数据");
      }

      // 分析提交消息长度
      const messageLengths = (commitsData as CommitDetailData[])
        .map((commit) => commit.message.length)
        .filter((length: number) => length > 0);

      if (messageLengths.length === 0) {
        return this.createErrorResult(
          itemName,
          itemConfig,
          "没有有效的提交消息"
        );
      }

      const avgLength =
        messageLengths.reduce(
          (sum: number, length: number) => sum + length,
          0
        ) / messageLengths.length;

      let value: string;

      if (avgLength < 20) {
        value = "terse";
      } else if (avgLength < 100) {
        value = "verbose";
      } else {
        value = "poetic";
      }

      return {
        name: itemName,
        value,
        source: itemConfig.source,
        usedLLM: false,
        enrichedNarrative: "",
        rawData: { avgLength, totalMessages: messageLengths.length },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析活跃时间
   */
  private async analyzeActiveHours(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const timeStatsData = extendedData.timeStats;

      if (!timeStatsData || timeStatsData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少时间统计数据");
      }

      // 分析punch card数据 [day, hour, commits]
      const hourlyCommits = new Array(24).fill(0);

      // 合并所有仓库的punch card数据
      timeStatsData.forEach((repoStats) => {
        if (repoStats.punchCard) {
          repoStats.punchCard.forEach(
            ([, hour, commits]: [number, number, number]) => {
              hourlyCommits[hour] += commits;
            }
          );
        }
      });

      // 找到最活跃的时间段
      const maxCommits = Math.max(...hourlyCommits);
      const peakHour = hourlyCommits.indexOf(maxCommits);

      let value: string;

      if (peakHour >= 6 && peakHour <= 10) {
        value = "earlybird";
      } else if (peakHour >= 22 || peakHour <= 2) {
        value = "night_owl";
      } else if (peakHour >= 0 && peakHour <= 5) {
        value = "insomniac";
      } else {
        // 检查是否有多个活跃时段
        const activeHours = hourlyCommits.filter(
          (commits) => commits > maxCommits * 0.5
        ).length;
        value = activeHours > 12 ? "insomniac" : "night_owl";
      }

      return {
        name: itemName,
        value,
        enrichedNarrative: "",
        source: itemConfig.source,
        usedLLM: false,
        rawData: { peakHour, hourlyCommits: hourlyCommits.slice() },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析周末开发
   */
  private async analyzeWeekendDev(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const timeStatsData = extendedData.timeStats;

      if (!timeStatsData || timeStatsData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少时间统计数据");
      }

      // 分析punch card数据 [day, hour, commits]
      // day: 0=Sunday, 1=Monday, ..., 6=Saturday
      const weekdayCommits = new Array(7).fill(0);

      // 合并所有仓库的punch card数据
      timeStatsData.forEach((repoStats) => {
        if (repoStats.punchCard) {
          repoStats.punchCard.forEach(
            ([day, , commits]: [number, number, number]) => {
              weekdayCommits[day] += commits;
            }
          );
        }
      });

      const weekendCommits = weekdayCommits[0] + weekdayCommits[6]; // Sunday + Saturday
      const weekdayCommitsTotal = weekdayCommits
        .slice(1, 6)
        .reduce((sum, commits) => sum + commits, 0);
      const totalCommits = weekendCommits + weekdayCommitsTotal;

      let value: string;

      if (totalCommits === 0) {
        value = "no";
      } else {
        const weekendRatio = weekendCommits / totalCommits;

        if (weekendRatio > 0.7) {
          value = "only_weekends";
        } else if (weekendRatio > 0.3) {
          value = "yes";
        } else {
          value = "no";
        }
      }

      return {
        name: itemName,
        value,
        enrichedNarrative: "",
        source: itemConfig.source,
        usedLLM: false,
        rawData: {
          weekendCommits,
          weekdayCommitsTotal,
          weekendRatio: weekendCommits / Math.max(totalCommits, 1),
        },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析长时间暂停间隔
   */
  private async analyzeLongPauseGaps(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const commitsData = extendedData.commits;

      if (!commitsData || commitsData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少提交数据");
      }

      // 分析提交时间间隔
      const commitDates = (commitsData as CommitDetailData[])
        .map((commit) => new Date(commit.author.date))
        .sort((a, b) => a.getTime() - b.getTime());

      const gaps = [];
      for (let i = 1; i < commitDates.length; i++) {
        const gapDays =
          (commitDates[i].getTime() - commitDates[i - 1].getTime()) /
          (1000 * 60 * 60 * 24);
        gaps.push(gapDays);
      }

      let value: string;

      if (gaps.length === 0) {
        value = "no_gap";
      } else {
        const maxGap = Math.max(...gaps);
        const longGaps = gaps.filter((gap) => gap > 30).length; // 超过30天的间隔

        if (maxGap > 180) {
          // 超过6个月
          value = "huge_gap";
        } else if (longGaps > gaps.length * 0.2) {
          // 超过20%的间隔是长间隔
          value = "occasional";
        } else {
          value = "no_gap";
        }
      }

      return {
        name: itemName,
        value,
        enrichedNarrative: "",
        source: itemConfig.source,
        usedLLM: false,
        rawData: {
          maxGap: Math.max(...gaps, 0),
          longGapsCount: gaps.filter((gap) => gap > 30).length,
        },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析语言多样性
   */
  private async analyzeLanguageDiversity(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { userData } = input;
      const languageStats = userData.languageStats;

      if (
        !languageStats ||
        !languageStats.languages ||
        languageStats.languages.length === 0
      ) {
        return this.createErrorResult(itemName, itemConfig, "缺少语言统计数据");
      }

      const languages = languageStats.languages.map((lang) => lang.language);
      const languageCount = languages.length;

      let value: string;

      if (languageCount === 1) {
        value = "mono";
      } else if (languageCount <= 3) {
        value = "multi:3";
      } else if (languageCount <= 5) {
        value = "multi:5";
      } else {
        value = "chaotic";
      }

      return {
        name: itemName,
        value,
        enrichedNarrative: "",
        source: itemConfig.source,
        usedLLM: false,
        rawData: { languageCount, languages: languages.slice(0, 10) },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析框架专注度
   */
  private async analyzeFrameworkFocus(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const techStackFilesData = extendedData.techStackFiles;

      if (!techStackFilesData || techStackFilesData.length === 0) {
        return this.createErrorResult(
          itemName,
          itemConfig,
          "缺少技术栈文件数据"
        );
      }

      // 分析技术栈文件中的框架使用情况
      const frameworks = this.extractFrameworks(techStackFilesData);

      let value: string;

      if (
        frameworks.react > frameworks.rust &&
        frameworks.react > frameworks.other
      ) {
        value = "react_purist";
      } else if (
        frameworks.rust > frameworks.react &&
        frameworks.rust > frameworks.other
      ) {
        value = "rustacean";
      } else {
        value = "scatter";
      }

      return {
        name: itemName,
        value,
        enrichedNarrative: "",
        source: itemConfig.source,
        usedLLM: false,
        rawData: frameworks,
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 0.8, // 技术栈分析可能不完整
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析实验性比例
   */
  private async analyzeExperimentalRatio(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { extendedData } = input;
      const repositoriesData = extendedData.repositories;

      if (!repositoriesData || repositoriesData.length === 0) {
        return this.createErrorResult(itemName, itemConfig, "缺少仓库数据");
      }

      // 分析实验性仓库的比例
      const experimentalCount = this.countExperimentalRepos(repositoriesData);
      const totalRepos = repositoriesData.length;
      const experimentalRatio = experimentalCount / totalRepos;

      let value: string;

      if (experimentalRatio < 0.3) {
        value = "low";
      } else if (experimentalRatio < 0.6) {
        value = "moderate";
      } else {
        value = "high";
      }

      return {
        name: itemName,
        value,
        enrichedNarrative: "",
        source: itemConfig.source,
        usedLLM: false,
        rawData: { experimentalCount, totalRepos, experimentalRatio },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 分析星标与关注者比例
   */
  private async analyzeStarToFollowerRatio(
    input: AnalyzerInput,
    itemName: AnalysisItemName
  ): Promise<AnalysisItem> {
    const startTime = Date.now();
    const itemConfig = (analyzerMapConfig as Record<string, AnalyzerMapItem>)[
      itemName
    ];

    try {
      const { userData } = input;
      const totalStars = userData.totalStars || 0;
      const followers = userData.followers || 0;

      let value: string;

      if (followers === 0) {
        value = totalStars > 0 ? "stealthy" : "asymmetrical";
      } else {
        const ratio = totalStars / followers;

        if (ratio < 2) {
          value = "popular";
        } else if (ratio < 10) {
          value = "stealthy";
        } else {
          value = "asymmetrical";
        }
      }

      return {
        name: itemName,
        value,
        enrichedNarrative: "",
        source: itemConfig.source,
        usedLLM: false,
        rawData: {
          totalStars,
          followers,
          ratio: followers > 0 ? totalStars / followers : 0,
        },
        metadata: {
          processingTime: Date.now() - startTime,
          dataCompleteness: 1.0,
        },
      };
    } catch (error) {
      return this.createErrorResult(itemName, itemConfig, error);
    }
  }

  /**
   * 计算账户年龄（月数）
   */
  private calculateAccountAgeMonths(createdAt: number): number {
    const created = new Date(createdAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30));
    return diffMonths;
  }

  /**
   * 找到最长连续提交天数
   */
  private findMaxCommitStreak(commitDates: Date[]): number {
    if (commitDates.length === 0) return 0;

    let maxStreak = 1;
    let currentStreak = 1;

    for (let i = 1; i < commitDates.length; i++) {
      const daysDiff = Math.floor(
        (commitDates[i].getTime() - commitDates[i - 1].getTime()) /
          (1000 * 60 * 60 * 24)
      );

      if (daysDiff <= 1) {
        currentStreak++;
        maxStreak = Math.max(maxStreak, currentStreak);
      } else {
        currentStreak = 1;
      }
    }

    return maxStreak;
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(
    itemName: AnalysisItemName,
    itemConfig: AnalyzerMapItem,
    error: unknown
  ): AnalysisItem {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      name: itemName,
      value: "error",
      source: itemConfig.source,
      usedLLM: false,
      enrichedNarrative: "分析失败，无法生成描述性内容",
      metadata: {
        processingTime: 0,
        dataCompleteness: 0,
        error: errorMessage,
      },
    };
  }

  /**
   * 提取框架信息
   */
  private extractFrameworks(techStackFilesData: TechStackFileData[]): {
    react: number;
    rust: number;
    other: number;
  } {
    const frameworks = { react: 0, rust: 0, other: 0 };

    try {
      // 处理技术栈文件数组，解码base64内容
      let filesContent = "";
      techStackFilesData.forEach((file) => {
        try {
          const decodedContent = atob(file.content || "");
          filesContent += decodedContent + " ";
        } catch {
          // 如果解码失败，跳过该文件
          console.warn(`Failed to decode content for file: ${file.name}`);
        }
      });

      filesContent = filesContent.toLowerCase();

      // 检测React相关
      if (
        filesContent.includes("react") ||
        filesContent.includes("jsx") ||
        filesContent.includes("tsx")
      ) {
        frameworks.react++;
      }

      // 检测Rust相关
      if (
        filesContent.includes("rust") ||
        filesContent.includes("cargo") ||
        filesContent.includes(".rs")
      ) {
        frameworks.rust++;
      }

      // 其他框架
      const otherFrameworks = [
        "vue",
        "angular",
        "svelte",
        "python",
        "java",
        "go",
        "php",
      ];
      for (const framework of otherFrameworks) {
        if (filesContent.includes(framework)) {
          frameworks.other++;
          break; // 只计算一次
        }
      }
    } catch (error) {
      console.error("提取框架信息失败:", error);
    }

    return frameworks;
  }

  /**
   * 计算实验性仓库数量
   */
  private countExperimentalRepos(
    repositoriesData: RepositoryDetailData[]
  ): number {
    let experimentalCount = 0;

    for (const repo of repositoriesData) {
      try {
        const name = (repo.name || "").toLowerCase();
        const description = (repo.description || "").toLowerCase();
        const topics = (repo.topics || []).map((t: string) => t.toLowerCase());

        // 实验性关键词
        const experimentalKeywords = [
          "test",
          "demo",
          "example",
          "playground",
          "experiment",
          "prototype",
          "poc",
          "trial",
          "sample",
          "learning",
          "tutorial",
          "practice",
        ];

        const isExperimental = experimentalKeywords.some(
          (keyword) =>
            name.includes(keyword) ||
            description.includes(keyword) ||
            topics.includes(keyword)
        );

        if (isExperimental) {
          experimentalCount++;
        }
      } catch (error) {
        console.error("分析仓库实验性失败:", error);
      }
    }

    return experimentalCount;
  }

  /**
   * 健康检查
   */
  healthCheck(): boolean {
    return true; // 算法分析器总是健康的
  }
}
