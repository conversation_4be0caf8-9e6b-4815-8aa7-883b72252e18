/**
 * 技术栈分析器
 * 基于GitHub扩展数据中的技术栈文件进行分析
 */

import type {
  GitHubExtendedData,
  TechStackFileData,
} from "@/types/github-extended";

export interface TechStackAnalysisResult {
  // 技术广度类 - 对应原始需求中的分析内容
  framework_focus: "react_purist" | "rustacean" | "scatter";

  // 分析元数据
  confidence: number;
  dataCompleteness: number;
  analysisTimestamp: number;

  // 详细信息
  detectedFrameworks: string[];
  primaryLanguages: string[];
  techStackFiles: {
    fileType: string;
    repository: string;
    hasContent: boolean;
  }[];
}

export class TechStackAnalyzer {
  /**
   * 分析技术栈特征
   */
  static analyze(extendedData?: GitHubExtendedData): TechStackAnalysisResult {
    const result: TechStackAnalysisResult = {
      framework_focus: this.analyzeFrameworkFocus(extendedData),
      confidence: this.calculateConfidence(extendedData),
      dataCompleteness: this.calculateDataCompleteness(extendedData),
      analysisTimestamp: Date.now(),
      detectedFrameworks: this.detectFrameworks(extendedData),
      primaryLanguages: this.detectPrimaryLanguages(extendedData),
      techStackFiles: this.summarizeTechStackFiles(extendedData),
    };

    return result;
  }

  /**
   * 分析框架专注度
   */
  private static analyzeFrameworkFocus(
    extendedData?: GitHubExtendedData
  ): "react_purist" | "rustacean" | "scatter" {
    if (!extendedData?.techStackFiles?.length) return "scatter";

    const frameworks = this.detectFrameworks(extendedData);
    const languages = this.detectPrimaryLanguages(extendedData);

    // React专家判断
    const reactIndicators = frameworks.filter(
      (f) =>
        f.toLowerCase().includes("react") ||
        f.toLowerCase().includes("next") ||
        f.toLowerCase().includes("gatsby")
    ).length;

    if (reactIndicators >= 2 && languages.includes("JavaScript")) {
      return "react_purist";
    }

    // Rust专家判断
    if (
      languages.includes("Rust") &&
      frameworks.some((f) =>
        ["tokio", "serde", "actix", "warp", "rocket"].some((rust) =>
          f.toLowerCase().includes(rust)
        )
      )
    ) {
      return "rustacean";
    }

    return "scatter";
  }

  /**
   * 检测框架
   */
  private static detectFrameworks(extendedData?: GitHubExtendedData): string[] {
    if (!extendedData?.techStackFiles?.length) return [];

    const frameworks = new Set<string>();

    for (const file of extendedData.techStackFiles) {
      try {
        const content = this.decodeBase64Content(file.content);

        switch (file.fileType) {
          case "package.json":
            this.extractNpmFrameworks(content, frameworks);
            break;
          case "Cargo.toml":
            this.extractRustFrameworks(content, frameworks);
            break;
          case "pyproject.toml":
          case "requirements.txt":
            this.extractPythonFrameworks(content, frameworks);
            break;
          case "go.mod":
            this.extractGoFrameworks(content, frameworks);
            break;
        }
      } catch (error) {
        console.warn(
          `Failed to parse ${file.fileType} for ${file.repository}:`,
          error
        );
      }
    }

    return Array.from(frameworks);
  }

  /**
   * 检测主要编程语言
   */
  private static detectPrimaryLanguages(
    extendedData?: GitHubExtendedData
  ): string[] {
    if (!extendedData?.repositories?.length) return [];

    const languageCount = new Map<string, number>();

    for (const repo of extendedData.repositories) {
      if (repo.language) {
        languageCount.set(
          repo.language,
          (languageCount.get(repo.language) || 0) + 1
        );
      }
    }

    return Array.from(languageCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([lang]) => lang);
  }

  /**
   * 汇总技术栈文件信息
   */
  private static summarizeTechStackFiles(
    extendedData?: GitHubExtendedData
  ): TechStackAnalysisResult["techStackFiles"] {
    if (!extendedData?.techStackFiles?.length) return [];

    return extendedData.techStackFiles.map((file) => ({
      fileType: file.fileType,
      repository: file.repository,
      hasContent: Boolean(file.content && file.content.length > 0),
    }));
  }

  /**
   * 解码Base64内容
   */
  private static decodeBase64Content(content: string): string {
    try {
      return Buffer.from(content, "base64").toString("utf-8");
    } catch {
      return content; // 如果不是Base64，直接返回
    }
  }

  /**
   * 提取NPM框架
   */
  private static extractNpmFrameworks(
    content: string,
    frameworks: Set<string>
  ): void {
    try {
      const packageJson = JSON.parse(content);
      const deps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
      };

      const frameworkKeywords = [
        "react",
        "vue",
        "angular",
        "svelte",
        "next",
        "nuxt",
        "gatsby",
        "express",
        "koa",
        "fastify",
        "nest",
        "electron",
        "react-native",
      ];

      for (const dep of Object.keys(deps || {})) {
        for (const keyword of frameworkKeywords) {
          if (dep.toLowerCase().includes(keyword)) {
            frameworks.add(dep);
          }
        }
      }
    } catch {
      // 解析失败，忽略
    }
  }

  /**
   * 提取Rust框架
   */
  private static extractRustFrameworks(
    content: string,
    frameworks: Set<string>
  ): void {
    const rustFrameworks = [
      "tokio",
      "serde",
      "actix-web",
      "warp",
      "rocket",
      "axum",
      "diesel",
      "sqlx",
      "clap",
      "reqwest",
    ];

    for (const framework of rustFrameworks) {
      if (content.includes(`"${framework}"`)) {
        frameworks.add(framework);
      }
    }
  }

  /**
   * 提取Python框架
   */
  private static extractPythonFrameworks(
    content: string,
    frameworks: Set<string>
  ): void {
    const pythonFrameworks = [
      "django",
      "flask",
      "fastapi",
      "tornado",
      "pyramid",
      "numpy",
      "pandas",
      "tensorflow",
      "pytorch",
      "scikit-learn",
    ];

    for (const framework of pythonFrameworks) {
      if (content.toLowerCase().includes(framework)) {
        frameworks.add(framework);
      }
    }
  }

  /**
   * 提取Go框架
   */
  private static extractGoFrameworks(
    content: string,
    frameworks: Set<string>
  ): void {
    const goFrameworks = [
      "gin-gonic/gin",
      "gorilla/mux",
      "echo",
      "fiber",
      "beego",
      "gorm.io/gorm",
      "go-redis/redis",
    ];

    for (const framework of goFrameworks) {
      if (content.includes(framework)) {
        frameworks.add(framework.split("/").pop() || framework);
      }
    }
  }

  /**
   * 计算分析置信度
   */
  private static calculateConfidence(
    extendedData?: GitHubExtendedData
  ): number {
    let confidence = 0.3; // 基础置信度

    if (extendedData?.techStackFiles?.length) {
      confidence += Math.min(0.4, extendedData.techStackFiles.length * 0.1);
    }

    if (extendedData?.repositories?.length) {
      confidence += Math.min(0.2, extendedData.repositories.length * 0.02);
    }

    return Math.min(0.95, confidence);
  }

  /**
   * 计算数据完整性
   */
  private static calculateDataCompleteness(
    extendedData?: GitHubExtendedData
  ): number {
    let completeness = 0.2; // 基础完整性

    if (extendedData?.techStackFiles?.length) {
      completeness += 0.5;
    }

    if (extendedData?.repositories?.length) {
      completeness += 0.3;
    }

    return Math.min(1.0, completeness);
  }
}
