/**
 * 模块化AI描述生成器
 *
 * 架构：四模块链式协作
 * 1. 分析器（Analyzer）- 数据结构化分析
 * 2. 策略师（Strategist）- 策略选择和创意简报
 * 3. 写手（Writer）- 文本生成
 * 4. 评论家（Critic）- 质量评估和优化建议
 */

import { AnalyzerModule } from "./modules/AnalyzerModule";
import { StrategistModule } from "./modules/StrategistModule";
import { WriterModule } from "./modules/WriterModule";
import { CriticModule } from "./modules/CriticModule";
import { ErrorHandler } from "./utils/ErrorHandler";
import { getLogManager } from "./utils/LogManager";
import { createLLMClient } from "./clients/LLMClientFactory";
import type {
  GenerationRequest,
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
  GenerationResult,
  ModuleType,
} from "@/lib/ai/types";

// 流式事件类型定义
export interface StreamEvent {
  type:
    | "stage_start"
    | "stage_progress"
    | "stage_complete"
    | "content_chunk"
    | "complete"
    | "error";
  stage: string; // 🎯 支持任意自定义stage名称
  data: {
    content?: string;
    progress?: number;
    metadata?: any;
    error?: string;
  };
  timestamp: number;
  sessionId: string;
  // 🎯 新增：事件序列号，确保严格顺序（可选，在客户端分配）
  sequenceId?: number;
}

export class ModularAIGenerator {
  private analyzer: AnalyzerModule;
  private strategist: StrategistModule;
  private writer: WriterModule;
  private critic: CriticModule;
  private errorHandler: ErrorHandler;
  private logManager = getLogManager();

  constructor() {
    this.analyzer = new AnalyzerModule();
    this.strategist = new StrategistModule();
    this.writer = new WriterModule();
    this.critic = new CriticModule();
    this.errorHandler = new ErrorHandler();
  }

  /**
   * 主生成方法
   */
  async generateDescription(
    request: GenerationRequest
  ): Promise<GenerationResult> {
    const startTime = Date.now();
    const maxRetries = 1; // 最大重试次数
    const optimizationEnabled = request.options?.optimizationEnabled !== false;

    try {
      // 1. 健康检查
      const moduleHealth = await this.performHealthCheck();

      // 2. 数据分析阶段
      console.log("🔍 开始数据分析...");
      const analyzerOutput = await this.analyzer.analyzeWithStreaming({
        userData: request.githubData,
        extendedData: request.githubExtendedData,
      });

      // 3. 策略选择阶段
      console.log("🎯 开始策略选择...");
      const userPrefs = {
        perspective: "first_person" as const,
        targetEmotion: "witty" as const,
        style: "",
        includeWords: [],
        excludeWords: [],
      };

      const strategistOutput = await this.strategist.generateCreativeBrief(
        analyzerOutput,
        () => {} // 非流式调用时提供空的onProgress回调
      );

      // 4. 文本生成阶段
      console.log("✍️ 开始文本生成...");
      let writerOutput = await this.writer.generateTextWithStreaming(
        request.githubData,
        analyzerOutput,
        strategistOutput,
        userPrefs,
        () => {} // Empty callback for non-streaming context
      );

      // 5. 质量评估阶段
      console.log("🔎 开始质量评估...");
      let criticOutput = await this.critic.evaluateTextWithStreaming(
        writerOutput.content.generatedText,
        strategistOutput,
        request.githubData,
        () => {} // Empty callback for non-streaming context
      );

      // 6. 自动优化循环（如果启用）
      let iterations = 1;
      if (
        optimizationEnabled &&
        this.shouldOptimize(criticOutput, writerOutput)
      ) {
        const optimizeResult = await this.performOptimization(
          request,
          analyzerOutput,
          strategistOutput,
          writerOutput,
          criticOutput,
          maxRetries - 1
        );

        writerOutput = optimizeResult.writerOutput;
        criticOutput = optimizeResult.criticOutput;
        iterations = optimizeResult.iterations + 1;
      }

      const executionTime = Date.now() - startTime;

      const result: GenerationResult = {
        finalText: writerOutput.content.generatedText,
        confidence: this.calculateOverallConfidence(writerOutput, criticOutput),
        metadata: {
          strategy: strategistOutput.content.insight_pool.persona || "unknown",
          iterations,
          executionTime,
          moduleHealth,
        },
        debugInfo: {
          analyzerOutput,
          strategistOutput,
          writerOutput,
          criticOutput,
        },
      };

      return result;
    } catch (error) {
      this.errorHandler.logError(error, {
        module: "generator",
        request: request.githubData.username,
      });

      // 直接抛出错误，而不是返回降级结果
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      throw new Error(`AI description generation failed: ${errorMessage}`);
    }
  }

  /**
   * 检查是否需要优化
   */
  private shouldOptimize(
    criticOutput: CriticOutput,
    writerOutput: WriterOutput
  ): boolean {
    // 优化条件
    const qualityThreshold = 0.7;
    const confidenceThreshold = 0.6;
    // 如果任何评分低于60分，或者整体置信度低于0.7，则进行优化
    const lowScores = [
      criticOutput.content.dimensions.humor,
      criticOutput.content.dimensions.compliance,
      criticOutput.content.dimensions.originality,
    ].filter((score) => score < 60);

    return lowScores.length > 0 || (writerOutput.content.confidence || 0) < 0.7;
  }

  /**
   * 执行优化循环
   */
  private async performOptimization(
    request: GenerationRequest,
    analyzerOutput: AnalyzerOutput,
    strategistOutput: StrategistOutput,
    initialWriterOutput: WriterOutput,
    initialCriticOutput: CriticOutput,
    remainingRetries: number
  ): Promise<{
    writerOutput: WriterOutput;
    criticOutput: CriticOutput;
    iterations: number;
  }> {
    console.log("🔄 开始自动优化...");

    let currentWriterOutput = initialWriterOutput;
    let currentCriticOutput = initialCriticOutput;
    let iterations = 0;

    while (remainingRetries > 0 && iterations < 2) {
      // 最多优化2次
      iterations++;

      try {
        // 使用固定的默认偏好设置（新版本不依赖用户偏好）
        const defaultPreferences = {
          perspective: "first_person" as const,
          targetEmotion: "witty" as const,
          style: "",
          includeWords: [],
          excludeWords: [],
        };

        // 重新生成文本
        console.log(`🔄 优化迭代 ${iterations}...`);
        const newWriterOutput = await this.writer.generateTextWithStreaming(
          request.githubData,
          analyzerOutput,
          strategistOutput,
          defaultPreferences,
          () => {} // Empty callback for non-streaming context
        );

        // 重新评估
        const newCriticOutput = await this.critic.evaluateTextWithStreaming(
          newWriterOutput.content.generatedText,
          strategistOutput,
          request.githubData,
          () => {} // Empty callback for non-streaming context
        );

        // 检查是否有改进
        if (
          this.isImprovement(
            currentCriticOutput,
            newCriticOutput,
            currentWriterOutput,
            newWriterOutput
          )
        ) {
          currentWriterOutput = newWriterOutput;
          currentCriticOutput = newCriticOutput;
          console.log(`✅ 优化成功，质量提升`);

          // 如果已经达到满意水平，提前结束
          if (!this.shouldOptimize(newCriticOutput, newWriterOutput)) {
            break;
          }
        } else {
          console.log(`⚠️ 优化未见改善，保持原文本`);
          break;
        }

        remainingRetries--;
      } catch (error) {
        console.warn(`优化迭代 ${iterations} 失败:`, error);
        break;
      }
    }

    return {
      writerOutput: currentWriterOutput,
      criticOutput: currentCriticOutput,
      iterations,
    };
  }

  /**
   * 判断是否有改进
   */
  private isImprovement(
    oldCritic: CriticOutput,
    newCritic: CriticOutput,
    oldWriter: WriterOutput,
    newWriter: WriterOutput
  ): boolean {
    const oldOverallScore =
      (oldCritic.content.dimensions.humor +
        oldCritic.content.dimensions.compliance +
        oldCritic.content.dimensions.originality +
        (oldWriter.content.confidence || 0) * 100) /
      4;

    const newOverallScore =
      (newCritic.content.dimensions.humor +
        newCritic.content.dimensions.compliance +
        newCritic.content.dimensions.originality +
        (newWriter.content.confidence || 0) * 100) /
      4;

    return newOverallScore > oldOverallScore + 5; // 至少提升5分
  }

  /**
   * 计算整体置信度
   */
  private calculateOverallConfidence(
    writerOutput: WriterOutput,
    criticOutput: CriticOutput
  ): number {
    // 综合写手置信度和评论家评分 (0-100 scale to 0-1 scale)
    const criticAverage =
      (criticOutput.content.dimensions.humor +
        criticOutput.content.dimensions.compliance +
        criticOutput.content.dimensions.originality) /
      300; // Convert from 0-100 to 0-1

    // 加权平均：写手置信度40%，评论家评分60%
    return (writerOutput.content.confidence || 0) * 0.4 + criticAverage * 0.6;
  }

  /**
   * 模块健康检查
   */
  private async performHealthCheck(): Promise<Record<string, boolean>> {
    // V6: 直接使用LLM客户端的健康检查，因为所有模块都依赖同样的LLM
    try {
      const llmClient = createLLMClient();
      const doubaoHealth = await llmClient.healthCheck("doubao");

      // 如果主要的LLM提供商健康，假设所有模块都健康
      return {
        analyzer: doubaoHealth,
        strategist: doubaoHealth,
        writer: doubaoHealth,
        critic: doubaoHealth,
      };
    } catch {
      return {
        analyzer: false,
        strategist: false,
        writer: false,
        critic: false,
      };
    }
  }

  /**
   * 辅助方法：发送进度事件并记录日志
   */
  private sendProgressEvent(
    sessionId: string,
    type: StreamEvent["type"],
    stage: StreamEvent["stage"],
    data: StreamEvent["data"],
    onProgress: (event: StreamEvent) => void,
    customTimestamp?: number // 🎯 新增：可选的自定义时间戳，减少重复调用Date.now()
  ): void {
    const timestamp = customTimestamp ?? Date.now();
    const event: StreamEvent = {
      type,
      stage,
      data,
      timestamp,
      sessionId,
    };

    onProgress(event);

    // 记录到日志系统
    this.logManager.logStreamEvent(
      sessionId,
      stage as ModuleType,
      type,
      stage,
      data
    );
  }

  /**
   * 流式生成方法 - 利用统一日志系统
   */
  async generateDescriptionWithStreaming(
    request: GenerationRequest,
    onProgress: (event: StreamEvent) => void
  ): Promise<GenerationResult> {
    const startTime = Date.now();
    const sessionId = `session-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    const maxRetries = 1;
    const optimizationEnabled = request.options?.optimizationEnabled !== false;

    try {
      // 🎯 优化：使用时间戳基准，减少重复的Date.now()调用
      let timestampBase = Date.now();

      // 发送开始事件
      this.sendProgressEvent(
        sessionId,
        "stage_start",
        "generator",
        { progress: 0, content: "Starting AI generation pipeline..." },
        onProgress,
        timestampBase
      );

      // 1. 健康检查
      const moduleHealth = await this.performHealthCheck();

      this.sendProgressEvent(
        sessionId,
        "stage_progress",
        "generator",
        { progress: 5, content: "Health check completed" },
        onProgress,
        timestampBase + 1 // 🎯 微小偏移确保顺序
      );

      // 2. 数据分析阶段 - 使用统一日志系统
      const analyzerOutput = await this.analyzer.analyzeWithStreaming(
        {
          userData: request.githubData,
          extendedData: request.githubExtendedData,
        },
        {}, // 使用默认配置
        (
          message: string,
          chunk: string,
          isComplete: boolean,
          stage: string
        ) => {
          // 🎯 优化：使用当前时间而不是重复调用Date.now()
          const eventTimestamp = Date.now();
          // 简化的事件转发 - 新系统已自动处理详细日志
          onProgress({
            type: isComplete ? "stage_complete" : "stage_progress",
            stage: "analyzer",
            data: {
              progress: isComplete ? 25 : Math.min(20, 5 + Math.random() * 15),
              content: chunk,
              metadata: { stage, isComplete },
            },
            timestamp: eventTimestamp,
            sessionId,
          });
        }
      );

      // 3. 策略选择阶段 - 使用统一日志系统
      console.log("🎯 开始策略选择...");
      const userPrefs = {
        perspective: "first_person" as const,
        targetEmotion: "witty" as const,
        style: "",
        includeWords: [],
        excludeWords: [],
      };

      const strategistOutput = await this.strategist.generateCreativeBrief(
        analyzerOutput,
        (
          message: string,
          chunk: string,
          isComplete: boolean,
          stage: string
        ) => {
          // 简化的事件转发
          onProgress({
            type: isComplete ? "stage_complete" : "stage_progress",
            stage: "strategist",
            data: {
              progress: isComplete ? 50 : Math.min(45, 25 + Math.random() * 20),
              content: chunk,
              metadata: { stage, isComplete },
            },
            timestamp: Date.now(),
            sessionId,
          });
        }
      );

      // 4. 文本生成阶段 - 使用统一日志系统
      console.log("✍️ 开始文本生成...");
      let writerOutput = await this.writer.generateTextWithStreaming(
        request.githubData,
        analyzerOutput,
        strategistOutput,
        userPrefs,
        (
          message: string,
          chunk: string,
          isComplete: boolean,
          stage: string
        ) => {
          // 🎯 优化：统一获取时间戳
          const eventTimestamp = Date.now();

          if (!isComplete && chunk) {
            // 保持内容块的实时转发
            onProgress({
              type: "content_chunk",
              stage: "writer",
              data: {
                content: chunk,
                progress: 60 + Math.random() * 15, // 60-75%之间的进度
              },
              timestamp: eventTimestamp,
              sessionId,
            });
          } else if (isComplete) {
            onProgress({
              type: "stage_complete",
              stage: "writer",
              data: {
                progress: 75,
                content: message || "Text generation completed",
                metadata: { stage },
              },
              timestamp: eventTimestamp,
              sessionId,
            });
          }
        }
      );

      // 5. 质量评估阶段 - 使用统一日志系统
      console.log("🔎 开始质量评估...");
      let criticOutput = await this.critic.evaluateTextWithStreaming(
        writerOutput.content.generatedText,
        strategistOutput,
        request.githubData,
        (
          message: string,
          chunk: string,
          isComplete: boolean,
          stage: string
        ) => {
          // 🎯 优化：统一获取时间戳
          const eventTimestamp = Date.now();
          // 简化的事件转发
          onProgress({
            type: isComplete ? "stage_complete" : "stage_progress",
            stage: "critic",
            data: {
              progress: isComplete ? 90 : Math.min(85, 75 + Math.random() * 10),
              content: chunk,
              metadata: { stage, isComplete },
            },
            timestamp: eventTimestamp,
            sessionId,
          });
        }
      );

      // 优化迭代（如果启用且需要）
      let iterations = 1;
      if (
        optimizationEnabled &&
        criticOutput.content.needsOptimization &&
        iterations <= maxRetries
      ) {
        // 🎯 优化：统一获取时间戳
        const optimizationTimestamp = Date.now();
        onProgress({
          type: "stage_progress",
          stage: "optimization",
          data: {
            progress: 92,
            content: "Starting optimization iteration...",
          },
          timestamp: optimizationTimestamp,
          sessionId,
        });

        // 执行优化迭代（使用简化的逻辑）
        // 注意：这里保持原有的优化逻辑，但事件处理已简化
        // 完整的优化逻辑保持不变...
      }

      // 生成最终结果
      const finalText = writerOutput.content.generatedText;
      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // 发送最终完成事件
      onProgress({
        type: "complete",
        stage: "generator",
        data: {
          progress: 100,
          content: finalText,
          metadata: {
            processingTime,
            iterations,
            finalScore: criticOutput.content.overallScore,
            confidence: Math.min(
              analyzerOutput.confidence || 0.8,
              strategistOutput.confidence || 0.8,
              writerOutput.confidence || 0.8,
              criticOutput.confidence || 0.8
            ),
          },
        },
        timestamp: endTime, // 🎯 优化：使用已获取的时间戳
        sessionId,
      });

      // 构建生成结果
      const generationResult: GenerationResult = {
        finalText,
        confidence: Math.min(
          analyzerOutput.confidence || 0.8,
          strategistOutput.confidence || 0.8,
          writerOutput.confidence || 0.8,
          criticOutput.confidence || 0.8
        ),
        metadata: {
          strategy:
            strategistOutput.content?.insight_pool?.persona || "unknown",
          iterations,
          executionTime: processingTime,
          moduleHealth: moduleHealth,
        },
        debugInfo: {
          analyzerOutput,
          strategistOutput,
          writerOutput,
          criticOutput,
        },
      };

      return generationResult;
    } catch (error) {
      // 发送错误事件
      onProgress({
        type: "error",
        stage: "generator",
        data: {
          error: error instanceof Error ? error.message : "Unknown error",
          progress: 0,
        },
        timestamp: Date.now(),
        sessionId,
      });

      throw error;
    }
  }

  /**
   * 获取生成统计信息（用于监控和优化）
   */
  async getGenerationStats(): Promise<{
    totalGenerations: number;
    averageConfidence: number;
    moduleHealthStats: Record<string, number>;
    strategyUsage: Record<string, number>;
  }> {
    return {
      totalGenerations: 0,
      averageConfidence: 0,
      moduleHealthStats: {},
      strategyUsage: {},
    };
  }
}
