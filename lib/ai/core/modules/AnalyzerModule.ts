/**
 * GitHub 数据分析器
 * 基于 analyzer-map.json 实现12项分析功能
 *
 * 职责：分析 UserData + GitHubExtendedData，输出结构化分析结果
 * 策略：算法分析 + LLM分析的混合模式
 *
 * 设计原则：
 * - 类型安全优先：严格的 TypeScript 类型定义
 * - 约定大于配置：提供合理的默认值
 * - 尽早报错：明确的参数校验和错误处理
 */

import { BaseModule } from "./BaseModule";
import { createStreamlineLogger } from "../utils/StreamlineLogger";
import type {
  AnalyzerOutput,
  AnalyzerConfig,
  AnalyzerInput,
  AnalysisItem,
  AnalysisItemName,
} from "../../types";
import { DEFAULT_ANALYZER_CONFIG } from "@/types/analyzer";
import { AnalyzerFactory } from "../../analyzers/AnalyzerFactory";
import {
  createSelfDescribingData,
  callProgressCallback,
} from "../utils/AdaptiveMetadataDisplayManager";
import {
  type SemanticTagsResult,
  type SimplifiedEnrichedNarrativeResult,
  SemanticTagsConfig,
  SimplifiedEnrichedNarrativeConfig,
} from "../../schemas";

export class AnalyzerModule extends BaseModule {
  private analyzerFactory: AnalyzerFactory;

  constructor() {
    super("analyzer", "analyzer", {
      timeout: 60000,
      enableLogging: true,
      enableMetrics: true,
    });
    this.analyzerFactory = new AnalyzerFactory();
  }

  /**
   * 主分析方法 - 基于 analyzer-map.json 的混合分析 + 语义标签提取
   */
  async analyzeWithStreaming(
    input: AnalyzerInput,
    config: AnalyzerConfig = DEFAULT_ANALYZER_CONFIG,
    onProgress?: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<AnalyzerOutput> {
    const startTime = Date.now();

    return this.executeWithWrapper(
      async () => {
        const logger = createStreamlineLogger(
          this.config.debugSessionId || `analyzer-session-${Date.now()}`,
          "analyzer"
        );

        try {
          // Stage 1: 数据验证
          this.validateInput(input);

          // 🚀 应用自描述协议优化数据验证结果展示
          const validateResult = createSelfDescribingData(
            {
              status: "ok",
              username: input.userData.username,
            },
            {
              title: "🔍 输入数据验证完成",
              color: "#10b981",
            }
          );

          // 使用重载的 onProgress 支持自描述数据
          callProgressCallback(
            onProgress,
            "输入数据验证完成",
            validateResult,
            true,
            "data_validation"
          );

          // Stage 2: 执行分析项
          const results = await logger.executeStage("执行分析项", async () => {
            // 🚀 创建进度自描述数据
            const initProgress = createSelfDescribingData("--> 10%", {
              title: "📊 分析进度: 10%",
              color: "#3b82f6",
            });

            callProgressCallback(
              onProgress,
              "开始初始化分析器",
              initProgress,
              false,
              "analysis_progress"
            );

            await new Promise((resolve) => setTimeout(resolve, 100));

            const analysisResults = await this.executeAnalysisItems(
              input,
              config,
              onProgress
            );

            await new Promise((resolve) => setTimeout(resolve, 100));
            // 🚀 创建完成自描述数据
            const completionProgress = createSelfDescribingData("--> 100%", {
              title: `✅ 分析完成: ${analysisResults.length}项`,
              color: "#10b981",
            });

            callProgressCallback(
              onProgress,
              `✅ 分析完成:${analysisResults.length}个分析项`,
              completionProgress,
              true,
              "analysis_progress"
            );

            await new Promise((resolve) => setTimeout(resolve, 100));
            // 🚀 创建完成自描述数据
            const completionData = createSelfDescribingData(analysisResults, {
              type: "json",
              title: `✅ 分析结果`,
            });

            callProgressCallback(
              onProgress,
              `分析结果`,
              completionData,
              true,
              "analysis_complete"
            );

            return analysisResults;
          });

          // Stage 3: enrichedNarrative 补全
          const enrichedResults = await logger.executeStage(
            "enrichedNarrative补全",
            async () => {
              const enrichedAnalysisResults =
                await this.enrichAnalysisItemsWithNarrative(
                  results,
                  onProgress
                );

              return enrichedAnalysisResults;
            }
          );

          // Stage 4: 语义标签提取
          const semanticTags = await logger.executeStage(
            "语义标签提取",
            async () => {
              // 🚀 改为流式版本 - 支持实时打字机效果
              const extractedTags = await this.extractSemanticTagsWithStreaming(
                enrichedResults,
                (message, chunk, isComplete, stage) => {
                  // 🎯 立即转发流式内容到外部回调
                  callProgressCallback(
                    onProgress,
                    message,
                    chunk,
                    isComplete,
                    stage
                  );
                }
              );

              onProgress?.("提取语义标签完成", "💯", true, "semantic_tags");

              return extractedTags;
            }
          );

          // Stage 5: 生成最终结果
          const finalOutput = await logger.executeStage(
            "生成最终结果",
            async () => {
              const output = this.buildFinalOutput(
                enrichedResults,
                semanticTags,
                Date.now() - startTime
              );
              return output;
            }
          );

          return finalOutput;
        } catch (error) {
          // 🚀 应用自描述协议优化错误信息展示
          const errorInfo = createSelfDescribingData(
            {
              errorType: "analysis_error",
              message: error instanceof Error ? error.message : "未知错误",
              stage: "analyzer_execution",
              timestamp: new Date().toISOString(),
              username: input?.userData?.username || "unknown",
            },
            {
              title: "❌ 分析过程发生错误",
              color: "#ef4444",
            }
          );

          callProgressCallback(
            onProgress,
            "分析过程中发生错误",
            errorInfo,
            true,
            "analysis_error"
          );

          throw error;
        }
      },
      { input, config }
    );
  }

  /**
   * 验证输入数据
   */
  private validateInput(input: AnalyzerInput): void {
    if (!input) {
      throw new Error("分析输入数据不能为空");
    }

    if (!input.userData) {
      throw new Error("用户数据不能为空");
    }

    if (!input.userData.username) {
      throw new Error("用户名不能为空");
    }

    if (!input.extendedData) {
      throw new Error("扩展数据不能为空");
    }
  }

  /**
   * 执行所有分析项
   */
  private async executeAnalysisItems(
    input: AnalyzerInput,
    config: AnalyzerConfig,
    onProgress?: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<AnalysisItem[]> {
    // 获取要分析的项目列表
    const itemsToAnalyze = this.getItemsToAnalyze(config);

    // 使用AnalyzerFactory批量执行分析
    return await this.analyzerFactory.analyzeMultiple(
      input,
      itemsToAnalyze,
      (result) => {
        const completionProgress = createSelfDescribingData(
          ` --> ${result.name} 分析完成`
        );
        callProgressCallback(
          onProgress,
          `分析完成`,
          completionProgress,
          false,
          "analysis_progress"
        );
      }
    );
  }

  /**
   * 获取要分析的项目列表
   */
  private getItemsToAnalyze(config: AnalyzerConfig): AnalysisItemName[] {
    let itemsToAnalyze = this.analyzerFactory.getSupportedItems();

    // 应用包含列表
    if (config.includeItems && config.includeItems.length > 0) {
      itemsToAnalyze = config.includeItems.filter((item) =>
        this.analyzerFactory.isItemSupported(item)
      );
    }

    // 应用排除列表
    if (config.excludeItems && config.excludeItems.length > 0) {
      itemsToAnalyze = itemsToAnalyze.filter(
        (item) => !config.excludeItems!.includes(item)
      );
    }

    return itemsToAnalyze;
  }

  /**
   * 构建最终输出结果
   */
  private buildFinalOutput(
    results: AnalysisItem[],
    semanticTags: string[],
    totalProcessingTime: number
  ): AnalyzerOutput {
    const successCount = results.filter((r) => r.value !== "error").length;
    const llmCallCount = results.filter((r) => r.usedLLM).length;

    return {
      status: "success",
      content: semanticTags, // 直接返回语义标签数组
      processing_time: totalProcessingTime,
      metadata: {
        module: this.moduleName,
        timestamp: Date.now(),
        // 将分析数据存储在metadata中，供调试使用
        analysisResults: results,
        summary: {
          successCount,
          totalCount: results.length,
          dataQuality: this.assessDataQuality(successCount, results.length),
        },
        llmCallCount,
        dataVersion: "1.0.0",
      },
    };
  }

  /**
   * 评估数据质量
   */
  private assessDataQuality(
    successCount: number,
    totalCount: number
  ): "excellent" | "good" | "fair" | "poor" {
    const successRate = totalCount > 0 ? successCount / totalCount : 0;
    if (successRate >= 0.9) return "excellent";
    if (successRate >= 0.7) return "good";
    if (successRate >= 0.5) return "fair";
    return "poor";
  }

  /**
   * 为分析结果补全 enrichedNarrative
   * 基于 llm-enrichedNarrative.md 模板，为每个分析项生成创造性解读
   */
  private async enrichAnalysisItemsWithNarrative(
    analysisResults: AnalysisItem[],
    onProgress?: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<AnalysisItem[]> {
    // 构建 enrichedNarrative 补全的 prompt
    const prompt = this.buildEnrichedNarrativePrompt(analysisResults);
    let accumulatedContent = "";

    // 🚀 立即发送开始状态
    onProgress?.(
      "🎨 启动 enrichedNarrative 补全...",
      "🔄",
      false,
      "enriched_narrative"
    );

    try {
      // V6.1: 使用约定大于配置的简化调用方式
      // V6.3: 使用简化的 response_format 配置，内部自动转换
      // 需要格式化输出，使用 OpenRouter GPT-4o
      const streamGenerator = this.callLLMStream({
        provider: "openrouter",
        model: "openai/gpt-4o",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        response_format: SimplifiedEnrichedNarrativeConfig,
        temperature: 0.7, // 创意性场景，适中的温度
        max_tokens: 4096,
        stream: true,
      });

      // 🚀 逐块处理流式输出
      let chunkCount = 0;
      for await (const chunk of streamGenerator) {
        chunkCount++;

        if (chunkCount === 1) {
          onProgress?.(
            "🤔 AI正在为分析结果创作 enrichedNarrative...",
            "",
            false,
            "enriched_narrative"
          );
        }

        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          accumulatedContent += content;

          const statusMessage =
            accumulatedContent.length === content.length
              ? "💭 AI开始生成创造性解读..."
              : "💭 AI正在生成创造性解读...";

          onProgress?.(statusMessage, content, false, "enriched_narrative");
        }
      }

      // 🚀 解析最终结果
      const finalContent = accumulatedContent.trim();
      if (!finalContent) {
        throw new Error("LLM未返回 enrichedNarrative 内容");
      }

      const parsedResult: SimplifiedEnrichedNarrativeResult =
        JSON.parse(finalContent);

      // 验证结果结构 - 简化版只需要验证 enrichedNarratives 数组
      if (
        !parsedResult.enrichedNarratives ||
        !Array.isArray(parsedResult.enrichedNarratives) ||
        parsedResult.enrichedNarratives.length !== analysisResults.length
      ) {
        console.log("LLM返回的 enrichedNarrative 格式不正确", finalContent);
        throw new Error("LLM返回的 enrichedNarrative 格式不正确");
      }

      // 🚀 高效数据合并：通过 name 字段匹配原始数据和生成的 enrichedNarrative
      const enrichedData: AnalysisItem[] = this.mergeEnrichedNarratives(
        analysisResults,
        parsedResult.enrichedNarratives
      );

      // 🎯 发送完成状态
      onProgress?.(
        "✅ enrichedNarrative 补全完成",
        "",
        true,
        "enriched_narrative"
      );

      return enrichedData;
    } catch (error) {
      // 🚀 发送错误状态，但不中断流程
      onProgress?.(
        "⚠️ enrichedNarrative 补全失败，使用占位符",
        error instanceof Error ? error.message : "未知错误",
        true,
        "enriched_narrative"
      );

      // 返回带有基本 enrichedNarrative 的结果
      return analysisResults.map((item) => ({
        ...item,
        enrichedNarrative:
          item.enrichedNarrative || `${item.name}: ${item.value}的特征表现`,
      }));
    }
  }

  /**
   * 🚀 高效数据合并：通过 name 字段匹配原始数据和生成的 enrichedNarrative
   * Token 优化核心逻辑 - 避免重复传输原始数据
   */
  private mergeEnrichedNarratives(
    originalResults: AnalysisItem[],
    enrichedNarratives: Array<{ name: string; enrichedNarrative: string }>
  ): AnalysisItem[] {
    return originalResults.map((item) => {
      const narrative = enrichedNarratives.find((n) => n.name === item.name);
      return {
        ...item,
        enrichedNarrative:
          narrative?.enrichedNarrative || `${item.name}的特征表现`,
      };
    });
  }

  /**
   * 构建 enrichedNarrative 补全的提示词
   * 🚀 Token 优化：只传输必要的 name 和 value 字段
   */
  private buildEnrichedNarrativePrompt(
    analysisResults: AnalysisItem[]
  ): string {
    // 🚀 只传输核心字段，减少输入 token
    const analysisData = analysisResults.map((item) => ({
      name: item.name,
      value: item.value,
    }));

    return `你是一位洞察人性的中文叙事者，擅长从琐碎的数据中读出深层的行为动机与性格片段。你不解释行为本身，而是讲述这些行为背后的人、他可能的状态、他带给世界的微妙影响。

现在你会获得一组开发者的行为特征 JSON 数据。请为每一个特征生成一段 narrative，它不需要对数据做理性说明，而应像写散文那样，呈现这些行为在日常语境下可能出现的样子。可以幽默，也可以感性，保持语言简洁、诚恳、有想象力，但不要浮夸。

请注意，整体语调需与下列三种风格之一保持一致：
	1.	李诞式：像讲笑话一样讲孤独
	2.	姜文式：像讲历史一样讲 bug
	3.	韩寒式：像讲车祸一样讲常识

以下是数据：

${JSON.stringify(analysisData, null, 2)}

请返回 {enrichedNarratives: [{name: string, value: string, enrichedNarrative: string}]} 格式的JSON 数组`;
  }

  /**
   * 流式语义标签提取方法
   * 将12项分析结果转换为具有画面感的语义标签，支持实时流式输出
   */
  private async extractSemanticTagsWithStreaming(
    analysisResults: AnalysisItem[],
    onProgress?: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<string[]> {
    // 构建语义标签提取的prompt（严格按照json-to-tags模板）
    const prompt = this.buildSemanticTagsPrompt(analysisResults);
    let accumulatedContent = "";

    // 🚀 立即发送开始状态
    onProgress?.("🎯 启动语义标签生成...", "🔄", false, "semantic_tags");

    try {
      // V6.1: 使用约定大于配置的简化调用方式
      // V6.3: 使用简化的 response_format 配置，内部自动转换
      // 需要格式化输出，使用 OpenRouter GPT-4o
      const streamGenerator = this.callLLMStream({
        provider: "openrouter",
        model: "openai/gpt-4o",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        response_format: SemanticTagsConfig,
        temperature: 0.8, // 特定场景提高创意性
        max_tokens: 4096,
        stream: true,
      });

      // 🚀 逐块处理流式输出
      let chunkCount = 0;
      for await (const chunk of streamGenerator) {
        chunkCount++;

        if (chunkCount === 1) {
          // 🎯 第一个chunk时更新状态
          onProgress?.(
            "🤔 AI正在分析GitHub数据特征...",
            "",
            false,
            "semantic_tags"
          );
        }

        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          accumulatedContent += content;

          // 🎯 关键：立即传递每个chunk到前端，实现打字机效果
          const statusMessage =
            accumulatedContent.length === content.length
              ? "💭 AI开始生成语义标签..."
              : "💭 AI正在生成语义标签...";

          onProgress?.(statusMessage, content, false, "semantic_tags");
        }
      }

      // 🚀 解析最终结果
      const finalContent = accumulatedContent.trim();
      if (!finalContent) {
        throw new Error("LLM未返回语义标签内容");
      }

      const result: SemanticTagsResult = JSON.parse(finalContent);

      // 验证结果
      if (!result.tags || !Array.isArray(result.tags)) {
        throw new Error("LLM返回的语义标签格式不正确");
      }

      if (result.tags.length < 5 || result.tags.length > 8) {
        throw new Error(`语义标签数量不符合要求: ${result.tags.length}个`);
      }

      // 🎯 发送完成状态
      onProgress?.("✅ 语义标签生成完成", "", true, "semantic_tags");

      return result.tags;
    } catch (error) {
      // 🚀 发送错误状态
      onProgress?.(
        "❌ 语义标签生成失败",
        error instanceof Error ? error.message : "未知错误",
        true,
        "semantic_tags"
      );
      throw error;
    }
  }

  /**
   * 构建语义标签提取的提示词（严格按照json-to-tags模板）
   */
  private buildSemanticTagsPrompt(analysisResults: AnalysisItem[]): string {
    return `你是一位文字功底深厚的中文作家，擅长从人类行为数据中提炼出令人会心一笑的标签句。
你不是在陈述行为特征本身，而是用一句话，把“这个人为什么有趣”讲清楚。

请从开发者的行为特征数据中挑选 6 个最可能引发认知冲突、生活误解、团队笑谈的点，为每个点写一句【风格化标签】。

语言风格可以任选以下三类之一：
- 李诞：看起来是个冷知识，其实是个笑话
- 姜文：讲的是 bug，听起来像传记
- 韩寒：句子很短，偏偏不好反驳

不要用浮夸修辞。要朴素而锋利，有观察者的质感。

以下是行为特征JSON数据：

\`\`\`
${JSON.stringify(analysisResults, null, 2)}
\`\`\`

请输出一个JSON对象，包含tags数组字段，内容为这一用户的风格化标签：
`;
  }

  /**
   *   这些是参考风格示例：
	•	提交频率低得像笔友回信，可能他家网速不太好
	•	用写遗嘱的精神在写 commit 日志，生怕有哪一笔精神遗产没安排好
	•	总在深夜写代码，仿佛整座城市都跟你一起，倾听键盘敲击的声音
	•	一定是严谨的人没错，不然想不出这样的项目命名
	•	几乎不在周末提交代码，他不会真的找到 work-life balance 了吧
	•	使用的框架很多，头顶的发量很少
	•	沉默的时间跨度巨大，想必是个深思熟虑的人吧
	•	作品比人红，点 star 的朋友真的不考虑 follow 一下吗
   * */

  /**
   * 健康检查
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      return await this.analyzerFactory.healthCheck();
    } catch {
      return false;
    }
  }
}
