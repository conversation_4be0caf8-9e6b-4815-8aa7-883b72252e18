/**
 * 基础模块类 - 动态LLM配置版本
 *
 * 统一处理：
 * - 动态LLM配置 (V6: 每次调用指定provider和model)
 * - 错误处理逻辑
 * - 性能监控
 * - 日志记录 (V5.3: 集成统一日志管理器)
 * - 健康检查
 * - 配置管理
 */

import type { ILLMClient, DynamicChatOptions } from "../clients/ILLMClient";
import {
  DynamicLLMClient,
  SUPPORTED_MODELS,
} from "../clients/LLMClientFactory";
import { ErrorHandler } from "../utils/ErrorHandler";
import { JSONValidator } from "../utils/JSONValidator";
import { ConfigManager } from "../utils/ConfigManager";
import type { ModuleType } from "../../types";
import { getLogManager, type LogManager } from "../utils/LogManager";
import type { ModuleOutput } from "../../types";
import type { PerformanceMetrics } from "@/types/debug-logging";
import {
  SchemaManager,
  type ResponseFormatConfig,
} from "../../schemas/SchemaManager";

// 扩展的聊天选项，支持简化的 response_format
type ExtendedChatOptions = Omit<DynamicChatOptions, "response_format"> & {
  response_format?:
    | ResponseFormatConfig
    | DynamicChatOptions["response_format"];
};

export interface ModuleMetrics {
  executionCount: number;
  successCount: number;
  failureCount: number;
  averageProcessingTime: number;
  lastExecutionTime: number;
  totalTokensUsed: number;
}

export interface ModuleConfig {
  timeout: number;
  enableLogging: boolean;
  enableMetrics: boolean;
  debugSessionId?: string;
}

/**
 * 基础模块抽象类 - 动态LLM配置版
 * V6.1: 新增约定大于配置支持
 */
export abstract class BaseModule {
  protected moduleName: string;
  protected moduleType: ModuleType;
  protected llmClient: ILLMClient;
  protected errorHandler: ErrorHandler;
  protected jsonValidator: JSONValidator;
  protected configManager: ConfigManager;
  protected logManager: LogManager;
  protected config: ModuleConfig;
  protected metrics: ModuleMetrics;
  /** V5.3: 当前执行的日志ID，用于跟踪执行过程 */
  protected currentLogId?: string;

  constructor(
    moduleName: string,
    moduleType: ModuleType,
    config: Partial<ModuleConfig> = {}
  ) {
    this.moduleName = moduleName;
    this.moduleType = moduleType;
    this.llmClient = new DynamicLLMClient(); // 创建动态LLM客户端
    this.errorHandler = new ErrorHandler();
    this.jsonValidator = new JSONValidator();
    this.configManager = ConfigManager.getInstance();
    this.logManager = getLogManager();

    // 默认配置
    this.config = {
      timeout: 30000,
      enableLogging: true,
      enableMetrics: true,
      ...config,
    };

    // 初始化指标
    this.metrics = {
      executionCount: 0,
      successCount: 0,
      failureCount: 0,
      averageProcessingTime: 0,
      lastExecutionTime: 0,
      totalTokensUsed: 0,
    };

    // 验证环境配置（可选）
    console.log(`${moduleName} module initialized with dynamic LLM support`);
  }

  /**
   * V6.1: 获取默认LLM配置 - 约定大于配置
   * 各模块可以重写此方法来自定义默认配置
   * V6.3: 更改默认为AI Gateway GPT-4O模型
   */
  protected getDefaultLLMConfig(): Pick<
    DynamicChatOptions,
    "provider" | "model"
  > {
    return {
      provider: "aigateway",
      model: SUPPORTED_MODELS.AIGATEWAY.GPT_4O,
    };
  }

  /**
   * V6.1: 简化的LLM调用方法 - 使用默认配置（约定大于配置）
   * 子模块可以直接调用而无需每次指定provider和model
   */
  protected async callLLM(
    options: Omit<DynamicChatOptions, "provider" | "model"> & {
      provider?: DynamicChatOptions["provider"];
      model?: DynamicChatOptions["model"];
    }
  ) {
    // 合并默认配置和传入配置
    const defaultConfig = this.getDefaultLLMConfig();
    const finalConfig = {
      ...defaultConfig,
      ...options,
      // 如果用户显式传入了provider或model，则使用用户配置
      provider: options.provider || defaultConfig.provider,
      model: options.model || defaultConfig.model,
    };

    return await this.llmClient.chat(finalConfig);
  }

  /**
   * V6.1: 简化的LLM流式调用方法 - 使用默认配置（约定大于配置）
   * V6.3: 支持自动 response_format 转换
   */
  protected async *callLLMStream(
    options: Omit<ExtendedChatOptions, "provider" | "model"> & {
      provider?: DynamicChatOptions["provider"];
      model?: DynamicChatOptions["model"];
    }
  ) {
    // 合并默认配置和传入配置
    const defaultConfig = this.getDefaultLLMConfig();
    const finalConfig = {
      ...defaultConfig,
      ...options,
      // 如果用户显式传入了provider或model，则使用用户配置
      provider: options.provider || defaultConfig.provider,
      model: options.model || defaultConfig.model,
    };

    // 自动转换 response_format
    if (options.response_format) {
      // 检查是否为简化配置对象（ResponseFormatConfig）
      const responseFormat = options.response_format;
      if (
        "name" in responseFormat &&
        "schema" in responseFormat &&
        !("type" in responseFormat)
      ) {
        // 这是 ResponseFormatConfig，需要转换
        const convertedFormat = SchemaManager.convertResponseFormat(
          responseFormat as ResponseFormatConfig,
          finalConfig.model
        );
        (finalConfig as any).response_format = convertedFormat;
      } else {
        // 这是标准的 DoubaoResponseFormat，直接使用
        (finalConfig as any).response_format = responseFormat;
      }
    }

    for await (const chunk of this.llmClient.chatStream(
      finalConfig as DynamicChatOptions
    )) {
      yield chunk;
    }
  }

  /**
   * V6: 检查指定provider的健康状态
   */
  protected async checkLLMHealth(
    provider: "doubao" | "openrouter" | "aigateway"
  ): Promise<boolean> {
    return await this.llmClient.healthCheck(provider);
  }

  /**
   * V6: 获取指定provider的性能统计
   */
  protected getLLMStats(provider?: "doubao" | "openrouter") {
    return this.llmClient.getPerformanceStats(provider);
  }

  /**
   * V6: 获取结构化输出格式（模块可选使用）
   */
  protected getStructuredOutputFormat() {
    return this.configManager.getResponseFormat(this.moduleType);
  }

  /**
   * V6: 检查模块是否推荐使用推理模式（模块可自定义）
   */
  protected shouldUseReasoningMode(): boolean {
    return this.configManager.shouldUseReasoningMode(this.moduleType);
  }

  /**
   * V6: 获取模块配置摘要（简化版）
   */
  protected getConfigSummary(): string {
    const defaultConfig = this.getDefaultLLMConfig();
    return `${this.moduleType} module (provider: ${defaultConfig.provider}, model: ${defaultConfig.model}, timeout: ${this.config.timeout}ms)`;
  }

  /**
   * 统一的执行包装器
   * 提供错误处理、性能监控、结构化日志记录 (V5.3)
   */
  protected async executeWithWrapper<T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    const sessionId = this.config.debugSessionId || "default";

    // V5.3: 开始日志记录
    const executionContext = {
      timeout: this.config.timeout,
      retryCount: 0,
      maxRetries: 3,
      configSummary: this.getConfigSummary(),
      customData: context,
    };

    this.currentLogId = this.logManager.logExecutionStart(
      sessionId,
      this.moduleType,
      this.moduleName,
      executionContext
    );

    const startTime = Date.now();

    // 更新执行计数
    this.updateMetrics("execution");

    try {
      // 设置超时
      const result = await Promise.race([
        operation(),
        this.createTimeoutPromise<T>(),
      ]);

      // 记录成功
      const processingTime = Date.now() - startTime;
      this.updateMetrics("success", processingTime);

      // V5.3: 构建性能指标
      const performanceMetrics: PerformanceMetrics = {
        processingTime,
        tokensUsed: this.metrics.totalTokensUsed,
        confidence: (result as any)?.confidence,
        ...(context?.metrics || {}),
      };

      // V5.3: 记录执行完成
      if (this.currentLogId) {
        const moduleOutput: ModuleOutput = {
          status: "success",
          content: result,
          processing_time: processingTime,
          confidence: (result as any)?.confidence,
        };

        this.logManager.logExecutionComplete(
          this.currentLogId,
          moduleOutput,
          performanceMetrics
        );
      }

      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));

      // 记录错误到ErrorHandler（保持兼容性）
      this.errorHandler.logError(err, {
        module: this.moduleName,
        moduleType: this.moduleType,
        context,
        configSummary: this.getConfigSummary(),
      });

      // 记录失败
      const processingTime = Date.now() - startTime;
      this.updateMetrics("failure", processingTime);

      // V5.3: 记录执行失败
      if (this.currentLogId) {
        this.logManager.logExecutionFailure(this.currentLogId, err, {
          type: this.categorizeError(err),
          recoveryAttempted: false,
          requestInfo: context,
        });
      }

      throw err;
    } finally {
      // 清理当前日志ID
      this.currentLogId = undefined;
    }
  }

  /**
   * 创建超时 Promise
   */
  private createTimeoutPromise<T>(): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(
          new Error(
            `${this.moduleName} execution timeout (${this.config.timeout}ms)`
          )
        );
      }, this.config.timeout);
    });
  }

  /**
   * 更新性能指标
   */
  protected updateMetrics(
    type: "execution" | "success" | "failure",
    processingTime?: number
  ): void {
    if (type === "execution") {
      this.metrics.executionCount++;
    } else if (type === "success") {
      this.metrics.successCount++;
      if (processingTime) {
        this.metrics.lastExecutionTime = processingTime;
        this.metrics.averageProcessingTime =
          (this.metrics.averageProcessingTime *
            (this.metrics.successCount - 1) +
            processingTime) /
          this.metrics.successCount;
      }
    } else if (type === "failure") {
      this.metrics.failureCount++;
    }
  }

  /**
   * 错误分类
   */
  protected categorizeError(
    error: Error
  ): import("@/types/debug-logging").ErrorType {
    const message = error.message.toLowerCase();

    if (message.includes("timeout")) return "timeout";
    if (message.includes("network") || message.includes("connection"))
      return "network";
    if (message.includes("rate limit") || message.includes("api"))
      return "api_error";
    if (message.includes("auth") || message.includes("config"))
      return "configuration";
    if (message.includes("parse") || message.includes("json")) return "parsing";
    if (message.includes("valid")) return "validation";

    return "execution";
  }

  /**
   * 标准化模块输出
   */
  protected createModuleOutput<T>(
    content: T,
    processingTime: number,
    confidence?: number,
    metadata?: Record<string, any>
  ): ModuleOutput {
    return {
      status: "success",
      content,
      processing_time: processingTime,
      confidence,
      metadata: {
        module: this.moduleName,
        timestamp: Date.now(),
        ...metadata,
      },
    };
  }

  /**
   * 标准化错误输出
   */
  protected createErrorOutput(
    error: Error,
    processingTime: number,
    metadata?: Record<string, any>
  ): ModuleOutput {
    return {
      status: "error",
      content: null,
      error_details: error.message,
      processing_time: processingTime,
      metadata: {
        module: this.moduleName,
        timestamp: Date.now(),
        ...metadata,
      },
    };
  }

  /**
   * 健康检查
   * V6.2: 更新为检查默认provider（OpenRouter）的健康状态
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      // 检查默认provider的健康状态
      const defaultConfig = this.getDefaultLLMConfig();
      const health = await this.checkLLMHealth(defaultConfig.provider);
      return health;
    } catch {
      return false;
    }
  }

  /**
   * 获取模块指标
   */
  getMetrics(): ModuleMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取模块配置
   */
  getConfig(): ModuleConfig {
    return { ...this.config };
  }

  /**
   * 更新模块配置
   */
  updateConfig(newConfig: Partial<ModuleConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 重置模块指标
   */
  resetMetrics(): void {
    this.metrics = {
      executionCount: 0,
      successCount: 0,
      failureCount: 0,
      averageProcessingTime: 0,
      lastExecutionTime: 0,
      totalTokensUsed: 0,
    };
  }

  /**
   * 获取成功率
   */
  getSuccessRate(): number {
    if (this.metrics.executionCount === 0) return 0;
    return (this.metrics.successCount / this.metrics.executionCount) * 100;
  }

  /**
   * 获取模块状态摘要
   */
  getStatusSummary(): {
    name: string;
    healthy: boolean;
    successRate: number;
    avgProcessingTime: number;
    lastExecution: number;
  } {
    return {
      name: this.moduleName,
      healthy: this.getSuccessRate() > 80, // 成功率大于80%视为健康
      successRate: this.getSuccessRate(),
      avgProcessingTime: this.metrics.averageProcessingTime,
      lastExecution: this.metrics.lastExecutionTime,
    };
  }

  /**
   * V5.3: 便捷的模块状态日志记录方法
   * 供子模块内部记录运行状态使用
   */
  protected logModuleInfo(
    message: string,
    context?: Record<string, any>
  ): void {
    if (!this.config.enableLogging) return;

    const sessionId = this.config.debugSessionId || "default";
    this.logManager.logInfo(sessionId, this.moduleType, message, context);
  }

  /**
   * V5.3: 记录模块警告信息
   */
  protected logModuleWarning(
    message: string,
    context?: Record<string, any>
  ): void {
    if (!this.config.enableLogging) return;

    const sessionId = this.config.debugSessionId || "default";
    this.logManager.logWarning(sessionId, this.moduleType, message, context);
  }

  /**
   * V5.3: 记录模块错误信息
   */
  protected logModuleError(error: Error, context?: Record<string, any>): void {
    if (!this.config.enableLogging) return;

    const sessionId = this.config.debugSessionId || "default";
    this.logManager.logError(sessionId, this.moduleType, error, context);
  }

  /**
   * V5.3: 动态设置调试会话ID
   * 用于API调试时关联正确的会话
   */
  setDebugSessionId(sessionId: string): void {
    this.config.debugSessionId = sessionId;
  }
}
