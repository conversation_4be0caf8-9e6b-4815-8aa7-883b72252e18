/**
 * AI Quality Guardian - AI质量守护者 (V5.3版)
 *
 * 职责：对生成的文本进行AI推理深度质量评估
 * 架构：纯AI推理评估 + 流式实时反馈
 *
 * V5.3简化优化：
 * - 专注AI推理评估，移除算法预检查复杂度
 * - 集成Doubao推理模式，提升评估深度和准确性
 * - 流式处理架构，提供实时反馈体验
 * - 类型安全，完全移除any类型使用
 * - 精简代码量，提升可维护性
 */

import { BaseModule } from "./BaseModule";
import { createStreamlineLogger } from "../utils/StreamlineLogger";
import { schemaManager, AIModuleType } from "../../schemas";
import type { UserData } from "@/types/user-data";
import type {
  StrategistOutput,
  WriterOutput,
  CriticOutput,
  QualityDimensions,
} from "../../types";

interface EvaluationContext {
  text: string;
  strategy: StrategistOutput;
  githubData: UserData;
  writerOutput?: WriterOutput;
}

// 已删除不需要的算法检查接口

interface ReasoningEvaluationResult {
  dimensions: QualityDimensions;
  confidence: number;
  detailedAssessment: string;
  improvementSuggestions: string[];
  qualityInsights: string[];
}

interface AIEvaluationStageResult {
  dimensions: QualityDimensions;
  confidence: number;
  detailedAssessment: string;
  improvementSuggestionsCount: number;
  qualityInsightsCount: number;
  reasoningResult: ReasoningEvaluationResult;
}

interface FinalEvaluation {
  overallScore: number;
  dimensions: QualityDimensions;
  assessment: string;
  suggestions: string[];
  confidence: number;
  needsOptimization: boolean;
  standardizedGrade: "A" | "B" | "C" | "D" | "F";
}

interface EvaluationStageResult {
  overallScore: number;
  dimensions: QualityDimensions;
  standardizedGrade: string;
  needsOptimization: boolean;
  evaluationMode: string;
  finalEvaluation: FinalEvaluation;
}

export class CriticModule extends BaseModule {
  // AI推理评估配置
  private readonly AI_CONFIG = {
    TEMPERATURE: 0.6,
    MAX_TOKENS: 1000,
    MIN_CONFIDENCE_THRESHOLD: 0.7,

    // 标准化评分配置
    SCORING_CONFIG: {
      EXCELLENT_THRESHOLD: 85,
      GOOD_THRESHOLD: 75,
      ACCEPTABLE_THRESHOLD: 70,
      POOR_THRESHOLD: 60,

      DIMENSION_WEIGHTS: {
        humor: 0.3,
        compliance: 0.25,
        originality: 0.2,
        naturalness: 0.15,
        relevance: 0.1,
      },

      OPTIMIZATION_THRESHOLD: 72,
    },
  };

  constructor() {
    super("critic", "critic", {
      enableLogging: true,
      enableMetrics: true,
      timeout: 30000,
    });
  }

  /**
   * 流式质量评估方法 - 支持实时反馈
   */
  async evaluateTextWithStreaming(
    generatedText: string,
    strategistOutput: StrategistOutput,
    githubData: UserData,
    onProgress: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<CriticOutput> {
    const startTime = Date.now();

    return this.executeWithWrapper(
      async () => {
        // 创建StreamlineLogger实例
        const logger = createStreamlineLogger(
          this.config.debugSessionId || `critic-session-${Date.now()}`,
          "critic"
        );

        try {
          // 流式输出：开始AI质量评估

          const context: EvaluationContext = {
            text: generatedText,
            strategy: strategistOutput,
            githubData,
          };

          // Stage 1: AI推理深度评估
          const aiEvaluationResult =
            await logger.executeStage<AIEvaluationStageResult>(
              "AI推理深度评估",
              async (
                reportProgress: (progress: number, message: string) => void
              ) => {
                reportProgress(30, "正在启动AI推理深度评估...");

                // 流式AI推理深度评估
                const reasoningResult =
                  await this.streamingReasoningEnhancedEvaluation(
                    context,
                    (message, chunk, isComplete, stage) => {
                      // 转发流式内容到外部回调
                      onProgress(message, chunk, isComplete, stage);

                      // 同时报告内部进度
                      if (chunk) {
                        reportProgress(80, "AI推理评估中...");
                      }
                    }
                  );

                reportProgress(100, "AI推理深度评估完成");

                return {
                  dimensions: reasoningResult.dimensions,
                  confidence: reasoningResult.confidence,
                  detailedAssessment: reasoningResult.detailedAssessment,
                  improvementSuggestionsCount:
                    reasoningResult.improvementSuggestions.length,
                  qualityInsightsCount: reasoningResult.qualityInsights.length,
                  reasoningResult, // 返回完整推理结果
                };
              }
            );
          const reasoningResult = aiEvaluationResult.reasoningResult;
          onProgress("AI推理评估完成", "", true, "ai_evaluation_complete");

          // Stage 2: 生成最终评估
          const finalEvaluationResult =
            await logger.executeStage<EvaluationStageResult>(
              "生成最终评估",
              async (
                reportProgress: (progress: number, message: string) => void
              ) => {
                reportProgress(90, "正在生成最终评估...");

                const finalEvaluation = this.createFinalEvaluation(
                  reasoningResult,
                  context
                );

                reportProgress(
                  100,
                  `最终评分: ${finalEvaluation.overallScore}/100`
                );

                return {
                  overallScore: finalEvaluation.overallScore,
                  dimensions: finalEvaluation.dimensions,
                  standardizedGrade: finalEvaluation.standardizedGrade,
                  needsOptimization: finalEvaluation.needsOptimization,
                  evaluationMode: "ai_reasoning",
                  finalEvaluation, // 返回完整评估结果
                };
              }
            );
          const finalEvaluation = finalEvaluationResult.finalEvaluation;

          onProgress("质量评估完成", "", true, "evaluation_complete");

          const processingTime = Date.now() - startTime;

          // 🎯 正确构造CriticOutput，不使用强制类型转换
          const criticOutput: CriticOutput = {
            status: "success",
            content: {
              overallScore: finalEvaluation.overallScore,
              dimensions: finalEvaluation.dimensions,
              assessment: finalEvaluation.assessment,
              suggestions: finalEvaluation.suggestions,
              confidence: finalEvaluation.confidence,
              needsOptimization: finalEvaluation.needsOptimization,
              standardizedGrade: finalEvaluation.standardizedGrade,
            },
            processing_time: processingTime,
            confidence: finalEvaluation.confidence,
            metadata: {
              module: this.moduleName,
              timestamp: Date.now(),
              overallScore: finalEvaluation.overallScore,
              grade: finalEvaluation.standardizedGrade,
              needsOptimization: finalEvaluation.needsOptimization,
              evaluationMode: "ai_reasoning",
              smartGuardianVersion: "V5.3-AI-Only",
            },
          };

          return criticOutput;
        } catch (error) {
          onProgress(
            "质量评估过程发生错误",
            error instanceof Error ? error.message : "未知错误",
            true,
            "evaluation_error"
          );
          throw error;
        }
      },
      { generatedText, strategistOutput, githubData }
    );
  }

  // 已删除算法预检查方法，专注于AI推理评估

  // 已删除所有算法检查方法

  // 已删除所有算法检查、快速分数计算和问题识别相关方法

  // 这些方法已不再需要，因为始终使用AI推理评估

  /**
   * 创建最终评估结果
   */
  private createFinalEvaluation(
    reasoningResult: ReasoningEvaluationResult,
    context: EvaluationContext
  ): FinalEvaluation {
    // 计算加权总分
    const overallScore = this.calculateWeightedScore(
      reasoningResult.dimensions
    );

    return {
      overallScore,
      dimensions: reasoningResult.dimensions,
      assessment: reasoningResult.detailedAssessment,
      suggestions: reasoningResult.improvementSuggestions,
      confidence: reasoningResult.confidence,
      needsOptimization:
        overallScore < this.AI_CONFIG.SCORING_CONFIG.OPTIMIZATION_THRESHOLD,
      standardizedGrade: this.calculateGrade(overallScore),
    };
  }

  /**
   * 流式AI推理深度评估
   */
  private async streamingReasoningEnhancedEvaluation(
    context: EvaluationContext,
    onProgress: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<ReasoningEvaluationResult> {
    const startTime = Date.now();

    try {
      const prompt = this.buildReasoningEvaluationPrompt(context);
      let accumulatedContent = "";

      // 获取 CriticModule 的结构化输出格式
      const responseFormat = schemaManager.getResponseFormat(
        AIModuleType.CRITIC
      );

      // V6.1: 使用约定大于配置的简化调用方式
      // 需要格式化输出，使用 OpenRouter GPT-4o
      const streamGenerator = this.callLLMStream({
        provider: "openrouter",
        model: "openai/gpt-4o",
        messages: [
          {
            role: "system",
            content: `你是一位世界级的文案质量评估专家，专门评估技术幽默内容的质量。

## 专业背景
- 拥有丰富的文案评估经验和深厚的语言学功底
- 深度理解程序员群体的文化特征和审美偏好
- 精通各种文案质量评估标准和方法
- 擅长发现文案中的优点和改进空间

## 评估要求
使用系统性的质量评估框架：
1. **内容分析**: 深度分析文案的内容质量和相关性
2. **创意评估**: 评估创意的独特性和吸引力
3. **技术准确性**: 确保技术内容的准确性和专业性
4. **用户体验**: 评估目标用户的接受度和共鸣度

请发挥你的专业评估能力，提供详细的质量分析报告。`,
          },
          { role: "user", content: prompt },
        ],
        temperature: 0.6,
        max_tokens: 1000,
        stream: true,
        // 使用官方结构化输出 API
        response_format: responseFormat || undefined,
      });

      // 🚀 简化的流式处理，解决TTFT延迟问题
      let chunkCount = 0;
      for await (const chunk of streamGenerator) {
        chunkCount++;
        if (chunkCount === 1) {
          // 🚀 立即显示AI正在思考的状态，不等待有内容的chunk
          onProgress(
            "🤔 AI正在深度分析文案质量...",
            "🔄",
            false,
            "evaluation_content"
          );
        }

        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          accumulatedContent += content;

          // 🚀 第一次收到真正内容时，更新状态消息
          const statusMessage =
            accumulatedContent.length === content.length
              ? "💡 AI开始生成评估报告..."
              : "AI质量评估生成中...";

          onProgress(statusMessage, content, false, "evaluation_content");
        }
      }

      // 解析推理评估结果
      const evaluationAnalysis =
        this.parseReasoningEvaluationResult(accumulatedContent);

      const processingTime = Date.now() - startTime;

      return {
        dimensions: evaluationAnalysis.dimensions || {
          humor: 75,
          compliance: 75,
          originality: 75,
          naturalness: 75,
          relevance: 75,
        },
        detailedAssessment: evaluationAnalysis.detailedAssessment || "评估完成",
        confidence: evaluationAnalysis.confidence || 0.8,
        improvementSuggestions: evaluationAnalysis.improvementSuggestions || [],
        qualityInsights: evaluationAnalysis.qualityInsights || [],
      };
    } catch (error) {
      console.error("CriticModule streaming evaluation failed:", error);
      throw error;
    }
  }

  /**
   * 构建推理评估提示词
   */
  private buildReasoningEvaluationPrompt(context: EvaluationContext): string {
    const { text, strategy, githubData } = context;

    return `你是一位专业的文本质量评估专家，请运用深度推理对以下GitHub开发者描述文案进行全面质量分析。

## 📋 评估对象

**待评估文案**:
"${text}"

## 📊 创作背景信息

**策略要求**:
- 人物设定: ${strategy.content.insight_pool.persona}
- 幽默角度: ${strategy.content.insight_pool.humor_angle}
- 语言气质: ${strategy.content.punchline_focus.tone}
- 关键类比: ${strategy.content.insight_pool.analogy_pool.slice(0, 2).join("; ")}

**数据背景**:
- 用户: ${githubData.username}
- 主要数据: commits=${githubData.commits}, stars=${
      githubData.totalStars
    }, PRs=${githubData.pullRequests}

## 🧠 深度推理评估要求

请运用系统性推理对文案进行深度分析：

### 第一步：语言质量推理
- 词汇选择是否精准恰当？
- 语法结构是否自然流畅？
- 表达节奏是否富有变化？
- 标点使用是否合理得体？

### 第二步：内容逻辑推理
- 内容与数据的相关性如何？
- 策略执行是否准确到位？
- 视角运用是否恰当自然？
- 信息传达是否清晰有效？

### 第三步：创意深度推理
- 表达方式是否新颖独特？
- 幽默效果是否自然得体？
- 修辞技巧是否运用巧妙？
- 整体创意是否令人印象深刻？

### 第四步：用户体验推理
- 读者接受度如何？
- 情感共鸣程度如何？
- 记忆点是否突出？
- 分享传播价值如何？

### 第五步：综合质量推理
- 各维度表现如何平衡？
- 整体质量水平如何？
- 主要优势和不足是什么？
- 改进方向有哪些？

## 📝 输出要求

请按以下格式提供深度评估结果：

**五维评分** (0-100整数分):
幽默性: [分数] - [评分理由]
合规性: [分数] - [评分理由]
原创性: [分数] - [评分理由]
自然度: [分数] - [评分理由]
相关性: [分数] - [评分理由]

**深度分析**: [200字以内的综合分析]

**质量洞察**: [3-4个具体的深度洞察点]

**改进建议**: [3个具体可操作的改进建议]

**置信度评估**: [0.1-1.0的评估置信度]

现在请开始你的推理分析：`;
  }

  /**
   * 解析推理评估结果 - 简化版本，使用官方结构化输出
   */
  private parseReasoningEvaluationResult(
    content: string
  ): ReasoningEvaluationResult {
    if (!content.trim()) {
      throw new Error("Empty response content from CriticModule");
    }

    try {
      // 使用官方 response_format 时，假设模型输出正确的 JSON
      const parsed = JSON.parse(content);

      // 验证必需字段
      if (!parsed.dimensions) {
        throw new Error("Missing required field: dimensions");
      }

      const dimensions: QualityDimensions = {
        humor: parsed.dimensions.humor || 75,
        compliance: parsed.dimensions.compliance || 75,
        originality: parsed.dimensions.originality || 75,
        naturalness: parsed.dimensions.naturalness || 75,
        relevance: parsed.dimensions.relevance || 75,
      };

      return {
        dimensions,
        confidence: Math.min(Math.max(parsed.confidence || 0.8, 0.1), 1.0),
        detailedAssessment:
          parsed.detailedAssessment || "推理评估完成，各维度表现良好",
        improvementSuggestions: parsed.improvementSuggestions || [
          "继续保持高质量创作",
        ],
        qualityInsights: parsed.qualityInsights || ["运用推理模式提升评估深度"],
      };
    } catch (error) {
      // 按照开发规范：尽早报错退出，不做兜底处理
      throw new Error(
        `Failed to parse CriticModule structured JSON response: ${
          error instanceof Error ? error.message : "Unknown error"
        }. Content: ${content.substring(0, 200)}...`
      );
    }
  }

  // 已删除所有算法评估和混合评估相关方法

  /**
   * 计算加权总分
   */
  private calculateWeightedScore(dimensions: QualityDimensions): number {
    const weights = this.AI_CONFIG.SCORING_CONFIG.DIMENSION_WEIGHTS;
    return Math.round(
      dimensions.humor * weights.humor +
        dimensions.compliance * weights.compliance +
        dimensions.originality * weights.originality +
        dimensions.naturalness * weights.naturalness +
        dimensions.relevance * weights.relevance
    );
  }

  /**
   * 计算等级
   */
  private calculateGrade(score: number): "A" | "B" | "C" | "D" | "F" {
    if (score >= this.AI_CONFIG.SCORING_CONFIG.EXCELLENT_THRESHOLD) return "A";
    if (score >= this.AI_CONFIG.SCORING_CONFIG.GOOD_THRESHOLD) return "B";
    if (score >= this.AI_CONFIG.SCORING_CONFIG.ACCEPTABLE_THRESHOLD) return "C";
    if (score >= this.AI_CONFIG.SCORING_CONFIG.POOR_THRESHOLD) return "D";
    return "F";
  }

  /**
   * 健康检查 - V6.1 约定大于配置版本
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      // V6.1: 使用约定大于配置的简化调用
      // 健康检查不需要格式化输出，使用 AI Gateway
      const testPrompt = '请返回JSON格式: {"status": "ok"}';

      const response = await this.callLLM({
        provider: "aigateway",
        model: "openai/gpt-4o",
        messages: [
          {
            role: "system",
            content: "你是一个测试助手，请严格按要求返回JSON格式。",
          },
          {
            role: "user",
            content: testPrompt,
          },
        ],
        temperature: 0.1,
        max_tokens: 50,
        timeout: 30000,
      });

      const responseText = response.choices[0]?.message?.content || "";
      return responseText.includes("ok");
    } catch {
      return false;
    }
  }
}
