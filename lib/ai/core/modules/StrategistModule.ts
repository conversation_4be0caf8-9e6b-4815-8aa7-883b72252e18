/**
 * 策略师模块 - 简化版本 (V6.0)
 *
 * 职责：基于分析器输出的语义标签生成创意简报
 * 架构：纯LLM推理模式，基于tags-to-strategist.md模板
 *
 * 设计原则：
 * - 约定大于配置：提供合理默认值，最小化配置
 * - 保持代码精简：删除复杂的策略选择规则引擎
 * - 类型安全优先：严格的TypeScript类型定义
 * - 尽早报错退出：明确的参数校验和错误处理
 * - 单一职责原则：专注于创意简报生成
 */

import { BaseModule } from "./BaseModule";
import { createStreamlineLogger } from "../utils/StreamlineLogger";
import {
  createSelfDescribingData,
  callProgressCallback,
} from "../utils/AdaptiveMetadataDisplayManager";
import {
  StrategistResponseFormat,
  type CreativeBriefResult,
} from "../../schemas";
import type { AnalyzerOutput, StrategistOutput } from "../../types";

export class StrategistModule extends BaseModule {
  constructor() {
    super("strategist", "strategist", {
      enableLogging: true,
      enableMetrics: true,
      timeout: 180000,
    });
  }

  /**
   * 基于语义标签生成创意简报
   *
   * 遵循单一职责原则：专注于创意简报生成
   *
   * @param analyzerOutput 分析器输出（包含语义标签数组）
   * @param onProgress 流式进度回调
   * @returns 创意简报结果
   */
  async generateCreativeBrief(
    analyzerOutput: AnalyzerOutput,
    onProgress?: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<StrategistOutput> {
    const startTime = Date.now();

    return this.executeWithWrapper(
      async () => {
        // 创建StreamlineLogger实例
        const logger = createStreamlineLogger(
          this.config.debugSessionId || `strategist-session-${Date.now()}`,
          "strategist"
        );

        try {
          // Stage 1: 输入验证 - 尽早报错退出
          this.validateInput(analyzerOutput);

          // 🚀 自描述数据展示验证结果
          const validateResult = createSelfDescribingData(
            {
              status: "ok",
              semanticTagsCount: analyzerOutput.content.length,
            },
            {
              title: "🔍 输入验证完成",
              color: "#10b981",
            }
          );

          callProgressCallback(
            onProgress,
            "输入验证完成",
            validateResult,
            true,
            "input_validation"
          );

          // Stage 2: 生成创意简报
          const creativeBrief = await logger.executeStage(
            "生成创意简报",
            async () => {
              return await this.generateCreativeBriefWithStreaming(
                analyzerOutput.content,
                (message, chunk, isComplete, stage) => {
                  // 立即转发流式内容到外部回调
                  callProgressCallback(
                    onProgress,
                    message,
                    chunk,
                    isComplete,
                    stage
                  );
                }
              );
            }
          );

          const processingTime = Date.now() - startTime;

          // 构造最终输出 - 类型安全优先
          const strategistOutput: StrategistOutput = {
            status: "success",
            content: creativeBrief,
            processing_time: processingTime,
            confidence: 0.9, // 基于LLM结构化输出的高置信度
            metadata: {
              module: this.moduleName,
              timestamp: Date.now(),
              semanticTagsCount: analyzerOutput.content.length,
              llmModel: "default", // V6.1: 使用约定大于配置，无需指定具体模型
              templateVersion: "tags-to-strategist-v1.0",
            },
          };

          return strategistOutput;
        } catch (error) {
          // 错误处理 - 尽早报错退出
          const errorInfo = createSelfDescribingData(
            {
              errorType: "creative_brief_generation_error",
              message: error instanceof Error ? error.message : "未知错误",
              stage: "strategist_execution",
              timestamp: new Date().toISOString(),
            },
            {
              title: "❌ 创意简报生成失败",
              color: "#ef4444",
            }
          );

          callProgressCallback(
            onProgress,
            "创意简报生成失败",
            errorInfo,
            true,
            "creative_brief_error"
          );

          throw error;
        }
      },
      { analyzerOutput }
    );
  }

  /**
   * 输入验证 - 尽早报错退出原则
   */
  private validateInput(analyzerOutput: AnalyzerOutput): void {
    if (!analyzerOutput) {
      throw new Error("分析器输出不能为空");
    }

    if (!analyzerOutput.content || !Array.isArray(analyzerOutput.content)) {
      throw new Error("分析器输出必须包含语义标签数组");
    }

    if (analyzerOutput.content.length < 3) {
      throw new Error(
        `语义标签数量不足，至少需要3个，当前: ${analyzerOutput.content.length}个`
      );
    }

    if (analyzerOutput.content.length > 10) {
      throw new Error(
        `语义标签数量过多，最多支持10个，当前: ${analyzerOutput.content.length}个`
      );
    }

    // 验证标签内容质量
    const invalidTags = analyzerOutput.content.filter(
      (tag) => !tag || typeof tag !== "string" || tag.trim().length < 5
    );

    if (invalidTags.length > 0) {
      throw new Error(`发现无效的语义标签，标签长度不能少于5个字符`);
    }
  }

  /**
   * 流式创意简报生成
   *
   * 复用DoubaoClient，遵循复用优先原则
   */
  private async generateCreativeBriefWithStreaming(
    semanticTags: string[],
    onProgress?: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<CreativeBriefResult> {
    // 构建基于tags-to-strategist.md模板的提示词
    const prompt = this.buildCreativeBriefPrompt(semanticTags);
    let accumulatedContent = "";

    // 立即发送开始状态
    onProgress?.("🚀 启动创意简报生成...", "🔄", false, "creative_brief");

    try {
      // V6.1: 使用约定大于配置的简化调用方式
      // 需要格式化输出，使用 OpenRouter GPT-4o
      const streamGenerator = this.callLLMStream({
        provider: "openrouter",
        model: "openai/gpt-4o",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        response_format: StrategistResponseFormat,
        temperature: 0.8, // 创意性场景提高温度
        max_tokens: 4096,
        stream: true,
      });

      // 流式处理 - 单一职责原则
      let chunkCount = 0;
      for await (const chunk of streamGenerator) {
        chunkCount++;

        if (chunkCount === 1) {
          onProgress?.("🤔 AI正在分析语义标签...", "", false, "creative_brief");
        }

        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          accumulatedContent += content;

          // 立即传递流式内容，实现实时展示
          const statusMessage =
            accumulatedContent.length === content.length
              ? "💡 AI开始生成创意简报..."
              : "💡 AI正在生成创意简报...";

          onProgress?.(statusMessage, content, false, "creative_brief");
        }
      }

      // 解析最终结果 - 类型安全优先
      const finalContent = accumulatedContent.trim();
      if (!finalContent) {
        throw new Error("LLM未返回创意简报内容");
      }

      const result: CreativeBriefResult = JSON.parse(finalContent);

      // 验证结果完整性 - 尽早报错退出
      this.validateCreativeBriefResult(result);

      onProgress?.("✅ 创意简报生成完成", "", true, "creative_brief");

      return result;
    } catch (error) {
      onProgress?.(
        "❌ 创意简报生成失败",
        error instanceof Error ? error.message : "未知错误",
        true,
        "creative_brief"
      );
      throw error;
    }
  }

  /**
   * 构建创意简报提示词
   *
   * 基于tags-to-strategist.md模板 - 约定大于配置原则
   */
  private buildCreativeBriefPrompt(semanticTags: string[]): string {
    return `你是一个资深创意策划专家，擅长将程序员行为数据转化为具备风格化叙事和幽默气质的文案策略。

我将提供一位 GitHub 用户的行为特征标签，请你基于这些标签，构建出一份结构化的三段式创意策略，供后续写作模型生成高质量文案使用。

要求输出的结构如下（JSON 格式）：

{
    "narrative_scaffold": {
        "A": "（概述型铺垫：描述该用户持续进行的行为习惯）",
        "B": "（反差型过渡：揭示这些投入与产出间的落差或荒诞）",
        "C_hint": "（点明 C 段写作方向，例如暗喻式、自黑式、哲思式等）"
    },
    "insight_pool": {
        "behavior_summary": "（简洁复述该用户整体行为特征）",
        "persona": "（建议的人物设定，用一句有品位的短语概括）",
        "humor_angle": "（本次幽默的落点方式，如认知错位、自我反转、社交隐喻等）",
        "analogy_pool": [
            "（类比句，用文学或生活化意象强化人物特征）",
            "…"
        ]
    },
    "punchline_focus": {
        "punchline_candidates": [
            "（收尾候选句，应具备哲思感、传播力或冷幽默调性）",
            "…"
        ],
        "tone": "（简要描述语言气质，如 冷幽默 + 文艺讽刺）",
        "stylistic_suggestion": "（结构建议，如'短句收尾'、'反转+暗喻'）"
    },
    "writing_instruction": {
        "focus_on": "请重点打磨三段式中的 C 段，使其具备幽默力量和人物定型效果，避免空泛总结。",
        "overall_tone": "整体语气应轻盈、克制、自嘲、体现跨学科视角与美感，不可堆砌术语或显摆技术细节。"
    }
}

行为特征标签如下：
${JSON.stringify(semanticTags, null, 2)}

请基于这些标签生成一份完整 JSON 输出，用于供文案写作模型使用。`;
  }

  /**
   * 验证创意简报结果完整性
   *
   * 尽早报错退出原则
   */
  private validateCreativeBriefResult(result: CreativeBriefResult): void {
    if (
      !result.narrative_scaffold?.A ||
      !result.narrative_scaffold?.B ||
      !result.narrative_scaffold?.C_hint
    ) {
      throw new Error("创意简报缺少必要的叙事架构字段");
    }

    if (
      !result.insight_pool?.behavior_summary ||
      !result.insight_pool?.persona ||
      !result.insight_pool?.humor_angle
    ) {
      throw new Error("创意简报缺少必要的洞察资源池字段");
    }

    if (
      !result.punchline_focus?.tone ||
      !result.punchline_focus?.stylistic_suggestion
    ) {
      throw new Error("创意简报缺少必要的收尾焦点字段");
    }

    if (
      !Array.isArray(result.insight_pool.analogy_pool) ||
      result.insight_pool.analogy_pool.length < 2
    ) {
      throw new Error("类比句池至少需要2个条目");
    }

    if (
      !Array.isArray(result.punchline_focus.punchline_candidates) ||
      result.punchline_focus.punchline_candidates.length < 2
    ) {
      throw new Error("收尾候选句至少需要2个条目");
    }
  }

  /**
   * 健康检查 - 复用BaseModule的基础设施
   * V6.1: 使用约定大于配置的简化调用方式
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      // V6.1: 使用约定大于配置的简化调用
      // 健康检查不需要格式化输出，使用 AI Gateway
      const testResponse = await this.callLLM({
        provider: "aigateway",
        model: "openai/gpt-4o",
        messages: [{ role: "user", content: '请返回: {"status": "ok"}' }],
        temperature: 0.1,
        max_tokens: 50,
        timeout: 180000,
      });

      return testResponse.choices[0]?.message?.content?.includes("ok") || false;
    } catch {
      return false;
    }
  }
}
