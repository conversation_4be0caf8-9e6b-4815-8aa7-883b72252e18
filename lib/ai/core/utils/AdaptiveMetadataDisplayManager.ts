/**
 * Adaptive Metadata Display Manager - 自适应元数据展示管理器
 *
 * 🚀 核心功能：
 * - 基本数据类型检测：自动识别 JSON、代码、文本
 * - 智能展示选择：基于数据特征选择最佳展示方式
 * - 零配置使用：无需记忆固定格式和规则
 *
 * 设计原则：
 * 1. 简单直接：基于数据内容直接判断类型
 * 2. 智能推断：自动选择最佳展示策略
 * 3. 零记忆成本：开发者无需记住任何约定
 */

import type { ModuleType } from "../../types";

// =================================================================
// 核心接口定义
// =================================================================

/**
 * 自描述数据协议 - 用于向后兼容
 * 数据可以携带自己的展示提示信息
 */
export interface SelfDescribingData {
  // 实际数据内容
  data: any;

  // 展示提示
  displayHints: {
    type?: DisplayType;
    title?: string;
    icon?: string;
    color?: string;
  };
}

/**
 * 展示类型枚举 - 精简为三种基本类型
 */
export type DisplayType =
  | "text" // 纯文本内容
  | "code" // 代码内容
  | "json"; // JSON数据

/**
 * 通用的展示结果
 */
export interface DisplayResult {
  type: DisplayType;
  title: string;
  preview: string;
  content: {
    formatted: string;
    raw: any;
    structure?: any;
  };
  metadata: {
    confidence: number;
    processingTime?: number;
    dataSize: number;
    complexity: "simple" | "medium" | "complex";
    warnings?: string[];
  };
  styling?: {
    color?: string;
    bgColor?: string;
    borderColor?: string;
    textSize?: string;
    icon?: string;
  };
}

export interface DisplayContext {
  stage?: string;
  moduleType?: ModuleType;
  timestamp?: number;
  sessionId?: string;
  metadata?: Record<string, any>;
  preferences?: {
    verbosity?: "minimal" | "standard" | "detailed";
    theme?: "light" | "dark";
  };
}

// =================================================================
// 简化的数据类型检测工具
// =================================================================

export class DataTypeChecker {
  /**
   * 检查是否为自描述数据
   */
  static isSelfDescribing(data: any): boolean {
    return data && typeof data === "object" && data.displayHints;
  }
}

// =================================================================
// 主要的自适应展示管理器
// =================================================================

export class AdaptiveMetadataDisplayManager {
  private static instance: AdaptiveMetadataDisplayManager;

  private constructor() {}

  static getInstance(): AdaptiveMetadataDisplayManager {
    if (!this.instance) {
      this.instance = new AdaptiveMetadataDisplayManager();
    }
    return this.instance;
  }

  /**
   * 🚀 核心方法：自适应显示任意数据
   *
   * 基于数据内容自动判断类型并选择最佳展示方式
   */
  displayAny(data: any, context?: DisplayContext): DisplayResult {
    const startTime = Date.now();

    try {
      let type: DisplayType;
      let content: string;
      let preview: string;
      let title: string;
      let icon: string;
      let color: string;
      let complexity: "simple" | "medium" | "complex" = "simple";

      // 🎯 处理自描述数据：提取data部分和displayHints
      let actualData = data;
      let displayHints: SelfDescribingData["displayHints"] | undefined;

      if (DataTypeChecker.isSelfDescribing(data)) {
        actualData = data.data; // 只使用data部分进行检测和展示
        displayHints = data.displayHints;
      }

      // 基本类型检测逻辑
      if (typeof actualData === "string") {
        if (this.isCodeContent(actualData)) {
          // 代码内容
          type = "code";
          title = "代码内容";
          icon = "💻";
          color = "#6366f1";
          content = actualData;
          const lineCount = actualData.split("\n").length;
          preview = `${lineCount}行代码，${actualData.length}字符`;
          complexity =
            lineCount > 50 ? "complex" : lineCount > 10 ? "medium" : "simple";
        } else {
          // 普通文本
          type = "text";
          title = "文本内容";
          icon = "📄";
          color = "#93c5fd";
          content = actualData;
          const wordCount = actualData.split(/\s+/).length;
          preview = `${wordCount}词，${actualData.length}字符`;
          complexity =
            actualData.length > 500
              ? "complex"
              : actualData.length > 100
              ? "medium"
              : "simple";
        }
      } else if (typeof actualData === "object" && actualData !== null) {
        // JSON对象
        type = "json";
        title = "结构化数据";
        icon = "📊";
        color = "#8b5cf6";
        content = JSON.stringify(actualData, null, 2);
        const keyCount = this.countObjectKeys(actualData);
        preview = `${keyCount}个字段，${content.length}字符`;
        complexity =
          keyCount > 20 ? "complex" : keyCount > 5 ? "medium" : "simple";
      } else {
        // 基本类型（number, boolean, null, undefined等）
        type = "text";
        title = "基本数据";
        icon = "📄";
        color = "#9ca3af";
        content = String(actualData);
        preview = `${typeof actualData}: ${content}`;
        complexity = "simple";
      }

      // 🎨 应用自描述数据的显示提示
      if (displayHints) {
        if (displayHints.type) type = displayHints.type;
        if (displayHints.title) title = displayHints.title;
        if (displayHints.icon) icon = displayHints.icon;
        if (displayHints.color) color = displayHints.color;
      }

      const result: DisplayResult = {
        type,
        title,
        preview,
        content: {
          formatted: content,
          raw: actualData,
          structure: type === "json" ? actualData : undefined,
        },
        metadata: {
          confidence: 0.9,
          processingTime: Date.now() - startTime,
          dataSize: JSON.stringify(actualData).length,
          complexity,
        },
        styling: {
          color,
          icon,
          textSize:
            type === "code" || type === "json"
              ? "font-mono text-sm"
              : undefined,
          bgColor:
            type === "code"
              ? "#f8fafc"
              : type === "json"
              ? "#faf5ff"
              : undefined,
          borderColor:
            type === "code"
              ? "#e2e8f0"
              : type === "json"
              ? "#e9d5ff"
              : undefined,
        },
      };

      return result;
    } catch (error) {
      // 错误兜底处理
      return {
        type: "text",
        title: "展示错误",
        preview: "无法展示此数据",
        content: {
          formatted: `展示错误: ${
            error instanceof Error ? error.message : "未知错误"
          }`,
          raw: data,
        },
        metadata: {
          confidence: 0,
          processingTime: Date.now() - startTime,
          dataSize: 0,
          complexity: "simple",
          warnings: ["数据展示失败"],
        },
        styling: {
          color: "#ef4444",
          icon: "❌",
        },
      };
    }
  }

  /**
   * 检测是否为代码内容
   */
  private isCodeContent(text: string): boolean {
    // 代码特征关键词
    const codeIndicators = [
      // JavaScript/TypeScript
      "function",
      "const",
      "let",
      "var",
      "class",
      "import",
      "export",
      "interface",
      "type",
      "async",
      "await",
      "return",
      // 通用编程语言
      "if (",
      "for (",
      "while (",
      "switch (",
      "catch (",
      "try {",
      // 代码符号
      "=>",
      "==",
      "!=",
      "===",
      "!==",
      "++",
      "--",
      "&&",
      "||",
      // 注释
      "//",
      "/*",
      "*/",
      "#",
      '"""',
      "'''",
      // 括号和分号组合（强代码特征）
      "};",
      "){",
      "})",
      "({",
      "}]",
      "[{",
    ];

    // 计算匹配的关键词数量
    let matchCount = 0;
    for (const indicator of codeIndicators) {
      if (text.includes(indicator)) {
        matchCount++;
      }
    }

    // 检查代码结构特征
    const hasCodeStructure =
      (text.includes("{") && text.includes("}")) || // 有大括号
      (text.includes("(") && text.includes(")") && text.includes(";")) || // 函数调用+分号
      text.split("\n").some((line) => line.trim().endsWith(";")); // 有以分号结尾的行

    // 如果有2个以上关键词匹配或者有明显的代码结构，认为是代码
    return matchCount >= 2 || hasCodeStructure;
  }

  /**
   * 计算对象的键数量（包括嵌套）
   */
  private countObjectKeys(obj: any): number {
    if (typeof obj !== "object" || obj === null) return 0;
    if (Array.isArray(obj)) return obj.length;
    return Object.keys(obj).length;
  }

  /**
   * 批量处理多个数据项
   */
  displayBatch(
    items: Array<{ data: any; context?: DisplayContext }>
  ): DisplayResult[] {
    return items.map((item) => this.displayAny(item.data, item.context));
  }

  /**
   * 获取数据的展示预览（轻量级）
   */
  getPreview(data: any, maxLength: number = 100): string {
    try {
      const result = this.displayAny(data);
      return result.preview.length > maxLength
        ? result.preview.substring(0, maxLength) + "..."
        : result.preview;
    } catch {
      return "无法预览";
    }
  }

  /**
   * 检查数据是否可以被展示
   */
  canDisplay(data: any, context?: DisplayContext): boolean {
    try {
      this.displayAny(data, context);
      return true;
    } catch {
      return false;
    }
  }
}

// =================================================================
// 便捷的工厂函数和工具
// =================================================================

/**
 * 快速展示函数（单例模式）
 */
export function displayData(
  data: any,
  context?: DisplayContext
): DisplayResult {
  return AdaptiveMetadataDisplayManager.getInstance().displayAny(data, context);
}

/**
 * 快速预览函数
 */
export function previewData(data: any, maxLength?: number): string {
  return AdaptiveMetadataDisplayManager.getInstance().getPreview(
    data,
    maxLength
  );
}

/**
 * 创建自描述数据 - 向后兼容方法
 * 用于其他模块的兼容性
 */
export function createSelfDescribingData(
  data: any,
  displayHints?: Partial<SelfDescribingData["displayHints"]>
): SelfDescribingData {
  const finalDisplayHints: Required<SelfDescribingData["displayHints"]> = {
    type: displayHints?.type || "text",
    title: displayHints?.title || "数据展示",
    icon: displayHints?.icon || "📄",
    color: displayHints?.color || "#6b7280",
  };

  return {
    data,
    displayHints: finalDisplayHints,
  };
}

/**
 * 智能 onProgress 调用函数 - 向后兼容方法
 * 自动检测第二个参数类型并调用相应的处理逻辑
 */
export function callProgressCallback(
  onProgress:
    | ((
        message: string,
        chunk: string,
        isComplete: boolean,
        stage: string
      ) => void)
    | undefined,
  message: string,
  data: string | SelfDescribingData,
  isComplete: boolean,
  stage: string
): void {
  if (!onProgress) return;

  // 如果是自描述数据，转换为格式化字符串
  if (typeof data === "object" && data !== null && "displayHints" in data) {
    const displayResult = displayData(data);
    onProgress(message, displayResult.content.formatted, isComplete, stage);
  } else {
    // 传统字符串格式
    onProgress(message, data as string, isComplete, stage);
  }
}

// 默认导出实例
export default AdaptiveMetadataDisplayManager.getInstance();
