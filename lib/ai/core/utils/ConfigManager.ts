/**
 * Configuration Manager - AI Module Configuration Provider
 *
 * Simplified version focusing on:
 * - Structured output format management
 * - Streaming configuration
 * - API validation
 */

import { schemaManager, AIModuleType } from "../../schemas";
import type { DoubaoResponseFormat } from "../clients/DoubaoClient";

export type UnifiedModuleType = "analyzer" | "strategist" | "writer" | "critic";

export class ConfigManager {
  private static instance: ConfigManager;

  private constructor() {}

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Get structured output format for specific module
   */
  public getResponseFormat(
    moduleType: UnifiedModuleType
  ): DoubaoResponseFormat | null {
    const aiModuleType = moduleType.toUpperCase() as keyof typeof AIModuleType;
    return schemaManager.getResponseFormat(AIModuleType[aiModuleType]);
  }

  /**
   * Validate Doubao API configuration
   */
  public validateDoubaoConfig(): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check API key
    if (!process.env.DOUBAO_API_KEY) {
      issues.push("DOUBAO_API_KEY environment variable is not set");
      recommendations.push("Set DOUBAO_API_KEY in your environment variables");
    }

    // Validate API key format (should be UUID-like)
    if (process.env.DOUBAO_API_KEY) {
      const uuidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidPattern.test(process.env.DOUBAO_API_KEY)) {
        issues.push(
          "DOUBAO_API_KEY format appears invalid (expected UUID format)"
        );
        recommendations.push("Verify your API key from Volcano Engine console");
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }

  /**
   * Check if module typically uses reasoning mode
   * Note: This is now a suggestion - modules can override this
   */
  public shouldUseReasoningMode(moduleType: UnifiedModuleType): boolean {
    // ANALYZER, STRATEGIST, CRITIC typically use reasoning; WRITER doesn't
    return moduleType !== "writer";
  }
}
