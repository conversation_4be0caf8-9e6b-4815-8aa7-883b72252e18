/**
 * V6.2 简化流式事件发射器
 *
 * 核心原则：约定大于配置
 * - 移除复杂构建器模式
 * - 约定化配置选项
 * - 简化 API 接口
 * - 保持核心功能完整
 */

import { EventEmitter } from "events";
import { getLogManager, type LogManager } from "./LogManager";
import type { StreamEvent, SupportedModuleType } from "../../types/streaming";

// 约定化默认值
const DEFAULTS = {
  BATCH_DELAY: 5, // 约定 5ms 批处理延迟
  MAX_QUEUE_SIZE: 100, // 约定队列大小
  DEBUG_MODE: false, // 约定生产环境禁用调试
} as const;

/**
 * 简化流式事件发射器
 *
 * 基于约定大于配置原则：
 * - 只有2个必需参数：sessionId 和 moduleType
 * - 其他配置都使用合理的默认约定
 * - 统一的 EventEmitter 接口
 */
export class StreamingEventEmitter extends EventEmitter {
  private sessionId: string;
  private moduleType: SupportedModuleType;
  private logManager: LogManager;
  private eventQueue: StreamEvent[] = [];
  private batchTimer?: NodeJS.Timeout;
  private isDestroyed = false;

  constructor(sessionId: string, moduleType: SupportedModuleType) {
    super();

    this.sessionId = sessionId;
    this.moduleType = moduleType;
    this.logManager = getLogManager();

    // 约定：生产环境不输出调试信息
    if (DEFAULTS.DEBUG_MODE) {
      console.log(`🚀 [StreamingEventEmitter] ${moduleType}:${sessionId}`);
    }
  }

  // =================================================================
  // 核心事件发射方法 - 简化版本
  // =================================================================

  /**
   * 发射内容块事件
   */
  emitChunk(content: string, stage: string, metadata?: any): void {
    if (this.isDestroyed) return;

    const event = this.createEvent("content_chunk", stage, content, {
      metadata,
      isComplete: false,
    });

    this.emitEvent(event);
    this.logToManager(event);
  }

  /**
   * 发射进度事件
   */
  emitProgress(progress: number, message: string, stage: string): void {
    if (this.isDestroyed) return;

    const event = this.createEvent("stage_progress", stage, message, {
      progress: Math.min(100, Math.max(0, progress)),
    });

    this.emitEvent(event);
    this.logToManager(event);
  }

  /**
   * 发射阶段开始事件
   */
  emitStageStart(stage: string, context?: any): void {
    if (this.isDestroyed) return;

    const event = this.createEvent("stage_start", stage, `🟢 开始: ${stage}`, {
      metadata: { contextData: context },
    });

    this.emitEvent(event);
    this.logToManager(event);
  }

  /**
   * 发射阶段完成事件
   */
  emitStageComplete(stage: string, result: any, metadata?: any): void {
    if (this.isDestroyed) return;

    const event = this.createEvent(
      "stage_complete",
      stage,
      `✅ 完成: ${stage}`,
      {
        isComplete: true,
        progress: 100,
        metadata: { stageOutput: result, ...metadata },
      }
    );

    this.emitEvent(event);
    this.logToManager(event);

    // 发射标准 EventEmitter 事件
    this.emit("complete", result, metadata);
  }

  /**
   * 发射错误事件
   */
  emitError(error: Error, stage: string, context?: any): void {
    if (this.isDestroyed) return;

    const event = this.createEvent(
      "stage_error",
      stage,
      `❌ 错误: ${error.message}`,
      {
        metadata: { contextData: { error: error.message, context } },
      }
    );

    this.emitEvent(event);
    this.logToManager(event);

    // 发射标准 EventEmitter 事件
    this.emit("error", error, stage, context);
  }

  // =================================================================
  // 简化的监听器接口
  // =================================================================

  /**
   * 添加事件监听器 - 使用标准 EventEmitter 模式
   */
  onEvent(listener: (event: StreamEvent) => void): void {
    this.on("stream_event", listener);
  }

  /**
   * 移除事件监听器
   */
  offEvent(listener: (event: StreamEvent) => void): void {
    this.off("stream_event", listener);
  }

  // =================================================================
  // 访问器方法
  // =================================================================

  getSessionId(): string {
    return this.sessionId;
  }

  getModuleType(): SupportedModuleType {
    return this.moduleType;
  }

  // =================================================================
  // 生命周期管理
  // =================================================================

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.isDestroyed) return;

    this.isDestroyed = true;

    // 清理定时器
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    // 处理剩余事件
    if (this.eventQueue.length > 0) {
      this.processBatch();
    }

    // 移除所有监听器
    this.removeAllListeners();

    if (DEFAULTS.DEBUG_MODE) {
      console.log(`🧹 [Cleanup] ${this.moduleType}:${this.sessionId}`);
    }
  }

  // =================================================================
  // 内部实现方法 - 简化版本
  // =================================================================

  /**
   * 创建标准流式事件
   */
  private createEvent(
    eventType: StreamEvent["eventType"],
    stage: string,
    content: string,
    options: Partial<
      Omit<
        StreamEvent,
        | "sessionId"
        | "moduleType"
        | "eventType"
        | "stage"
        | "content"
        | "timestamp"
      >
    > = {}
  ): StreamEvent {
    return {
      sessionId: this.sessionId,
      moduleType: this.moduleType,
      eventType,
      stage,
      content,
      timestamp: Date.now(),
      ...options,
    };
  }

  /**
   * 发射事件 - 简化的批处理
   */
  private emitEvent(event: StreamEvent): void {
    // 约定：简化的批处理机制
    this.eventQueue.push(event);

    if (this.eventQueue.length >= DEFAULTS.MAX_QUEUE_SIZE) {
      this.processBatch();
    } else if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, DEFAULTS.BATCH_DELAY);
    }
  }

  /**
   * 处理事件批次
   */
  private processBatch(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue.length = 0;

    // 分发事件
    events.forEach((event) => {
      this.emit("stream_event", event);
    });
  }

  /**
   * 记录到 LogManager
   */
  private logToManager(event: StreamEvent): void {
    this.logManager.logStreamEvent(
      event.sessionId,
      event.moduleType as any,
      this.mapEventType(event.eventType),
      event.stage,
      {
        content: event.content,
        metadata: event.metadata,
        isComplete: event.isComplete,
        progress: event.progress,
      }
    );
  }

  /**
   * 映射事件类型
   */
  private mapEventType(
    eventType: StreamEvent["eventType"]
  ):
    | "stage_start"
    | "stage_progress"
    | "stage_complete"
    | "content_chunk"
    | "complete"
    | "error" {
    switch (eventType) {
      case "stage_start":
        return "stage_start";
      case "stage_progress":
        return "stage_progress";
      case "stage_complete":
        return "stage_complete";
      case "stage_error":
        return "error";
      case "content_chunk":
        return "content_chunk";
      default:
        return "content_chunk";
    }
  }
}

/**
 * 简化的工厂函数 - 替代复杂构建器
 */
export function createStreamingEventEmitter(
  sessionId: string,
  moduleType: SupportedModuleType
): StreamingEventEmitter {
  return new StreamingEventEmitter(sessionId, moduleType);
}
