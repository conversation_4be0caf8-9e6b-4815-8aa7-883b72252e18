/**
 * V5.3 调试平台 - 统一日志管理器
 *
 * 核心功能：
 * - 日志收集和存储
 * - 实时查询和过滤
 * - 性能监控集成
 * - 会话报告生成
 * - 批量导出和清理
 */

import type {
  DebugLoggerInterface,
  ModuleExecutionLog,
  LogLevel,
  ExecutionStatus,
  LogFilter,
  LogQueryResult,
  LogManagerConfig,
  SessionReport,
  LogExportOptions,
  ExecutionContext,
  PerformanceMetrics,
  ErrorDetails,
  LogEvent,
  LogEventCallback,
  LogEventType,
  ErrorType,
  LogAggregations,
  LogStatistics,
} from "@/types/debug-logging";
import type { ModuleType, ModuleOutput } from "@/lib/ai/types";

/**
 * 默认配置
 */
const DEFAULT_CONFIG: LogManagerConfig = {
  maxLogs: 1000,
  retentionTime: 24 * 60 * 60 * 1000, // 24小时
  enablePersistence: true,
  storageEngine: "memory", // 在浏览器环境下可改为 localStorage
  batchSize: 10,
  cleanupInterval: 60 * 60 * 1000, // 1小时
  enablePerformanceMonitoring: true,
  enableErrorTracking: true,
  compressionLevel: 6,
};

export class LogManager implements DebugLoggerInterface {
  private logs: Map<string, ModuleExecutionLog> = new Map();
  private sessionLogs: Map<string, string[]> = new Map();
  private config: LogManagerConfig;
  private eventCallbacks: LogEventCallback[] = [];
  private cleanupTimer?: NodeJS.Timeout;
  private batchQueue: ModuleExecutionLog[] = [];
  private batchTimer?: NodeJS.Timeout;
  private logCounter: number = 0; // 添加计数器保证时间戳唯一性

  // 流式日志聚合器 - 将多个content_chunk整合到一条日志中
  private streamingLogs: Map<
    string,
    {
      logId: string;
      sessionId: string;
      moduleType: ModuleType;
      stage: string;
      accumulatedContent: string;
      startTime: number;
      lastUpdateTime: number;
      chunkCount: number;
    }
  > = new Map();

  constructor(config?: Partial<LogManagerConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeCleanupTimer();
    this.initializeBatchProcessor();
  }

  // =================================================================
  // 核心日志记录方法
  // =================================================================

  /**
   * 记录模块执行开始
   */
  logExecutionStart(
    sessionId: string,
    moduleType: ModuleType,
    moduleName: string,
    context?: Record<string, any>
  ): string {
    const timestamp = Date.now();
    const logId = this.generateLogId(timestamp);

    const log: ModuleExecutionLog = {
      id: logId,
      sessionId,
      moduleType,
      moduleName,
      level: "info",
      status: "running",
      message: `Module ${moduleName} execution started`,
      timestamp,
      startTime: timestamp,
      context: context || {},
    };

    this.addLog(log);
    this.emit("log_created", log);

    // 确保会话日志跟踪
    if (!this.sessionLogs.has(sessionId)) {
      this.sessionLogs.set(sessionId, []);
      this.emit("session_started", log);
    }
    this.sessionLogs.get(sessionId)!.push(logId);

    return logId;
  }

  /**
   * 记录模块执行完成
   */
  logExecutionComplete(
    logId: string,
    output: ModuleOutput,
    metrics?: PerformanceMetrics
  ): void {
    const startLog = this.logs.get(logId);
    if (!startLog) {
      console.warn(`Start log not found: ${logId}`);
      return;
    }

    const endTime = Date.now();
    const processingTime = endTime - startLog.startTime;

    // 创建新的完成日志，而不是更新开始日志
    const completionLog: ModuleExecutionLog = {
      id: this.generateLogId(endTime),
      sessionId: startLog.sessionId,
      moduleType: startLog.moduleType,
      moduleName: startLog.moduleName,
      level: output.status === "success" ? "info" : "error",
      status: output.status === "success" ? "completed" : "failed",
      message: `Module ${startLog.moduleName} execution ${
        output.status === "success" ? "completed" : "failed"
      } in ${processingTime}ms`,
      timestamp: endTime,
      startTime: startLog.startTime,
      endTime,
      processingTime,
      outputSnapshot: output,
      metrics: {
        ...metrics,
        confidence: output.confidence,
        processingTime: output.processing_time || processingTime,
      },
      context: {
        ...startLog.context,
        processingTimeMs: processingTime,
        confidence: output.confidence,
        outputStatus: output.status,
        outputMetadata: output.metadata,
      },
    };

    this.addLog(completionLog);
    this.emit("log_created", completionLog);

    // 将完成日志添加到会话中
    if (this.sessionLogs.has(startLog.sessionId)) {
      this.sessionLogs.get(startLog.sessionId)!.push(completionLog.id);
    }

    if (output.status === "error") {
      this.emit("error_occurred", completionLog);
    }
  }

  /**
   * 记录模块执行失败
   */
  logExecutionFailure(
    logId: string,
    error: Error,
    errorDetails?: Partial<ErrorDetails>
  ): void {
    const startLog = this.logs.get(logId);
    if (!startLog) {
      console.warn(`Start log not found: ${logId}`);
      return;
    }

    const endTime = Date.now();
    const processingTime = endTime - startLog.startTime;

    // 创建新的失败日志，而不是更新开始日志
    const failureLog: ModuleExecutionLog = {
      id: this.generateLogId(endTime),
      sessionId: startLog.sessionId,
      moduleType: startLog.moduleType,
      moduleName: startLog.moduleName,
      level: "error",
      status: "failed",
      message: `Module ${startLog.moduleName} execution failed: ${error.message}`,
      timestamp: endTime,
      startTime: startLog.startTime,
      endTime,
      processingTime,
      context: startLog.context,
      error: {
        type: this.categorizeError(error),
        message: error.message,
        stackTrace: error.stack,
        recoveryAttempted: false,
        ...errorDetails,
      },
    };

    this.addLog(failureLog);
    this.emit("log_created", failureLog);

    // 将失败日志添加到会话中
    if (this.sessionLogs.has(startLog.sessionId)) {
      this.sessionLogs.get(startLog.sessionId)!.push(failureLog.id);
    }

    this.emit("error_occurred", failureLog);
  }

  /**
   * 记录一般信息
   */
  logInfo(
    sessionId: string,
    moduleType: ModuleType,
    message: string,
    context?: Record<string, any>
  ): void {
    this.createSimpleLog(sessionId, moduleType, "info", message, context);
  }

  /**
   * 记录警告
   */
  logWarning(
    sessionId: string,
    moduleType: ModuleType,
    message: string,
    context?: Record<string, any>
  ): void {
    this.createSimpleLog(sessionId, moduleType, "warn", message, context);
  }

  /**
   * 记录错误
   */
  logError(
    sessionId: string,
    moduleType: ModuleType,
    error: Error,
    context?: Record<string, any>
  ): void {
    const log = this.createSimpleLog(
      sessionId,
      moduleType,
      "error",
      `Error: ${error.message}`,
      context
    );

    // 添加错误详情
    const updatedLog: ModuleExecutionLog = {
      ...log,
      error: {
        type: this.categorizeError(error),
        message: error.message,
        stackTrace: error.stack,
        recoveryAttempted: false,
      },
    };

    this.updateLog(updatedLog);
    this.emit("error_occurred", updatedLog);
  }

  /**
   * 记录流式事件 - 支持实时流式输出日志聚合
   */
  logStreamEvent(
    sessionId: string,
    moduleType: ModuleType,
    eventType:
      | "stage_start"
      | "stage_progress"
      | "stage_complete"
      | "content_chunk"
      | "complete"
      | "error",
    stage: string, // 🎯 支持任意自定义stage名称
    data: {
      content?: string;
      message?: string;
      progress?: number;
      metadata?: any;
      error?: string;
      isComplete?: boolean; // 🎯 新增：明确指定是否完成
    }
  ): string {
    // 对于content_chunk事件，使用聚合逻辑
    if (eventType === "content_chunk" && data.content) {
      return this.handleStreamingContentChunk(
        sessionId,
        moduleType,
        stage,
        data.content,
        data.message,
        data.metadata,
        data.isComplete || false // 🎯 传递isComplete参数
      );
    }
    const timestamp = Date.now();
    const logId = this.generateLogId(timestamp);

    // 根据事件类型确定日志级别和状态
    let level: LogLevel = "info";
    let status: ExecutionStatus = "running";
    let message = "";

    switch (eventType) {
      case "stage_start":
        message = `${stage} stage started`;
        status = "running";
        break;
      case "stage_progress":
        message = `${stage} stage progress: ${data.progress || 0}%`;
        status = "running";
        break;
      case "stage_complete":
        message = `${stage} stage completed`;
        status = "completed";
        break;
      case "content_chunk":
        message = `Content chunk received (${data.content?.length || 0} chars)`;
        status = "running";
        level = "debug";
        break;
      case "complete":
        message = `Stream generation completed`;
        status = "completed";
        break;
      case "error":
        message = `Stream error: ${data.error || "Unknown error"}`;
        status = "failed";
        level = "error";
        break;
    }

    const log: ModuleExecutionLog = {
      id: logId,
      sessionId,
      moduleType,
      moduleName: `${moduleType}-stream`,
      level,
      status,
      message,
      timestamp,
      startTime: timestamp,
      context: {
        streamEvent: {
          type: eventType,
          stage,
          data,
        },
        isStreamingLog: true,
      },
    };

    this.addLog(log);
    this.emit("log_created", log);

    // 确保会话日志跟踪
    if (!this.sessionLogs.has(sessionId)) {
      this.sessionLogs.set(sessionId, []);
      this.emit("session_started", log);
    }
    this.sessionLogs.get(sessionId)!.push(logId);

    // 发送流式事件
    this.emit("stream_event", {
      logId,
      sessionId,
      eventType,
      stage,
      data,
      timestamp,
    });

    return logId;
  }

  /**
   * 批量记录流式事件 - 优化性能
   */
  logStreamEventBatch(
    events: Array<{
      sessionId: string;
      moduleType: ModuleType;
      eventType:
        | "stage_start"
        | "stage_progress"
        | "stage_complete"
        | "content_chunk"
        | "complete"
        | "error";
      stage: string; // 🎯 支持任意自定义stage名称
      data: {
        content?: string;
        progress?: number;
        metadata?: any;
        error?: string;
        isComplete?: boolean; // 🎯 支持isComplete参数
      };
    }>
  ): string[] {
    const logIds: string[] = [];

    for (const event of events) {
      const logId = this.logStreamEvent(
        event.sessionId,
        event.moduleType,
        event.eventType,
        event.stage,
        event.data
      );
      logIds.push(logId);
    }

    return logIds;
  }

  // =================================================================
  // 查询和统计方法
  // =================================================================

  /**
   * 查询日志
   */
  async queryLogs(filter: LogFilter): Promise<LogQueryResult> {
    const startTime = Date.now();

    let filteredLogs = Array.from(this.logs.values());

    // 应用过滤条件
    if (filter.sessionIds?.length) {
      filteredLogs = filteredLogs.filter((log) =>
        filter.sessionIds!.includes(log.sessionId)
      );
    }

    if (filter.moduleTypes?.length) {
      filteredLogs = filteredLogs.filter((log) =>
        filter.moduleTypes!.includes(log.moduleType)
      );
    }

    if (filter.levels?.length) {
      filteredLogs = filteredLogs.filter((log) =>
        filter.levels!.includes(log.level)
      );
    }

    if (filter.statuses?.length) {
      filteredLogs = filteredLogs.filter((log) =>
        filter.statuses!.includes(log.status)
      );
    }

    if (filter.timeRange) {
      filteredLogs = filteredLogs.filter(
        (log) =>
          log.timestamp >= filter.timeRange!.start &&
          log.timestamp <= filter.timeRange!.end
      );
    }

    if (filter.errorTypes?.length) {
      filteredLogs = filteredLogs.filter(
        (log) => log.error && filter.errorTypes!.includes(log.error.type)
      );
    }

    if (filter.keywords?.length) {
      const keywords = filter.keywords.map((k) => k.toLowerCase());
      filteredLogs = filteredLogs.filter((log) =>
        keywords.some(
          (keyword) =>
            log.message.toLowerCase().includes(keyword) ||
            log.moduleName.toLowerCase().includes(keyword)
        )
      );
    }

    // 排序
    if (filter.sort) {
      filteredLogs.sort((a, b) => {
        const aValue = this.getSortValue(a, filter.sort!.field);
        const bValue = this.getSortValue(b, filter.sort!.field);

        // 首先按指定字段排序
        let primaryDiff: number;
        if (filter.sort!.direction === "desc") {
          primaryDiff = bValue - aValue;
        } else {
          primaryDiff = aValue - bValue;
        }

        // 如果主排序字段值相同，使用日志ID序列号作为二级排序
        if (primaryDiff === 0) {
          const aSequence = this.extractSequenceFromLogId(a.id);
          const bSequence = this.extractSequenceFromLogId(b.id);
          return filter.sort!.direction === "desc"
            ? bSequence - aSequence // 🎯 修复：降序时序列号大的在前
            : aSequence - bSequence; // 升序时序列号小的在前
        }

        return primaryDiff;
      });
    } else {
      // 默认按时间戳降序排列，时间戳相同时按日志ID序列号排序
      filteredLogs.sort((a, b) => {
        // 首先按时间戳排序
        const timestampDiff = b.timestamp - a.timestamp;
        if (timestampDiff !== 0) {
          return timestampDiff;
        }

        // 时间戳相同时，按日志ID中的序列号排序
        const aSequence = this.extractSequenceFromLogId(a.id);
        const bSequence = this.extractSequenceFromLogId(b.id);
        return bSequence - aSequence; // 🎯 修复：序列号大的在前面，保持与时间戳降序一致
      });
    }

    // 限制结果数量
    const total = filteredLogs.length;
    if (filter.limit) {
      filteredLogs = filteredLogs.slice(0, filter.limit);
    }

    // 生成聚合统计
    const aggregations = this.generateAggregations(filteredLogs);

    const queryTime = Date.now() - startTime;

    return {
      logs: filteredLogs,
      total,
      hasMore: filter.limit ? total > filter.limit : false,
      queryTime,
      aggregations,
    };
  }

  /**
   * 生成会话报告
   */
  async generateSessionReport(sessionId: string): Promise<SessionReport> {
    const sessionLogIds = this.sessionLogs.get(sessionId) || [];
    const logs = sessionLogIds
      .map((id) => this.logs.get(id))
      .filter(Boolean) as ModuleExecutionLog[];

    if (logs.length === 0) {
      throw new Error(`No logs found for session: ${sessionId}`);
    }

    // 排序日志
    logs.sort((a, b) => a.timestamp - b.timestamp);

    const startTime = Math.min(...logs.map((l) => l.startTime));
    const endTime = Math.max(...logs.map((l) => l.endTime || l.timestamp));
    const duration = endTime - startTime;

    // 执行的模块列表
    const executedModules = Array.from(
      new Set(logs.map((l) => l.moduleType))
    ) as ModuleType[];

    // 总体状态
    const hasErrors = logs.some(
      (l) => l.status === "failed" || l.level === "error"
    );
    const hasWarnings = logs.some((l) => l.level === "warn");
    const overallStatus = hasErrors
      ? "failed"
      : hasWarnings
      ? "partial"
      : "success";

    // 性能概述
    const performanceSummary = {
      totalProcessingTime: logs.reduce(
        (sum, l) => sum + (l.processingTime || 0),
        0
      ),
      totalTokensUsed: logs.reduce(
        (sum, l) => sum + (l.metrics?.tokensUsed || 0),
        0
      ),
      averageConfidence: this.calculateAverage(
        logs.map((l) => l.metrics?.confidence).filter(Boolean) as number[]
      ),
      cacheHitRate: this.calculateAverage(
        logs.map((l) => l.metrics?.cacheHitRate).filter(Boolean) as number[]
      ),
    };

    // 错误概述
    const errorLogs = logs.filter((l) => l.error);
    const errorsByType: Record<ErrorType, number> = {} as any;
    errorLogs.forEach((log) => {
      if (log.error) {
        errorsByType[log.error.type] = (errorsByType[log.error.type] || 0) + 1;
      }
    });

    const errorSummary = {
      totalErrors: errorLogs.length,
      errorsByType,
      criticalErrors: errorLogs.filter(
        (l) => l.level === "fatal" || l.level === "error"
      ).length,
    };

    // 生成建议
    const recommendations = this.generateRecommendations(logs);

    return {
      sessionId,
      startTime,
      endTime,
      duration,
      executedModules,
      overallStatus,
      performanceSummary,
      errorSummary,
      logs,
      recommendations,
    };
  }

  /**
   * 导出日志
   */
  async exportLogs(options: LogExportOptions): Promise<Blob> {
    const queryResult = await this.queryLogs(options.filter || {});
    const logs = queryResult.logs;

    switch (options.format) {
      case "json":
        return this.exportAsJSON(logs, options);
      case "csv":
        return this.exportAsCSV(logs, options);
      case "txt":
        return this.exportAsText(logs, options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * 清理所有日志
   */
  async cleanup(): Promise<number> {
    this.emit(
      "cleanup_started",
      this.createSystemLog("cleanup_started", "Starting log cleanup")
    );

    const totalCount = this.logs.size;

    // 直接清空所有日志
    this.logs.clear();
    this.sessionLogs.clear();

    this.emit(
      "cleanup_ended",
      this.createSystemLog(
        "cleanup_ended",
        `Cleanup completed, removed ${totalCount} logs`
      )
    );

    return totalCount;
  }

  // =================================================================
  // 事件管理
  // =================================================================

  /**
   * 添加事件监听器
   */
  addEventCallback(callback: LogEventCallback): void {
    this.eventCallbacks.push(callback);
  }

  /**
   * 移除事件监听器
   */
  removeEventCallback(callback: LogEventCallback): void {
    const index = this.eventCallbacks.indexOf(callback);
    if (index > -1) {
      this.eventCallbacks.splice(index, 1);
    }
  }

  // =================================================================
  // 流式日志聚合方法
  // =================================================================

  /**
   * 处理流式内容块 - 支持聚合和优化显示
   */
  private handleStreamingContentChunk(
    sessionId: string,
    moduleType: ModuleType,
    stage: string,
    content: string,
    message?: string,
    metadata?: any,
    isComplete?: boolean
  ): string {
    const streamKey = `${sessionId}-${moduleType}-${stage}`;
    const now = Date.now();

    let streamingLog = this.streamingLogs.get(streamKey);

    if (!streamingLog) {
      // 创建新的流式日志聚合器
      const logId = this.generateLogId(now);
      streamingLog = {
        logId,
        sessionId,
        moduleType,
        stage,
        accumulatedContent: content,
        startTime: now,
        lastUpdateTime: now,
        chunkCount: 1,
      };
      this.streamingLogs.set(streamKey, streamingLog);

      // 🎯 创建初始日志条目 - 状态基于isComplete参数
      const initialLog: ModuleExecutionLog = {
        id: logId,
        sessionId,
        moduleType,
        moduleName: `${moduleType}-streaming`,
        level: "info",
        status: isComplete ? "completed" : "running", // 🎯 基于isComplete参数
        message: this.generateLogMessage(
          message,
          content,
          stage,
          isComplete || false // 🎯 传递isComplete参数
        ),
        timestamp: now,
        startTime: now,
        endTime: isComplete ? now : undefined, // 🎯 基于isComplete参数
        context: {
          streamingContent: {
            stage,
            isStreaming: !isComplete, // 🎯 基于isComplete参数
            accumulatedContent: content,
            chunkCount: 1,
            startTime: now,
            completedAt: isComplete ? now : undefined, // 🎯 基于isComplete参数
          },
          metadata,
        },
      };

      this.addLog(initialLog);
      this.emit("log_created", initialLog);

      // 确保会话日志跟踪
      if (!this.sessionLogs.has(sessionId)) {
        this.sessionLogs.set(sessionId, []);
        this.emit("session_started", initialLog);
      }
      this.sessionLogs.get(sessionId)!.push(logId);

      // 🎯 如果标记为完成，立即清理聚合器
      if (isComplete) {
        this.streamingLogs.delete(streamKey);
      }
    } else {
      // 更新现有的流式日志
      streamingLog.accumulatedContent += content;
      streamingLog.lastUpdateTime = now;
      streamingLog.chunkCount++;

      // 更新日志条目
      const existingLog = this.logs.get(streamingLog.logId);
      if (existingLog) {
        const updatedLog: ModuleExecutionLog = {
          ...existingLog,
          message: this.generateUpdatedLogMessage(
            message,
            stage,
            streamingLog.chunkCount,
            streamingLog.accumulatedContent.length,
            isComplete || false // 🎯 传递isComplete参数
          ),
          timestamp: now,
          status: isComplete ? "completed" : "running", // 🎯 基于isComplete参数
          endTime: isComplete ? now : undefined, // 🎯 基于isComplete参数
          context: {
            ...existingLog.context,
            streamingContent: {
              stage,
              isStreaming: !isComplete, // 🎯 基于isComplete参数
              accumulatedContent: streamingLog.accumulatedContent,
              chunkCount: streamingLog.chunkCount,
              startTime: streamingLog.startTime,
              lastUpdateTime: now,
              completedAt: isComplete ? now : undefined, // 🎯 基于isComplete参数
            },
            metadata,
          },
        };

        this.logs.set(streamingLog.logId, updatedLog);
        this.emit("log_updated", updatedLog);
      }

      // 🎯 如果标记为完成，清理聚合器
      if (isComplete) {
        this.streamingLogs.delete(streamKey);
      }
    }

    // 发送流式更新事件（用于实时UI更新）
    this.emit("stream_content_update", {
      logId: streamingLog.logId,
      sessionId,
      moduleType,
      stage,
      content,
      accumulatedContent: streamingLog.accumulatedContent,
      chunkCount: streamingLog.chunkCount,
      timestamp: now,
    });

    return streamingLog.logId;
  }

  /**
   * 完成流式日志聚合
   */
  completeStreamingLog(
    sessionId: string,
    moduleType: ModuleType,
    stage: string,
    finalMetadata?: any
  ): string | null {
    const streamKey = `${sessionId}-${moduleType}-${stage}`;
    const streamingLog = this.streamingLogs.get(streamKey);

    if (!streamingLog) {
      return null;
    }

    const now = Date.now();
    const processingTime = now - streamingLog.startTime;

    // 更新最终日志状态
    const existingLog = this.logs.get(streamingLog.logId);
    if (existingLog) {
      const completedLog: ModuleExecutionLog = {
        ...existingLog,
        status: "completed",
        message: `✅ ${stage} 流式内容生成完成 (${streamingLog.chunkCount} chunks, ${streamingLog.accumulatedContent.length} chars)`,
        timestamp: now,
        endTime: now,
        processingTime,
        context: {
          ...existingLog.context,
          streamingContent: {
            stage,
            isStreaming: false,
            accumulatedContent: streamingLog.accumulatedContent,
            chunkCount: streamingLog.chunkCount,
            startTime: streamingLog.startTime,
            lastUpdateTime: now,
            completedAt: now,
            processingTime,
          },
          finalMetadata,
        },
      };

      this.logs.set(streamingLog.logId, completedLog);
      this.emit("log_updated", completedLog);
    }

    // 清理流式日志聚合器
    this.streamingLogs.delete(streamKey);

    return streamingLog.logId;
  }

  // =================================================================
  // 私有辅助方法
  // =================================================================

  private generateLogId(timestamp?: number): string {
    // 使用时间戳 + 计数器 + 随机字符串确保唯一性
    const ts = timestamp || Date.now();
    const counter = this.logCounter++;
    const random = Math.random().toString(36).substring(2, 11);
    const logId = `log-${ts}-${counter}-${random}`;

    return logId;
  }

  private addLog(log: ModuleExecutionLog): void {
    this.logs.set(log.id, log);

    if (this.config.enablePersistence) {
      this.batchQueue.push(log);
      this.scheduleBatchWrite();
    }
  }

  private updateLog(log: ModuleExecutionLog): void {
    this.logs.set(log.id, log);

    if (this.config.enablePersistence) {
      this.batchQueue.push(log);
      this.scheduleBatchWrite();
    }
  }

  private createSimpleLog(
    sessionId: string,
    moduleType: ModuleType,
    level: LogLevel,
    message: string,
    context?: Record<string, any>
  ): ModuleExecutionLog {
    const timestamp = Date.now();
    const logId = this.generateLogId(timestamp);

    const log: ModuleExecutionLog = {
      id: logId,
      sessionId,
      moduleType,
      moduleName: moduleType,
      level,
      status: "completed",
      message,
      timestamp,
      startTime: timestamp,
      endTime: timestamp,
      processingTime: 0,
      metadata: context,
      context: context,
    };

    this.addLog(log);
    this.emit("log_created", log);

    // 确保会话日志跟踪
    if (!this.sessionLogs.has(sessionId)) {
      this.sessionLogs.set(sessionId, []);
    }
    this.sessionLogs.get(sessionId)!.push(logId);

    return log;
  }

  private createSystemLog(
    _type: LogEventType,
    message: string
  ): ModuleExecutionLog {
    return this.createSimpleLog("system", "analyzer", "info", message);
  }

  // Method overloads for emit
  private emit(eventType: LogEventType, log: ModuleExecutionLog): void;
  private emit(
    eventType: "stream_event" | "stream_content_update",
    data: any
  ): void;
  private emit(
    eventType: LogEventType | "stream_event" | "stream_content_update",
    logOrData: ModuleExecutionLog | any
  ): void {
    if (eventType === "stream_event" || eventType === "stream_content_update") {
      // Handle custom stream events
      const customEvent = {
        type: eventType,
        ...logOrData,
      };

      this.eventCallbacks.forEach((callback) => {
        try {
          callback(customEvent);
        } catch (error) {
          console.error("Error in log event callback:", error);
        }
      });
    } else {
      // Handle standard log events
      const event: LogEvent = {
        type: eventType as LogEventType,
        log: logOrData as ModuleExecutionLog,
        timestamp: Date.now(),
      };

      this.eventCallbacks.forEach((callback) => {
        try {
          callback(event);
        } catch (error) {
          console.error("Error in log event callback:", error);
        }
      });
    }
  }

  private categorizeError(error: Error): ErrorType {
    const message = error.message.toLowerCase();

    if (message.includes("timeout")) return "timeout";
    if (message.includes("network") || message.includes("fetch"))
      return "network";
    if (message.includes("validation") || message.includes("invalid"))
      return "validation";
    if (message.includes("permission") || message.includes("unauthorized"))
      return "permission";
    if (message.includes("parse") || message.includes("json")) return "parsing";
    if (message.includes("config")) return "configuration";
    if (message.includes("resource") || message.includes("memory"))
      return "resource";
    if (message.includes("api")) return "api_error";

    return "unknown";
  }

  private getSortValue(log: ModuleExecutionLog, field: string): number {
    switch (field) {
      case "timestamp":
        return log.timestamp;
      case "processingTime":
        return log.processingTime || 0;
      case "level":
        return this.getLevelPriority(log.level);
      case "status":
        return this.getStatusPriority(log.status);
      default:
        return 0;
    }
  }

  private getLevelPriority(level: LogLevel): number {
    const priorities = {
      trace: 1,
      debug: 2,
      info: 3,
      warn: 4,
      error: 5,
      fatal: 6,
    };
    return priorities[level] || 0;
  }

  private getStatusPriority(status: ExecutionStatus): number {
    const priorities = {
      completed: 1,
      running: 2,
      pending: 3,
      retrying: 4,
      timeout: 5,
      cancelled: 6,
      failed: 7,
    };
    return priorities[status] || 0;
  }

  private generateAggregations(logs: ModuleExecutionLog[]): LogAggregations {
    const byModuleType: Record<ModuleType, LogStatistics> = {} as any;
    const byStatus: Record<ExecutionStatus, number> = {} as any;
    const byErrorType: Record<ErrorType, number> = {} as any;

    // 统计各模块类型
    const moduleTypes = Array.from(new Set(logs.map((l) => l.moduleType)));
    moduleTypes.forEach((moduleType) => {
      const moduleLogs = logs.filter((l) => l.moduleType === moduleType);
      const successCount = moduleLogs.filter(
        (l) => l.status === "completed"
      ).length;
      const totalProcessingTime = moduleLogs.reduce(
        (sum, l) => sum + (l.processingTime || 0),
        0
      );

      byModuleType[moduleType] = {
        totalExecutions: moduleLogs.length,
        successCount,
        failureCount: moduleLogs.length - successCount,
        successRate:
          moduleLogs.length > 0 ? successCount / moduleLogs.length : 0,
        averageProcessingTime:
          moduleLogs.length > 0 ? totalProcessingTime / moduleLogs.length : 0,
        totalProcessingTime,
      };
    });

    // 统计状态分布
    logs.forEach((log) => {
      byStatus[log.status] = (byStatus[log.status] || 0) + 1;
    });

    // 统计错误类型
    logs.forEach((log) => {
      if (log.error) {
        byErrorType[log.error.type] = (byErrorType[log.error.type] || 0) + 1;
      }
    });

    // 性能统计
    const processingTimes = logs
      .map((l) => l.processingTime)
      .filter(Boolean) as number[];
    const tokenCounts = logs
      .map((l) => l.metrics?.tokensUsed)
      .filter(Boolean) as number[];
    const confidenceScores = logs
      .map((l) => l.metrics?.confidence)
      .filter(Boolean) as number[];

    const performance = {
      averageProcessingTime: this.calculateAverage(processingTimes),
      medianProcessingTime: this.calculateMedian(processingTimes),
      totalTokensUsed: tokenCounts.reduce((sum, count) => sum + count, 0),
      averageConfidence: this.calculateAverage(confidenceScores),
    };

    return {
      byModuleType,
      byStatus,
      byErrorType,
      performance,
    };
  }

  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
  }

  private calculateMedian(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }

  private generateRecommendations(logs: ModuleExecutionLog[]): string[] {
    const recommendations: string[] = [];
    const errorLogs = logs.filter((l) => l.error);
    const slowLogs = logs.filter((l) => (l.processingTime || 0) > 10000); // 超过10秒

    if (errorLogs.length > 0) {
      recommendations.push(
        `发现 ${errorLogs.length} 个错误，建议检查模块配置和输入数据`
      );
    }

    if (slowLogs.length > 0) {
      recommendations.push(
        `发现 ${slowLogs.length} 个慢执行模块，建议优化处理逻辑或增加超时设置`
      );
    }

    const highTokenUsage = logs.filter(
      (l) => (l.metrics?.tokensUsed || 0) > 2000
    );
    if (highTokenUsage.length > 0) {
      recommendations.push(`发现高Token使用，建议优化提示词长度或调整模型参数`);
    }

    return recommendations;
  }

  private initializeCleanupTimer(): void {
    if (this.config.cleanupInterval > 0) {
      this.cleanupTimer = setInterval(
        () => this.cleanup(),
        this.config.cleanupInterval
      );
    }
  }

  private initializeBatchProcessor(): void {
    // 批量处理定时器会在需要时启动
  }

  private scheduleBatchWrite(): void {
    if (this.batchTimer) return;

    this.batchTimer = setTimeout(() => {
      this.processBatch();
      this.batchTimer = undefined;
    }, 1000); // 1秒后处理批量写入
  }

  private processBatch(): void {
    if (this.batchQueue.length === 0) return;

    // 在真实应用中，这里会进行持久化存储
    // 例如写入 localStorage、IndexedDB 或发送到服务器
    console.log(`Processing batch of ${this.batchQueue.length} logs`);

    this.batchQueue.length = 0; // 清空队列
  }

  /**
   * 导出为JSON格式
   */
  private exportAsJSON(
    logs: ModuleExecutionLog[],
    options: LogExportOptions
  ): Blob {
    const data = {
      timestamp: Date.now(),
      totalLogs: logs.length,
      logs: options.includeMetadata
        ? logs
        : logs.map((log) => ({
            id: log.id,
            sessionId: log.sessionId,
            moduleType: log.moduleType,
            moduleName: log.moduleName,
            level: log.level,
            status: log.status,
            message: log.message,
            timestamp: log.timestamp,
            processingTime: log.processingTime,
          })),
    };

    const jsonString = JSON.stringify(data, null, 2);
    return new Blob([jsonString], { type: "application/json" });
  }

  /**
   * 导出为CSV格式
   */
  private exportAsCSV(
    logs: ModuleExecutionLog[],
    _options: LogExportOptions
  ): Blob {
    const headers = [
      "ID",
      "Session ID",
      "Module Type",
      "Module Name",
      "Level",
      "Status",
      "Message",
      "Timestamp",
      "Processing Time",
      "Error Type",
    ];

    const rows = logs.map((log) => [
      log.id,
      log.sessionId,
      log.moduleType,
      log.moduleName,
      log.level,
      log.status,
      `"${log.message.replace(/"/g, '""')}"`, // 转义引号
      new Date(log.timestamp).toISOString(),
      log.processingTime || "",
      log.error?.type || "",
    ]);

    const csvContent = [headers, ...rows]
      .map((row) => row.join(","))
      .join("\n");

    return new Blob([csvContent], { type: "text/csv" });
  }

  /**
   * 导出为文本格式
   */
  private exportAsText(
    logs: ModuleExecutionLog[],
    _options: LogExportOptions
  ): Blob {
    const content = logs
      .map((log) => {
        const timestamp = new Date(log.timestamp).toISOString();
        const duration = log.processingTime ? ` (${log.processingTime}ms)` : "";
        const error = log.error ? ` ERROR: ${log.error.message}` : "";

        return `[${timestamp}] ${log.level.toUpperCase()} [${log.moduleType}:${
          log.moduleName
        }] ${log.status.toUpperCase()}${duration} - ${log.message}${error}`;
      })
      .join("\n");

    return new Blob([content], { type: "text/plain" });
  }

  /**
   * 获取当前状态摘要
   */
  getStatus(): {
    totalLogs: number;
    activeSessions: number;
    memoryUsage: number;
    oldestLog?: number;
    newestLog?: number;
  } {
    const allLogs = Array.from(this.logs.values());
    const timestamps = allLogs.map((l) => l.timestamp);

    return {
      totalLogs: this.logs.size,
      activeSessions: this.sessionLogs.size,
      memoryUsage: JSON.stringify(allLogs).length, // 粗略估算
      oldestLog: timestamps.length > 0 ? Math.min(...timestamps) : undefined,
      newestLog: timestamps.length > 0 ? Math.max(...timestamps) : undefined,
    };
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    this.logs.clear();
    this.sessionLogs.clear();
    this.eventCallbacks.length = 0;
    this.batchQueue.length = 0;
  }

  /**
   * 生成流式日志消息
   */
  private generateLogMessage(
    providedMessage: string | undefined,
    content: string,
    stage: string,
    isComplete: boolean
  ): string {
    if (providedMessage) {
      return providedMessage;
    }

    if (isComplete) {
      return `✅ ${content}`;
    }

    return `🌊 ${stage} 流式内容生成中...`;
  }

  /**
   * 生成更新后的流式日志消息
   */
  private generateUpdatedLogMessage(
    providedMessage: string | undefined,
    stage: string,
    chunkCount: number,
    accumulatedContentLength: number,
    isComplete: boolean
  ): string {
    if (providedMessage) {
      return providedMessage;
    }

    if (isComplete) {
      return `✅ ${stage} 完成 (${chunkCount} chunks, ${accumulatedContentLength} chars)`;
    }

    return `🌊 ${stage} 流式内容生成中... (${chunkCount} chunks, ${accumulatedContentLength} chars)`;
  }

  /**
   * V5.3: 添加外部日志（用于服务端日志同步）
   */
  addExternalLog(log: ModuleExecutionLog): void {
    // 检查是否已存在相同ID的日志，避免重复
    if (this.logs.has(log.id)) {
      console.warn(`Log with ID ${log.id} already exists, skipping`);
      return;
    }

    this.addLog(log);
    this.emit("log_created", log);

    // 确保会话日志跟踪
    if (!this.sessionLogs.has(log.sessionId)) {
      this.sessionLogs.set(log.sessionId, []);
    }
    this.sessionLogs.get(log.sessionId)!.push(log.id);

    console.log(
      `📥 [External Log] Added external log: ${log.id} to session ${log.sessionId}`
    );
  }

  /**
   * 从日志ID中提取序列号
   */
  private extractSequenceFromLogId(logId: string): number {
    // 日志ID格式：log-{timestamp}-{sequence}-{randomString}
    const match = logId.match(/^log-\d+-(\d+)-/);
    if (match && match[1]) {
      return parseInt(match[1], 10);
    }
    return 0; // 默认序列号
  }
}

/**
 * 单例实例
 */
let logManagerInstance: LogManager | null = null;

export function getLogManager(config?: Partial<LogManagerConfig>): LogManager {
  if (!logManagerInstance) {
    logManagerInstance = new LogManager(config);
  }
  return logManagerInstance;
}

export function resetLogManager(): void {
  if (logManagerInstance) {
    logManagerInstance.destroy();
    logManagerInstance = null;
  }
}
