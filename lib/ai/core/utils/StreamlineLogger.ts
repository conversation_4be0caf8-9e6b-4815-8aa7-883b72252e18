/**
 * V6 简化日志记录器
 *
 * 职责：
 * - 集成StageLifecycleManager和LogManager
 * - 提供极简的executeStage接口
 * - 自动处理所有日志记录和状态管理
 * - 支持所有AI模块的统一使用
 */

import {
  StageLifecycleManager,
  StageLifecycleManagerConfig,
} from "./StageLifecycleManager";
import { getLogManager, type LogManager } from "./LogManager";
import type { SupportedModuleType } from "../../types/streaming";

export interface StreamlineLoggerConfig extends StageLifecycleManagerConfig {
  // 继承所有StageLifecycleManager配置
  logToConsole?: boolean;
  logLevel?: "debug" | "info" | "warn" | "error";
}

/**
 * 简化日志记录器
 *
 * 核心特性：
 * - 自动生命周期管理：每个阶段自动开始-进度-完成
 * - 统一日志记录：单一真相源，避免重复记录
 * - 极简API：executeStage方法包含所有复杂逻辑
 * - 类型安全：完整的TypeScript支持
 */
export class StreamlineLogger {
  private lifecycleManager: StageLifecycleManager;
  private logManager: LogManager;
  private config: StreamlineLoggerConfig;

  constructor(config: StreamlineLoggerConfig) {
    this.config = {
      logToConsole: false, // 默认禁用控制台输出
      logLevel: "info",
      enableDebugLogs: false, // 默认禁用调试日志
      ...config,
    };

    // 初始化生命周期管理器
    this.lifecycleManager = new StageLifecycleManager({
      sessionId: this.config.sessionId,
      moduleType: this.config.moduleType,
      enableDebugLogs: this.config.enableDebugLogs,
    });

    // 获取日志管理器实例
    this.logManager = getLogManager();

    // 连接生命周期事件到日志系统
    this.setupEventLogging();

    if (this.config.logToConsole && this.config.logLevel === "debug") {
      console.log(
        `🚀 [StreamlineLogger] 初始化完成 - ${this.config.moduleType}:${this.config.sessionId}`
      );
    }
  }

  /**
   * 执行阶段 - 核心简化接口
   *
   * 自动处理：
   * - 阶段开始记录
   * - 进度追踪和日志记录
   * - 阶段完成记录
   * - 错误处理和日志记录
   *
   * @param stageName 阶段名称（完全自由命名）
   * @param executor 阶段执行函数
   * @returns 执行结果
   */
  async executeStage<T>(
    stageName: string,
    executor: (
      progressReporter: (progress: number, message?: string) => void
    ) => Promise<T>
  ): Promise<T> {
    const stageId = this.lifecycleManager.startStage(stageName);

    try {
      // 创建进度报告器
      const progressReporter = (progress: number, message?: string) => {
        this.lifecycleManager.progressStage(stageId, progress, message);
      };

      // 执行阶段逻辑
      const result = await executor(progressReporter);

      // 阶段完成
      this.lifecycleManager.completeStage(stageId, result);

      return result;
    } catch (error) {
      // 阶段错误
      this.lifecycleManager.errorStage(stageId, error as Error);
      throw error;
    }
  }

  /**
   * 记录信息日志
   */
  logInfo(message: string, context?: any): void {
    this.logManager.logInfo(
      this.config.sessionId,
      this.config.moduleType,
      message,
      context
    );
  }

  /**
   * 记录警告日志
   */
  logWarning(message: string, context?: any): void {
    this.logManager.logWarning(
      this.config.sessionId,
      this.config.moduleType,
      message,
      context
    );
  }

  /**
   * 记录错误日志
   */
  logError(message: string, error?: Error, context?: any): void {
    this.logManager.logError(
      this.config.sessionId,
      this.config.moduleType,
      error || new Error(message),
      context
    );
  }

  /**
   * 记录流式事件（高级用法）
   */
  logStreamEvent(
    eventType:
      | "stage_start"
      | "stage_progress"
      | "stage_complete"
      | "stage_error"
      | "debug_start"
      | "debug_complete"
      | "debug_error"
      | "content_chunk",
    stage: string,
    content: string,
    metadata?: any
  ): void {
    this.logManager.logStreamEvent(
      this.config.sessionId,
      this.config.moduleType as any, // 临时类型转换，待LogManager更新
      this.mapEventTypeForLogManager(eventType),
      stage,
      {
        content,
        metadata,
      }
    );
  }

  /**
   * 映射新事件类型到LogManager兼容的类型
   */
  private mapEventTypeForLogManager(
    eventType:
      | "stage_start"
      | "stage_progress"
      | "stage_complete"
      | "stage_error"
      | "debug_start"
      | "debug_complete"
      | "debug_error"
      | "content_chunk"
  ):
    | "stage_start"
    | "stage_progress"
    | "stage_complete"
    | "content_chunk"
    | "complete"
    | "error" {
    switch (eventType) {
      case "stage_start":
        return "stage_start";
      case "stage_progress":
        return "stage_progress";
      case "stage_complete":
        return "stage_complete";
      case "stage_error":
        return "error";
      case "debug_start":
        return "stage_start";
      case "debug_complete":
        return "complete";
      case "debug_error":
        return "error";
      case "content_chunk":
        return "content_chunk";
      default:
        return "content_chunk"; // 默认回退
    }
  }

  /**
   * 获取生命周期管理器统计
   */
  getStats() {
    return this.lifecycleManager.getStats();
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.lifecycleManager.cleanup();
  }

  /**
   * 设置事件日志记录
   *
   * 将生命周期事件自动转换为StreamEvent并记录到LogManager
   */
  private setupEventLogging(): void {
    this.lifecycleManager.addLifecycleListener((eventType, stage, data) => {
      // 创建标准StreamEvent
      const streamEvent = {
        sessionId: this.config.sessionId,
        moduleType: this.config.moduleType,
        eventType: eventType,
        stage: stage,
        content: this.formatEventMessage(eventType, stage, data),
        isComplete: eventType === "stage_complete",
        progress: this.calculateProgress(eventType, data),
        metadata: {
          ...data,
          processingMetrics: this.extractProcessingMetrics(data),
          stageOutput: this.extractStageOutput(data),
          contextData: this.extractContextData(data),
        },
      };

      // 记录到LogManager (映射事件类型以兼容现有接口)
      this.logManager.logStreamEvent(
        streamEvent.sessionId,
        streamEvent.moduleType as any, // 临时类型转换，待LogManager更新
        this.mapEventTypeForLogManager(streamEvent.eventType),
        streamEvent.stage,
        {
          content: streamEvent.content,
          isComplete: streamEvent.isComplete,
          progress: streamEvent.progress,
          metadata: streamEvent.metadata,
        }
      );

      // 可选的控制台输出
      if (this.config.logToConsole) {
        this.logToConsole(eventType, stage, data);
      }
    });
  }

  /**
   * 格式化事件消息
   */
  private formatEventMessage(
    eventType:
      | "stage_start"
      | "stage_progress"
      | "stage_complete"
      | "stage_error"
      | "debug_start"
      | "debug_complete"
      | "debug_error"
      | "content_chunk",
    stage: string,
    data: any
  ): string {
    switch (eventType) {
      case "stage_start":
        return `🟢 开始执行: ${stage}`;
      case "stage_progress":
        const progress = data.progress || 0;
        const message = data.message || "";
        return `📈 ${stage}: ${progress}% ${message}`;
      case "stage_complete":
        const duration = data.duration || 0;
        return `✅ 完成: ${stage} (${duration}ms)`;
      case "stage_error":
        const error = data.error || "未知错误";
        return `❌ 错误: ${stage} - ${error}`;
      default:
        return `${stage}: ${JSON.stringify(data)}`;
    }
  }

  /**
   * 计算进度值
   */
  private calculateProgress(
    eventType:
      | "stage_start"
      | "stage_progress"
      | "stage_complete"
      | "stage_error"
      | "debug_start"
      | "debug_complete"
      | "debug_error"
      | "content_chunk",
    data: any
  ): number | undefined {
    switch (eventType) {
      case "stage_start":
        return 0;
      case "stage_progress":
        return data.progress || undefined;
      case "stage_complete":
        return 100;
      case "stage_error":
        return undefined;
      default:
        return undefined;
    }
  }

  /**
   * 提取性能指标
   */
  private extractProcessingMetrics(data: any): any {
    return {
      duration: data.duration || 0,
      itemsProcessed: data.itemsProcessed || 1,
      confidence: data.confidence || 1.0,
      timestamp: data.timestamp || Date.now(),
    };
  }

  /**
   * 提取阶段输出
   */
  private extractStageOutput(data: any): any {
    return data.result || data.output || null;
  }

  /**
   * 提取上下文数据
   */
  private extractContextData(data: any): any {
    return {
      stageId: data.stageId,
      moduleType: this.config.moduleType,
      sessionId: this.config.sessionId,
      timestamp: data.timestamp || Date.now(),
    };
  }

  /**
   * 控制台日志输出
   */
  private logToConsole(
    eventType:
      | "stage_start"
      | "stage_progress"
      | "stage_complete"
      | "stage_error"
      | "debug_start"
      | "debug_complete"
      | "debug_error"
      | "content_chunk",
    stage: string,
    data: any
  ): void {
    if (this.config.logLevel === "debug" || this.config.enableDebugLogs) {
      const message = this.formatEventMessage(eventType, stage, data);
      console.log(`[${this.config.moduleType}] ${message}`);
    }
  }
}

/**
 * 创建StreamlineLogger实例的便捷函数
 */
export function createStreamlineLogger(
  sessionId: string,
  moduleType: SupportedModuleType,
  options?: Partial<StreamlineLoggerConfig>
): StreamlineLogger {
  return new StreamlineLogger({
    sessionId,
    moduleType,
    ...options,
  });
}
