export interface ModuleError {
  module: string;
  message: string;
  originalError?: any;
}

export class ErrorHandler {
  /**
   * 包装模块错误
   */
  wrapModuleError(moduleName: string, error: any): ModuleError {
    return {
      module: moduleName,
      message: error instanceof Error ? error.message : String(error),
      originalError: error instanceof Error ? error : undefined,
    };
  }

  /**
   * 处理生成错误 - 快速失败原则
   */
  handleGenerationError(error: any, requestId: string): never {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Generation failed [${requestId}]: ${errorMessage}`);
  }

  /**
   * 记录错误
   */
  logError(error: any, context?: Record<string, any>): void {
    console.error("AI Module Error:", {
      error: error instanceof Error ? error.message : String(error),
      context,
      timestamp: new Date().toISOString(),
    });
  }
}
