/**
 * V6 阶段生命周期管理器
 *
 * 职责：
 * - 自动管理阶段的开始、进行、完成状态
 * - 支持所有AI模块的统一阶段管理
 * - 防止阶段状态遗漏或不一致
 * - 提供事件驱动的状态通知
 */

import { EventEmitter } from "events";
import {
  StageContext,
  StreamEventType,
  LifecycleEventListener,
  SupportedModuleType,
} from "../../types/streaming";

export interface StageLifecycleManagerConfig {
  sessionId: string;
  moduleType: SupportedModuleType;
  enableDebugLogs?: boolean;
}

/**
 * 阶段生命周期管理器
 *
 * 核心特性：
 * - 自动生命周期管理：确保每个阶段都有完整的开始-结束
 * - 进度追踪：支持细粒度的进度更新
 * - 事件驱动：基于EventEmitter的状态通知
 * - 防遗漏：通过Map管理，避免阶段状态丢失
 */
export class StageLifecycleManager extends EventEmitter {
  private config: StageLifecycleManagerConfig;
  private activeStages = new Map<string, StageContext>();
  private stageCounter = 0;

  constructor(config: StageLifecycleManagerConfig) {
    super();
    this.config = config;

    if (this.config.enableDebugLogs) {
      console.log(
        `🔄 [StageLifecycleManager] 初始化 - ${config.moduleType}:${config.sessionId}`
      );
    }
  }

  /**
   * 开始一个新阶段
   *
   * @param stage 阶段名称（完全自由命名，支持中文、emoji等）
   * @param context 可选的初始上下文数据
   * @returns 阶段ID，用于后续操作
   */
  startStage(stage: string, context?: Record<string, any>): string {
    const stageId = `${stage}-${Date.now()}-${++this.stageCounter}`;
    const stageContext: StageContext = {
      stage,
      startTime: Date.now(),
      status: "running",
      context,
    };

    this.activeStages.set(stageId, stageContext);

    // 发射阶段开始事件
    this.emit("stage_start", stage, {
      stageId,
      context,
      timestamp: stageContext.startTime,
    });

    if (this.config.enableDebugLogs) {
      console.log(`🟢 [StageLifecycleManager] 阶段开始: ${stage} (${stageId})`);
    }

    return stageId;
  }

  /**
   * 更新阶段进度
   *
   * @param stageId 阶段ID
   * @param progress 进度值 (0-100)
   * @param message 可选的进度描述
   */
  progressStage(stageId: string, progress: number, message?: string): void {
    const stageContext = this.activeStages.get(stageId);
    if (!stageContext) {
      console.warn(`⚠️ [StageLifecycleManager] 阶段不存在: ${stageId}`);
      return;
    }

    // 确保进度值在有效范围内
    const clampedProgress = Math.max(0, Math.min(100, progress));
    stageContext.progress = clampedProgress;

    // 发射进度事件
    this.emit("stage_progress", stageContext.stage, {
      stageId,
      progress: clampedProgress,
      message: message || `进度 ${clampedProgress}%`,
      timestamp: Date.now(),
    });

    if (this.config.enableDebugLogs && clampedProgress % 25 === 0) {
      console.log(
        `📈 [StageLifecycleManager] 阶段进度: ${stageContext.stage} - ${clampedProgress}%`
      );
    }
  }

  /**
   * 完成阶段
   *
   * @param stageId 阶段ID
   * @param result 可选的阶段结果
   */
  completeStage(stageId: string, result?: any): void {
    const stageContext = this.activeStages.get(stageId);
    if (!stageContext) {
      console.warn(`⚠️ [StageLifecycleManager] 阶段不存在: ${stageId}`);
      return;
    }

    // 更新阶段状态
    stageContext.status = "completed";
    stageContext.endTime = Date.now();
    stageContext.result = result;
    stageContext.progress = 100;

    const duration = stageContext.endTime - stageContext.startTime;

    // 发射完成事件
    this.emit("stage_complete", stageContext.stage, {
      stageId,
      result,
      duration,
      timestamp: stageContext.endTime,
    });

    // 从活跃阶段中移除
    this.activeStages.delete(stageId);

    if (this.config.enableDebugLogs) {
      console.log(
        `✅ [StageLifecycleManager] 阶段完成: ${stageContext.stage} (${duration}ms)`
      );
    }
  }

  /**
   * 阶段出错
   *
   * @param stageId 阶段ID
   * @param error 错误信息
   */
  errorStage(stageId: string, error: Error | string): void {
    const stageContext = this.activeStages.get(stageId);
    if (!stageContext) {
      console.warn(`⚠️ [StageLifecycleManager] 阶段不存在: ${stageId}`);
      return;
    }

    // 更新阶段状态
    stageContext.status = "error";
    stageContext.endTime = Date.now();
    stageContext.error = error instanceof Error ? error : new Error(error);

    const duration = stageContext.endTime - stageContext.startTime;

    // 发射错误事件
    this.emit("stage_error", stageContext.stage, {
      stageId,
      error: error instanceof Error ? error.message : error,
      duration,
      timestamp: stageContext.endTime,
    });

    // 从活跃阶段中移除
    this.activeStages.delete(stageId);

    if (this.config.enableDebugLogs) {
      console.error(
        `❌ [StageLifecycleManager] 阶段错误: ${stageContext.stage} - ${error}`
      );
    }
  }

  /**
   * 添加生命周期事件监听器
   *
   * @param listener 事件监听器
   */
  addLifecycleListener(listener: LifecycleEventListener): void {
    this.on("stage_start", (stage, data) =>
      listener("stage_start", stage, data)
    );
    this.on("stage_progress", (stage, data) =>
      listener("stage_progress", stage, data)
    );
    this.on("stage_complete", (stage, data) =>
      listener("stage_complete", stage, data)
    );
    this.on("stage_error", (stage, data) =>
      listener("stage_error", stage, data)
    );
  }

  /**
   * 获取当前活跃的阶段
   */
  getActiveStages(): Map<string, StageContext> {
    return new Map(this.activeStages);
  }

  /**
   * 获取特定阶段的状态
   *
   * @param stageId 阶段ID
   */
  getStageStatus(stageId: string): StageContext | undefined {
    return this.activeStages.get(stageId);
  }

  /**
   * 强制清理所有活跃阶段（通常在错误恢复时使用）
   */
  cleanup(): void {
    const activeCount = this.activeStages.size;

    if (activeCount > 0 && this.config.enableDebugLogs) {
      console.warn(
        `🧹 [StageLifecycleManager] 强制清理 ${activeCount} 个活跃阶段`
      );
    }

    this.activeStages.clear();
    this.removeAllListeners();
  }

  /**
   * 获取管理器状态统计
   */
  getStats(): {
    sessionId: string;
    moduleType: SupportedModuleType;
    activeStagesCount: number;
    totalStagesStarted: number;
  } {
    return {
      sessionId: this.config.sessionId,
      moduleType: this.config.moduleType,
      activeStagesCount: this.activeStages.size,
      totalStagesStarted: this.stageCounter,
    };
  }
}
