/**
 * JSON 验证器 - 验证模块输出格式
 */

import type {
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
} from "@/lib/ai/types";

export class JSONValidator {
  /**
   * 验证分析器输出
   */
  validateAnalyzerOutput(output: any): output is AnalyzerOutput {
    if (!output || typeof output !== "object") {
      return false;
    }

    if (!Array.isArray(output.metrics)) {
      return false;
    }

    return output.metrics.every(
      (metric: any) =>
        typeof metric === "object" &&
        typeof metric.metric === "string" &&
        typeof metric.value === "number" &&
        typeof metric.valence === "string" &&
        typeof metric.analysis === "string"
    );
  }

  /**
   * 验证策略师输出
   */
  validateStrategistOutput(output: any): output is StrategistOutput {
    return (
      output &&
      typeof output === "object" &&
      typeof output.selectedStrategyId === "string" &&
      typeof output.targetEmotion === "string" &&
      typeof output.perspective === "string" &&
      typeof output.selectedTemplateId === "string" &&
      Array.isArray(output.keyTalkingPoints) &&
      typeof output.justification === "string"
    );
  }

  /**
   * 验证写手输出 - V6.0简化版本
   */
  validateWriterOutput(output: any): output is WriterOutput {
    return (
      output &&
      typeof output === "object" &&
      typeof output.generatedText === "string" &&
      typeof output.usedTemplate === "string" && // V6.0新增：模板路径验证
      typeof output.confidence === "number"
      // V6.0移除：usedExamples数组验证（已简化移除）
    );
  }

  /**
   * 验证评论家输出
   */
  validateCriticOutput(output: any): output is CriticOutput {
    return (
      output &&
      typeof output === "object" &&
      typeof output.humorScore === "number" &&
      typeof output.complianceScore === "number" &&
      typeof output.originalityScore === "number" &&
      typeof output.overallAssessment === "string" &&
      Array.isArray(output.improvementSuggestions)
    );
  }

  /**
   * 解析和验证JSON响应 - 简化版本，适用于官方结构化输出
   */
  parseAndValidate(responseText: string, schema: any): any {
    if (!responseText.trim()) {
      throw new Error("Empty response text for JSON validation");
    }

    try {
      // 使用官方 response_format 时，假设模型输出正确的 JSON，直接解析
      const parsed = JSON.parse(responseText);

      // 基础验证
      if (!parsed || typeof parsed !== "object") {
        throw new Error("Parsed result is not a valid object");
      }

      // 根据schema验证必需字段
      if (schema && schema.required) {
        for (const field of schema.required) {
          if (!(field in parsed)) {
            throw new Error(`Missing required field: ${field}`);
          }
        }
      }

      return parsed;
    } catch (error) {
      // 按照开发规范：尽早报错退出，不做兜底处理
      throw new Error(
        `JSON validation failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }. Response text: ${responseText.substring(0, 200)}...`
      );
    }
  }
}
