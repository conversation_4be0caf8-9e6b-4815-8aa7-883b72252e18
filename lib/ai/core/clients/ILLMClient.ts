/**
 * 统一LLM客户端接口 - 动态配置版本
 *
 * 设计原则：
 * - 每次调用动态指定：支持在同一项目中混合使用多个AI提供商
 * - 约定大于配置：提供合理默认值，简化常用场景
 * - 类型安全优先：严格TypeScript类型定义，避免any类型
 * - 尽早报错退出：明确的配置验证和错误处理
 *
 * 使用示例：
 * ```typescript
 * // 单次调用指定provider和模型
 * const response = await llmClient.chat({
 *   provider: "doubao",
 *   model: "ep-**************-hrc7t",
 *   messages: [{ role: "user", content: "Hello" }]
 * });
 *
 * // 使用OpenRouter的智能路由
 * const response = await llmClient.chat({
 *   provider: "openrouter",
 *   model: "openai/gpt-4o-mini",
 *   messages: [{ role: "user", content: "Hello" }],
 *   enableRouting: true
 * });
 * ```
 */

// 导入DoubaoClient的核心类型，确保接口一致性
import type {
  ChatMessage,
  ChatResponse,
  StreamChunk,
  RequestMetrics,
  DoubaoResponseFormat,
} from "./DoubaoClient";

// =================================================================
// 动态配置类型定义
// =================================================================

/**
 * LLM提供商类型
 */
export type LLMProviderType = "doubao" | "openrouter" | "aigateway";

/**
 * 动态LLM调用配置
 */
export interface DynamicLLMConfig {
  /** LLM提供商 */
  provider: LLMProviderType;

  /** 模型名称 */
  model: string;

  /** API密钥（可选，使用环境变量中的默认值） */
  apiKey?: string;

  /** 基础URL（可选，使用默认值） */
  baseURL?: string;

  /** 超时时间（可选，使用默认值） */
  timeout?: number;

  /** 是否启用OpenRouter智能路由（仅OpenRouter有效） */
  enableRouting?: boolean;

  /** 应用名称（仅OpenRouter有效） */
  appName?: string;
}

/**
 * 扩展的聊天选项 - 支持动态配置
 */
export interface DynamicChatOptions {
  /** 动态LLM配置 */
  provider: LLMProviderType;
  model: string;

  /** 基础聊天配置 */
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  timeout?: number;

  /** 结构化输出配置 */
  response_format?: DoubaoResponseFormat;

  /** 流式配置 */
  stream?: boolean;

  /** OpenRouter特有配置 */
  enableRouting?: boolean;
  models?: string[]; // 智能路由候选模型

  /** 认证配置（可选，使用环境变量默认值） */
  apiKey?: string;
  baseURL?: string;
  appName?: string;
}

// =================================================================
// 核心接口定义
// =================================================================

/**
 * 统一LLM客户端接口 - 动态配置版
 */
export interface ILLMClient {
  /**
   * 动态聊天方法 - 每次调用指定provider和模型
   * @param options 动态聊天选项配置
   * @returns Promise<ChatResponse> 聊天响应
   */
  chat(options: DynamicChatOptions): Promise<ChatResponse>;

  /**
   * 动态流式聊天方法 - 每次调用指定provider和模型
   * @param options 动态聊天选项配置
   * @returns AsyncGenerator<StreamChunk> 流式响应生成器
   */
  chatStream(
    options: DynamicChatOptions
  ): AsyncGenerator<StreamChunk, void, unknown>;

  /**
   * 健康检查 - 检查指定provider的可用性
   * @param provider 要检查的提供商
   * @returns Promise<boolean> 健康状态
   */
  healthCheck(provider: LLMProviderType): Promise<boolean>;

  /**
   * 获取性能统计 - 按provider分组
   * @param provider 可选的提供商过滤
   * @returns 性能统计数据
   */
  getPerformanceStats(
    provider?: LLMProviderType
  ): TokenUsageStats | Record<LLMProviderType, TokenUsageStats>;

  /**
   * 获取请求指标 - 支持按provider过滤
   * @param requestId 可选的请求ID
   * @param provider 可选的提供商过滤
   * @returns 请求指标数据
   */
  getRequestMetrics(
    requestId?: string,
    provider?: LLMProviderType
  ): RequestMetrics | RequestMetrics[] | undefined;
}

// =================================================================
// 配置验证和工具类型
// =================================================================

/**
 * 令牌使用统计类型
 */
export interface TokenUsageStats {
  totalRequests: number;
  totalTokens: number;
  avgTokensPerRequest: number;
  lastUpdated: number;
}

/**
 * LLM配置验证结果
 */
export interface LLMConfigValidationResult {
  /** 当前提供商 */
  provider: LLMProviderType;

  /** 配置是否有效 */
  isValid: boolean;

  /** 配置问题列表 */
  issues: string[];

  /** 建议解决方案 */
  recommendations: string[];

  /** 警告信息（非致命） */
  warnings?: string[];
}

/**
 * LLM客户端错误基类
 */
export abstract class LLMClientError extends Error {
  abstract readonly provider: LLMProviderType;
  abstract readonly errorCode: string;
  abstract readonly retryable: boolean;

  constructor(message: string, public readonly originalError?: Error) {
    super(message);
    this.name = this.constructor.name;
  }
}

// =================================================================
// 常量定义
// =================================================================

/**
 * 默认配置常量
 */
export const DEFAULT_LLM_CONFIG = {
  /** 默认超时时间（毫秒） */
  TIMEOUT: 60000,

  /** 默认最大令牌数 */
  MAX_TOKENS: 1024,

  /** 默认温度参数 */
  TEMPERATURE: 0.7,

  /** 默认重试次数 */
  MAX_RETRIES: 3,

  /** 默认重试延迟基数（毫秒） */
  RETRY_DELAY_BASE: 1000,

  /** OpenRouter默认模型 */
  OPENROUTER_DEFAULT_MODEL: "openai/gpt-4o-mini",

  /** OpenRouter默认应用名称 */
  OPENROUTER_DEFAULT_APP_NAME: "GitHub-Card",
} as const;

/**
 * 支持的模型列表
 */
export const SUPPORTED_MODELS = {
  DOUBAO: {
    THINKING: "ep-20250709090402-wfdrh",
    STANDARD: "ep-**************-hrc7t",
    FLASH: "ep-20250709090557-str8m",
  },
  OPENROUTER: {
    GPT_4O: "openai/gpt-4o",
    GPT_4O_MINI: "openai/gpt-4o-mini",
    CLAUDE_3_HAIKU: "anthropic/claude-3-haiku",
    CLAUDE_3_SONNET: "anthropic/claude-3-5-sonnet",
    DEEPSEEK_CHAT: "deepseek/deepseek-chat",
    LLAMA_3_1_70B: "meta-llama/llama-3.1-70b-instruct",
  },
  AIGATEWAY: {
    GPT_4O: "openai/gpt-4o",
    GPT_4O_MINI: "openai/gpt-4o-mini",
    CLAUDE_SONNET_4: "anthropic/claude-sonnet-4",
    CLAUDE_HAIKU: "anthropic/claude-3-haiku",
    GROK_3: "xai/grok-3",
    LLAMA_3_1_70B: "meta-llama/llama-3.1-70b-instruct",
  },
} as const;

/**
 * 便捷配置函数
 */
export const createLLMConfig = {
  /**
   * 创建Doubao配置
   */
  doubao: (
    model?: string,
    options?: Partial<DynamicLLMConfig>
  ): DynamicLLMConfig => ({
    provider: "doubao",
    model: model || SUPPORTED_MODELS.DOUBAO.STANDARD,
    ...options,
  }),

  /**
   * 创建OpenRouter配置
   */
  openrouter: (
    model?: string,
    options?: Partial<DynamicLLMConfig>
  ): DynamicLLMConfig => ({
    provider: "openrouter",
    model: model || SUPPORTED_MODELS.OPENROUTER.GPT_4O_MINI,
    ...options,
  }),

  /**
   * 创建AIGateway配置
   */
  aigateway: (
    model?: string,
    options?: Partial<DynamicLLMConfig>
  ): DynamicLLMConfig => ({
    provider: "aigateway",
    model: model || SUPPORTED_MODELS.AIGATEWAY.GPT_4O,
    ...options,
  }),
};
