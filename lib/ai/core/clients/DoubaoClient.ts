/**
 * Doubao 1.6 API Client - Optimized Version
 *
 * Based on official Volcano Engine documentation: https://www.volcengine.com/docs/82379/1494384
 *
 * ✨ Core Features:
 * - 🚀 Doubao 1.6 series models (Thinking, Standard, Flash)
 * - 🖼️ Multimodal support (text, image, audio)
 * - 📊 Structured output (JSON mode)
 * - 🌊 SSE streaming capabilities
 * - 💾 Token optimization and caching
 * - 🔄 Enhanced error handling and retry mechanisms
 * - 📈 Performance monitoring and metrics
 * - 🛡️ Environment validation and security
 *
 * 📖 Usage Examples:
 *
 * // Basic text completion
 * const client = new DoubaoClient();
 * const response = await client.chat({
 *   messages: [{ role: "user", content: "Hello, world!" }],
 *   temperature: 0.7,
 *   max_tokens: 1000
 * });
 *
 * // JSON mode for structured output
 * const jsonResponse = await client.chat({
 *   messages: [{ role: "user", content: "Generate user profile data" }],
 *   response_format: { type: "json_object" }
 * });
 *
 * // Thinking mode for complex reasoning
 * const thinkingResponse = await client.chat({
 *   messages: [{ role: "user", content: "请分析这个问题的解决方案" }],
 *   model: "ep-20250709090402-wfdrh" // Explicitly use Thinking model
 * });
 *
 * // Multimodal input
 * const multimodalResponse = await client.chat({
 *   messages: [{
 *     role: "user",
 *     content: [
 *       { type: "text", text: "What do you see in this image?" },
 *       { type: "image", image_url: { url: "https://example.com/image.jpg" } }
 *     ]
 *   }]
 * });
 *
 * // Streaming response
 * for await (const chunk of client.chatStream({
 *   messages: [{ role: "user", content: "Write a story" }],
 *   stream: true
 * })) {
 *   console.log(chunk.choices[0]?.delta?.content || "");
 * }
 *
 * 🔧 Environment Setup:
 * - Set DOUBAO_API_KEY in your environment variables
 * - API key format: UUID (e.g., 12345678-1234-5678-9012-123456789012)
 * - Base URL: https://ark.cn-beijing.volces.com/api/v3
 *
 * 🏆 Performance Features:
 * - Intelligent retry with exponential backoff
 * - Comprehensive metrics tracking
 * - Memory-efficient streaming
 * - Request timeout and error handling
 */

// Core interfaces compatible with existing system
export interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string | MultimodalContent[];
}

// Doubao 1.6 multimodal support
export interface MultimodalContent {
  type: "text" | "image" | "audio";
  text?: string;
  image_url?: {
    url: string;
    detail?: "auto" | "low" | "high";
  };
  audio_url?: {
    url: string;
    format?: "mp3" | "wav" | "m4a";
  };
}

// Doubao 官方结构化输出接口
export interface DoubaoResponseFormat {
  type: "json_object";
  json_schema?: {
    name: string;
    schema: DoubaoJsonSchema;
    strict?: boolean;
  };
}

export interface DoubaoJsonSchema {
  type: "object";
  properties: Record<string, any>;
  required?: string[];
  additionalProperties?: boolean;
}

export interface ChatOptions {
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  model?: string;
  timeout?: number;
  // Doubao 官方结构化输出 API
  response_format?: DoubaoResponseFormat;
  // SSE streaming
  stream?: boolean;
  // 思考模式
  thinking?: boolean;
}

export interface ChatResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason?: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
  created?: number;
}

// SSE streaming interfaces
export interface StreamChunk {
  choices: Array<{
    delta: {
      content?: string;
      role?: string;
    };
    finish_reason?: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
}

// Performance metrics
export interface RequestMetrics {
  requestId: string;
  startTime: number;
  endTime?: number;
  tokenUsage?: ChatResponse["usage"];
  retryCount?: number;
}

export class DoubaoClient {
  private apiKey: string;
  private baseURL: string = "https://ark.cn-beijing.volces.com/api/v3";

  // Doubao 1.6 模型配置 - 简化版本，直接引用constants
  private readonly DOUBAO_MODELS = {
    THINKING: "ep-20250709090402-wfdrh", // Doubao-Seed-1.6-thinking (思维链模型)
    STANDARD: "ep-20250708091953-hrc7t", // Doubao-Seed-1.6 (标准版)
    FLASH: "ep-20250709090557-str8m", // Doubao-Seed-1.6-flash (快速版)
  };

  // Core configuration
  private readonly DEFAULT_TIMEOUT = 60000;
  private readonly DEFAULT_MAX_TOKENS = 1024;
  private readonly DEFAULT_TEMPERATURE = 0.7;
  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly RETRY_DELAY_BASE = 1000;

  // Performance tracking
  private requestMetrics: Map<string, RequestMetrics> = new Map();
  private tokenUsageStats = {
    totalRequests: 0,
    totalTokens: 0,
    avgTokensPerRequest: 0,
    lastUpdated: 0,
  };

  constructor() {
    this.apiKey = process.env.DOUBAO_API_KEY || "";

    // Validate environment configuration
    if (!this.apiKey) {
      throw new Error(
        "DOUBAO_API_KEY environment variable is required. " +
          "Please set it in your environment variables or .env file."
      );
    }

    // Validate API key format (should be UUID-like)
    const uuidPattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidPattern.test(this.apiKey)) {
      console.warn(
        "DOUBAO_API_KEY format appears invalid. Expected UUID format. " +
          "Please verify your API key from Volcano Engine console."
      );
    }

    // Initialize performance tracking
    this.tokenUsageStats = {
      totalRequests: 0,
      totalTokens: 0,
      avgTokensPerRequest: 0,
      lastUpdated: Date.now(),
    };

    console.log("DoubaoClient initialized with Doubao 1.6 series models");
  }

  /**
   * Main chat method with Doubao optimization
   */
  async chat(options: ChatOptions): Promise<ChatResponse> {
    if (!this.apiKey) {
      throw new Error("Doubao API key not configured");
    }

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // Initialize metrics tracking
      this.requestMetrics.set(requestId, {
        requestId,
        startTime,
      });

      // Standard chat
      return this.executeChat(options, requestId);
    } catch (error) {
      this.handleRequestError(requestId, error);
      throw error;
    }
  }

  /**
   * Execute chat request with retry mechanism
   */
  private async executeChat(
    options: ChatOptions,
    requestId: string,
    retryCount = 0
  ): Promise<ChatResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      options.timeout || this.DEFAULT_TIMEOUT
    );

    try {
      // 简化模型选择 - 直接使用传入的模型或默认模型
      const selectedModel = options.model || this.DOUBAO_MODELS.STANDARD;

      const requestBody: any = {
        model: selectedModel,
        messages: options.messages,
        temperature: options.temperature || this.DEFAULT_TEMPERATURE,
        max_tokens: options.max_tokens || this.DEFAULT_MAX_TOKENS,
        stream: options.stream || false,
        response_format: options.response_format,
        thinking: {
          type: options.thinking ? "enabled" : "disabled",
        },
      };

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
          "X-Request-ID": requestId,
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(
          `Doubao API error: ${response.status} ${response.statusText}`
        );
      }

      const result = (await response.json()) as ChatResponse;

      // Update metrics
      this.updateRequestMetrics(requestId, result);

      // 使用官方 API 时，假设模型输出正确的 JSON，直接解析
      if (options.response_format) {
        return this.parseStructuredResponse(result);
      }

      return result;
    } catch (error) {
      clearTimeout(timeoutId);

      // Retry logic
      if (retryCount < this.MAX_RETRY_ATTEMPTS && this.shouldRetry(error)) {
        await this.delay(this.RETRY_DELAY_BASE * Math.pow(2, retryCount));
        return this.executeChat(options, requestId, retryCount + 1);
      }

      if ((error as Error).name === "AbortError") {
        throw new Error(
          `Doubao API timeout after ${
            options.timeout || this.DEFAULT_TIMEOUT
          }ms`
        );
      }

      throw error;
    }
  }

  /**
   * 解析结构化响应 - 使用官方 API 时直接解析 JSON
   */
  private parseStructuredResponse(response: ChatResponse): ChatResponse {
    const content = response.choices[0]?.message?.content || "";

    if (!content.trim()) {
      throw new Error("Empty response content from structured output");
    }

    try {
      // 使用官方 response_format 时，假设模型输出正确的 JSON
      const parsedJson = JSON.parse(content);

      // 返回格式化的响应
      return {
        ...response,
        choices: [
          {
            ...response.choices[0],
            message: {
              ...response.choices[0].message,
              content: JSON.stringify(parsedJson, null, 2),
            },
          },
        ],
      };
    } catch (error) {
      // 按照开发规范：尽早报错退出，不做兜底处理
      throw new Error(
        `Failed to parse structured JSON response: ${
          error instanceof Error ? error.message : "Unknown error"
        }. Content: ${content.substring(0, 200)}...`
      );
    }
  }

  /**
   * SSE streaming method with enhanced error handling
   */
  async *chatStream(
    options: ChatOptions
  ): AsyncGenerator<StreamChunk, void, unknown> {
    if (!this.apiKey) {
      throw new Error("Doubao API key not configured");
    }

    const requestId = this.generateRequestId();
    // 简化模型选择 - 直接使用传入的模型或默认模型
    const selectedModel = options.model || this.DOUBAO_MODELS.STANDARD;

    // Initialize metrics tracking
    this.requestMetrics.set(requestId, {
      requestId,
      startTime: Date.now(),
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      options.timeout || this.DEFAULT_TIMEOUT
    );

    const requestBody: any = {
      model: selectedModel,
      messages: options.messages,
      temperature: options.temperature || this.DEFAULT_TEMPERATURE,
      max_tokens: options.max_tokens || this.DEFAULT_MAX_TOKENS,
      stream: options.stream || true,
      response_format: options.response_format,
      thinking: {
        type: options.thinking ? "enabled" : "disabled",
      },
    };

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
          Accept: "text/event-stream",
          "X-Request-ID": requestId,
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(
          `Doubao streaming API error: ${response.status} ${response.statusText}`
        );
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Failed to get response stream reader");
      }

      const decoder = new TextDecoder();
      let buffer = "";
      let totalTokens = 0;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(6).trim();
              if (data === "[DONE]") {
                // Update final metrics
                const metrics = this.requestMetrics.get(requestId);
                if (metrics) {
                  metrics.endTime = Date.now();
                  metrics.tokenUsage = {
                    prompt_tokens: 0,
                    completion_tokens: totalTokens,
                    total_tokens: totalTokens,
                  };
                }
                return;
              }

              try {
                const parsed = JSON.parse(data);

                // Track token usage for streaming
                if (parsed.usage) {
                  totalTokens = parsed.usage.total_tokens || totalTokens;
                }

                yield parsed;
              } catch (error) {
                console.warn("Failed to parse SSE chunk:", error);
                // Continue processing other chunks
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
        clearTimeout(timeoutId);
      }
    } catch (error) {
      clearTimeout(timeoutId);
      this.handleRequestError(requestId, error);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.chat({
        messages: [{ role: "user", content: "ping" }],
        max_tokens: 10,
        timeout: 5000,
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Performance stats
   */
  getPerformanceStats(): typeof this.tokenUsageStats {
    return { ...this.tokenUsageStats };
  }

  /**
   * Request metrics
   */
  getRequestMetrics(
    requestId?: string
  ): RequestMetrics | RequestMetrics[] | undefined {
    if (requestId) {
      return this.requestMetrics.get(requestId);
    }
    return Array.from(this.requestMetrics.values());
  }

  // Utility methods
  private generateRequestId(): string {
    return `doubao_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;
  }

  private shouldRetry(error: any): boolean {
    const retryableErrors = [
      "ECONNRESET",
      "ENOTFOUND",
      "ECONNREFUSED",
      "ETIMEDOUT",
      "rate_limit_exceeded",
      "server_error",
    ];

    const errorMessage = error?.message?.toLowerCase() || "";
    return retryableErrors.some((err) => errorMessage.includes(err));
  }

  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private updateRequestMetrics(
    requestId: string,
    response: ChatResponse
  ): void {
    const metrics = this.requestMetrics.get(requestId);
    if (metrics) {
      metrics.endTime = Date.now();
      metrics.tokenUsage = response.usage;

      // Update global stats
      this.tokenUsageStats.totalRequests++;
      this.tokenUsageStats.totalTokens += response.usage.total_tokens;
      this.tokenUsageStats.avgTokensPerRequest =
        this.tokenUsageStats.totalTokens / this.tokenUsageStats.totalRequests;
    }
  }

  private handleRequestError(requestId: string, error: any): void {
    const metrics = this.requestMetrics.get(requestId);
    if (metrics) {
      metrics.endTime = Date.now();
    }

    console.error(`Doubao request ${requestId} failed:`, error);
  }
}
