/**
 * 动态LLM客户端管理器 - 按需创建版本
 *
 * 设计原则：
 * - 每次调用动态配置：不依赖环境变量，支持单次调用指定provider
 * - 约定大于配置：提供合理默认值，简化常用场景
 * - 保持代码精简：统一的调用接口，隐藏具体实现细节
 * - 类型安全优先：严格的TypeScript类型定义
 *
 * 📖 使用示例：
 *
 * // 创建动态LLM客户端管理器
 * const llmClient = createLLMClient();
 *
 * // 使用Doubao生成文本
 * const response = await llmClient.chat({
 *   provider: "doubao",
 *   model: "ep-**************-hrc7t",
 *   messages: [{ role: "user", content: "Hello" }]
 * });
 *
 * // 使用OpenRouter智能路由
 * const response = await llmClient.chat({
 *   provider: "openrouter",
 *   model: "openai/gpt-4o-mini",
 *   enableRouting: true,
 *   messages: [{ role: "user", content: "Hello" }]
 * });
 *
 * 🔧 环境变量（可选，提供默认值）：
 * - DOUBAO_API_KEY: Doubao API密钥
 * - OPENROUTER_API_KEY: OpenRouter API密钥
 */

import {
  ILLMClient,
  DEFAULT_LLM_CONFIG,
  SUPPORTED_MODELS,
  type DynamicChatOptions,
  type LLMProviderType,
  type LLMConfigValidationResult,
  type TokenUsageStats,
  LLMClientError,
} from "./ILLMClient";

import { DoubaoClient } from "./DoubaoClient";
import { OpenRouterClient } from "./OpenRouterClient";
import { AIGatewayClient } from "./AIGatewayClient";
import type { ChatResponse, StreamChunk, RequestMetrics } from "./DoubaoClient";

// =================================================================
// 动态LLM客户端实现
// =================================================================

/**
 * 动态LLM客户端管理器
 * 统一接口，按需创建和调用不同provider的客户端
 */
export class DynamicLLMClient implements ILLMClient {
  // 客户端缓存 - 避免重复创建相同配置的客户端
  private clientCache = new Map<
    string,
    DoubaoClient | OpenRouterClient | AIGatewayClient
  >();

  // 性能统计 - 按provider分组
  private performanceStats: Record<LLMProviderType, TokenUsageStats> = {
    doubao: {
      totalRequests: 0,
      totalTokens: 0,
      avgTokensPerRequest: 0,
      lastUpdated: 0,
    },
    openrouter: {
      totalRequests: 0,
      totalTokens: 0,
      avgTokensPerRequest: 0,
      lastUpdated: Date.now(),
    },
    aigateway: {
      totalRequests: 0,
      totalTokens: 0,
      avgTokensPerRequest: 0,
      lastUpdated: 0,
    },
  };

  // 请求指标存储
  private requestMetrics = new Map<
    string,
    RequestMetrics & { provider: LLMProviderType }
  >();

  /**
   * 动态聊天方法 - 每次调用指定provider和模型
   */
  async chat(options: DynamicChatOptions): Promise<ChatResponse> {
    // 验证配置
    this.validateConfig(options);

    // 获取或创建客户端
    const client = this.getOrCreateClient(options);

    // 转换为客户端特定的配置格式
    const clientOptions = this.transformToClientOptions(options);

    try {
      // 执行调用
      const response = await client.chat(clientOptions);

      // 更新性能统计
      this.updatePerformanceStats(options.provider, response);

      return response;
    } catch (error) {
      throw new DynamicLLMError(
        `${options.provider} chat failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        options.provider,
        "chat_error",
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 动态流式聊天方法
   */
  async *chatStream(
    options: DynamicChatOptions
  ): AsyncGenerator<StreamChunk, void, unknown> {
    // 验证配置
    this.validateConfig(options);

    // 获取或创建客户端
    const client = this.getOrCreateClient(options);

    // 转换为客户端特定的配置格式
    const clientOptions = this.transformToClientOptions(options);

    try {
      // 执行流式调用
      const stream = client.chatStream(clientOptions);

      let totalTokens = 0;
      for await (const chunk of stream) {
        // 统计token使用
        if (chunk.usage) {
          totalTokens = chunk.usage.total_tokens;
        }

        yield chunk;
      }

      // 更新流式调用的性能统计
      this.updateStreamingStats(options.provider, totalTokens);
    } catch (error) {
      throw new DynamicLLMError(
        `${options.provider} streaming failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        options.provider,
        "streaming_error",
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 健康检查 - 检查指定provider的可用性
   */
  async healthCheck(provider: LLMProviderType): Promise<boolean> {
    try {
      // 创建基础配置进行健康检查
      const testConfig: DynamicChatOptions = {
        provider,
        model:
          provider === "doubao"
            ? SUPPORTED_MODELS.DOUBAO.FLASH
            : SUPPORTED_MODELS.OPENROUTER.GPT_4O_MINI,
        messages: [{ role: "user", content: "ping" }],
        max_tokens: 10,
        timeout: 5000,
      };

      await this.chat(testConfig);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取性能统计 - 按provider分组
   */
  getPerformanceStats(
    provider?: LLMProviderType
  ): TokenUsageStats | Record<LLMProviderType, TokenUsageStats> {
    if (provider) {
      return { ...this.performanceStats[provider] };
    }

    return {
      doubao: { ...this.performanceStats.doubao },
      openrouter: { ...this.performanceStats.openrouter },
      aigateway: { ...this.performanceStats.aigateway },
    };
  }

  /**
   * 获取请求指标 - 支持按provider过滤
   */
  getRequestMetrics(
    requestId?: string,
    provider?: LLMProviderType
  ): RequestMetrics | RequestMetrics[] | undefined {
    if (requestId) {
      const metric = this.requestMetrics.get(requestId);
      return metric && (!provider || metric.provider === provider)
        ? metric
        : undefined;
    }

    const allMetrics = Array.from(this.requestMetrics.values());
    if (provider) {
      return allMetrics.filter((m) => m.provider === provider);
    }

    return allMetrics;
  }

  // =================================================================
  // 私有方法 - 内部实现
  // =================================================================

  /**
   * 获取或创建客户端实例
   */
  private getOrCreateClient(
    options: DynamicChatOptions
  ): DoubaoClient | OpenRouterClient | AIGatewayClient {
    // 生成缓存键（基于provider和关键配置）
    const cacheKey = this.generateCacheKey(options);

    // 尝试从缓存获取
    let client = this.clientCache.get(cacheKey);

    if (!client) {
      // 创建新客户端
      client = this.createClient(options);

      // 缓存客户端（避免重复创建）
      this.clientCache.set(cacheKey, client);
    }

    return client;
  }

  /**
   * 创建具体的客户端实例
   */
  private createClient(
    options: DynamicChatOptions
  ): DoubaoClient | OpenRouterClient | AIGatewayClient {
    switch (options.provider) {
      case "doubao":
        return this.createDoubaoClient(options);

      case "openrouter":
        return this.createOpenRouterClient(options);

      case "aigateway":
        return this.createAIGatewayClient(options);

      default:
        throw new DynamicLLMError(
          `Unsupported provider: ${options.provider}`,
          options.provider,
          "unsupported_provider"
        );
    }
  }

  /**
   * 创建Doubao客户端
   */
  private createDoubaoClient(options: DynamicChatOptions): DoubaoClient {
    // 如果指定了自定义API key，需要临时设置环境变量
    if (options.apiKey) {
      process.env.DOUBAO_API_KEY = options.apiKey;
    }

    return new DoubaoClient();
  }

  /**
   * 创建OpenRouter客户端
   */
  private createOpenRouterClient(
    options: DynamicChatOptions
  ): OpenRouterClient {
    // 如果指定了自定义配置，需要临时设置环境变量
    if (options.apiKey) {
      process.env.OPENROUTER_API_KEY = options.apiKey;
    }
    if (options.appName) {
      process.env.OPENROUTER_APP_NAME = options.appName;
    }

    return new OpenRouterClient();
  }

  /**
   * 创建AIGateway客户端
   */
  private createAIGatewayClient(options: DynamicChatOptions): AIGatewayClient {
    // 如果指定了自定义配置，需要临时设置环境变量
    if (options.apiKey) {
      process.env.AI_GATEWAY_API_KEY = options.apiKey;
    }
    if (options.appName) {
      process.env.AI_GATEWAY_APP_NAME = options.appName;
    }
    if (options.baseURL) {
      process.env.AI_GATEWAY_BASE_URL = options.baseURL;
    }

    return new AIGatewayClient();
  }

  /**
   * 转换为客户端特定的配置格式
   */
  private transformToClientOptions(options: DynamicChatOptions) {
    return {
      messages: options.messages,
      model: options.model,
      temperature: options.temperature,
      max_tokens: options.max_tokens,
      timeout: options.timeout,
      response_format: options.response_format,
      stream: options.stream,
    };
  }

  /**
   * 生成客户端缓存键
   */
  private generateCacheKey(options: DynamicChatOptions): string {
    return `${options.provider}_${options.apiKey || "default"}_${
      options.baseURL || "default"
    }`;
  }

  /**
   * 验证配置
   */
  private validateConfig(options: DynamicChatOptions): void {
    if (!options.provider) {
      throw new DynamicLLMError(
        "Provider is required",
        "doubao",
        "invalid_config"
      );
    }

    if (!options.model) {
      throw new DynamicLLMError(
        "Model is required",
        options.provider,
        "invalid_config"
      );
    }

    if (
      !options.messages ||
      !Array.isArray(options.messages) ||
      options.messages.length === 0
    ) {
      throw new DynamicLLMError(
        "Messages array is required",
        options.provider,
        "invalid_config"
      );
    }

    // 验证API密钥（如果没有自定义，检查环境变量）
    if (!options.apiKey) {
      const envKey =
        options.provider === "doubao" ? "DOUBAO_API_KEY" : "OPENROUTER_API_KEY";
      if (!process.env[envKey]) {
        throw new DynamicLLMError(
          `${envKey} environment variable is required or provide apiKey in options`,
          options.provider,
          "missing_api_key"
        );
      }
    }
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(
    provider: LLMProviderType,
    response: ChatResponse
  ): void {
    const stats = this.performanceStats[provider];

    stats.totalRequests++;
    stats.totalTokens += response.usage.total_tokens;
    stats.avgTokensPerRequest = stats.totalTokens / stats.totalRequests;
    stats.lastUpdated = Date.now();
  }

  /**
   * 更新流式调用统计
   */
  private updateStreamingStats(
    provider: LLMProviderType,
    totalTokens: number
  ): void {
    const stats = this.performanceStats[provider];

    stats.totalRequests++;
    stats.totalTokens += totalTokens;
    stats.avgTokensPerRequest = stats.totalTokens / stats.totalRequests;
    stats.lastUpdated = Date.now();
  }
}

// =================================================================
// 错误处理
// =================================================================

/**
 * 动态LLM客户端错误类
 */
class DynamicLLMError extends LLMClientError {
  readonly retryable: boolean;

  constructor(
    message: string,
    public readonly provider: LLMProviderType,
    public readonly errorCode: string,
    originalError?: Error
  ) {
    super(message, originalError);

    // 判断错误是否可重试
    this.retryable = this.isRetryableError(errorCode);
  }

  private isRetryableError(errorCode: string): boolean {
    const retryableErrors = [
      "timeout",
      "connection_error",
      "rate_limit_exceeded",
      "server_error",
    ];

    return retryableErrors.includes(errorCode);
  }
}

// =================================================================
// 便捷函数和工厂方法
// =================================================================

/**
 * 创建动态LLM客户端实例 - 主入口函数
 */
export function createLLMClient(): ILLMClient {
  return new DynamicLLMClient();
}

/**
 * 便捷函数：快速创建Doubao调用配置
 */
export function createDoubaoConfig(
  model?: string,
  options?: Partial<DynamicChatOptions>
): Pick<DynamicChatOptions, "provider" | "model"> &
  Partial<DynamicChatOptions> {
  return {
    provider: "doubao",
    model: model || SUPPORTED_MODELS.DOUBAO.STANDARD,
    ...options,
  };
}

/**
 * 便捷函数：快速创建OpenRouter调用配置
 */
export function createOpenRouterConfig(
  model?: string,
  options?: Partial<DynamicChatOptions>
): Pick<DynamicChatOptions, "provider" | "model"> &
  Partial<DynamicChatOptions> {
  return {
    provider: "openrouter",
    model: model || SUPPORTED_MODELS.OPENROUTER.GPT_4O_MINI,
    enableRouting: true, // 默认启用智能路由
    ...options,
  };
}

/**
 * 便捷函数：快速创建AIGateway调用配置
 */
export function createAIGatewayConfig(
  model?: string,
  options?: Partial<DynamicChatOptions>
): Pick<DynamicChatOptions, "provider" | "model"> &
  Partial<DynamicChatOptions> {
  return {
    provider: "aigateway",
    model: model || SUPPORTED_MODELS.AIGATEWAY.GPT_4O,
    ...options,
  };
}

/**
 * 验证provider的可用性
 */
export async function validateProvider(
  provider: LLMProviderType
): Promise<LLMConfigValidationResult> {
  const issues: string[] = [];
  const recommendations: string[] = [];
  const warnings: string[] = [];

  const envKey =
    provider === "doubao"
      ? "DOUBAO_API_KEY"
      : provider === "openrouter"
      ? "OPENROUTER_API_KEY"
      : "AI_GATEWAY_API_KEY";

  // 检查API密钥
  if (!process.env[envKey]) {
    issues.push(`${envKey} environment variable is not set`);
    recommendations.push(`Set ${envKey} in your environment variables`);
  }

  // Doubao特殊验证
  if (provider === "doubao" && process.env.DOUBAO_API_KEY) {
    const uuidPattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidPattern.test(process.env.DOUBAO_API_KEY)) {
      warnings.push(
        "DOUBAO_API_KEY format appears invalid (expected UUID format)"
      );
    }
  }

  // OpenRouter特殊验证
  if (provider === "openrouter" && process.env.OPENROUTER_API_KEY) {
    if (!process.env.OPENROUTER_API_KEY.startsWith("sk-or-")) {
      warnings.push(
        "OPENROUTER_API_KEY format appears invalid (expected sk-or-* format)"
      );
    }
  }

  // AI Gateway特殊验证
  if (provider === "aigateway" && process.env.AI_GATEWAY_API_KEY) {
    // AI Gateway API Key 通常是一个较长的字符串，没有特定前缀
    if (process.env.AI_GATEWAY_API_KEY.length < 10) {
      warnings.push(
        "AI_GATEWAY_API_KEY appears too short (expected longer key)"
      );
    }
  }

  return {
    provider,
    isValid: issues.length === 0,
    issues,
    recommendations,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

// =================================================================
// 重新导出类型和常量
// =================================================================

export type {
  ILLMClient,
  DynamicChatOptions,
  LLMProviderType,
  TokenUsageStats,
} from "./ILLMClient";

export { DEFAULT_LLM_CONFIG, SUPPORTED_MODELS } from "./ILLMClient";

export { DoubaoClient } from "./DoubaoClient";
export { OpenRouterClient } from "./OpenRouterClient";
export { AIGatewayClient } from "./AIGatewayClient";
