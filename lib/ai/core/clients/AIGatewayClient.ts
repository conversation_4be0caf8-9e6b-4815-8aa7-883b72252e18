/**
 * Vercel AI Gateway Client - OpenAI Compatible Implementation
 *
 * 基于 Vercel AI Gateway 官方文档: https://vercel.com/docs/ai-gateway/openai-compat
 *
 * ✨ 核心特性:
 * - 🌐 多模型聚合：支持 OpenAI、Anthropic、xAI、Groq 等主流模型
 * - 🎯 统一接口：OpenAI 完全兼容的 API
 * - 💰 透明定价：无额外加价，按提供商原价计费
 * - 🔄 智能路由：自动负载均衡和故障转移
 * - 📊 内置监控：完整的可观测性支持
 * - 🌊 SSE流式处理：实时响应流
 *
 * 🏗️ 设计原则:
 * - 接口完全兼容现有 LLM 客户端，确保无缝切换
 * - 约定大于配置：提供合理默认值，最小化配置需求
 * - 尽早报错退出：构造函数验证环境变量
 * - 类型安全优先：严格 TypeScript 类型定义
 *
 * 📖 使用示例:
 *
 * // 基础文本对话
 * const client = new AIGatewayClient();
 * const response = await client.chat({
 *   messages: [{ role: "user", content: "Hello, world!" }],
 *   temperature: 0.7,
 *   max_tokens: 1000
 * });
 *
 * // 指定模型
 * const response = await client.chat({
 *   model: "anthropic/claude-sonnet-4",
 *   messages: [{ role: "user", content: "Hello" }]
 * });
 *
 * // 流式响应
 * for await (const chunk of client.chatStream({
 *   messages: [{ role: "user", content: "Write a story" }],
 *   stream: true
 * })) {
 *   console.log(chunk.choices[0]?.delta?.content || "");
 * }
 *
 * 🔧 环境配置:
 * - AI_GATEWAY_API_KEY: Vercel AI Gateway API密钥 (必需)
 * - AI_GATEWAY_DEFAULT_MODEL: 默认模型 (可选，默认 openai/gpt-4o)
 * - AI_GATEWAY_APP_NAME: 应用名称 (可选，默认 GitHub-Card)
 */

import { ILLMClient, TokenUsageStats, LLMClientError } from "./ILLMClient";

import type {
  ChatOptions,
  ChatResponse,
  StreamChunk,
  RequestMetrics,
  ChatMessage,
  MultimodalContent,
} from "./DoubaoClient";

// =================================================================
// AI Gateway 专用配置和常量
// =================================================================

/**
 * AI Gateway 默认配置
 */
const AI_GATEWAY_CONFIG = {
  BASE_URL: "https://ai-gateway.vercel.sh/v1",
  DEFAULT_MODEL: "openai/gpt-4o",
  DEFAULT_APP_NAME: "GitHub-Card",
  DEFAULT_TEMPERATURE: 0.7,
  DEFAULT_MAX_TOKENS: 1024,
  DEFAULT_TIMEOUT: 60000,
  MAX_RETRIES: 3,
  RETRY_DELAY_BASE: 1000, // 基础重试延迟（毫秒）
  RETRY_DELAY_MAX: 10000, // 最大重试延迟（毫秒）
} as const;

/**
 * AI Gateway 支持的模型列表
 */
const AI_GATEWAY_MODELS = {
  // OpenAI 模型
  GPT_4O: "openai/gpt-4o",
  GPT_4O_MINI: "openai/gpt-4o-mini",

  // Anthropic 模型
  CLAUDE_SONNET_4: "anthropic/claude-sonnet-4",
  CLAUDE_HAIKU: "anthropic/claude-3-haiku",

  // xAI 模型
  GROK_3: "xai/grok-3",

  // Meta 模型
  LLAMA_3_1_70B: "meta-llama/llama-3.1-70b-instruct",
} as const;

/**
 * AI Gateway 扩展的多模态内容接口
 * 支持文件（PDF）处理
 */
interface AIGatewayMultimodalContent extends Omit<MultimodalContent, "type"> {
  type: "text" | "image" | "audio" | "file";
  file?: {
    data: string; // base64 编码的文件数据
    media_type: string; // MIME 类型，如 "application/pdf"
    filename?: string; // 可选的文件名
  };
}

/**
 * AI Gateway 工具调用接口
 * 兼容 OpenAI Function Calling 规范
 */
interface AIGatewayTool {
  type: "function";
  function: {
    name: string;
    description?: string;
    parameters?: {
      type: "object";
      properties: Record<string, any>;
      required?: string[];
    };
  };
}

/**
 * AI Gateway 扩展的聊天选项
 * 支持工具调用
 */
interface AIGatewayChatOptions extends ChatOptions {
  tools?: AIGatewayTool[];
  tool_choice?:
    | "auto"
    | "none"
    | { type: "function"; function: { name: string } };
}

/**
 * AI Gateway 客户端错误类
 */
export class AIGatewayClientError extends LLMClientError {
  readonly provider = "aigateway" as const;

  constructor(
    message: string,
    public readonly errorCode: string,
    public readonly retryable: boolean = false,
    originalError?: Error
  ) {
    super(message, originalError);
    this.name = "AIGatewayClientError";
  }
}

// =================================================================
// AI Gateway 客户端实现
// =================================================================

export class AIGatewayClient implements ILLMClient {
  private apiKey: string;
  private baseURL: string = AI_GATEWAY_CONFIG.BASE_URL;
  private appName: string;

  // 性能监控
  private tokenUsageStats: TokenUsageStats;
  private requestMetrics: Map<string, RequestMetrics> = new Map();

  constructor() {
    // 验证必需的环境变量
    this.apiKey = process.env.AI_GATEWAY_API_KEY || "";

    if (!this.apiKey) {
      throw new AIGatewayClientError(
        "AI_GATEWAY_API_KEY environment variable is required. " +
          "Please set it in your environment variables or .env file.",
        "missing_api_key",
        false
      );
    }

    // 可选配置
    this.appName =
      process.env.AI_GATEWAY_APP_NAME || AI_GATEWAY_CONFIG.DEFAULT_APP_NAME;

    // 如果提供了自定义 baseURL，使用它
    if (process.env.AI_GATEWAY_BASE_URL) {
      this.baseURL = process.env.AI_GATEWAY_BASE_URL;
    }

    // 初始化性能统计
    this.tokenUsageStats = {
      totalRequests: 0,
      totalTokens: 0,
      avgTokensPerRequest: 0,
      lastUpdated: Date.now(),
    };

    console.log("AIGatewayClient initialized with Vercel AI Gateway");
  }

  /**
   * 主要聊天方法
   */
  async chat(options: AIGatewayChatOptions): Promise<ChatResponse> {
    if (!this.apiKey) {
      throw new AIGatewayClientError(
        "AI Gateway API key not configured",
        "missing_api_key",
        false
      );
    }

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // 初始化请求指标
      this.requestMetrics.set(requestId, {
        requestId,
        startTime,
      });

      // 执行聊天请求
      return await this.executeChat(options, requestId);
    } catch (error) {
      this.handleRequestError(requestId, error);
      throw error;
    }
  }

  /**
   * 执行聊天请求的核心逻辑（带重试）
   */
  private async executeChat(
    options: AIGatewayChatOptions,
    requestId: string
  ): Promise<ChatResponse> {
    let lastError: unknown;

    for (let attempt = 0; attempt <= AI_GATEWAY_CONFIG.MAX_RETRIES; attempt++) {
      try {
        return await this.executeChatAttempt(options, requestId, attempt);
      } catch (error) {
        lastError = error;

        // 如果是最后一次尝试，或者错误不可重试，直接抛出
        if (
          attempt === AI_GATEWAY_CONFIG.MAX_RETRIES ||
          !this.isRetryableError(error)
        ) {
          throw error;
        }

        // 等待重试延迟
        const delay = this.calculateRetryDelay(attempt);
        console.warn(
          `AI Gateway request ${requestId} failed (attempt ${
            attempt + 1
          }), retrying in ${delay}ms...`
        );
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * 执行单次聊天请求尝试
   */
  private async executeChatAttempt(
    options: AIGatewayChatOptions,
    requestId: string,
    attempt: number
  ): Promise<ChatResponse> {
    const controller = new AbortController();
    const timeout = setTimeout(
      () => controller.abort(),
      options.timeout || AI_GATEWAY_CONFIG.DEFAULT_TIMEOUT
    );

    try {
      // 选择模型
      const selectedModel =
        options.model ||
        process.env.AI_GATEWAY_DEFAULT_MODEL ||
        AI_GATEWAY_CONFIG.DEFAULT_MODEL;

      const requestBody: any = {
        model: selectedModel,
        messages: this.transformMessages(options.messages),
        temperature:
          options.temperature || AI_GATEWAY_CONFIG.DEFAULT_TEMPERATURE,
        max_tokens: options.max_tokens || AI_GATEWAY_CONFIG.DEFAULT_MAX_TOKENS,
        stream: options.stream || false,
        response_format: options.response_format,
      };

      // 添加工具调用支持
      if (options.tools && options.tools.length > 0) {
        requestBody.tools = options.tools;
        if (options.tool_choice) {
          requestBody.tool_choice = options.tool_choice;
        }
      }

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
          "X-Request-ID": requestId,
          "User-Agent": this.appName,
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = (await response.json().catch(() => ({}))) as any;
        throw new AIGatewayClientError(
          `AI Gateway API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || ""
          }`,
          `http_${response.status}`,
          response.status >= 500 || response.status === 429
        );
      }

      const result: ChatResponse = await response.json();

      // 更新性能统计
      this.updatePerformanceStats(result);
      this.updateRequestMetrics(requestId, result);

      return result;
    } catch (error) {
      clearTimeout(timeout);

      if (error instanceof AIGatewayClientError) {
        throw error;
      }

      if (error instanceof Error && error.name === "AbortError") {
        throw new AIGatewayClientError(
          "Request timeout",
          "timeout",
          true,
          error
        );
      }

      throw new AIGatewayClientError(
        `Network error (attempt ${attempt}): ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        "network_error",
        true,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 流式聊天方法 - SSE 实现
   */
  async *chatStream(
    options: AIGatewayChatOptions
  ): AsyncGenerator<StreamChunk, void, unknown> {
    if (!this.apiKey) {
      throw new AIGatewayClientError(
        "AI Gateway API key not configured",
        "missing_api_key",
        false
      );
    }

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // 初始化请求指标
      this.requestMetrics.set(requestId, {
        requestId,
        startTime,
      });

      yield* this.executeStreamChat(options, requestId);
    } catch (error) {
      this.handleRequestError(requestId, error);
      throw error;
    }
  }

  /**
   * 执行流式聊天请求的核心逻辑
   */
  private async *executeStreamChat(
    options: AIGatewayChatOptions,
    requestId: string
  ): AsyncGenerator<StreamChunk, void, unknown> {
    const controller = new AbortController();
    const timeout = setTimeout(
      () => controller.abort(),
      options.timeout || AI_GATEWAY_CONFIG.DEFAULT_TIMEOUT
    );

    try {
      // 选择模型
      const selectedModel =
        options.model ||
        process.env.AI_GATEWAY_DEFAULT_MODEL ||
        AI_GATEWAY_CONFIG.DEFAULT_MODEL;

      const requestBody: any = {
        model: selectedModel,
        messages: this.transformMessages(options.messages),
        temperature:
          options.temperature || AI_GATEWAY_CONFIG.DEFAULT_TEMPERATURE,
        max_tokens: options.max_tokens || AI_GATEWAY_CONFIG.DEFAULT_MAX_TOKENS,
        stream: true, // 强制启用流式
        response_format: options.response_format,
      };

      // 添加工具调用支持
      if (options.tools && options.tools.length > 0) {
        requestBody.tools = options.tools;
        if (options.tool_choice) {
          requestBody.tool_choice = options.tool_choice;
        }
      }

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
          "X-Request-ID": requestId,
          "User-Agent": this.appName,
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = (await response.json().catch(() => ({}))) as any;
        throw new AIGatewayClientError(
          `AI Gateway API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || ""
          }`,
          `http_${response.status}`,
          response.status >= 500 || response.status === 429
        );
      }

      if (!response.body) {
        throw new AIGatewayClientError(
          "No response body received for streaming request",
          "no_response_body",
          false
        );
      }

      // 处理 SSE 流
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");

          // 保留最后一行（可能不完整）
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(6).trim();

              if (data === "[DONE]") {
                return;
              }

              if (data) {
                try {
                  const chunk: StreamChunk = JSON.parse(data);
                  yield chunk;
                } catch (parseError) {
                  console.warn(`Failed to parse SSE data: ${data}`, parseError);
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      clearTimeout(timeout);

      if (error instanceof AIGatewayClientError) {
        throw error;
      }

      if (error instanceof Error && error.name === "AbortError") {
        throw new AIGatewayClientError(
          "Stream request timeout",
          "timeout",
          true,
          error
        );
      }

      throw new AIGatewayClientError(
        `Stream network error: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        "network_error",
        true,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
      });

      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): TokenUsageStats {
    return { ...this.tokenUsageStats };
  }

  /**
   * 获取请求指标
   */
  getRequestMetrics(
    requestId?: string
  ): RequestMetrics | RequestMetrics[] | undefined {
    if (requestId) {
      return this.requestMetrics.get(requestId);
    }
    return Array.from(this.requestMetrics.values());
  }

  // =================================================================
  // 私有辅助方法
  // =================================================================

  /**
   * 转换消息格式以支持多模态内容
   * 将内部的 MultimodalContent 格式转换为 AI Gateway 兼容格式
   */
  private transformMessages(messages: ChatMessage[]): any[] {
    return messages.map((message) => {
      if (typeof message.content === "string") {
        // 纯文本消息，直接返回
        return {
          role: message.role,
          content: message.content,
        };
      } else if (Array.isArray(message.content)) {
        // 多模态消息，转换格式
        const transformedContent = message.content.map((content) => {
          switch (content.type) {
            case "text":
              return {
                type: "text",
                text: content.text || "",
              };

            case "image":
              return {
                type: "image_url",
                image_url: {
                  url: content.image_url?.url || "",
                  detail: content.image_url?.detail || "auto",
                },
              };

            case "audio":
              // AI Gateway 可能不直接支持音频，但保留格式以备将来使用
              return {
                type: "audio_url",
                audio_url: {
                  url: content.audio_url?.url || "",
                  format: content.audio_url?.format || "mp3",
                },
              };

            default:
              // 处理扩展类型（如文件）或未知类型
              const extendedContent = content as AIGatewayMultimodalContent;
              if (extendedContent.type === "file" && extendedContent.file) {
                return {
                  type: "file",
                  file: {
                    data: extendedContent.file.data,
                    media_type: extendedContent.file.media_type,
                    filename: extendedContent.file.filename,
                  },
                };
              }
              // 如果没有文件数据或其他未知类型，作为文本处理
              return {
                type: "text",
                text: content.text || "",
              };
          }
        });

        return {
          role: message.role,
          content: transformedContent,
        };
      } else {
        // 兜底处理，转换为文本
        return {
          role: message.role,
          content: String(message.content),
        };
      }
    });
  }

  private generateRequestId(): string {
    return `aigateway_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;
  }

  private updatePerformanceStats(response: ChatResponse): void {
    if (response.usage) {
      this.tokenUsageStats.totalRequests++;
      this.tokenUsageStats.totalTokens += response.usage.total_tokens;
      this.tokenUsageStats.avgTokensPerRequest =
        this.tokenUsageStats.totalTokens / this.tokenUsageStats.totalRequests;
      this.tokenUsageStats.lastUpdated = Date.now();
    }
  }

  private updateRequestMetrics(
    requestId: string,
    response: ChatResponse
  ): void {
    const metrics = this.requestMetrics.get(requestId);
    if (metrics) {
      metrics.endTime = Date.now();
      metrics.tokenUsage = response.usage;
    }
  }

  /**
   * 计算重试延迟（指数退避）
   */
  private calculateRetryDelay(attempt: number): number {
    const delay = AI_GATEWAY_CONFIG.RETRY_DELAY_BASE * Math.pow(2, attempt);
    return Math.min(delay, AI_GATEWAY_CONFIG.RETRY_DELAY_MAX);
  }

  /**
   * 判断错误是否可重试
   */
  private isRetryableError(error: unknown): boolean {
    if (error instanceof AIGatewayClientError) {
      return error.retryable;
    }

    if (error instanceof Error) {
      // 网络错误通常可重试
      if (error.name === "AbortError" || error.message.includes("fetch")) {
        return true;
      }
    }

    return false;
  }

  /**
   * 等待指定时间
   */
  private async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private handleRequestError(requestId: string, error: unknown): void {
    const metrics = this.requestMetrics.get(requestId);
    if (metrics) {
      metrics.endTime = Date.now();
      // 记录重试次数
      if (!metrics.retryCount) {
        metrics.retryCount = 0;
      }
      metrics.retryCount++;
    }

    console.error(`AI Gateway request ${requestId} failed:`, error);
  }
}

// =================================================================
// 导出
// =================================================================

export { AI_GATEWAY_MODELS, AI_GATEWAY_CONFIG };
export type { ChatOptions, ChatResponse, StreamChunk, RequestMetrics };
