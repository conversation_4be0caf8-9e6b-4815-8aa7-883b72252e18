// R2存储管理器 - Phase 2.3
import { cacheManager } from "@/lib/cloudflare/kv-cache-manager";
import { getR2Client, uploadImageToR2 } from "./r2-client";
import { generateImageId } from "@/utils/img-util";
import {
  R2ImageMapping,
  StorageOperationResult,
  ImageQueryResult,
  R2StorageConfig,
  ImageSource,
  R2_CONSTANTS,
} from "@/types/r2-image-mapping";

export class R2StorageManager {
  private config: R2StorageConfig;

  constructor() {
    this.config = R2_CONSTANTS.DEFAULT_CONFIG;
  }

  /**
   * 存储图片 - 主要入口函数
   */
  async storeImage(
    imageUrl: string,
    contentType: string,
    source: ImageSource = "smart_cache"
  ): Promise<StorageOperationResult> {
    const startTime = Date.now();
    const imageId = generateImageId(imageUrl);

    try {
      // 检查是否已存在
      const existing = await this.getImageMapping(imageId);
      if (existing) {
        return {
          success: true,
          imageId,
          r2Url: existing.r2Url,
          operationTime: Date.now() - startTime,
        };
      }

      // 获取图片数据
      const imageBuffer = await this.fetchImageBuffer(imageUrl);
      if (!imageBuffer) {
        return {
          success: false,
          imageId,
          error: "Failed to fetch image data",
          operationTime: Date.now() - startTime,
        };
      }

      // 上传到R2
      const uploadResult = await uploadImageToR2(
        imageId,
        imageBuffer,
        contentType
      );
      if (!uploadResult.success) {
        return {
          success: false,
          imageId,
          error: uploadResult.error,
          operationTime: Date.now() - startTime,
        };
      }

      // 创建映射记录
      const mapping: R2ImageMapping = {
        id: imageId,
        r2Key: uploadResult.r2Key,
        r2Url: uploadResult.publicUrl,
        originalUrl: imageUrl,
        contentType,
        fileSize: imageBuffer.byteLength,
        uploadedAt: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        source,
      };

      // 保存映射到KV
      await this.saveImageMapping(mapping);

      console.log(`Successfully stored image in R2: ${imageId}`);
      return {
        success: true,
        imageId,
        r2Url: uploadResult.publicUrl,
        operationTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error("Error storing image:", error);
      return {
        success: false,
        imageId,
        error: error instanceof Error ? error.message : String(error),
        operationTime: Date.now() - startTime,
      };
    }
  }

  /**
   * 从Base64数据存储图片 - Phase 3.3
   */
  async storeImageFromBase64(
    base64Data: string,
    contentType: string,
    imageId: string,
    source: ImageSource = "smart_cache"
  ): Promise<StorageOperationResult> {
    const startTime = Date.now();

    try {
      // 检查是否已存在
      const existing = await this.getImageMapping(imageId);
      if (existing) {
        return {
          success: true,
          imageId,
          r2Url: existing.r2Url,
          operationTime: Date.now() - startTime,
        };
      }

      // 转换Base64为ArrayBuffer
      const imageBuffer = Buffer.from(base64Data, "base64").buffer;

      // 上传到R2
      const uploadResult = await uploadImageToR2(
        imageId,
        imageBuffer,
        contentType
      );
      if (!uploadResult.success) {
        return {
          success: false,
          imageId,
          error: uploadResult.error,
          operationTime: Date.now() - startTime,
        };
      }

      // 创建映射记录
      const mapping: R2ImageMapping = {
        id: imageId,
        r2Key: uploadResult.r2Key,
        r2Url: uploadResult.publicUrl,
        originalUrl: `base64:${imageId}`, // 标记为base64来源
        contentType,
        fileSize: imageBuffer.byteLength,
        uploadedAt: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        source,
      };

      // 保存映射到KV
      await this.saveImageMapping(mapping);

      console.log(`Successfully stored base64 image in R2: ${imageId}`);
      return {
        success: true,
        imageId,
        r2Url: uploadResult.publicUrl,
        operationTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error("Error storing base64 image:", error);
      return {
        success: false,
        imageId,
        error: error instanceof Error ? error.message : String(error),
        operationTime: Date.now() - startTime,
      };
    }
  }

  /**
   * 删除图片 - Phase 4.1
   */
  async deleteImage(imageId: string): Promise<boolean> {
    try {
      // 1. 获取映射信息
      const mapping = await this.getImageMapping(imageId);
      if (!mapping) {
        console.log(`Image mapping not found for deletion: ${imageId}`);
        return true; // 已经不存在，视为删除成功
      }

      // 2. 从R2删除图片文件
      const r2Client = getR2Client();
      if (r2Client) {
        try {
          await r2Client.deleteImage(mapping.r2Key);
          console.log(`Deleted image from R2: ${imageId}`);
        } catch (error) {
          console.error(`Failed to delete from R2: ${imageId}`, error);
          // 继续删除映射，即使R2删除失败
        }
      }

      // 3. 删除KV映射
      await cacheManager.delete(
        `${R2_CONSTANTS.IMAGE_MAPPING_PREFIX}${imageId}`
      );
      console.log(`Deleted image mapping: ${imageId}`);

      return true;
    } catch (error) {
      console.error(`Error deleting image ${imageId}:`, error);
      return false;
    }
  }

  /**
   * 查询图片
   */
  async queryImage(imageId: string): Promise<ImageQueryResult> {
    try {
      const mapping = await this.getImageMapping(imageId);
      if (mapping) {
        return {
          found: true,
          imageId,
          r2Url: mapping.r2Url,
          mapping,
          source: "r2",
          cached: true,
        };
      }

      return {
        found: false,
        imageId,
        source: "not_found",
        cached: false,
      };
    } catch (error) {
      console.error("Error querying image:", error);
      return {
        found: false,
        imageId,
        source: "not_found",
        cached: false,
      };
    }
  }

  // 私有辅助方法
  private async fetchImageBuffer(
    imageUrl: string
  ): Promise<ArrayBuffer | null> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000);

      const response = await fetch(imageUrl, {
        signal: controller.signal,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.arrayBuffer();
    } catch (error) {
      console.error("Error fetching image buffer:", error);
      return null;
    }
  }

  private async getImageMapping(
    imageId: string
  ): Promise<R2ImageMapping | null> {
    try {
      return await cacheManager.get<R2ImageMapping>(
        `${R2_CONSTANTS.IMAGE_MAPPING_PREFIX}${imageId}`
      );
    } catch (error) {
      return null;
    }
  }

  private async saveImageMapping(mapping: R2ImageMapping): Promise<void> {
    try {
      await cacheManager.set(
        `${R2_CONSTANTS.IMAGE_MAPPING_PREFIX}${mapping.id}`,
        mapping,
        { expirationTtl: this.config.cacheTTL }
      );
    } catch (error) {
      console.error("Error saving image mapping:", error);
    }
  }
}

// 全局实例
export const r2StorageManager = new R2StorageManager();
