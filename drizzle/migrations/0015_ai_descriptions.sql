-- Migration: Add ai_descriptions table for V5 AI Description Feature
-- Created: 2025-01-30
-- Purpose: Store AI-generated and customized user descriptions

CREATE TABLE ai_descriptions (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- AI生成的描述内容
  generated_description TEXT NOT NULL,
  description_style TEXT NOT NULL, -- technical-expert, community-builder, innovation-pioneer, learning-enthusiast
  
  -- 生成时的数据快照
  data_snapshot TEXT NOT NULL, -- JSON格式的GitHub数据
  prompt_used TEXT NOT NULL,
  ai_model_version TEXT NOT NULL DEFAULT 'deepseek-chat',
  
  -- 用户自定义内容
  custom_description TEXT, -- 用户编辑后的版本
  is_custom_applied BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- 元数据
  generated_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL,
  expires_at INTEGER NOT NULL, -- 描述过期时间，需要重新生成
  
  -- 性能优化索引
  UNIQUE(user_id) -- 每个用户只有一条最新记录
);

-- 创建索引优化查询性能
CREATE INDEX idx_ai_descriptions_user_id ON ai_descriptions(user_id);
CREATE INDEX idx_ai_descriptions_expires_at ON ai_descriptions(expires_at);
CREATE INDEX idx_ai_descriptions_style ON ai_descriptions(description_style);
CREATE INDEX idx_ai_descriptions_generated_at ON ai_descriptions(generated_at);

-- 创建描述生成历史表（可选，用于分析和改进）
CREATE TABLE description_history (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  description_content TEXT NOT NULL,
  generation_type TEXT NOT NULL, -- 'ai-generated', 'user-customized', 'fallback'
  style TEXT,
  created_at INTEGER NOT NULL
);

-- 历史表索引
CREATE INDEX idx_description_history_user_id ON description_history(user_id);
CREATE INDEX idx_description_history_created_at ON description_history(created_at);
CREATE INDEX idx_description_history_type ON description_history(generation_type); 