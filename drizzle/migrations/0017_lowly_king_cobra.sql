CREATE TABLE `ai_generation_requests` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`preferred_style` text,
	`context_type` text,
	`stream_response` integer DEFAULT false,
	`force_regenerate` integer DEFAULT false,
	`status` text DEFAULT 'pending' NOT NULL,
	`current_step` text,
	`progress_percentage` integer DEFAULT 0,
	`step_timings` text,
	`started_at` integer NOT NULL,
	`completed_at` integer,
	`error_message` text,
	`retry_count` integer DEFAULT 0,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `ai_module_logs` (
	`id` text PRIMARY KEY NOT NULL,
	`request_id` text NOT NULL,
	`module_name` text NOT NULL,
	`input_data` text NOT NULL,
	`output_data` text,
	`prompt_used` text,
	`status` text NOT NULL,
	`processing_time` integer,
	`token_usage` text,
	`model_version` text,
	`error_code` text,
	`error_message` text,
	`quality_score` integer,
	`confidence_score` integer,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`request_id`) REFERENCES `ai_generation_requests`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `comedy_strategies` (
	`id` text PRIMARY KEY NOT NULL,
	`strategy_name` text NOT NULL,
	`strategy_type` text NOT NULL,
	`description` text NOT NULL,
	`target_emotions` text,
	`suitable_data_patterns` text,
	`template_examples` text,
	`usage_count` integer DEFAULT 0,
	`success_rate` integer DEFAULT 0,
	`average_rating` integer DEFAULT 0,
	`is_active` integer DEFAULT true,
	`difficulty_level` integer DEFAULT 1,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `comedy_strategies_strategy_name_unique` ON `comedy_strategies` (`strategy_name`);--> statement-breakpoint
CREATE TABLE `few_shot_examples` (
	`id` text PRIMARY KEY NOT NULL,
	`strategy_id` text,
	`input_pattern` text NOT NULL,
	`example_output` text NOT NULL,
	`style_tags` text,
	`quality_rating` integer,
	`humor_rating` integer,
	`appropriateness_rating` integer,
	`selection_count` integer DEFAULT 0,
	`success_rate` integer DEFAULT 0,
	`source` text,
	`author` text,
	`language` text DEFAULT 'zh-CN',
	`is_active` integer DEFAULT true,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`strategy_id`) REFERENCES `comedy_strategies`(`id`) ON UPDATE no action ON DELETE cascade
);
