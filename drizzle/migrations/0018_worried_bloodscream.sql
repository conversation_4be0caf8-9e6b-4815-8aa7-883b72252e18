DROP TABLE `comedy_strategies`;--> statement-breakpoint
DROP TABLE `few_shot_examples`;--> statement-breakpoint
PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_ai_module_logs` (
	`id` text PRIMARY KEY NOT NULL,
	`request_id` text NOT NULL,
	`module_name` text NOT NULL,
	`input_data` text NOT NULL,
	`output_data` text,
	`prompt_used` text,
	`status` text NOT NULL,
	`processing_time` integer,
	`token_usage` text,
	`model_version` text,
	`error_code` text,
	`error_message` text,
	`quality_score` real,
	`confidence_score` real,
	`created_at` integer NOT NULL,
	FOREIGN KEY (`request_id`) REFERENCES `ai_generation_requests`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_ai_module_logs`("id", "request_id", "module_name", "input_data", "output_data", "prompt_used", "status", "processing_time", "token_usage", "model_version", "error_code", "error_message", "quality_score", "confidence_score", "created_at") SELECT "id", "request_id", "module_name", "input_data", "output_data", "prompt_used", "status", "processing_time", "token_usage", "model_version", "error_code", "error_message", "quality_score", "confidence_score", "created_at" FROM `ai_module_logs`;--> statement-breakpoint
DROP TABLE `ai_module_logs`;--> statement-breakpoint
ALTER TABLE `__new_ai_module_logs` RENAME TO `ai_module_logs`;--> statement-breakpoint
PRAGMA foreign_keys=ON;