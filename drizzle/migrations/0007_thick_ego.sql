PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_contribute_datas` (
	`id` text PRIMARY KEY NOT NULL,
	`username` text NOT NULL,
	`login` text NOT NULL,
	`name` text,
	`avatar_url` text NOT NULL,
	`bio` text,
	`blog` text,
	`location` text,
	`twitter_username` text,
	`public_repos` integer NOT NULL,
	`followers` integer NOT NULL,
	`following` integer NOT NULL,
	`user_created_at` integer NOT NULL,
	`total_stars` integer NOT NULL,
	`contribution_score` integer NOT NULL,
	`contribution_grade` text NOT NULL,
	`commits` integer NOT NULL,
	`pull_requests` integer NOT NULL,
	`issues` integer NOT NULL,
	`reviews` integer NOT NULL,
	`last_updated` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`record_created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`user_id` text,
	FOREI<PERSON>N KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_contribute_datas`("id", "username", "login", "name", "avatar_url", "bio", "blog", "location", "twitter_username", "public_repos", "followers", "following", "user_created_at", "total_stars", "contribution_score", "contribution_grade", "commits", "pull_requests", "issues", "reviews", "last_updated", "record_created_at", "user_id") SELECT "id", "username", "login", "name", "avatar_url", "bio", "blog", "location", "twitter_username", "public_repos", "followers", "following", "user_created_at", "total_stars", "contribution_score", "contribution_grade", "commits", "pull_requests", "issues", "reviews", "last_updated", "record_created_at", "user_id" FROM `contribute_datas`;--> statement-breakpoint
DROP TABLE `contribute_datas`;--> statement-breakpoint
ALTER TABLE `__new_contribute_datas` RENAME TO `contribute_datas`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `contribute_datas_username_unique` ON `contribute_datas` (`username`);--> statement-breakpoint
CREATE TABLE `__new_share_links` (
	`id` text PRIMARY KEY NOT NULL,
	`userId` text NOT NULL,
	`link_token` text NOT NULL,
	`github_username` text NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`expires_at` integer NOT NULL,
	`is_active` integer DEFAULT true NOT NULL,
	`template_type` text DEFAULT 'contribute' NOT NULL,
	`background_image_id` text,
	FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_share_links`("id", "userId", "link_token", "github_username", "created_at", "expires_at", "is_active", "template_type", "background_image_id") SELECT "id", "userId", "link_token", "github_username", "created_at", "expires_at", "is_active", "template_type", "background_image_id" FROM `share_links`;--> statement-breakpoint
DROP TABLE `share_links`;--> statement-breakpoint
ALTER TABLE `__new_share_links` RENAME TO `share_links`;--> statement-breakpoint
CREATE UNIQUE INDEX `share_links_link_token_unique` ON `share_links` (`link_token`);--> statement-breakpoint
CREATE TABLE `__new_user_behaviors` (
	`id` text PRIMARY KEY NOT NULL,
	`userId` text NOT NULL,
	`action_type` text NOT NULL,
	`action_data` blob,
	`performed_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_user_behaviors`("id", "userId", "action_type", "action_data", "performed_at") SELECT "id", "userId", "action_type", "action_data", "performed_at" FROM `user_behaviors`;--> statement-breakpoint
DROP TABLE `user_behaviors`;--> statement-breakpoint
ALTER TABLE `__new_user_behaviors` RENAME TO `user_behaviors`;--> statement-breakpoint
CREATE TABLE `__new_user_subscriptions` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`plan_id` text NOT NULL,
	`status` text NOT NULL,
	`price_id` text NOT NULL,
	`current_period_start` integer NOT NULL,
	`current_period_end` integer NOT NULL,
	`cancel_at` integer,
	`canceled_at` integer,
	`cancel_at_period_end` integer DEFAULT true NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans`(`id`) ON UPDATE no action ON DELETE restrict
);
--> statement-breakpoint
INSERT INTO `__new_user_subscriptions`("id", "user_id", "plan_id", "status", "price_id", "current_period_start", "current_period_end", "cancel_at", "canceled_at", "cancel_at_period_end", "created_at", "updated_at") SELECT "id", "user_id", "plan_id", "status", "price_id", "current_period_start", "current_period_end", "cancel_at", "canceled_at", "cancel_at_period_end", "created_at", "updated_at" FROM `user_subscriptions`;--> statement-breakpoint
DROP TABLE `user_subscriptions`;--> statement-breakpoint
ALTER TABLE `__new_user_subscriptions` RENAME TO `user_subscriptions`;