PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_contribute_datas` (
	`id` text PRIMARY KEY NOT NULL,
	`username` text NOT NULL,
	`login` text NOT NULL,
	`name` text,
	`avatar_url` text NOT NULL,
	`bio` text,
	`blog` text,
	`location` text,
	`twitter_username` text,
	`public_repos` integer NOT NULL,
	`followers` integer NOT NULL,
	`following` integer NOT NULL,
	`user_created_at` integer NOT NULL,
	`total_stars` integer NOT NULL,
	`contribution_score` integer NOT NULL,
	`commits` integer NOT NULL,
	`pull_requests` integer NOT NULL,
	`issues` integer NOT NULL,
	`reviews` integer NOT NULL,
	`total_forks` integer DEFAULT 0 NOT NULL,
	`contributed_repos` integer DEFAULT 0 NOT NULL,
	`language_stats` text,
	`data_version` integer DEFAULT 1 NOT NULL,
	`last_full_update` integer DEFAULT 0 NOT NULL,
	`update_status` text DEFAULT 'completed' NOT NULL,
	`last_updated` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`record_created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`user_id` text NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_contribute_datas`("id", "username", "login", "name", "avatar_url", "bio", "blog", "location", "twitter_username", "public_repos", "followers", "following", "user_created_at", "total_stars", "contribution_score", "commits", "pull_requests", "issues", "reviews", "total_forks", "contributed_repos", "language_stats", "data_version", "last_full_update", "update_status", "last_updated", "record_created_at", "user_id") SELECT "id", "username", "login", "name", "avatar_url", "bio", "blog", "location", "twitter_username", "public_repos", "followers", "following", "user_created_at", "total_stars", "contribution_score", "commits", "pull_requests", "issues", "reviews", "total_forks", "contributed_repos", "language_stats", "data_version", "last_full_update", "update_status", "last_updated", "record_created_at", "user_id" FROM `contribute_datas`;--> statement-breakpoint
DROP TABLE `contribute_datas`;--> statement-breakpoint
ALTER TABLE `__new_contribute_datas` RENAME TO `contribute_datas`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `contribute_datas_username_unique` ON `contribute_datas` (`username`);