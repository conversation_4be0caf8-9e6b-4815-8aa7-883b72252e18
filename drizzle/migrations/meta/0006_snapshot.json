{"version": "6", "dialect": "sqlite", "id": "86b1e4a8-07f7-48fe-8ec4-0bf5c8524ab9", "prevId": "a4a02461-2458-4480-80b8-70481bf613d0", "tables": {"accounts": {"name": "accounts", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_provider_providerAccountId_pk": {"columns": ["provider", "providerAccountId"], "name": "accounts_provider_providerAccountId_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "contribute_datas": {"name": "contribute_datas", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "login": {"name": "login", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "blog": {"name": "blog", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "twitter_username": {"name": "twitter_username", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "public_repos": {"name": "public_repos", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "following": {"name": "following", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_created_at": {"name": "user_created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_stars": {"name": "total_stars", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "contribution_score": {"name": "contribution_score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "contribution_grade": {"name": "contribution_grade", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "commits": {"name": "commits", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "pull_requests": {"name": "pull_requests", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "issues": {"name": "issues", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "reviews": {"name": "reviews", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_updated": {"name": "last_updated", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "record_created_at": {"name": "record_created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"contribute_datas_username_unique": {"name": "contribute_datas_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {"contribute_datas_user_id_users_id_fk": {"name": "contribute_datas_user_id_users_id_fk", "tableFrom": "contribute_datas", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'USD'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "receipt_url": {"name": "receipt_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"payments_user_id_users_id_fk": {"name": "payments_user_id_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_subscription_id_user_subscriptions_id_fk": {"name": "payments_subscription_id_user_subscriptions_id_fk", "tableFrom": "payments", "tableTo": "user_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"sessions_sessionToken_unique": {"name": "sessions_sessionToken_unique", "columns": ["sessionToken"], "isUnique": true}}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "share_links": {"name": "share_links", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "link_token": {"name": "link_token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_username": {"name": "github_username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "template_type": {"name": "template_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'contribute'"}, "background_image_id": {"name": "background_image_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"share_links_link_token_unique": {"name": "share_links_link_token_unique", "columns": ["link_token"], "isUnique": true}}, "foreignKeys": {"share_links_userId_users_id_fk": {"name": "share_links_userId_users_id_fk", "tableFrom": "share_links", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscription_plans": {"name": "subscription_plans", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'USD'"}, "interval": {"name": "interval", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "features": {"name": "features", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "stripe_price_id": {"name": "stripe_price_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_behaviors": {"name": "user_behaviors", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action_type": {"name": "action_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action_data": {"name": "action_data", "type": "blob", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_at": {"name": "performed_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"user_behaviors_userId_users_id_fk": {"name": "user_behaviors_userId_users_id_fk", "tableFrom": "user_behaviors", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_subscriptions": {"name": "user_subscriptions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price_id": {"name": "price_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_start": {"name": "current_period_start", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_end": {"name": "current_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cancel_at": {"name": "cancel_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "canceled_at": {"name": "canceled_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"user_subscriptions_user_id_users_id_fk": {"name": "user_subscriptions_user_id_users_id_fk", "tableFrom": "user_subscriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_subscriptions_plan_id_subscription_plans_id_fk": {"name": "user_subscriptions_plan_id_subscription_plans_id_fk", "tableFrom": "user_subscriptions", "tableTo": "subscription_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "github_id": {"name": "github_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {"users_github_id_unique": {"name": "users_github_id_unique", "columns": ["github_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification_tokens": {"name": "verification_tokens", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_tokens_identifier_token_pk": {"columns": ["identifier", "token"], "name": "verification_tokens_identifier_token_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}