{"version": "6", "dialect": "sqlite", "id": "79947c3d-0749-4a9e-8ae7-e4d9fa031d7c", "prevId": "f64e169d-b1ab-4015-b2c7-ae65852c2ac6", "tables": {"ai_descriptions": {"name": "ai_descriptions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "generated_description": {"name": "generated_description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description_style": {"name": "description_style", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data_snapshot": {"name": "data_snapshot", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "prompt_used": {"name": "prompt_used", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ai_model_version": {"name": "ai_model_version", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'doubao-pro-128k'"}, "custom_description": {"name": "custom_description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_custom_applied": {"name": "is_custom_applied", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "generated_at": {"name": "generated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"ai_descriptions_user_id_unique": {"name": "ai_descriptions_user_id_unique", "columns": ["user_id"], "isUnique": true}}, "foreignKeys": {"ai_descriptions_user_id_users_id_fk": {"name": "ai_descriptions_user_id_users_id_fk", "tableFrom": "ai_descriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "ai_generation_requests": {"name": "ai_generation_requests", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "preferred_style": {"name": "preferred_style", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "context_type": {"name": "context_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stream_response": {"name": "stream_response", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "force_regenerate": {"name": "force_regenerate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "current_step": {"name": "current_step", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_percentage": {"name": "progress_percentage", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "step_timings": {"name": "step_timings", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "started_at": {"name": "started_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "completed_at": {"name": "completed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"ai_generation_requests_user_id_users_id_fk": {"name": "ai_generation_requests_user_id_users_id_fk", "tableFrom": "ai_generation_requests", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "ai_module_logs": {"name": "ai_module_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "request_id": {"name": "request_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "module_name": {"name": "module_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "input_data": {"name": "input_data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "output_data": {"name": "output_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "prompt_used": {"name": "prompt_used", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "processing_time": {"name": "processing_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "token_usage": {"name": "token_usage", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "model_version": {"name": "model_version", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_code": {"name": "error_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "quality_score": {"name": "quality_score", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "confidence_score": {"name": "confidence_score", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"ai_module_logs_request_id_ai_generation_requests_id_fk": {"name": "ai_module_logs_request_id_ai_generation_requests_id_fk", "tableFrom": "ai_module_logs", "tableTo": "ai_generation_requests", "columnsFrom": ["request_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "contribute_datas": {"name": "contribute_datas", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "login": {"name": "login", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "blog": {"name": "blog", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "twitter_username": {"name": "twitter_username", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "public_repos": {"name": "public_repos", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "following": {"name": "following", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_created_at": {"name": "user_created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_stars": {"name": "total_stars", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "contribution_score": {"name": "contribution_score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "commits": {"name": "commits", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "pull_requests": {"name": "pull_requests", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "issues": {"name": "issues", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "reviews": {"name": "reviews", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_forks": {"name": "total_forks", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "contributed_repos": {"name": "contributed_repos", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "language_stats": {"name": "language_stats", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "data_version": {"name": "data_version", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "last_full_update": {"name": "last_full_update", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "update_status": {"name": "update_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'completed'"}, "last_updated": {"name": "last_updated", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "record_created_at": {"name": "record_created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {"contribute_datas_user_id_unique": {"name": "contribute_datas_user_id_unique", "columns": ["user_id"], "isUnique": true}}, "foreignKeys": {"contribute_datas_user_id_users_id_fk": {"name": "contribute_datas_user_id_users_id_fk", "tableFrom": "contribute_datas", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "description_history": {"name": "description_history", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description_content": {"name": "description_content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "generation_type": {"name": "generation_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "style": {"name": "style", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"description_history_user_id_users_id_fk": {"name": "description_history_user_id_users_id_fk", "tableFrom": "description_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'USD'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "receipt_url": {"name": "receipt_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"payments_user_id_users_id_fk": {"name": "payments_user_id_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_subscription_id_user_subscriptions_id_fk": {"name": "payments_subscription_id_user_subscriptions_id_fk", "tableFrom": "payments", "tableTo": "user_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "share_links": {"name": "share_links", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "link_token": {"name": "link_token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "template_type": {"name": "template_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'contribute'"}, "background_image_id": {"name": "background_image_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"share_links_link_token_unique": {"name": "share_links_link_token_unique", "columns": ["link_token"], "isUnique": true}}, "foreignKeys": {"share_links_userId_users_id_fk": {"name": "share_links_userId_users_id_fk", "tableFrom": "share_links", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscription_logs": {"name": "subscription_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "from_status": {"name": "from_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "to_status": {"name": "to_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"subscription_logs_subscription_id_user_subscriptions_id_fk": {"name": "subscription_logs_subscription_id_user_subscriptions_id_fk", "tableFrom": "subscription_logs", "tableTo": "user_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscription_logs_user_id_users_id_fk": {"name": "subscription_logs_user_id_users_id_fk", "tableFrom": "subscription_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscription_plans": {"name": "subscription_plans", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'USD'"}, "interval": {"name": "interval", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "features": {"name": "features", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "stripe_price_id": {"name": "stripe_price_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_behaviors": {"name": "user_behaviors", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action_type": {"name": "action_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action_data": {"name": "action_data", "type": "blob", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_at": {"name": "performed_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"user_behaviors_userId_users_id_fk": {"name": "user_behaviors_userId_users_id_fk", "tableFrom": "user_behaviors", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_subscriptions": {"name": "user_subscriptions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price_id": {"name": "price_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_start": {"name": "current_period_start", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_end": {"name": "current_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cancel_at": {"name": "cancel_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "canceled_at": {"name": "canceled_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"user_subscriptions_user_id_users_id_fk": {"name": "user_subscriptions_user_id_users_id_fk", "tableFrom": "user_subscriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_subscriptions_plan_id_subscription_plans_id_fk": {"name": "user_subscriptions_plan_id_subscription_plans_id_fk", "tableFrom": "user_subscriptions", "tableTo": "subscription_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "github_id": {"name": "github_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {"users_github_id_unique": {"name": "users_github_id_unique", "columns": ["github_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}