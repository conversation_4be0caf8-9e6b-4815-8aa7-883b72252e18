CREATE TABLE `github_extended_datas` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`repositories_data` text,
	`commits_data` text,
	`readme_data` text,
	`time_stats_data` text,
	`fetch_config` text,
	`data_version` text DEFAULT '1.0.0' NOT NULL,
	`fetched_at` integer NOT NULL,
	`expires_at` integer NOT NULL,
	`update_status` text DEFAULT 'completed' NOT NULL,
	`error_message` text,
	`repositories_count` integer DEFAULT 0 NOT NULL,
	`commits_count` integer DEFAULT 0 NOT NULL,
	`readmes_count` integer DEFAULT 0 NOT NULL,
	`time_stats_count` integer DEFAULT 0 NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREI<PERSON><PERSON> KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
ALTER TABLE `contribute_datas` DROP COLUMN `repositories_data`;--> statement-breakpoint
ALTER TABLE `contribute_datas` DROP COLUMN `commits_data`;--> statement-breakpoint
ALTER TABLE `contribute_datas` DROP COLUMN `readme_data`;--> statement-breakpoint
ALTER TABLE `contribute_datas` DROP COLUMN `time_stats_data`;