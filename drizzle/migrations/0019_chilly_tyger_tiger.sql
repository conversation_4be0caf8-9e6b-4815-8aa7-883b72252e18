PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_ai_descriptions` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`generated_description` text NOT NULL,
	`description_style` text NOT NULL,
	`data_snapshot` text NOT NULL,
	`prompt_used` text NOT NULL,
	`ai_model_version` text DEFAULT 'doubao-pro-128k' NOT NULL,
	`custom_description` text,
	`is_custom_applied` integer DEFAULT false NOT NULL,
	`generated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`expires_at` integer NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_ai_descriptions`("id", "user_id", "generated_description", "description_style", "data_snapshot", "prompt_used", "ai_model_version", "custom_description", "is_custom_applied", "generated_at", "updated_at", "expires_at") SELECT "id", "user_id", "generated_description", "description_style", "data_snapshot", "prompt_used", "ai_model_version", "custom_description", "is_custom_applied", "generated_at", "updated_at", "expires_at" FROM `ai_descriptions`;--> statement-breakpoint
DROP TABLE `ai_descriptions`;--> statement-breakpoint
ALTER TABLE `__new_ai_descriptions` RENAME TO `ai_descriptions`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `ai_descriptions_user_id_unique` ON `ai_descriptions` (`user_id`);