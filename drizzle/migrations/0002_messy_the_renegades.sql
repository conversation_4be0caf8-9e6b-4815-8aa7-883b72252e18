ALTER TABLE `contribute_datas` ADD `login` text NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `name` text;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `avatar_url` text NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `bio` text;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `blog` text;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `location` text;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `twitter_username` text;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `public_repos` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `followers` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `following` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `user_created_at` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `total_stars` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `contribution_score` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `contribution_grade` text NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `commits` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `pull_requests` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `issues` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `reviews` integer NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `record_created_at` integer DEFAULT (unixepoch()) NOT NULL;--> statement-breakpoint
ALTER TABLE `contribute_datas` ADD `user_id` text REFERENCES users(id);--> statement-breakpoint
ALTER TABLE `contribute_datas` DROP COLUMN `github_data`;--> statement-breakpoint
ALTER TABLE `contribute_datas` DROP COLUMN `created_at`;