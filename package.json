{"name": "github-card", "version": "0.3.3", "private": true, "type": "module", "engines": {"node": "20"}, "scripts": {"dev": "next dev", "build": "next build", "worker:build": "opennextjs-cloudflare build build", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "worker:dev": "wrangler dev --env dev --port 3000", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest __tests__/integration", "db:generate": "drizzle-kit generate", "db:studio": "drizzle-kit studio", "db:push": "wrangler d1 migrations apply github-card-db", "db:push-remote": "wrangler d1 migrations apply github-card-db --remote", "test:extended-data": "tsx scripts/test-extended-data-service.ts", "db:init-dev": "node scripts/clear-dev-db.cjs && tsx scripts/reset-dev-db-from-schema.ts", "clean": "rm -rf .next", "find-active-chinese-devs": "node scripts/find-active-chinese-devs.js", "debug:contributed-repos": "tsx scripts/debug-contributed-repos.ts", "worker:tail": "wrangler tail github-card"}, "dependencies": {"@auth/d1-adapter": "^1.9.1", "@auth/drizzle-adapter": "^1.9.0", "@cloudflare/d1": "^1.0.0", "@cloudflare/kv-asset-handler": "^0.4.0", "@cloudflare/workers-types": "^4.20250506.0", "@libsql/client": "^0.15.9", "@monaco-editor/react": "^4.7.0", "@opennextjs/cloudflare": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.3.0", "@types/better-sqlite3": "^7.6.13", "@types/recharts": "^2.0.1", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.25", "crypto-browserify": "^3.12.1", "drizzle-orm": "^0.43.1", "framer-motion": "^12.9.4", "html-to-image": "^1.11.13", "jose": "^6.0.11", "jwt-decode": "^4.0.0", "lucide-react": "^0.516.0", "monaco-editor": "^0.52.2", "motion": "^12.9.4", "next": "^14.2.30", "next-auth": "^5.0.0-beta.28", "pg": "^8.15.6", "qrcode": "^1.5.4", "react": "18.2.0", "react-dom": "18.2.0", "react-github-calendar": "^4.5.6", "react-hot-toast": "^2.5.2", "recharts": "^2.15.3", "stream-browserify": "^3.0.0", "stripe": "^18.1.1", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.12", "@types/node": "^24.0.7", "@types/pg": "^8.15.0", "@types/qrcode": "^1.5.5", "@types/react": "18.2.52", "@types/react-dom": "18.2.18", "@types/uuid": "^10.0.0", "@typescript-eslint/parser": "^8.32.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "eslint": "^9.26.0", "eslint-config-next": "^14", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^4.1.5", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "wrangler": "^4.25.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "resolutions": {"@types/react": "18.2.52", "@types/react-dom": "18.2.18"}}