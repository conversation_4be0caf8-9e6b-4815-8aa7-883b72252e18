import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// 请求缓存Map
const pendingRequests = new Map<string, Promise<Response>>();

/**
 * 生成请求的唯一键
 */
const generateRequestKey = (
  input: RequestInfo | URL,
  init: RequestInit = {}
): string => {
  const url = typeof input === "string" ? input : input.toString();
  const method = init.method || "GET";
  const body = init.body ? JSON.stringify(init.body) : "";
  return `${method}:${url}:${body}`;
};

/**
 * 通用鉴权 fetch 封装，自动带上 cookie/session，支持请求去重
 * @param input fetch url 或 Request
 * @param init fetch 选项
 */
export const fetchWithAuth = async (
  input: RequestInfo | URL,
  init: RequestInit = {}
): Promise<Response> => {
  const requestKey = generateRequestKey(input, init);

  // 如果相同的请求正在进行中，返回现有的 Promise
  if (pendingRequests.has(requestKey)) {
    // 克隆响应以允许多个使用者
    return pendingRequests
      .get(requestKey)!
      .then((response) => response.clone());
  }

  const fetchPromise = (async () => {
    try {
      const response = await fetch(input, {
        credentials: "include",
        ...init,
        headers: {
          "Content-Type": "application/json",
          ...init.headers,
        },
      });

      if (!response.ok) {
        let errorPayload: { message?: string } = {};
        try {
          errorPayload = await response.json();
        } catch (e) {
          // JSON parsing failed, do nothing and let the default error be thrown
        }
        throw new Error(errorPayload.message || "请求失败");
      }

      return response;
    } finally {
      // 请求完成后从缓存中移除
      pendingRequests.delete(requestKey);
    }
  })();

  // 将新的请求添加到缓存中
  pendingRequests.set(requestKey, fetchPromise);
  return fetchPromise;
};

/**
 * Format a date with the specified format pattern
 * @param format - Format string using placeholders:
 *   - YYYY: 4-digit year (e.g., 2023)
 *   - YY: 2-digit year (e.g., 23)
 *   - MM: 2-digit month (01-12)
 *   - M: 1 or 2-digit month (1-12)
 *   - DD: 2-digit day (01-31)
 *   - D: 1 or 2-digit day (1-31)
 *   - HH: 2-digit hour in 24h format (00-23)
 *   - H: 1 or 2-digit hour in 24h format (0-23)
 *   - hh: 2-digit hour in 12h format (01-12)
 *   - h: 1 or 2-digit hour in 12h format (1-12)
 *   - mm: 2-digit minute (00-59)
 *   - m: 1 or 2-digit minute (0-59)
 *   - ss: 2-digit second (00-59)
 *   - s: 1 or 2-digit second (0-59)
 *   - A: AM/PM
 *   - a: am/pm
 * @param date - Date object to format (defaults to current date if not provided)
 * @returns Formatted date string
 */
export const formatDate = (format: string, date: Date = new Date()): string => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  const pad = (n: number, length: number = 2): string =>
    String(n).padStart(length, "0");

  const replacements: Record<string, string> = {
    YYYY: String(year),
    YY: String(year).slice(-2),
    MM: pad(month),
    M: String(month),
    DD: pad(day),
    D: String(day),
    HH: pad(hours),
    H: String(hours),
    hh: pad(hours % 12 || 12),
    h: String(hours % 12 || 12),
    mm: pad(minutes),
    m: String(minutes),
    ss: pad(seconds),
    s: String(seconds),
    A: hours >= 12 ? "PM" : "AM",
    a: hours >= 12 ? "pm" : "am",
  };

  return format.replace(
    /YYYY|YY|MM|M|DD|D|HH|H|hh|h|mm|m|ss|s|A|a/g,
    (match) => replacements[match] || match
  );
};
