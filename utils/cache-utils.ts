import { cacheManager } from "@/lib/cloudflare/kv-cache-manager";
import { CACHE_IMAGE_PREFIX } from "@/constants";

type CacheMetadata = {
  referenceCount?: number;
  [key: string]: any;
};

/**
 * 原子化地增加缓存项的引用计数
 * @param key 缓存键
 * @returns 更新后的引用计数
 */
export async function incrementReferenceCount(key: string): Promise<number> {
  const cacheKey = `${CACHE_IMAGE_PREFIX}${key}`;
  const { value, metadata = {} } =
    (await cacheManager.getWithMetadata<CacheMetadata>(cacheKey)) || {};

  if (value === undefined) {
    throw new Error(`Cache item not found: ${key}`);
  }

  const newCount = (metadata?.referenceCount || 0) + 1;

  await cacheManager.set(cacheKey, value, {
    metadata: {
      ...(metadata || {}),
      referenceCount: newCount,
      lastUpdated: Date.now(),
    },
  });

  return newCount;
}

/**
 * 原子化地减少缓存项的引用计数
 * @param key 缓存键
 * @returns 更新后的引用计数
 */
export async function decrementReferenceCount(key: string): Promise<number> {
  const cacheKey = `${CACHE_IMAGE_PREFIX}${key}`;
  const { value, metadata = {} } =
    (await cacheManager.getWithMetadata<CacheMetadata>(cacheKey)) || {};

  if (value === undefined) {
    console.warn(
      `Cache item not found when decrementing reference count: ${key}`
    );
    return 0;
  }

  const newCount = Math.max((metadata?.referenceCount || 1) - 1, 0);

  await cacheManager.set(cacheKey, value, {
    metadata: {
      ...metadata,
      referenceCount: newCount,
      lastUpdated: Date.now(),
    },
  });

  return newCount;
}
