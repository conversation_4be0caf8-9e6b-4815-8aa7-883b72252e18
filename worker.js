import { getAssetFromKV } from "@cloudflare/kv-asset-handler";

/**
 * 处理请求的主函数
 * @param {Request} request - 传入的请求对象
 * @param {Object} env - 环境变量和绑定
 * @param {Object} ctx - 执行上下文
 */
// 安全的环境变量管理 - 避免全局作用域污染
function getSecureBinding(env, bindingName) {
  if (!env || typeof env !== 'object') {
    console.warn(`Environment object is invalid for binding: ${bindingName}`);
    return null;
  }
  
  const binding = env[bindingName];
  if (!binding) {
    console.warn(`Binding ${bindingName} not found in environment`);
    return null;
  }
  
  // 只在必要时设置全局变量，并添加验证
  if (typeof globalThis[bindingName] === "undefined") {
    Object.defineProperty(globalThis, bindingName, {
      value: binding,
      writable: false,
      enumerable: false,
      configurable: false
    });
  }
  
  return binding;
}

export default {
  async fetch(request, env, ctx) {
    // 在 worker.js 的 fetch 函数开始处添加
    console.log("Environment bindings:", Object.keys(env));

    // 安全地设置 KV 绑定
    const kvBinding = getSecureBinding(env, 'GITHUB_CARD_KV');
    if (kvBinding) {
      console.log("GITHUB_CARD_KV binding established securely");
    } else {
      console.error("GITHUB_CARD_KV binding failed - check wrangler.jsonc configuration");
    }

    try {
      const url = new URL(request.url);
      const { pathname } = url;

      console.log(`处理请求: ${request.method} ${pathname}`);

      // 1. 处理认证请求
      if (pathname.startsWith("/api/auth/")) {
        try {
          console.log("处理认证请求，使用无DB模式");

          // 导入Auth和认证处理函数
          const { Auth } = await import("@auth/core");
          const { handleAuthWithoutDB } = await import("./lib/d1-auth.js");
          const { config } = handleAuthWithoutDB(env);
          console.log("无DB认证配置创建完成", config);

          // 处理认证请求
          return Auth(request, config);
        } catch (error) {
          console.error(`认证处理详细错误:`, error);
          return new Response(
            JSON.stringify({
              error: "认证处理错误",
              message: error.message,
              stack: error.stack,
            }),
            {
              status: 500,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
      }

      // 2. 优先处理静态资源
      if (
        pathname.startsWith("/_next/") ||
        pathname.startsWith("/static/") ||
        pathname.match(/\.(ico|png|svg|jpe?g|webp|js|css|json|txt)$/)
      ) {
        try {
          return await getAssetFromKV(
            {
              request,
              waitUntil: ctx.waitUntil.bind(ctx),
            },
            {
              ASSET_NAMESPACE: env.ASSETS,
              ASSET_MANIFEST: env.ASSETS.manifest,
            }
          );
        } catch (error) {
          console.error(`静态资源错误 [${pathname}]:`, error);
        }
      }

      // 在处理静态资源后，添加根路径处理逻辑
      if (pathname === "/") {
        try {
          // 尝试获取根页面（通常是 index.html 或特定的 Next.js 路由文件）
          const indexRequest = new Request(`${url.origin}/index.html`, request);

          try {
            return await getAssetFromKV(
              {
                request: indexRequest,
                waitUntil: ctx.waitUntil.bind(ctx),
              },
              {
                ASSET_NAMESPACE: env.ASSETS,
                ASSET_MANIFEST: env.ASSETS.manifest,
              }
            );
          } catch (error) {
            // 如果找不到 index.html，尝试获取 app 目录下的首页
            console.log("尝试获取 app 目录下的首页");
            const appIndexRequest = new Request(
              `${url.origin}/.next/server/app/index.html`,
              request
            );
            return await getAssetFromKV(
              {
                request: appIndexRequest,
                waitUntil: ctx.waitUntil.bind(ctx),
              },
              {
                ASSET_NAMESPACE: env.ASSETS,
                ASSET_MANIFEST: env.ASSETS.manifest,
              }
            );
          }
        } catch (error) {
          console.error("根路径处理错误:", error);
        }
      }

      // 3. 处理API路由
      if (pathname.startsWith("/api/")) {
        // 调用Next.js API路由处理函数
        try {
          // 查找匹配的API路由处理程序
          const apiRoutePath = `./.next/server/pages${pathname}.js`;
          const apiModule = await import(apiRoutePath);

          if (apiModule.default) {
            const apiHandler = apiModule.default;
            const response = await apiHandler(request, env, ctx);
            return response;
          }
        } catch (error) {
          console.error(`API路由错误 [${pathname}]:`, error);
          return new Response(JSON.stringify({ error: "API处理错误" }), {
            status: 500,
            headers: { "Content-Type": "application/json" },
          });
        }
      }

      // 4. 处理预渲染页面 (ISR/SSG)
      const prerenderRoute = false; // 或尝试动态加载manifest
      if (prerenderRoute) {
        try {
          // 构造预渲染页面的路径
          const htmlPath = `./.next/server/pages${pathname}.html`;
          return await getAssetFromKV(
            {
              request: new Request(htmlPath, request),
              waitUntil: ctx.waitUntil.bind(ctx),
            },
            {
              ASSET_NAMESPACE: env.ASSETS,
              ASSET_MANIFEST: env.ASSETS.manifest,
            }
          );
        } catch (error) {
          console.error(`预渲染页面错误 [${pathname}]:`, error);
        }
      }

      // 5. 处理动态路由 (SSR)
      try {
        // 查找匹配的页面处理程序
        const pagePath = `./.next/server/pages${pathname}.js`;
        const pageModule = await import(pagePath);

        if (pageModule.default) {
          const { renderToHTML } = await import("./.next/server/ssr-module.js");
          const html = await renderToHTML(request, {
            env,
            ctx,
            params: {}, // 解析路由参数
          });

          return new Response(html, {
            headers: { "Content-Type": "text/html; charset=utf-8" },
          });
        }
      } catch (error) {
        console.error(`SSR页面错误 [${pathname}]:`, error);
      }

      // 6. 如果所有尝试都失败，返回404错误
      return new Response(
        `<!DOCTYPE html>
         <html>
           <head>
             <title>Page Not Found</title>
             <meta charset="UTF-8">
           </head>
           <body>
             <h1>404 - Page Not Found</h1>
             <p>The requested page "${pathname}" could not be found.</p>
           </body>
         </html>`,
        {
          status: 404,
          headers: { "Content-Type": "text/html; charset=utf-8" },
        }
      );
    } catch (error) {
      console.error("Worker核心错误:", error);
      return new Response(`服务器错误: ${error.message}`, {
        status: 500,
        headers: { "Content-Type": "text/plain" },
      });
    }
  },
};
