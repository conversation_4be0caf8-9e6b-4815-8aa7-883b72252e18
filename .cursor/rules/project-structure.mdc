---
description: 
globs: 
alwaysApply: false
---
# GitHub Card Project Structure Guide

## 🏗️ Project Overview

This is a Next.js 14 application for generating GitHub profile cards with multi-dimensional scoring. The project uses TypeScript, Tailwind CSS, and is deployed on Cloudflare Workers.

## 📁 Core Directory Structure

### `/app` - Next.js App Router
- **Main Pages**: [page.tsx](mdc:app/page.tsx) - Homepage with template showcase
- **User Profiles**: `[username]/page.tsx` - Dynamic user profile pages
- **API Routes**: [api/](mdc:app/api) - Backend API endpoints
- **Authentication**: [auth/](mdc:app/auth) - NextAuth.js authentication pages
- **Subscription**: [subscription/](mdc:app/subscription) - Stripe payment integration
- **Leaderboard**: [leaderboard/](mdc:app/leaderboard) - Community rankings

### `/components` - React Components
- **UI Components**: [ui/](mdc:components/ui) - Reusable UI primitives (shadcn/ui)
- **Charts**: [charts/](mdc:components/charts) - Data visualization components
- **Cards**: [cards/](mdc:components/cards) - Profile card templates
- **MultiDimension**: [MultiDimension/](mdc:components/MultiDimension) - Multi-dimensional scoring components
- **Authentication**: [auth/](mdc:components/auth) - Auth-related components
- **Magic UI**: [magicui/](mdc:components/magicui) - Advanced UI components

### `/lib` - Core Business Logic
- **GitHub Integration**: [github/](mdc:lib/github) - GitHub API client and data processing
- **Database**: [db/](mdc:lib/db) - Drizzle ORM schema and queries
- **Image Management**: Image fetching, caching, and R2 storage
- **Subscription**: [subscription/](mdc:lib/subscription) - Stripe integration
- **Adapters**: [adapters/](mdc:lib/adapters) - Data transformation utilities

### `/constants` - Configuration
- **Dimensions**: [dimensions.ts](mdc:constants/dimensions.ts) - Multi-dimensional scoring configuration

### `/types` - TypeScript Definitions
- Global type definitions for GitHub data, user profiles, and scoring

## 🎯 Key Architecture Patterns

### Component Architecture
- **Atomic Design**: UI components follow atomic design principles
- **Composition**: Complex components built from smaller, reusable parts
- **TypeScript First**: All components have proper type definitions

### Data Flow
1. **GitHub API** → Data fetching via GraphQL
2. **Processing** → Multi-dimensional scoring algorithms
3. **Storage** → Cloudflare D1 database + KV cache
4. **Rendering** → React components with server-side rendering

### Styling System
- **Tailwind CSS**: Utility-first CSS framework
- **CSS Variables**: Custom properties for theming
- **Liquid Glass**: Custom glassmorphism design system
- **Responsive**: Mobile-first responsive design

## 🔧 Development Workflow

### Package Management
- **Yarn**: Primary package manager (as per user preference)
- **Scripts**: Defined in [package.json](mdc:package.json)

### Database Management
- **Schema**: Defined in [lib/db/](mdc:lib/db)
- **Migrations**: Use `yarn db:generate` to create migrations
- **Studio**: Use `yarn db:studio` for database GUI

### Deployment
- **Cloudflare Workers**: Serverless deployment target
- **OpenNext**: Cloudflare adapter for Next.js
- **Environment**: Configuration via [wrangler.jsonc](mdc:wrangler.jsonc)

## 🎨 UI Component Guidelines

### Component Location Rules
- **Reusable UI**: Place in `components/ui/`
- **Feature-specific**: Place in feature directories (e.g., `components/charts/`)
- **Page-specific**: Co-locate with page components

### Naming Conventions
- **Components**: PascalCase (e.g., `MultiDimensionCard`)
- **Files**: kebab-case for utilities, PascalCase for components
- **Types**: PascalCase with descriptive names

### Import/Export Patterns
- **Barrel Exports**: Use index files for clean imports
- **Named Exports**: Prefer named exports over default exports
- **Type Imports**: Use `import type` for type-only imports

## 🔍 Key Files Reference

### Configuration Files
- [package.json](mdc:package.json) - Dependencies and scripts
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [next.config.mjs](mdc:next.config.mjs) - Next.js configuration
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS configuration
- [components.json](mdc:components.json) - shadcn/ui configuration

### Core Application Files
- [app/layout.tsx](mdc:app/layout.tsx) - Root layout component
- [app/globals.css](mdc:app/globals.css) - Global styles
- [middleware.ts](mdc:middleware.ts) - Next.js middleware
- [auth.ts](mdc:auth.ts) - NextAuth.js configuration

### Memory Bank System
- [memory-bank/](mdc:memory-bank) - Project documentation and context
- Follows RIPER framework for project management

## 🚀 Getting Started

1. **Install Dependencies**: `yarn install`
2. **Environment Setup**: Copy `example.env` to `.env.local`
3. **Database Setup**: `yarn db:generate && yarn db:push`
4. **Development**: `yarn dev`
5. **Build**: `yarn build`
6. **Deploy**: `yarn deploy`

## 📝 Development Notes

- **English First**: All user-facing content must be in English
- **Mobile First**: Responsive design with mobile-first approach
- **Performance**: Optimized for Cloudflare Workers environment
- **Type Safety**: Comprehensive TypeScript coverage
- **Testing**: Focus on core functionality and user flows
