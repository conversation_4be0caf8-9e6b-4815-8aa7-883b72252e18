/** @type {import('next').NextConfig} */
import { initOpenNextCloudflareForDev } from "@opennextjs/cloudflare";
import { createRequire } from "module";
const require = createRequire(import.meta.url);

const nextConfig = {
  // Enable output compression
  compress: true,

  // Reduce output size with production settings
  productionBrowserSourceMaps: false,

  // Disable static optimization if not needed
  reactStrictMode: true,

  // 输出模式改为 standalone，更好地兼容 OpenNext
  output: "standalone",

  // 统一禁用 trailing slash 重定向（开发和生产环境）
  trailingSlash: false,
  skipTrailingSlashRedirect: true,

  // Image optimization settings
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "bing.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.bing.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "cn.bing.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "cdn.bimg.cc",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "avatars.githubusercontent.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "avatar.vercel.sh",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "github.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "cdn.pixabay.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "pub-7fc4aeafd81144928c2b7700b70025da.r2.dev",
        port: "",
        pathname: "/**",
      },
    ],
    formats: ["image/avif", "image/webp"],
  },
  experimental: {
    webpackBuildWorker: true,
    optimizeCss: true,
    typedRoutes: false,
    serverComponentsExternalPackages: ["next-cloudflare-edge-adapter"],
  },
  webpack: (config, { dev, isServer, nextRuntime }) => {
    // Fix 'self is not defined' error in server bundle
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };

      // Exclude browser-specific modules from server build
      config.externals = [
        ...(config.externals || []),
        "react-github-calendar",
        "html-to-image",
        "framer-motion",
        "motion",
        "vaul",
      ];
    }

    if (!dev) {
      // Production optimizations
      config.optimization.minimize = true;

      // Clean cache during build to reduce disk usage
      config.cache = false;

      if (!isServer) {
        // Bundle optimization
        config.resolve.alias = {
          ...(config.resolve.alias || {}),
          moment$: "moment/moment.js",
        };

        // Aggressively optimize JavaScript
        config.optimization.minimizer.forEach((minimizer) => {
          if (minimizer.constructor.name === "TerserPlugin") {
            minimizer.options.terserOptions = {
              ...minimizer.options.terserOptions,
              compress: {
                ...minimizer.options.terserOptions.compress,
                drop_console: true,
                passes: 2,
              },
              mangle: true,
            };
          }
        });
      }
    }

    // Edge环境中添加Node核心模块的polyfill
    if (nextRuntime === "edge") {
      config.resolve.alias = {
        ...config.resolve.alias,
        crypto: false,
        stream: false,
        http: false,
        https: false,
        querystring: false,
        zlib: false,
        os: false,
        path: false,
        fs: false,
        "aws-crt": false,
      };
    }

    return config;
  },
  // Purge temporary files during build
  onDemandEntries: {
    maxInactiveAge: 15 * 1000,
    pagesBufferLength: 2,
  },
};

export default nextConfig;
