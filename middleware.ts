import { NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const isProtectedPath = pathname.startsWith("/generate");

  // 开发环境直接跳过所有 middleware 逻辑
  if (process.env.NODE_ENV === "development") {
    return NextResponse.next();
  }

  if (!isProtectedPath) return NextResponse.next();

  // 生产环境才动态导入 auth 模块
  try {
    const { auth } = await import("@/auth");
    const session = await auth();

    if (!session && isProtectedPath) {
      return NextResponse.redirect(new URL("/", request.url));
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Authentication error in middleware:", error);
    return NextResponse.redirect(new URL("/", request.url));
  }
}

export const config = {
  matcher: ["/generate", "/generate/:path*"],
};
