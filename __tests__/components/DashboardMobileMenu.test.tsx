import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock the Badge component to avoid dependency issues
jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }: any) => <span {...props}>{children}</span>,
}));

// Mock the cn utility
jest.mock('@/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

import { DashboardMobileMenu } from '@/components/dashboard/DashboardMobileMenu';

// Mock Next.js components
jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  );
});

jest.mock('next/image', () => {
  return ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} {...props} />
  );
});

describe('DashboardMobileMenu', () => {
  const defaultProps = {
    open: true,
    onOpenChange: jest.fn(),
    currentPath: '/dashboard',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open is true', () => {
    render(<DashboardMobileMenu {...defaultProps} />);

    expect(screen.getByText('Dashboard Navigation')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('AI Features')).toBeInTheDocument();
  });

  it('does not render when open is false', () => {
    render(<DashboardMobileMenu {...defaultProps} open={false} />);

    expect(screen.queryByText('Dashboard Navigation')).not.toBeInTheDocument();
  });

  it('calls onOpenChange when close button is clicked', () => {
    const onOpenChange = jest.fn();
    render(<DashboardMobileMenu {...defaultProps} onOpenChange={onOpenChange} />);

    const closeButton = screen.getByRole('button');
    fireEvent.click(closeButton);

    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  it('calls onOpenChange when background overlay is clicked', () => {
    const onOpenChange = jest.fn();
    render(<DashboardMobileMenu {...defaultProps} onOpenChange={onOpenChange} />);

    // The background overlay is the first div with the backdrop
    const overlay = document.querySelector('.fixed.inset-0.z-40');
    expect(overlay).toBeInTheDocument();

    if (overlay) {
      fireEvent.click(overlay);
      expect(onOpenChange).toHaveBeenCalledWith(false);
    }
  });

  it('highlights the active menu item with proper colors', () => {
    render(<DashboardMobileMenu {...defaultProps} currentPath="/dashboard/ai" />);

    const aiFeatureLink = screen.getByText('AI Features').closest('a');
    expect(aiFeatureLink).toHaveClass('bg-blue-500/20', 'text-blue-300');
  });

  it('shows Profile as the first menu item', () => {
    render(<DashboardMobileMenu {...defaultProps} currentPath="/dashboard" />);

    const profileLink = screen.getByText('Profile').closest('a');
    expect(profileLink).toHaveClass('bg-blue-500/20', 'text-blue-300');
  });

  it('shows Pro badge for AI Features', () => {
    render(<DashboardMobileMenu {...defaultProps} />);

    expect(screen.getByText('Pro')).toBeInTheDocument();
  });

  it('calls onOpenChange when a menu item is clicked', () => {
    const onOpenChange = jest.fn();
    render(<DashboardMobileMenu {...defaultProps} onOpenChange={onOpenChange} />);

    const profileLink = screen.getByText('Profile');
    fireEvent.click(profileLink);

    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  it('applies correct hover styles for non-active items', () => {
    render(<DashboardMobileMenu {...defaultProps} currentPath="/dashboard/ai" />);

    const profileLink = screen.getByText('Profile').closest('a');
    expect(profileLink).toHaveClass('hover:bg-gray-700/50', 'hover:text-gray-100', 'text-gray-300');
  });

  it('shows descriptions with proper visibility for active and non-active items', () => {
    render(<DashboardMobileMenu {...defaultProps} currentPath="/dashboard/ai" />);

    // Active item description should have blue color
    const activeDescription = screen.getByText('AI description generation and customization');
    expect(activeDescription).toHaveClass('text-blue-300/80');

    // Non-active item description should have gray color
    const nonActiveDescription = screen.getByText('Manage your profile settings and GitHub information');
    expect(nonActiveDescription).toHaveClass('text-gray-400');
  });

  it('applies correct icon colors for active and non-active items', () => {
    render(<DashboardMobileMenu {...defaultProps} currentPath="/dashboard/ai" />);

    // Find the AI Features link and check its icon
    const aiFeatureLink = screen.getByText('AI Features').closest('a');
    const aiIcon = aiFeatureLink?.querySelector('svg');
    expect(aiIcon).toHaveClass('text-blue-300');

    // Find a non-active link and check its icon
    const profileLink = screen.getByText('Profile').closest('a');
    const profileIcon = profileLink?.querySelector('svg');
    expect(profileIcon).toHaveClass('text-gray-400');
  });
});
