import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock the Badge component to avoid dependency issues
jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }: any) => <span {...props}>{children}</span>,
}));

// Mock the cn utility
jest.mock('@/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

// Mock the Button component
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
}));

import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';

// Mock Next.js components
jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  );
});

jest.mock('next/image', () => {
  return ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} {...props} />
  );
});

describe('DashboardSidebar', () => {
  const defaultProps = {
    collapsed: false,
    onToggleCollapse: jest.fn(),
    currentPath: '/dashboard',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all navigation items', () => {
    render(<DashboardSidebar {...defaultProps} />);

    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('AI Features')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Share Center')).toBeInTheDocument();
    expect(screen.getByText('Recent Activity')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('shows descriptions for all menu items', () => {
    render(<DashboardSidebar {...defaultProps} />);

    expect(screen.getByText('Manage your profile settings and GitHub information')).toBeInTheDocument();
    expect(screen.getByText('AI description generation and customization')).toBeInTheDocument();
    expect(screen.getByText('Advanced GitHub data analysis')).toBeInTheDocument();
    expect(screen.getByText('Manage sharing links and templates')).toBeInTheDocument();
    expect(screen.getByText('Account and app settings')).toBeInTheDocument();
  });

  it('highlights the active menu item with proper colors', () => {
    render(<DashboardSidebar {...defaultProps} currentPath="/dashboard/ai" />);

    const aiFeatureLink = screen.getByText('AI Features').closest('a');
    expect(aiFeatureLink).toHaveClass('bg-blue-500/20', 'text-blue-300');
  });

  it('shows Pro badge for AI Features', () => {
    render(<DashboardSidebar {...defaultProps} />);

    expect(screen.getByText('Pro')).toBeInTheDocument();
  });

  it('applies correct hover styles for non-active items', () => {
    render(<DashboardSidebar {...defaultProps} currentPath="/dashboard/ai" />);

    const profileLink = screen.getByText('Profile').closest('a');
    expect(profileLink).toHaveClass('hover:bg-gray-700/50', 'hover:text-gray-100', 'text-gray-300');
  });

  it('shows descriptions with proper visibility', () => {
    render(<DashboardSidebar {...defaultProps} currentPath="/dashboard/ai" />);

    // Active item should show description
    const activeDescription = screen.getByText('AI description generation and customization');
    expect(activeDescription).toHaveClass('text-blue-300/80');

    // Non-active items should show descriptions with opacity
    const nonActiveDescription = screen.getByText('Manage your profile settings and GitHub information');
    expect(nonActiveDescription).toHaveClass('text-gray-400', 'opacity-70');
  });

  it('renders in collapsed mode correctly', () => {
    render(<DashboardSidebar {...defaultProps} collapsed={true} />);

    // Text should not be visible in collapsed mode
    expect(screen.queryByText('Profile')).not.toBeInTheDocument();
    expect(screen.queryByText('Manage your profile settings and GitHub information')).not.toBeInTheDocument();
  });
});
