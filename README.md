# GitHub Card Generator

A Next.js application that transforms GitHub profiles into beautiful, shareable cards with contribution analytics and premium features.

## TODO

- V6 render api https://developers.cloudflare.com/browser-rendering/workers-binding-api/browser-rendering-with-do/


## Features

- GitHub OAuth authentication
- Beautiful profile card generation
- Contribution analytics and statistics
- Premium subscription with Stripe
- Shareable profile links
- Developer leaderboard

## Tech Stack

- Next.js 14 with App Router
- TypeScript
- Tailwind CSS
- Drizzle ORM with SQLite
- NextAuth.js for authentication
- Stripe for payments
- Deployed on Cloudflare Workers

## Getting Started

1. Clone the repository
2. Install dependencies with `yarn`
3. Set up environment variables
4. Run database migrations
5. Start the development server with `yarn dev`

## License

MIT
