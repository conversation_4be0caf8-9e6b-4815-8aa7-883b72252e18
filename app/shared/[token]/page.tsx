"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import dynamic from "next/dynamic";
import { SharedCardResponse, UserData } from "@/types/user-data";
import Loading from "@/components/loading";
import { ProfileDimensionPage } from "@/components/cards/profile-dimension-page";
import { ProfileStatisticsPage } from "@/components/cards/profile-statistics-page";
import { TemplateType, isValidTemplateType } from "@/constants/templates";

// Dynamic imports with loading fallbacks
const ProfileContributePage = dynamic(
  () =>
    import("@/components/cards/profile-contribute-page").then((mod) => ({
      default: mod.ProfileContributePage,
    })),
  { loading: () => <Loading /> }
);

const ProfileLinktreePage = dynamic(
  () =>
    import("@/components/cards/profile-linktree-page").then((mod) => ({
      default: mod.ProfileLinktreePage,
    })),
  { loading: () => <Loading /> }
);

const ProfileFlomoPage = dynamic(
  () =>
    import("@/components/cards/profile-flomo-page").then((mod) => ({
      default: mod.ProfileFlomoPage,
    })),
  { loading: () => <Loading /> }
);

const ProfileMultiDimensionPage = dynamic(
  () =>
    import("@/components/cards/profile-multidimension-page").then((mod) => ({
      default: mod.ProfileMultiDimensionPage,
    })),
  { loading: () => <Loading /> }
);

const ProfileActivityPage = dynamic(
  () =>
    import("@/components/cards/profile-activity-page").then((mod) => ({
      default: mod.ProfileActivityPage,
    })),
  { loading: () => <Loading /> }
);

interface ErrorData {
  error: string;
}

export default function SharedCardPage() {
  const params = useParams();
  const token = params.token as string;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<SharedCardResponse | null>(null);

  useEffect(() => {
    async function fetchShareLink() {
      try {
        setLoading(true);
        const response = await fetch(`/api/share-links/${token}`);
        if (!response.ok) {
          const errorData: ErrorData = await response.json();
          throw new Error(errorData.error || "Failed to load shared card");
        }

        const linkData: SharedCardResponse = await response.json();
        console.log("Share link data received:", linkData);
        setData(linkData);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );

        setTimeout(() => {
          window.location.href = "/";
        }, 4000);
      } finally {
        setLoading(false);
      }
    }

    if (token) {
      fetchShareLink();
    }
  }, [token]);

  if (loading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-[#0d1117] text-white p-4 items-center justify-center">
        <div className="rounded-lg max-w-md bg-gray-800 shadow-md w-full p-6">
          <h1 className="font-bold text-center mb-4 text-2xl">Error</h1>
          <p className="text-center mb-6">
            {error || "Failed to load the shared GitHub card."}
          </p>
          <div className="flex justify-center">
            <Link
              href="/"
              className="rounded-md bg-[#fa7b19] text-white py-2 px-4 transition-colors hover:bg-[#e76b0a]"
            >
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex flex-col min-h-screen bg-[#0d1117] text-white p-4 items-center justify-center">
        <div className="rounded-lg max-w-md bg-gray-800 shadow-md w-full p-6">
          <h1 className="font-bold text-center mb-4 text-2xl">
            No Data Available
          </h1>
          <p className="text-center mb-6">
            The shared GitHub card data could not be found.
          </p>
          <div className="flex justify-center">
            <Link
              href="/"
              className="rounded-md bg-[#fa7b19] text-white py-2 px-4 transition-colors hover:bg-[#e76b0a]"
            >
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // 模板组件映射
  const templates: Record<TemplateType, React.ComponentType<any>> = {
    contribute: ProfileContributePage,
    linktree: ProfileLinktreePage,
    flomo: ProfileFlomoPage,
    multidimension: ProfileMultiDimensionPage,
    activity: ProfileActivityPage,
    dimension: ProfileDimensionPage,
    statistics: ProfileStatisticsPage,
  };

  // 验证并获取模板组件
  const templateType = isValidTemplateType(data.templateType)
    ? data.templateType
    : "contribute";
  const SelectedComponent = templates[templateType];

  // Use the same component as in generate page, but pass hideShareButton prop
  return (
    <div className="min-h-screen bg-[#0d1117]">
      {data.userData.login && (
        <SelectedComponent
          username={data.userData.login}
          hideMenu={true}
          sharedData={data}
          backgroundId={data.backgroundId}
        />
      )}

      <div className="flex opacity-80 right-0 bottom-2 left-0 z-50 fixed justify-center">
        <Link
          href="/"
          className="bg-[#fa7b19] rounded-3xl shadow-lg text-white text-sm py-1.5 px-3 transition-colors hover:bg-[#e76b0a]"
        >
          Create Your Own
        </Link>
      </div>
    </div>
  );
}
