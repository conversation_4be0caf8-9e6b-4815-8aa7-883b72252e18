import { getActiveSubscriptionPlans } from "@/lib/subscription";
import { Suspense } from "react";
import { SubscriptionPlans } from "./SubscriptionPlans";

// Force dynamic rendering since we're using searchParams
export const dynamic = "force-dynamic";

type Plan = {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: "month" | "year";
  features: string[];
  isPopular?: boolean;
  buttonText: string;
  buttonVariant:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | null
    | undefined;
  stripePriceId?: string;
  stripeProductId?: string;
};

export default async function SubscriptionPage() {
  // Fetch plans from the database
  const dbPlans = await getActiveSubscriptionPlans();

  // Transform database plans to match our UI component's expected format
  const plans: Plan[] = [
    // Free plan (not in database)
    {
      id: "free",
      name: "Free",
      price: 0,
      currency: "USD",
      interval: "month",
      description: "Perfect for getting started",
      features: [
        // 所有模板免费使用
        "All templates available",
        // 同一时间只能发布一个 share-link
        "One share-link at a time",
        // 每个 share-link 三天有效期
        "Share-link 3 days validity",
        // 社区支持
        "Community support",
        // 基础分析
        "Basic analytics",
      ],
      buttonText: "Get Started",
      buttonVariant: "outline",
    },
    // Add database plans
    ...dbPlans.map((plan) => ({
      id: plan.id,
      name: plan.name,
      description: plan.description,
      price: plan.price / 100,
      currency: plan.currency,
      interval: plan.interval as "month" | "year",
      features: [
        "All templates available",
        // 无限制发布 share-links
        "Unlimited share-links",
        // share-links 永不过期
        "Share-links that never expire",
        // 优先技术支持
        "Priority support",
        // 专业分析
        "Professional analysis",
      ],
      isPopular: plan.name.toLowerCase().includes("pro"),
      buttonText: plan.name === "Pro" ? "Upgrade to Pro" : "Subscribe",
      buttonVariant: (plan.name.toLowerCase().includes("pro")
        ? "default"
        : "outline") as "default" | "outline",
      stripePriceId: plan.stripePriceId || undefined,
      stripeProductId: plan.stripeProductId || undefined,
    })),
  ];

  // Sort plans by price
  plans.sort((a, b) => a.price - b.price);

  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      }
    >
      <SubscriptionPlans initialPlans={plans} />
    </Suspense>
  );
}
