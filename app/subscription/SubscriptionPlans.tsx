"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Star, Zap, Crown, Shield } from "lucide-react";
import { useSubscription } from "@/hooks/use-subscription";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

type Plan = {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: "month" | "year";
  features: string[];
  isPopular?: boolean;
  buttonText: string;
  buttonVariant:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | null
    | undefined;
  stripePriceId?: string;
  stripeProductId?: string;
};

interface ErrorData {
  error: string;
}

interface CheckoutResponse {
  url: string;
}

type SubscriptionPlansProps = {
  initialPlans: Plan[];
};

export function SubscriptionPlans({ initialPlans }: SubscriptionPlansProps) {
  const { data: session } = useSession();
  const { isPro } = useSubscription();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const [error, setError] = useState<string | null>(null);

  const handleSubscribe = async (plan: Plan) => {
    if (!session) {
      router.push(
        `/auth/signin?callbackUrl=${encodeURIComponent(
          window.location.pathname
        )}`
      );
      return;
    }

    if (plan.id === "free") {
      router.push("/dashboard");
      return;
    }

    if (!plan.stripePriceId) {
      setError("This plan is not available for subscription at the moment.");
      return;
    }

    setError(null);
    setIsLoading(true);

    try {
      const response = await fetch("/api/stripe/create-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          priceId: plan.stripePriceId,
          successUrl: `${window.location.origin}/dashboard?success=true`,
          cancelUrl: window.location.href,
        }),
      });

      if (!response.ok) {
        let errorPayload: { error: string };
        try {
          errorPayload = await response.json();
        } catch (e) {
          errorPayload = { error: "Failed to create checkout session" };
        }
        throw new Error(
          errorPayload.error || "Failed to create checkout session"
        );
      }

      const { url }: CheckoutResponse = await response.json();
      if (url) {
        window.location.href = url;
      } else {
        throw new Error("No redirect URL received from server");
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred. Please try again later."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // show error toast
  useEffect(() => {
    if (error) {
      toast.error(error, {
        duration: 5000,
      });
    }
  }, [error]);

  // Filter and transform plans based on billing interval
  const plans = initialPlans.map((plan) => ({
    ...plan,
    buttonText: isPro && plan.id === "pro" ? "Current Plan" : plan.buttonText,
  }));

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Shield className="h-4 w-4" />
            Choose Your Plan
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-600 bg-clip-text text-transparent mb-6">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Choose the perfect plan for your needs. No hidden fees, cancel
            anytime.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <div key={plan.id} className="relative">
              {plan.isPopular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-primary to-primary/80 text-white px-4 py-1 text-sm font-medium">
                    Most Popular
                  </Badge>
                </div>
              )}
              <Card
                className={`h-full flex flex-col transition-all duration-300 hover:shadow-xl ${
                  plan.isPopular
                    ? "border-2 border-primary shadow-lg scale-[1.02]"
                    : "border border-gray-200"
                }`}
              >
                <CardHeader className="text-center pb-6">
                  <div
                    className={`inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4 mx-auto ${
                      plan.isPopular
                        ? "bg-gradient-to-r from-primary to-primary/80 text-white"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {plan.id === "free" ? (
                      <Star className="h-6 w-6" />
                    ) : plan.id === "pro" ? (
                      <Zap className="h-6 w-6" />
                    ) : (
                      <Crown className="h-6 w-6" />
                    )}
                  </div>
                  <CardTitle className="text-2xl font-bold">
                    {plan.name}
                  </CardTitle>
                  <CardDescription className="text-base">
                    {plan.description}
                  </CardDescription>
                  <div className="mt-6">
                    <div className="flex items-baseline justify-center gap-2">
                      <span className="text-4xl font-bold">
                        ${plan.id === "free" ? "0" : plan.price}
                      </span>
                      <span className="text-muted-foreground">
                        /
                        {plan.id === "free"
                          ? "forever"
                          : plan.interval.toLowerCase()}
                      </span>
                    </div>
                    {plan.interval === "year" && plan.id !== "free" && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Billed annually (${(plan.price / 12).toFixed(2)}/month)
                      </p>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="flex-1">
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div
                          className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mt-0.5 ${
                            plan.isPopular
                              ? "bg-primary/20 text-primary"
                              : "bg-muted text-muted-foreground"
                          }`}
                        >
                          <Check className="h-3 w-3" />
                        </div>
                        <span className="text-sm leading-relaxed">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>

                <CardFooter className="pt-6">
                  <Button
                    variant={plan.buttonVariant}
                    size="lg"
                    className={`w-full font-medium ${
                      plan.isPopular
                        ? "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg"
                        : ""
                    }`}
                    disabled={(isPro && plan.id === "pro") || isLoading}
                    onClick={() => handleSubscribe(plan)}
                  >
                    {isLoading ? "Processing..." : plan.buttonText}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          ))}
        </div>

        <div className="mt-24">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-muted-foreground">
              Find answers to common questions about our plans and features.
            </p>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            {[
              {
                question: "Can I change my plan later?",
                answer:
                  "Yes, you can upgrade or downgrade your plan at any time. Changes will be prorated.",
              },
              {
                question: "What payment methods do you accept?",
                answer:
                  "We accept all major credit cards. For enterprise plans, we also support bank transfers and invoices.",
              },
              {
                question: "Is there a free trial?",
                answer:
                  "Yes, all paid plans come with a 14-day free trial. No credit card is required to start a trial.",
              },
              {
                question: "How do I cancel my subscription?",
                answer:
                  "You can cancel your subscription at any time from your account settings. No questions asked.",
              },
            ].map((faq, index) => (
              <div key={index} className="border-b border-gray-200 pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {faq.question}
                </h3>
                <p className="text-muted-foreground">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-24 text-center">
          <h2 className="text-3xl font-bold mb-4">Still have questions?</h2>
          <p className="text-muted-foreground mb-8">
            Our team is here to help you find the perfect plan.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" size="lg">
              Contact Support
            </Button>
            <Button size="lg">Schedule a Demo</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
