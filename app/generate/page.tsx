"use client";

import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, Suspense, useState, useCallback } from "react";
import dynamic from "next/dynamic";
import { Navbar } from "@/components/auth/navbar";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { CompleteUserResponse } from "@/types/user-data";
import Loading from "@/components/loading";
import { useShareLinks } from "@/contexts/sharelinks-context";
import { useBackground } from "@/contexts/background-context";
import { ProfileStatisticsPage } from "@/components/cards/profile-statistics-page";
import { TemplateType, isValidTemplateType } from "@/constants/templates";

interface GitHubDataResponse {
  success: boolean;
  data?: CompleteUserResponse;
}

// Dynamic imports with loading fallbacks
const ProfileContributePage = dynamic(
  () =>
    import("@/components/cards/profile-contribute-page").then((mod) => ({
      default: mod.ProfileContributePage,
    })),
  { loading: () => <Loading /> }
);

const ProfileLinktreePage = dynamic(
  () =>
    import("@/components/cards/profile-linktree-page").then((mod) => ({
      default: mod.ProfileLinktreePage,
    })),
  { loading: () => <Loading /> }
);

const ProfileFlomoPage = dynamic(
  () =>
    import("@/components/cards/profile-flomo-page").then((mod) => ({
      default: mod.ProfileFlomoPage,
    })),
  { loading: () => <Loading /> }
);

const ProfileMultiDimensionPage = dynamic(
  () =>
    import("@/components/cards/profile-multidimension-page").then((mod) => ({
      default: mod.ProfileMultiDimensionPage,
    })),
  { loading: () => <Loading /> }
);

const ProfileActivityPage = dynamic(
  () =>
    import("@/components/cards/profile-activity-page").then((mod) => ({
      default: mod.ProfileActivityPage,
    })),
  { loading: () => <Loading /> }
);

const ProfileDimensionPage = dynamic(
  () =>
    import("@/components/cards/profile-dimension-page").then((mod) => ({
      default: mod.ProfileDimensionPage,
    })),
  { loading: () => <Loading /> }
);

// 分离逻辑到一个使用 useSearchParams 的组件
function GenerateContent() {
  // 1. 首先调用所有 Hooks
  const { generateShareLink, shareLink } = useShareLinks();
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isDownloading, setIsDownloading] = useState(false);
  const [userData, setUserData] = useState<CompleteUserResponse | undefined>(
    undefined
  );
  const { backgroundId } = useBackground();

  useEffect(() => {
    console.log("GenerateContent中的shareLink:", shareLink);
  }, [shareLink]);

  // 2. 定义常量和变量
  const template = searchParams.get("template") || "contribute";
  const templates: Record<TemplateType, React.ComponentType<any>> = {
    contribute: ProfileContributePage,
    linktree: ProfileLinktreePage,
    flomo: ProfileFlomoPage,
    multidimension: ProfileMultiDimensionPage,
    activity: ProfileActivityPage,
    dimension: ProfileDimensionPage,
    statistics: ProfileStatisticsPage,
  };

  // 验证模板类型
  const templateType = isValidTemplateType(template) ? template : "contribute";
  const SelectedComponent = templates[templateType];

  // 3. 定义所有回调函数
  const handleDownloadStateChange = useCallback((downloading: boolean) => {
    setIsDownloading(downloading);
  }, []);

  const fetchUserData = useCallback(async () => {
    try {
      const res = await fetch(`/api/github-data`);
      const result: GitHubDataResponse = await res.json();
      if (result.success && result.data && result.data.userData) {
        console.log("result.data", result.data);
        setUserData(result.data);
      } else {
        console.error("Error in API response:", result);
      }
    } catch (error: unknown) {
      console.error("Error fetching user data:", error);
    }
  }, []); // 依赖项为空，此函数在组件生命周期内稳定

  // 4. 使用 useEffect 处理副作用
  useEffect(() => {
    // 仅在开发环境添加调试日志
    if (process.env.NODE_ENV === "development") {
      console.log("🔧 [DEV] Generate page - Auth status:", {
        status,
        hasSession: !!session,
        hasUserData: !!userData,
        username: session?.user?.username,
      });
    }

    // 如果用户未登录，则重定向到首页
    if (status === "unauthenticated") {
      if (process.env.NODE_ENV === "development") {
        console.log(
          "🔧 [DEV] Generate page - Redirecting to home (unauthenticated)"
        );
      }
      router.push("/");
      return;
    }

    // 仅当用户已认证且尚未获取数据时，才调用 fetchUserData
    if (status === "authenticated" && !userData) {
      fetchUserData();
    }
  }, [status, router, fetchUserData]); // 依赖项中移除 userData

  // 6. 处理生成分享链接
  useEffect(() => {
    if (
      status === "authenticated" &&
      backgroundId &&
      shareLink?.templateType !== templateType
    ) {
      generateShareLink(templateType);
    }
  }, [status, generateShareLink, shareLink, templateType, backgroundId]);

  // 5. 处理加载状态
  if (status === "loading" || (status === "authenticated" && !userData)) {
    return <Loading />;
  }

  // 8. 渲染主界面
  return session?.user?.username ? (
    <div className="min-h-screen bg-[#0d1117] text-white">
      {!isDownloading && <Navbar />}
      <SelectedComponent
        username={session.user.username}
        onDownloadStateChange={handleDownloadStateChange}
        sharedData={userData}
      />
    </div>
  ) : (
    <div className="min-h-screen bg-[#0d1117] text-white">
      <Navbar />
      <div className="container mx-auto py-16 px-4">
        <div className="mx-auto max-w-md text-center">
          <h2 className="font-bold mb-4 text-3xl">Login Required</h2>
          <p className="mb-8 text-[#c9d1d9]">
            You need to sign in with GitHub to access this page.
          </p>
          <Link href="/">
            <Button className="bg-[#fa7b19] hover:bg-[#e76b0a]">
              Go Back to Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function GeneratePage() {
  return (
    <Suspense fallback={<Loading />}>
      <GenerateContent />
    </Suspense>
  );
}
