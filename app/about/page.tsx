import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { Navbar } from "@/components/auth/navbar";
import { Footer } from "@/components/footer";
import { Badge } from "@/components/ui/badge";
import dynamic from "next/dynamic";

// Dynamically import chart components
const DimensionVisualization = dynamic(
  () => import("@/components/scoring/DimensionVisualizations"),
  {
    ssr: false,
    loading: () => (
      <div className="flex h-96 items-center justify-center">
        <div className="rounded-full border-b-2 border-orange-500 h-12 animate-spin w-12"></div>
      </div>
    ),
  }
);

// Level System Visualization
const LevelSystemChart = () => {
  const levels = [
    {
      level: "S",
      title: "卓越贡献者",
      subtitle: "Elite Contributor",
      criteria: "3-4个领域突出(≥80分)",
      description: "在多个领域都有出色表现，全面发展",
      color: "from-yellow-400 to-yellow-600",
      count: "5%",
    },
    {
      level: "A",
      title: "双领域专家",
      subtitle: "Dual-Domain Expert",
      criteria: "2个领域突出(≥80分)",
      description: "在两个核心维度上达到专业水平",
      color: "from-gray-300 to-gray-500",
      count: "15%",
    },
    {
      level: "B",
      title: "专精型贡献者",
      subtitle: "Specialized Contributor",
      criteria: "1个领域突出(≥80分)",
      description: "在特定维度上展现出专业实力",
      color: "from-orange-400 to-orange-600",
      count: "25%",
    },
    {
      level: "C",
      title: "潜力型贡献者",
      subtitle: "Potential Contributor",
      criteria: "最高分未达80分但≥60分",
      description: "各维度均衡发展，具备成长潜力",
      color: "from-blue-400 to-blue-600",
      count: "35%",
    },
    {
      level: "D",
      title: "成长型贡献者",
      subtitle: "Growing Contributor",
      criteria: "最高分<60分",
      description: "正在建立自己的贡献轨迹",
      color: "from-gray-500 to-gray-700",
      count: "20%",
    },
  ];

  return (
    <div className="grid gap-4 grid-cols-1 md:grid-cols-5">
      {levels.map((item, index) => (
        <div
          key={index}
          className="text-center transition-transform duration-300 group hover:scale-105"
        >
          <div
            className={`w-20 h-20 rounded-full bg-gradient-to-br ${item.color} flex items-center justify-center text-3xl font-bold text-white mx-auto mb-3 shadow-lg`}
          >
            {item.level}
          </div>
          <h3 className="font-semibold text-lg text-white mb-1">
            {item.title}
          </h3>
          <p className="text-xs mb-1 text-orange-400">{item.subtitle}</p>
          <p className="text-sm mb-2 text-gray-300">{item.criteria}</p>
          <p className="text-xs mb-1 text-gray-500">{item.description}</p>
          <p className="text-xs text-gray-400">{item.count} of users</p>
        </div>
      ))}
    </div>
  );
};

export const metadata: Metadata = {
  title: "About | GitHub Developer Assessment System V4.2",
  description:
    "Learn how our V4.2 multi-dimensional GitHub developer assessment system works with intelligent scoring algorithms",
};

export default function AboutPage() {
  return (
    <div className="bg-gradient-to-br to-black min-h-screen from-gray-900 via-gray-900 text-white">
      <div className="z-50 relative">
        <Navbar showLinks={true} />
      </div>

      <main className="relative overflow-hidden">
        {/* Background Image */}
        <div className="inset-0 z-0 fixed">
          <div
            className="bg-cover bg-center bg-no-repeat opacity-50 inset-0 absolute"
            style={{
              backgroundImage: "url(/images/about_bg.png)",
            }}
          ></div>
        </div>

        {/* Hero Section */}
        <section className="px-4 pt-32 pb-20 z-10 relative sm:px-6 lg:px-8">
          <div className="mx-auto text-center max-w-7xl">
            <Badge
              variant="outline"
              className="font-medium bg-orange-500/20 border-orange-500/50 text-sm mb-6 text-orange-300"
            >
              V4.2 Multi-Dimensional Assessment
            </Badge>
            <h1 className="bg-clip-text bg-gradient-to-r font-bold from-orange-400 via-orange-500 to-orange-600 text-transparent mb-6 text-4xl md:text-6xl">
              GitHub Developer Assessment
            </h1>
            <p className="mx-auto text-xl mb-12 max-w-3xl text-gray-300">
              V4.2 智能评分算法：科学的多维度分析与领域突出度判定
            </p>
          </div>
        </section>

        {/* Four Dimensions Interactive Visualization */}
        <section className="py-20 px-4 z-10 relative sm:px-6 lg:px-8">
          <div className="mx-auto max-w-7xl">
            <h2 className="font-bold text-center mb-6 text-3xl">
              <span className="bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600 text-transparent">
                Four-Dimensional Framework
              </span>
            </h2>
            <p className="mx-auto mb-16 max-w-3xl text-gray-400">
              基于V4.2智能评分算法，采用科学的多维度分析方法，每个维度均衡权重25%，使用非线性增长模型和智能分层评分
            </p>
            <DimensionVisualization />
          </div>
        </section>

        {/* Developer Rating System */}
        <section className="py-20 px-4 z-10 relative sm:px-6 lg:px-8">
          <div className="mx-auto max-w-7xl">
            <h2 className="font-bold text-center mb-6 text-3xl">
              <span className="bg-clip-text bg-gradient-to-r from-green-400 to-blue-600 text-transparent">
                智能等级评定系统
              </span>
            </h2>
            <p className="mx-auto text-center mb-12 max-w-3xl text-gray-400">
              基于领域突出度的智能判定算法，摆脱传统分数段限制，根据在各维度的专业表现来评定等级，突出开发者类型差异
            </p>
            <LevelSystemChart />

            {/* 算法说明 */}
            <div className="bg-gradient-to-br border rounded-xl from-gray-800/50 to-gray-700/30 border-gray-600/50 mt-12 p-6">
              <h3 className="font-semibold text-lg text-white text-center mb-4">
                V4.2 智能评分算法原理
              </h3>
              <div className="text-sm grid gap-6 grid-cols-1 md:grid-cols-3">
                <div>
                  <h4 className="font-medium mb-2 text-green-400">
                    四维度均衡权重
                  </h4>
                  <ul className="space-y-1 text-gray-400">
                    <li>• <strong>Code Architect</strong>：代码创造与架构能力</li>
                    <li>• <strong>Community Builder</strong>：协作交流与社区建设</li>
                    <li>• <strong>Open Source Pioneer</strong>：开源影响力与领导力</li>
                    <li>• <strong>Innovation Explorer</strong>：技术探索与学习能力</li>
                    <li>• <strong>权重分配</strong>：每个维度均衡25%</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2 text-blue-400">
                    智能算法特性
                  </h4>
                  <ul className="space-y-1 text-gray-400">
                    <li>• <strong>非线性增长模型</strong>：新手阶段价值更高</li>
                    <li>• <strong>时间因素考量</strong>：账号年龄与经验深度</li>
                    <li>• <strong>质量指标融合</strong>：效率、平衡性、真实性</li>
                    <li>• <strong>防刷数据机制</strong>：避免低质量贡献</li>
                    <li>• <strong>精确度优化</strong>：精确到小数点后2位</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2 text-purple-400">
                    领域突出度判定
                  </h4>
                  <ul className="space-y-1 text-gray-400">
                    <li>• <strong>突出标准</strong>：单个维度评分 ≥ 80分</li>
                    <li>• <strong>智能分级</strong>：基于突出领域数量</li>
                    <li>• <strong>专业认证</strong>：体现开发者类型特征</li>
                    <li>• <strong>成长导向</strong>：鼓励全面发展</li>
                    <li>• <strong>动态评估</strong>：实时反映能力变化</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Statistics Dashboard */}
        <section className="py-20 px-4 z-10 relative sm:px-6 lg:px-8">
          <div className="mx-auto max-w-7xl">
            <h2 className="font-bold text-center mb-16 text-3xl">
              <span className="bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 text-transparent">
                Assessment Statistics
              </span>
            </h2>

            <div className="grid gap-6 grid-cols-1 md:grid-cols-4">
              {[
                {
                  title: "Total Assessments",
                  value: "50K+",
                  icon: "👥",
                  color: "from-orange-500 to-orange-600",
                },
                {
                  title: "Average Score",
                  value: "67.5",
                  icon: "📊",
                  color: "from-blue-500 to-blue-600",
                },
                {
                  title: "Top Performers",
                  value: "2.5K",
                  icon: "🏆",
                  color: "from-green-500 to-green-600",
                },
                {
                  title: "Active Users",
                  value: "15K",
                  icon: "⚡",
                  color: "from-purple-500 to-purple-600",
                },
              ].map((stat, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-br border rounded-xl from-gray-900/50 to-gray-800/30 border-gray-700/50 text-center p-6 transition-transform duration-300 backdrop-blur-sm hover:scale-105"
                >
                  <div
                    className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-full flex items-center justify-center text-2xl mx-auto mb-4`}
                  >
                    {stat.icon}
                  </div>
                  <div className="font-bold text-white mb-2 text-3xl">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-400">{stat.title}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gradient-to-r border-t from-orange-600/20 via-orange-500/20 to-orange-400/20 border-orange-500/20 py-20 px-4 z-10 relative sm:px-6 lg:px-8">
          <div className="mx-auto text-center max-w-4xl">
            <h2 className="font-bold mb-6 text-3xl">
              <span className="bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 text-transparent">
                Start Your Assessment Journey
              </span>
            </h2>
            <p className="mx-auto text-xl mb-8 max-w-2xl text-gray-300">
              Discover your developer strengths and growth opportunities
            </p>
            <Link
              href="/#template"
              className="bg-gradient-to-r rounded-xl font-medium from-orange-500 to-orange-600 text-white text-lg py-4 px-8 transition-all duration-300 group relative inline-flex items-center justify-center hover:from-orange-400 hover:to-orange-500 hover:shadow-lg hover:shadow-orange-500/25 hover:scale-105"
            >
              <span className="z-10 relative">Start Assessment</span>
              <div className="bg-gradient-to-r rounded-xl from-orange-400 to-orange-500 opacity-0 inset-0 transition-opacity duration-300 absolute group-hover:opacity-100"></div>
            </Link>
          </div>
        </section>
      </main>

      <div className="z-50 relative">
        <Footer />
      </div>
    </div>
  );
}
