import { Suspense } from "react";
import { Navbar } from "@/components/auth/navbar";
import LeaderboardList from "@/components/leaderboard/LeaderboardList";
import { LeaderboardSkeleton } from "@/components/leaderboard/LeaderboardSkeleton";

export const metadata = {
  title: "Leaderboard | GitHub Card",
  description:
    "View the GitHub user contribution leaderboard, showcasing the most active developers",
};


export default function LeaderboardPage(props: any) {
  return (
    <div className="min-h-screen bg-[#0d1117] text-white">
      <Navbar showLinks={true} />
      <main className="container mx-auto py-8 px-4">
        <div className="flex flex-col items-center justify-center">
          <div className="w-full max-w-4xl">
            <div className="flex mb-6 justify-between items-center">
              <h1 className="font-bold text-3xl">Contribution Leaderboard</h1>
            </div>
            <div className="border rounded-lg bg-[#161b22] border-[#30363d] shadow-lg overflow-hidden">
              <Suspense fallback={<LeaderboardSkeleton />}>
                <LeaderboardList /* @next-codemod-error 'props' is used with spread syntax (...). Any asynchronous properties of 'props' must be awaited when accessed. */
                  {...props} />
              </Suspense>
            </div>
            <p className="mt-4 text-sm text-center text-[#8b949e]">
              The data is updated every hour, showing the top 20 users with the
              highest contribute scores
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
