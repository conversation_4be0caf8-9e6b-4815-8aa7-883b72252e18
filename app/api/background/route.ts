import { NextResponse } from "next/server";
import { imageQueueManager } from "@/lib/image-queue-manager";
import { ImageSourceManager } from "@/lib/image-source-manager";
import { PexelsFetcher } from "@/lib/image-fetchers/pexels-fetcher";
import { DirectFetcher } from "@/lib/image-fetchers/direct-fetcher";
import { PixabayFetcher } from "@/lib/image-fetchers/pixabay-fetcher";
import { UnsplashFetcher } from "@/lib/image-fetchers/unsplash-fetcher";

// Phase 4.2: 简化的性能监控
interface SimpleMetrics {
  r2Redirects: number;
  queueHits: number;
  newImages: number;
  errors: number;
  totalRequests: number;
}

let metrics: SimpleMetrics = {
  r2Redirects: 0,
  queueHits: 0,
  newImages: 0,
  errors: 0,
  totalRequests: 0,
};

function updateMetrics(type: keyof SimpleMetrics) {
  metrics[type]++;
  metrics.totalRequests++;

  // 每50次请求输出一次统计
  if (metrics.totalRequests % 50 === 0) {
    console.log("R2 Queue Metrics:", metrics);
  }
}

/**
 * 创建302重定向响应 - 在Location URL中传递Image-Id
 * 开发环境下使用本地代理，生产环境使用R2直接重定向
 */
function createRedirectResponse(r2Url: string, imageId: string): Response {
  // 检查是否为开发环境
  const isDevelopment =
    process.env.NEXTJS_ENV === "development" ||
    process.env.NODE_ENV === "development";

  let redirectUrl: string;

  if (isDevelopment) {
    // 开发环境：使用本地代理路径
    const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
    redirectUrl = `${baseUrl}/api/image-proxy?imageId=${imageId}&r2Url=${encodeURIComponent(
      r2Url
    )}`;
  } else {
    // 生产环境：直接重定向到R2
    const url = new URL(r2Url);
    url.searchParams.set("imageId", imageId);
    redirectUrl = url.toString();
  }

  return new Response(null, {
    status: 302,
    headers: {
      Location: redirectUrl,
      "Image-Id": imageId, // 保留header以备兼容
      "Cache-Control": "public, s-maxage=3600, stale-while-revalidate=1800",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
      "Access-Control-Expose-Headers": "Image-Id",
    },
  });
}

/**
 * 获取新图片并添加到队列
 */
async function fetchAndAddNewImage(): Promise<{
  success: boolean;
  imageId?: string;
  r2Url?: string;
  error?: string;
}> {
  try {
    // 1. 尝试从配置的图片源获取
    const newImage = await fetchFromImageSources();
    if (newImage) {
      const result = await imageQueueManager.addToQueue(
        newImage.imageUrl,
        newImage.contentType,
        newImage.source
      );

      if (result.success) {
        console.log(`Successfully added new image: ${result.imageId}`);
        return {
          success: true,
          imageId: result.imageId,
          r2Url: result.r2Url,
        };
      }
    }

    return {
      success: false,
      error: "All image sources failed",
    };
  } catch (error) {
    console.error("Error fetching new image:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 从配置的图片源获取图片
 */
async function fetchFromImageSources(): Promise<{
  imageUrl: string;
  contentType: string;
  source: any;
} | null> {
  const sourceManager = new ImageSourceManager();

  if (sourceManager.getEnabledSourceCount() === 0) {
    return null;
  }

  const source = sourceManager.selectRandomSource();
  console.log(`Fetching from source: ${source.name} (${source.type})`);

  try {
    let result = null;

    switch (source.type) {
      case "pexels":
        const pexelsFetcher = new PexelsFetcher();
        const pexelsResult = await pexelsFetcher.fetchImage(source);
        if (pexelsResult) {
          result = {
            imageUrl: pexelsResult.imageUrl,
            contentType: pexelsResult.contentType,
            source: "pexels",
          };
        }
        break;

      case "pixabay":
        const pixabayFetcher = new PixabayFetcher();
        const pixabayResult = await pixabayFetcher.fetchImage(source);
        if (pixabayResult) {
          result = {
            imageUrl: pixabayResult.imageUrl,
            contentType: pixabayResult.contentType,
            source: "pixabay",
          };
        }
        break;

      case "direct":
        const directFetcher = new DirectFetcher();
        const directResult = await directFetcher.fetchImage(source);
        if (directResult) {
          result = {
            imageUrl: directResult.imageUrl,
            contentType: directResult.contentType,
            source: "direct",
          };
        }
        break;

      case "unsplash":
        const unsplashFetcher = new UnsplashFetcher();
        const unsplashResult = await unsplashFetcher.fetchImage(source);
        if (unsplashResult) {
          result = {
            imageUrl: unsplashResult.imageUrl,
            contentType: unsplashResult.contentType,
            source: "unsplash",
          };
        }
        break;
    }

    if (result) {
      console.log(`Successfully fetched from ${source.name}`);
      return result;
    }
  } catch (error) {
    console.error(`Error fetching from ${source.name}:`, error);
  }

  return null;
}

/**
 * 使用备用图片初始化队列 - KV清空后的恢复机制
 */
async function initializeQueueWithBackupImages(): Promise<{
  success: boolean;
  imageId?: string;
  r2Url?: string;
  error?: string;
}> {
  console.log("Initializing queue with backup images...");

  try {
    const sourceImage = await fetchFromImageSources();
    if (sourceImage) {
      const result = await imageQueueManager.addToQueue(
        sourceImage.imageUrl,
        sourceImage.contentType,
        sourceImage.source
      );

      if (result.success) {
        console.log(
          `Successfully initialized queue with source image: ${result.imageId}`
        );
        return {
          success: true,
          imageId: result.imageId,
          r2Url: result.r2Url,
        };
      } else {
        console.warn(`Source image failed: ${result.error}`);
      }
    }

    return {
      success: false,
      error: "All initialization methods failed",
    };
  } catch (error) {
    console.error("Error during queue initialization:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 主GET处理函数 - 简化的零CPU架构
 */
export async function GET(request: Request) {
  const url = new URL(request.url);
  const backgroundId = url.searchParams.get("backgroundId");

  try {
    // 1. 直接查询模式 - 根据ID查询特定图片
    if (backgroundId) {
      const queuedImage = await imageQueueManager.queryImage(backgroundId);

      if (queuedImage) {
        console.log(`Direct query hit: ${backgroundId}`);
        updateMetrics("queueHits");
        return createRedirectResponse(queuedImage.r2Url, queuedImage.id);
      }

      // 图片不存在或已过期
      updateMetrics("errors");
      return NextResponse.json(
        { success: false, error: `Image ${backgroundId} not found or expired` },
        { status: 404 }
      );
    }

    // 2. 队列模式 - 获取随机图片
    const isQueueFull = await imageQueueManager.isQueueFull();

    if (!isQueueFull) {
      // 队列未满，添加新图片
      console.log("Queue not full, adding new image");
      const newImageResult = await fetchAndAddNewImage();

      if (
        newImageResult.success &&
        newImageResult.imageId &&
        newImageResult.r2Url
      ) {
        console.log(`New image added: ${newImageResult.imageId}`);
        updateMetrics("newImages");
        return createRedirectResponse(
          newImageResult.r2Url,
          newImageResult.imageId
        );
      }

      // 新图片获取失败，从现有队列中选择
      console.warn("Failed to add new image, falling back to existing queue");
    }

    // 从队列中随机选择图片
    const selectedImage = await imageQueueManager.selectRandomImage();

    if (selectedImage) {
      console.log(`Queue selection: ${selectedImage.id}`);
      updateMetrics("queueHits");
      return createRedirectResponse(selectedImage.r2Url, selectedImage.id);
    }

    // 队列为空且无法获取新图片 - 尝试初始化队列
    console.warn("Queue is empty, attempting to initialize with backup images");
    const initResult = await initializeQueueWithBackupImages();

    if (initResult.success && initResult.imageId && initResult.r2Url) {
      console.log(`Queue initialized with backup image: ${initResult.imageId}`);
      updateMetrics("newImages");
      return createRedirectResponse(initResult.r2Url, initResult.imageId);
    }

    // 完全失败 - 返回错误
    updateMetrics("errors");
    return NextResponse.json(
      {
        success: false,
        error: "No images available in queue and failed to initialize",
        details: initResult.error,
      },
      { status: 503 }
    );
  } catch (error) {
    console.error("Error in background endpoint:", error);
    updateMetrics("errors");
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST处理函数 - 管理和监控功能
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, refreshToken } = body as {
      action: string;
      refreshToken: string;
    };

    // 健康检查
    if (action === "health_check") {
      const stats = await imageQueueManager.getStats();
      return NextResponse.json({
        success: true,
        metrics,
        queueStats: stats,
        timestamp: Date.now(),
      });
    }

    // 手动清理
    if (action === "cleanup") {
      const expectedToken = process.env.REFRESH_CACHE_TOKEN;
      if (expectedToken && refreshToken !== expectedToken) {
        return NextResponse.json(
          { success: false, error: "Unauthorized" },
          { status: 401 }
        );
      }

      const cleanupResult = await imageQueueManager.cleanup();
      return NextResponse.json({
        success: true,
        message: "Cleanup completed",
        result: cleanupResult,
      });
    }

    // 队列状态
    if (action === "queue_status") {
      const queue = await imageQueueManager.getQueue();
      const stats = await imageQueueManager.getStats();

      return NextResponse.json({
        success: true,
        queue: {
          size: queue.images.length,
          lastUpdated: queue.lastUpdated,
          version: queue.version,
          totalServed: queue.totalServed,
        },
        stats,
        metrics,
      });
    }

    // 手动初始化队列
    if (action === "init_queue") {
      const expectedToken = process.env.REFRESH_CACHE_TOKEN;
      if (expectedToken && refreshToken !== expectedToken) {
        return NextResponse.json(
          { success: false, error: "Unauthorized" },
          { status: 401 }
        );
      }

      console.log("Manual queue initialization requested");
      const initResult = await initializeQueueWithBackupImages();

      if (initResult.success) {
        const stats = await imageQueueManager.getStats();
        return NextResponse.json({
          success: true,
          message: "Queue initialized successfully",
          imageId: initResult.imageId,
          queueStats: stats,
        });
      } else {
        return NextResponse.json(
          {
            success: false,
            error: "Failed to initialize queue",
            details: initResult.error,
          },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Unknown action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error in POST endpoint:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
