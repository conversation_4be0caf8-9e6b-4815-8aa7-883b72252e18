import { NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";
import { getDb } from "@/lib/db";
import { shareLinks, userSubscriptions } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { SHARE_LINK_EXPIRATION_DAYS } from "@/constants";
import { auth } from "@/auth";
import {
  incrementReferenceCount,
  decrementReferenceCount,
} from "@/utils/cache-utils";

export interface ShareLinkResponse {
  shareLink: string;
  expiresAt: number;
  exists: boolean;
  templateType: string;
}

export interface ErrorResponse {
  error: string;
  message?: string;
}

interface ShareLinkPostBody {
  templateType: string;
  forceUpdate?: boolean;
  backgroundId?: string | null;
}

interface ShareLinkDeleteBody {
  linkId: string;
}

// 根据环境选择运行时：开发环境使用 nodejs (支持 better-sqlite3)，生产环境使用 edge (Cloudflare D1)
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

// Get baseUrl
const baseUrl =
  process.env.NEXTAUTH_URL || "https://github-card.refined-x.workers.dev";

export const POST = async (
  req: NextRequest
): Promise<NextResponse<ShareLinkResponse | ErrorResponse>> => {
  const db = await getDb();
  const session = await auth();
  const userId = session?.user?.id;

  if (!userId || !session.user.username) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body: ShareLinkPostBody = await req.json();

    const now = Date.now(); // Current timestamp in milliseconds
    console.log(`Current timestamp: ${now} (${new Date(now).toISOString()})`);

    // 查询用户是否有活跃的 shareLink
    const existingLinks = await db
      .select()
      .from(shareLinks)
      .where(and(eq(shareLinks.userId, userId), eq(shareLinks.isActive, true)))
      .orderBy(shareLinks.createdAt);

    console.log(`Found ${existingLinks.length} existing links`);

    // 筛选非当前模板的为过期的 shareLink
    const activeOtherLinks = existingLinks.filter((link) => {
      const isActive =
        link.expiresAt > now && link.templateType !== body.templateType;
      console.log(
        `Link ${link.id} - expiresAt: ${link.expiresAt} (${new Date(
          link.expiresAt
        ).toISOString()}), isActive: ${isActive}`
      );
      return isActive;
    });

    // 对于非订阅用户，不允许创建多个 shareLink
    const isProUser = await db.query.userSubscriptions.findFirst({
      where: (sub, { eq, and }) =>
        and(eq(sub.userId, userId), eq(sub.status, "active")),
    });
    if (!isProUser && activeOtherLinks.length > 0) {
      return NextResponse.json(
        { error: "Free users can only have one active link" },
        { status: 400 }
      );
    }

    // 筛选当前模板的未过期的 shareLink
    const activeLink = existingLinks.find((link) => {
      const isActive =
        link.expiresAt > now && link.templateType === body.templateType;
      console.log(
        `Link ${link.id} - expiresAt: ${link.expiresAt} (${new Date(
          link.expiresAt
        ).toISOString()}), isActive: ${isActive}`
      );
      return isActive;
    });

    // If active link found, return it
    if (activeLink) {
      console.log(
        `Found active link: ${activeLink.id}, token: ${activeLink.linkToken}`
      );

      if (body.forceUpdate && body.backgroundId !== activeLink.backgroundId) {
        // 先减少旧 backgroundId 的引用
        if (activeLink.backgroundId) {
          await decrementReferenceCount(activeLink.backgroundId);
        }

        // 增加新 backgroundId 的引用
        if (body.backgroundId) {
          await incrementReferenceCount(body.backgroundId);
        }

        // 更新数据库
        await db
          .update(shareLinks)
          .set({
            backgroundId: body.backgroundId,
          })
          .where(eq(shareLinks.id, activeLink.id));

        console.log(`Updated link with backgroundId: ${body.backgroundId}`);
      }

      return NextResponse.json({
        shareLink: `${baseUrl}/shared/${activeLink.linkToken}`,
        expiresAt: activeLink.expiresAt,
        exists: true,
        templateType: activeLink.templateType,
      });
    }

    // Calculate expiration time
    const expiresAt = now + SHARE_LINK_EXPIRATION_DAYS * 24 * 60 * 60 * 1000;
    console.log(
      `Creating new share link, expires at: ${expiresAt} (${new Date(
        expiresAt
      ).toISOString()})`
    );

    // Generate new token
    const token = uuidv4();
    console.log(`Generated new token: ${token}`);

    const result = await db
      .insert(shareLinks)
      .values({
        userId,
        linkToken: token,
        expiresAt: expiresAt,
        templateType: body.templateType,
        isActive: true,
        backgroundId: body.backgroundId,
      })
      .returning();

    if (!result || result.length === 0) {
      // 如果数据库插入失败，需要回滚引用计数
      if (body.backgroundId) {
        try {
          await decrementReferenceCount(body.backgroundId);
        } catch (error) {
          console.error("Failed to rollback reference count:", error);
        }
      }

      throw new Error(
        "Failed to create share link: database returned empty result"
      );
    }

    return NextResponse.json({
      shareLink: `${baseUrl}/shared/${token}`,
      expiresAt,
      exists: false,
      templateType: body.templateType,
    });
  } catch (error: unknown) {
    console.error("Creating share link failed:", error);

    // Return appropriate error response
    const status =
      error instanceof Error && error.message === "User not found" ? 404 : 500;
    return NextResponse.json(
      {
        error: "Failed to create share link",
        message:
          error instanceof Error ? error.message : "An unknown error occurred",
      },
      { status }
    );
  }
};

export const GET = async (req: NextRequest) => {
  const db = await getDb();
  const session = await auth();
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    );
  }

  try {
    const templateType = req.nextUrl.searchParams.get("templateType");

    if (!templateType) {
      return NextResponse.json(
        { error: "Template type is required" },
        { status: 400 }
      );
    }

    // Get all share links for the user
    const userShareLinks = await db
      .select()
      .from(shareLinks)
      .where(
        and(
          eq(shareLinks.userId, userId),
          eq(shareLinks.templateType, templateType)
        )
      )
      .orderBy(shareLinks.createdAt);

    // Return the share links
    return NextResponse.json(
      userShareLinks.map((link) => ({
        id: link.id,
        linkToken: link.linkToken,
        createdAt: link.createdAt,
        expiresAt: link.expiresAt,
        isActive: link.isActive,
        templateType: link.templateType,
        shareLink: `${baseUrl}/shared/${link.linkToken}`,
      }))
    );
  } catch (error) {
    console.error("Error retrieving share links:", error);
    return NextResponse.json(
      { error: "Failed to retrieve share links", message: String(error) },
      { status: 500 }
    );
  }
};

/**
 * DELETE /api/share-links
 * 删除指定的分享链接
 */
export const DELETE = async (req: NextRequest) => {
  const db = await getDb();
  const session = await auth();
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    );
  }

  try {
    const { linkId }: ShareLinkDeleteBody = await req.json();

    if (!linkId) {
      return NextResponse.json(
        { error: "Link ID is required" },
        { status: 400 }
      );
    }

    // 查找要删除的分享链接
    const shareLink = await db
      .select()
      .from(shareLinks)
      .where(and(eq(shareLinks.id, linkId), eq(shareLinks.userId, userId)))
      .get();

    if (!shareLink) {
      return NextResponse.json(
        { error: "Share link not found" },
        { status: 404 }
      );
    }

    // 删除分享链接
    await db
      .delete(shareLinks)
      .where(and(eq(shareLinks.id, linkId), eq(shareLinks.userId, userId)));

    // 减少背景图片的引用计数
    if (shareLink.backgroundId) {
      try {
        await decrementReferenceCount(shareLink.backgroundId);
        console.log(
          `Successfully decreased reference count for backgroundId: ${shareLink.backgroundId}`
        );
      } catch (error) {
        console.error("Failed to decrement reference count:", error);
        // 不阻止删除操作，只记录错误
      }
    }

    return NextResponse.json({
      success: true,
      message: "Share link deleted successfully",
      data: {
        id: linkId,
        deletedAt: Date.now(),
      },
    });
  } catch (error) {
    console.error("Delete share link API error:", error);
    return NextResponse.json(
      {
        error: "Failed to delete share link",
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
};
