import { NextRequest, NextResponse } from "next/server";
import { userDataService } from "@/lib/services/user-data-service";

// 根据环境选择运行时：开发环境使用 nodejs (支持 better-sqlite3)，生产环境使用 edge (Cloudflare D1)
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

/**
 * Phase 3.3: 统一数据服务API - 使用UserDataService重构
 * 保持API契约不变，内部切换到统一服务层
 */
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ token: string }> }
) {
  const params = await props.params;

  try {
    const token = params.token;
    if (!token) {
      return NextResponse.json(
        { error: "Share link token is required" },
        { status: 400 }
      );
    }

    const startTime = Date.now();

    // Phase 3.3: 使用统一数据服务获取分享数据 (重构后)
    const response = await userDataService.getUserDataByShareToken(token);

    if (!response.success || !response.data) {
      throw new Error(response.error || "Failed to fetch share data");
    }

    const responseTime = Date.now() - startTime;

    // 新的统一API响应格式：使用统一的userData，cardData作为向后兼容别名
    const apiResponse = NextResponse.json({
      ...response.data,
    });

    // 设置缓存头（分享数据可以缓存更长时间）
    apiResponse.headers.set(
      "Cache-Control",
      "public, max-age=900, s-maxage=900"
    );

    console.log(
      `✅ Unified share data served for token ${token} in ${responseTime}ms`
    );

    return apiResponse;
  } catch (error: any) {
    console.error("Share link API error:", error);

    // 根据错误类型返回不同的状态码
    if (error.message.includes("not found")) {
      return NextResponse.json(
        { error: "Share link not found" },
        { status: 404 }
      );
    }

    if (
      error.message.includes("expired") ||
      error.message.includes("inactive")
    ) {
      return NextResponse.json(
        { error: "Share link has expired or is inactive" },
        { status: 410 }
      );
    }

    return NextResponse.json(
      {
        error: "Failed to retrieve share link",
        message: error.message,
      },
      { status: 500 }
    );
  }
}
