import { NextRequest, NextResponse } from "next/server";
import { getDb } from "@/lib/db";
import { ShareLink, shareLinks } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";
import { auth } from "@/auth";

export type LinkData = Omit<ShareLink, "userId" | "backgroundId"> & {
  shareLink: string;
};

export type SuccessResponse = {
  success: true;
  data: LinkData[];
  metadata: {
    userId: string;
    totalCount: number;
    generatedAt: number;
  };
};

export type ErrorResponse = {
  success: false;
  error: string;
  message?: string;
  timestamp?: number;
};

export type ApiResponse = SuccessResponse | ErrorResponse;

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
): Promise<NextResponse<ApiResponse>> {
  try {
    const db = await getDb();
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // 用户只能查看自己的分享链接
    if (session.user.id !== params.userId) {
      return NextResponse.json(
        {
          success: false,
          error: "You can only access your own share links",
        },
        { status: 403 }
      );
    }

    // 获取用户的所有分享链接
    const userShareLinks = await db
      .select()
      .from(shareLinks)
      .where(eq(shareLinks.userId, params.userId))
      .orderBy(desc(shareLinks.createdAt));

    // 构建响应数据，包含完整的分享链接URL
    const baseUrl =
      process.env.NEXTAUTH_URL || "https://github-card.refined-x.workers.dev";

    const responseData: SuccessResponse = {
      success: true,
      data: userShareLinks.map((link) => ({
        id: link.id,
        linkToken: link.linkToken,
        createdAt: link.createdAt,
        expiresAt: link.expiresAt,
        isActive: link.isActive,
        templateType: link.templateType,
        shareLink: `${baseUrl}/shared/${link.linkToken}`,
      })),
      metadata: {
        userId: params.userId,
        totalCount: userShareLinks.length,
        generatedAt: Date.now(),
      },
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error retrieving user share links:", error);
    const errorResponse: ErrorResponse = {
      success: false,
      error: "Failed to retrieve share links",
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: Date.now(),
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
