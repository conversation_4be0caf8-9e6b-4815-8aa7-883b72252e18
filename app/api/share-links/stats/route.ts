// V5.1 Dashboard Feature - Share Links Stats API
// Created: 2025-06-24
// Purpose: 提供Share Links相关的统计数据的独立API端点

import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getDb } from "@/lib/db";
import { shareLinks } from "@/lib/db/schema";
import { eq, and, count } from "drizzle-orm";

// 根据环境选择运行时
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

/**
 * Share Links 统计数据API
 * GET /api/share-links/stats
 *
 * 功能：提供用户分享链接的统计指标
 * 缓存：10分钟缓存策略
 * 权限：需要用户登录
 */
export async function GET() {
  try {
    // 1. 用户身份验证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await getDb();
    const userId = session.user.id;
    const now = Date.now();

    // 2. 获取用户的所有分享链接
    const userShareLinks = await db
      .select()
      .from(shareLinks)
      .where(eq(shareLinks.userId, userId))
      .all();

    // 3. 计算统计指标
    const stats = {
      // 基础统计
      totalLinks: userShareLinks.length,
      activeLinks: userShareLinks.filter(
        (link) => link.isActive && link.expiresAt > now
      ).length,
      expiredLinks: userShareLinks.filter(
        (link) => !link.isActive || link.expiresAt <= now
      ).length,

      // 模板使用统计
      mostUsedTemplate: getMostUsedTemplate(userShareLinks),
      templateStats: getTemplateStats(userShareLinks),

      // 时间相关统计
      oldestLink:
        userShareLinks.length > 0
          ? Math.min(...userShareLinks.map((l) => l.createdAt))
          : null,
      newestLink:
        userShareLinks.length > 0
          ? Math.max(...userShareLinks.map((l) => l.createdAt))
          : null,

      // 本月创建的链接数
      thisMonthCreated: userShareLinks.filter((link) => {
        const linkDate = new Date(link.createdAt);
        const now = new Date();
        return (
          linkDate.getMonth() === now.getMonth() &&
          linkDate.getFullYear() === now.getFullYear()
        );
      }).length,

      // 活跃链接的剩余天数分布
      expirationDistribution: getExpirationDistribution(userShareLinks, now),
    };

    // 4. 构建响应数据
    const responseData = {
      success: true,
      data: stats,
      metadata: {
        userId,
        generatedAt: now,
        cacheExpiry: now + 10 * 60 * 1000, // 10分钟缓存
      },
    };

    // 5. 设置缓存头
    const response = NextResponse.json(responseData);
    response.headers.set(
      "Cache-Control",
      "public, max-age=600, stale-while-revalidate=120"
    ); // 10分钟缓存

    return response;
  } catch (error) {
    console.error("Share links stats API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch share links stats",
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// 辅助函数

/**
 * 获取最常用的模板类型
 */
function getMostUsedTemplate(shareLinks: any[]): string | null {
  if (shareLinks.length === 0) return null;

  const templateCounts = shareLinks.reduce(
    (acc: Record<string, number>, link) => {
      acc[link.templateType] = (acc[link.templateType] || 0) + 1;
      return acc;
    },
    {}
  );

  return (
    Object.entries(templateCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || null
  );
}

/**
 * 获取各模板类型的使用统计
 */
function getTemplateStats(shareLinks: any[]): Record<string, number> {
  return shareLinks.reduce((acc: Record<string, number>, link) => {
    acc[link.templateType] = (acc[link.templateType] || 0) + 1;
    return acc;
  }, {});
}

/**
 * 获取链接过期时间分布
 */
function getExpirationDistribution(shareLinks: any[], now: number) {
  const activeLinks = shareLinks.filter(
    (link) => link.isActive && link.expiresAt > now
  );

  const distribution = {
    expiringSoon: 0, // 24小时内过期
    expiringThisWeek: 0, // 7天内过期
    expiringThisMonth: 0, // 30天内过期
    longTerm: 0, // 30天以上
  };

  const oneDayMs = 24 * 60 * 60 * 1000;
  const oneWeekMs = 7 * oneDayMs;
  const oneMonthMs = 30 * oneDayMs;

  activeLinks.forEach((link) => {
    const timeToExpiry = link.expiresAt - now;

    if (timeToExpiry <= oneDayMs) {
      distribution.expiringSoon++;
    } else if (timeToExpiry <= oneWeekMs) {
      distribution.expiringThisWeek++;
    } else if (timeToExpiry <= oneMonthMs) {
      distribution.expiringThisMonth++;
    } else {
      distribution.longTerm++;
    }
  });

  return distribution;
}
