import { NextRequest, NextResponse } from "next/server";

/**
 * 开发环境图片代理 API
 * 用于在开发环境中代理访问 R2 存储的图片，解决网络访问限制问题
 */

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const imageId = searchParams.get("imageId");
  const r2Url = searchParams.get("r2Url");

  // 验证参数
  if (!imageId || !r2Url) {
    return NextResponse.json(
      { error: "Missing required parameters: imageId and r2Url" },
      { status: 400 }
    );
  }

  try {
    // 解码 R2 URL
    const decodedR2Url = decodeURIComponent(r2Url);
    
    console.log(`[Dev Image Proxy] Fetching image: ${imageId} from ${decodedR2Url}`);

    // 从 R2 获取图片
    const response = await fetch(decodedR2Url, {
      headers: {
        'User-Agent': 'GitHub-Card-Dev-Proxy/1.0',
      },
    });

    if (!response.ok) {
      console.error(`[Dev Image Proxy] Failed to fetch image: ${response.status} ${response.statusText}`);
      
      // 如果 R2 图片不可用，返回一个占位图片
      return getPlaceholderImage(imageId);
    }

    // 获取图片数据
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get("content-type") || "image/jpeg";

    console.log(`[Dev Image Proxy] Successfully proxied image: ${imageId}, size: ${imageBuffer.byteLength} bytes`);

    // 返回图片数据
    return new Response(imageBuffer, {
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=3600, stale-while-revalidate=1800",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
        "Image-Id": imageId,
      },
    });

  } catch (error) {
    console.error(`[Dev Image Proxy] Error proxying image ${imageId}:`, error);
    
    // 返回占位图片
    return getPlaceholderImage(imageId);
  }
}

/**
 * 生成占位图片 - 当 R2 图片不可用时使用
 */
function getPlaceholderImage(imageId: string): Response {
  // 创建一个简单的 SVG 占位图片
  const svg = `
    <svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad1)" />
      <text x="50%" y="45%" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">
        GitHub Card
      </text>
      <text x="50%" y="55%" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="24">
        Development Placeholder
      </text>
      <text x="50%" y="65%" text-anchor="middle" fill="rgba(255,255,255,0.6)" font-family="Arial, sans-serif" font-size="16">
        Image ID: ${imageId}
      </text>
    </svg>
  `.trim();

  return new Response(svg, {
    status: 200,
    headers: {
      "Content-Type": "image/svg+xml",
      "Cache-Control": "public, max-age=300", // 5分钟缓存，便于开发调试
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
      "Image-Id": imageId,
    },
  });
}

/**
 * 处理 OPTIONS 请求 (CORS 预检)
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
