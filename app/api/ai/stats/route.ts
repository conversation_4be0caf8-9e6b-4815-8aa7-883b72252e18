import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getDb } from "@/lib/db";
import {
  aiDescriptions,
  descriptionHistory,
  type AIDescription,
  type DescriptionHistory,
} from "@/lib/db/schema";
import { eq, desc, count, type InferSelectModel, sql } from "drizzle-orm";

// 定义响应数据类型
interface StatsResponse {
  success: boolean;
  data?: {
    current: {
      hasDescription: boolean;
      generatedAt: Date;
      isCustomApplied: boolean;
      style: string | null;
      expiresAt: number | null;
      isExpired: boolean;
    };
    history: {
      generationCount: number;
      lastGeneratedAt: Date;
    };
  };
  error?: string;
}

type AIDescriptionWithOptionalId = Omit<InferSelectModel<typeof aiDescriptions>, 'id'> & {
  style?: string | null;
  expiresAt?: number | null;
  isCustomApplied?: boolean;
};

/**
 * 获取用户的 AI 描述统计信息
 * @route GET /api/ai-description/stats
 */
export async function GET(request: Request): Promise<NextResponse<StatsResponse>> {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" } as const, 
      { status: 401 }
    ) as NextResponse<StatsResponse>;
  }

  try {
    const db = await getDb();
    const userId = session.user.id;

    // 使用 Promise.all 并行执行两个数据库查询
    const [descriptionResult, historyCountResult] = await Promise.all([
      // 查询1: 获取用户最新的AI描述
      db
        .select()
        .from(aiDescriptions)
        .where(eq(aiDescriptions.userId, userId))
        .orderBy(desc(aiDescriptions.generatedAt))
        .limit(1),
      // 查询2: 获取生成历史总次数 - 使用原始 SQL 查询
      db.select().from(descriptionHistory)
        .where(sql`${descriptionHistory.userId} = ${userId}`)
        .then(res => res.length),
    ]);

    const descriptionData = descriptionResult[0] as AIDescriptionWithOptionalId | undefined;
    const generationCount = historyCountResult as number;

    // 如果没有描述数据
    if (!descriptionData) {
      const response: StatsResponse = {
        success: true,
        data: {
          current: {
            hasDescription: false,
            generatedAt: new Date(),
            isCustomApplied: false,
            style: null,
            expiresAt: null,
            isExpired: false
          },
          history: {
            generationCount,
            lastGeneratedAt: new Date()
          },
        },
      };
      return NextResponse.json(response);
    }

    // 检查是否过期
    const isExpired = descriptionData.expiresAt
      ? Date.now() > descriptionData.expiresAt
      : false;
      
    // 确保日期是 Date 对象
    const generatedAt = descriptionData.generatedAt 
      ? new Date(descriptionData.generatedAt)
      : new Date();

    // 组装最终响应数据
    const responseData: StatsResponse['data'] = {
      current: {
        hasDescription: true,
        generatedAt,
        isCustomApplied: descriptionData.isCustomApplied ?? false,
        style: descriptionData.style ?? null,
        expiresAt: descriptionData.expiresAt ?? null,
        isExpired,
      },
      history: {
        generationCount,
lastGeneratedAt: generatedAt,
      },
    };

    return NextResponse.json({ success: true, data: responseData } as StatsResponse);
  } catch (error) {
    console.error("Error fetching AI description stats:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal Server Error' 
      } as StatsResponse, 
      { status: 500 }
    );
  }
}
