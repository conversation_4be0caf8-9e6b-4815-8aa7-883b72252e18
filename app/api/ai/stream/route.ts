import { NextRequest } from "next/server";
import { auth } from "@/auth";
import { z } from "zod";
import { ModularAIGenerator } from "@/lib/ai/core/ModularAIGenerator";
import type { UserData } from "@/types/user-data";
import type { GenerationRequest, UserPreferences } from "@/lib/ai/types";

/**
 * SSE流式AI生成API
 * POST /api/ai/stream
 *
 * 功能：提供AI模块的流式输出，支持实时进度反馈
 * 格式：Server-Sent Events (SSE)
 *
 * 必需参数：
 * - sessionId: 会话ID
 * - githubData: GitHub基础用户数据
 * - githubExtendedData: GitHub扩展数据（必须是真实获取的数据，不可为空或虚假数据）
 *
 * 可选参数：
 * - options: 生成选项配置
 */

// 流式事件类型
interface StreamEvent {
  type:
    | "stage_start"
    | "stage_progress"
    | "stage_complete"
    | "content_chunk"
    | "complete"
    | "error";
  stage: string; // 🎯 支持任意自定义stage名称
  data: {
    content?: string;
    progress?: number;
    metadata?: any;
    error?: string;
  };
  timestamp: number;
  sessionId: string;
  // 🎯 新增：事件序列号，确保严格顺序（可选，在客户端分配）
  sequenceId?: number;
}

// 请求体验证
const streamRequestSchema = z.object({
  sessionId: z.string(),
  githubData: z.object({
    login: z.string(),
    name: z.string().optional(),
    username: z.string(),
    avatarUrl: z.string(),
    bio: z.string().optional(),
    blog: z.string().optional(),
    location: z.string().optional(),
    twitterUsername: z.string().optional(),
    publicRepos: z.number(),
    followers: z.number(),
    following: z.number(),
    createdAt: z.number(),
    totalStars: z.number(),
    contributionScore: z.number(),
    commits: z.number(),
    pullRequests: z.number(),
    issues: z.number(),
    reviews: z.number(),
    totalForks: z.number(),
    contributedRepos: z.number(),
  }),
  githubExtendedData: z.object({
    version: z.string(),
    fetchedAt: z.number(),
    username: z.string(),
    repositories: z.array(z.any()),
    commits: z.array(z.any()),
    readmes: z.array(z.any()),
    techStackFiles: z.array(z.any()),
    timeStats: z.array(z.any()),
    fetchConfig: z.object({
      maxRepositories: z.number(),
      maxCommitsPerRepo: z.number(),
      includeReadme: z.boolean(),
      includeTechStackFiles: z.boolean(),
      includeTimeStats: z.boolean(),
    }),
  }),
  options: z
    .object({
      optimizationEnabled: z.boolean().optional(),
      enableStreaming: z.boolean().optional(),
    })
    .optional(),
});

type StreamRequestBody = z.infer<typeof streamRequestSchema>;

/**
 * 创建SSE响应流
 */
function createSSEStream(
  generator: ModularAIGenerator,
  request: GenerationRequest,
  sessionId: string
): ReadableStream {
  return new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();

      // 发送SSE事件
      const sendEvent = (event: StreamEvent) => {
        const data = `data: ${JSON.stringify(event)}\n\n`;
        controller.enqueue(encoder.encode(data));
      };

      // 发送连接建立事件
      sendEvent({
        type: "stage_start",
        stage: "analyzer",
        data: { progress: 0, content: "Starting AI generation..." },
        timestamp: Date.now(),
        sessionId,
      });

      try {
        // 使用流式生成器
        const result = await generator.generateDescriptionWithStreaming(
          request,
          (event: StreamEvent) => {
            sendEvent(event);
          }
        );

        // 发送完成事件
        sendEvent({
          type: "complete",
          stage: "critic",
          data: {
            content: result.finalText,
            progress: 100,
            metadata: result.metadata,
          },
          timestamp: Date.now(),
          sessionId,
        });
      } catch (error) {
        // 发送错误事件
        sendEvent({
          type: "error",
          stage: "analyzer",
          data: {
            error: error instanceof Error ? error.message : "Unknown error",
            progress: 0,
          },
          timestamp: Date.now(),
          sessionId,
        });
      } finally {
        // 发送结束标记
        controller.enqueue(encoder.encode("data: [DONE]\n\n"));
        controller.close();
      }
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    // 认证检查
    const session = await auth();
    if (!session?.user?.id) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // 解析请求体
    const body = await request.json();
    const validatedData = streamRequestSchema.parse(body);

    // 构建生成请求
    const generationRequest: GenerationRequest = {
      githubData: validatedData.githubData as UserData,
      githubExtendedData: validatedData.githubExtendedData as any,
      options: {
        optimizationEnabled:
          validatedData.options?.optimizationEnabled !== false,
        ...validatedData.options,
      },
    };

    // 创建AI生成器
    const generator = new ModularAIGenerator();

    // 创建SSE流
    const stream = createSSEStream(
      generator,
      generationRequest,
      validatedData.sessionId
    );

    // 返回SSE响应
    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "X-Session-ID": validatedData.sessionId,
      },
    });
  } catch (error) {
    console.error("Stream API error:", error);

    if (error instanceof z.ZodError) {
      return new Response(
        JSON.stringify({
          error: "Invalid request format",
          details: error.errors,
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

// 健康检查端点
export async function GET() {
  return new Response(
    JSON.stringify({
      status: "healthy",
      timestamp: Date.now(),
    }),
    {
      headers: { "Content-Type": "application/json" },
    }
  );
}
