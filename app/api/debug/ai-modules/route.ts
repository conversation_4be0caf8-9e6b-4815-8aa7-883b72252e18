import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { z } from "zod";
import { AnalyzerModule } from "@/lib/ai/core/modules/AnalyzerModule";
import { StrategistModule } from "@/lib/ai/core/modules/StrategistModule";
import { WriterModule } from "@/lib/ai/core/modules/WriterModule";
import { CriticModule } from "@/lib/ai/core/modules/CriticModule";
import type { UserData } from "@/types/user-data";
import type {
  UserPreferences,
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
} from "@/lib/ai/types";
import {
  UnifiedDebugValidator,
  DebugErrorHandler,
  RateLimitValidator,
} from "@/lib/debug/validation";
import type { SupportedModuleType, DebugResponse } from "@/types/debug-api";
// 🎯 导入简单工具函数（替代复杂框架）
import { createOnProgress } from "@/lib/debug/streaming-utils";

// 支持的模块类型
const MODULE_TYPES = ["analyzer", "strategist", "writer", "critic"] as const;

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let moduleType: SupportedModuleType = "analyzer"; // 默认值，用于错误处理

  // 🎯 移除复杂框架初始化，使用简单工具函数

  try {
    // 身份验证
    const session = await auth();
    if (!session?.user?.id) {
      const authError = DebugErrorHandler.handleAuthError();
      return NextResponse.json(
        {
          success: false,
          processingTime: Date.now() - startTime,
          error: authError.message,
          metadata: {
            code: authError.code,
            requiresAuth: true,
          },
        } as DebugResponse,
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // 请求频率限制
    if (!RateLimitValidator.checkRateLimit(userId)) {
      return NextResponse.json(
        {
          success: false,
          processingTime: Date.now() - startTime,
          error: "请求频率过高，请稍后重试",
          metadata: {
            code: "RATE_LIMIT_EXCEEDED",
            remainingRequests: RateLimitValidator.getRemainingRequests(userId),
            resetTime: RateLimitValidator.getResetTime(userId),
          },
        } as DebugResponse,
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": "60",
            "X-RateLimit-Remaining":
              RateLimitValidator.getRemainingRequests(userId).toString(),
            "X-RateLimit-Reset":
              RateLimitValidator.getResetTime(userId).toString(),
          },
        }
      );
    }

    // 获取和验证模块类型
    const { searchParams } = new URL(request.url);
    const moduleTypeParam = searchParams.get("module");

    if (!moduleTypeParam) {
      return NextResponse.json(
        {
          success: false,
          processingTime: Date.now() - startTime,
          error: "缺少模块类型参数",
          metadata: {
            code: "INVALID_MODULE",
            supportedModules: MODULE_TYPES,
          },
        } as DebugResponse,
        { status: 400 }
      );
    }

    moduleType = UnifiedDebugValidator.validateModuleType(moduleTypeParam);

    // 解析和验证请求体
    const body = await request.json();
    const validatedData = UnifiedDebugValidator.validateUnifiedRequest(
      moduleType,
      body
    );

    // 🎯 架构简化：统一使用流式传输

    return handleStreamingDebug(moduleType, validatedData, userId);
  } catch (error) {
    console.error("[DEBUG_API_ERROR]", error);

    // 使用统一的错误处理
    if (error instanceof z.ZodError) {
      const validationError = DebugErrorHandler.handleZodError(error);
      return NextResponse.json(
        {
          success: false,
          processingTime: Date.now() - startTime,
          error: validationError.message,
          metadata: {
            code: validationError.code,
            validationErrors: validationError.details,
          },
        } as DebugResponse,
        { status: 400 }
      );
    }

    // 处理验证器抛出的错误
    if (error && typeof error === "object" && "code" in error) {
      const debugError = error as any;
      return NextResponse.json(
        {
          success: false,
          processingTime: Date.now() - startTime,
          error: debugError.message,
          metadata: {
            code: debugError.code,
            details: debugError.details,
          },
        } as DebugResponse,
        { status: 400 }
      );
    }

    // 处理模块执行错误
    if (error instanceof Error) {
      const moduleError = DebugErrorHandler.handleModuleError(
        error,
        moduleType
      );
      const statusCode =
        moduleError.code === "INTERNAL_SERVER_ERROR" ? 500 : 400;

      return NextResponse.json(
        {
          success: false,
          processingTime: Date.now() - startTime,
          error: moduleError.message,
          metadata: {
            code: moduleError.code,
            details: moduleError.details,
          },
        } as DebugResponse,
        { status: statusCode }
      );
    }

    // 处理未知错误
    const genericError = DebugErrorHandler.handleGenericError(error);
    return NextResponse.json(
      {
        success: false,
        processingTime: Date.now() - startTime,
        error: genericError.message,
        metadata: {
          code: genericError.code,
          details: genericError.details,
        },
      } as DebugResponse,
      { status: 500 }
    );
  }
}

// 🎯 架构简化：已移除273行重复的非流式函数
// 统一使用流式传输，提供更好的用户体验和一致的接口

// 支持流水线调试的GET端点
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get("action");

  if (action === "health") {
    return NextResponse.json({
      status: "healthy",
      modules: MODULE_TYPES,
      timestamp: Date.now(),
    });
  }

  if (action === "modules") {
    return NextResponse.json({
      supported_modules: MODULE_TYPES,
      description: "AI modules available for debugging",
    });
  }

  return NextResponse.json(
    { error: "Invalid action. Use ?action=health or ?action=modules" },
    { status: 400 }
  );
}

// 🎯 重构后的流式调试处理函数 - 使用统一框架
async function handleStreamingDebug(
  moduleType: SupportedModuleType,
  data: any,
  _userId: string
): Promise<Response> {
  console.log(`🌊 [Unified Debug] 开始统一流式调试 ${moduleType} 模块`);

  // 创建SSE响应
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // 发送开始事件
        const startEvent = {
          type: "debug_start",
          module: moduleType,
          timestamp: Date.now(),
          sessionId: data.sessionId,
          framework: "SimpleStreamingUtils v1.0",
        };
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(startEvent)}\n\n`)
        );

        // 🎯 使用简单函数执行模块调试
        let result: any;

        switch (moduleType) {
          case "analyzer":
            result = await debugAnalyzerModuleWithStreaming(
              data,
              controller,
              encoder
            );
            break;
          case "strategist":
            result = await debugStrategistModuleWithStreaming(
              data,
              controller,
              encoder
            );
            break;
          case "writer":
            result = await debugWriterModuleWithStreaming(
              data,
              controller,
              encoder
            );
            break;
          case "critic":
            result = await debugCriticModuleWithStreaming(
              data,
              controller,
              encoder
            );
            break;
          default:
            throw new Error(
              `Streaming not supported for module: ${moduleType}`
            );
        }

        // 发送完成事件
        const completeEvent = {
          type: "debug_complete",
          module: moduleType,
          result,
          timestamp: Date.now(),
          sessionId: data.sessionId,
          framework: "SimpleStreamingUtils v1.0",
        };
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(completeEvent)}\n\n`)
        );

        controller.close();
      } catch (error) {
        console.error(
          `💥 [Unified Debug] ${moduleType} 模块流式调试失败:`,
          error
        );

        // 发送错误事件
        const errorEvent = {
          type: "debug_error",
          module: moduleType,
          error: error instanceof Error ? error.message : "Unknown error",
          timestamp: Date.now(),
          sessionId: data.sessionId,
          framework: "SimpleStreamingUtils v1.0",
        };
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(errorEvent)}\n\n`)
        );

        controller.close();
      }
    },
  });

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}

// 🎯 简化的模块调试函数：使用工具函数消除重复逻辑

// Analyzer模块流式调试实现 - 统一格式V2
async function debugAnalyzerModuleWithStreaming(
  data: any,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
): Promise<AnalyzerOutput> {
  const analyzerModule = new AnalyzerModule();

  // 设置调试会话ID
  analyzerModule.setDebugSessionId(data.sessionId);

  // 验证必需的数据 - 使用新的统一格式
  if (!data.context?.githubData) {
    throw new Error("缺少GitHub用户数据 (context.githubData)");
  }
  if (!data.context?.extendedData) {
    throw new Error("缺少GitHub扩展数据 (context.extendedData)");
  }

  // 🎯 使用工具函数创建onProgress，消除重复逻辑
  const onProgress = createOnProgress(
    "analyzer",
    data.sessionId,
    controller,
    encoder
  );

  console.log("🎯 [Debug] Analyzer模块流式调试启动 - 统一格式V2");

  // 构建AnalyzerInput对象 - 适配模块期望的数据结构
  const analyzerInput = {
    userData: data.context.githubData,
    extendedData: data.context.extendedData,
    config: data.config,
  };

  const result = await analyzerModule.analyzeWithStreaming(
    analyzerInput,
    data.config,
    onProgress
  );

  console.log("✅ [Debug] Analyzer模块流式调试完成");
  return result;
}

// Strategist模块流式调试实现 - 统一格式V2
async function debugStrategistModuleWithStreaming(
  data: any,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
): Promise<StrategistOutput> {
  const strategistModule = new StrategistModule();

  // 设置调试会话ID
  strategistModule.setDebugSessionId(data.sessionId);

  if (!data.input) {
    throw new Error("Strategist module requires analyzer output as input data");
  }

  // 🎯 使用工具函数创建onProgress，消除重复逻辑
  const onProgress = createOnProgress(
    "strategist",
    data.sessionId,
    controller,
    encoder
  );

  console.log("🎯 [Debug] Strategist模块流式调试启动 - 统一格式V2");

  // 使用新的简化API：基于语义标签生成创意简报
  // 统一格式中input直接是analyzerOutput
  const result = await strategistModule.generateCreativeBrief(
    data.input,
    onProgress
  );

  console.log("✅ [Debug] Strategist模块流式调试完成");
  return result;
}

// Writer模块流式调试实现 - 统一格式V2
async function debugWriterModuleWithStreaming(
  data: any,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
): Promise<WriterOutput> {
  const writerModule = new WriterModule();

  if (!data.input?.analyzerOutput || !data.input?.strategistOutput) {
    throw new Error(
      "Writer module requires both analyzer and strategist outputs"
    );
  }

  // 🎯 使用工具函数创建onProgress，带额外metadata
  const onProgress = createOnProgress(
    "writer",
    data.sessionId,
    controller,
    encoder,
    (chunk, stage) => ({
      isTextGeneration: stage.includes("text_generation"),
    })
  );

  console.log("✍️ [Debug] Writer模块流式调试启动 - 统一格式V2");

  const userPreferences = data.config || {
    perspective: "first_person" as const,
    targetEmotion: "witty" as const,
  };

  const result = await writerModule.generateTextWithStreaming(
    data.context.githubData as UserData,
    data.input.analyzerOutput,
    data.input.strategistOutput,
    userPreferences,
    onProgress
  );

  console.log("✅ [Debug] Writer模块流式调试完成");
  return result;
}

// Critic模块流式调试实现 - 统一格式V2
async function debugCriticModuleWithStreaming(
  data: any,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
): Promise<CriticOutput> {
  const criticModule = new CriticModule();

  if (!data.input?.writerOutput || !data.input?.strategistOutput) {
    throw new Error(
      "Critic module requires both writer and strategist outputs"
    );
  }

  // 🎯 使用工具函数创建onProgress，带额外metadata
  const onProgress = createOnProgress(
    "critic",
    data.sessionId,
    controller,
    encoder,
    (chunk, stage) => ({
      isEvaluation: stage.includes("evaluation") || stage.includes("analysis"),
    })
  );

  console.log("🔎 [Debug] Critic模块流式调试启动 - 统一格式V2");

  const generatedText =
    data.input.writerOutput.content?.generatedText ||
    data.input.writerOutput.generated_text;
  const strategistOutput = data.input.strategistOutput;

  const result = await criticModule.evaluateTextWithStreaming(
    generatedText,
    strategistOutput,
    data.context.githubData as UserData,
    onProgress
  );

  console.log("✅ [Debug] Critic模块流式调试完成");
  return result;
}
