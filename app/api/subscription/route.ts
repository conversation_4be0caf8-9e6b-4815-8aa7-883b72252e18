import { NextResponse } from "next/server";
import { getDb } from "@/lib/db";
import { userSubscriptions, subscriptionPlans } from "@/lib/db/schema";
import { auth } from "@/auth";
import { eq, and } from "drizzle-orm";

// 根据环境选择运行时：开发环境使用 nodejs (支持 better-sqlite3)，生产环境使用 edge (Cloudflare D1)
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const db = await getDb();

    // 获取用户的当前订阅，包含计划信息
    const result = await db
      .select()
      .from(userSubscriptions)
      .leftJoin(
        subscriptionPlans,
        eq(userSubscriptions.planId, subscriptionPlans.id)
      )
      .where(
        and(
          eq(userSubscriptions.userId, session.user.id),
          eq(userSubscriptions.status, "active")
        )
      )
      .get();

    if (!result) {
      return NextResponse.json(null);
    }

    // 构建响应对象
    const subscription = {
      id: result.user_subscriptions.id,
      userId: result.user_subscriptions.userId,
      planId: result.user_subscriptions.planId,
      status: result.user_subscriptions.status,
      priceId: result.user_subscriptions.priceId,
      currentPeriodStart: result.user_subscriptions.currentPeriodStart,
      currentPeriodEnd: result.user_subscriptions.currentPeriodEnd,
      cancelAt: result.user_subscriptions.cancelAt,
      cancelAtPeriodEnd: result.user_subscriptions.cancelAtPeriodEnd,
      createdAt: result.user_subscriptions.createdAt,
      updatedAt: result.user_subscriptions.updatedAt,
      plan: result.subscription_plans
        ? {
            id: result.subscription_plans.id,
            name: result.subscription_plans.name,
            description: result.subscription_plans.description,
            price: result.subscription_plans.price,
            currency: result.subscription_plans.currency,
            interval: result.subscription_plans.interval,
            features: result.subscription_plans.features,
          }
        : null,
    };

    // 如果有订阅数据，解析 features JSON 字符串
    if (subscription.plan?.features) {
      try {
        const parsedFeatures = JSON.parse(subscription.plan.features as string);
        (subscription.plan as any).features = parsedFeatures;
      } catch (error) {
        console.error("Error parsing features JSON:", error);
        (subscription.plan as any).features = [];
      }
    }

    return NextResponse.json(subscription);
  } catch (error) {
    console.error("Error fetching user subscription:", error);
    return NextResponse.json(
      { error: "Failed to fetch user subscription" },
      { status: 500 }
    );
  }
}
