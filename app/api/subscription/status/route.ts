// V5.1 Dashboard Feature - Subscription Status API
// Created: 2025-06-24
// Purpose: 提供用户订阅状态信息的独立API端点

import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getDb } from "@/lib/db";
import { userSubscriptions } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

// 根据环境选择运行时
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

/**
 * Subscription Status API
 * GET /api/subscription/status
 *
 * 功能：提供用户订阅状态信息
 * 缓存：5分钟缓存策略
 * 权限：需要用户登录
 */
export async function GET() {
  try {
    // 1. 用户身份验证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await getDb();
    const userId = session.user.id;
    const now = Date.now();

    // 2. 获取用户的活跃订阅
    const activeSubscription = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.userId, userId),
          eq(userSubscriptions.status, "active")
        )
      )
      .get();

    // 3. 获取用户的所有订阅历史（用于统计）
    const allSubscriptions = await db
      .select()
      .from(userSubscriptions)
      .where(eq(userSubscriptions.userId, userId))
      .all();

    // 4. 计算订阅状态
    const subscriptionStatus = {
      // 当前状态
      isPro: !!activeSubscription,
      status: activeSubscription?.status || "free",
      planId: activeSubscription?.planId || null,

      // 时间相关
      currentPeriodStart: activeSubscription?.currentPeriodStart || null,
      currentPeriodEnd: activeSubscription?.currentPeriodEnd || null,
      createdAt: activeSubscription?.createdAt || null,
      updatedAt: activeSubscription?.updatedAt || null,

      // 剩余时间计算
      daysRemaining: activeSubscription
        ? Math.max(
            0,
            Math.floor(
              (activeSubscription.currentPeriodEnd - now) /
                (1000 * 60 * 60 * 24)
            )
          )
        : null,

      hoursRemaining: activeSubscription
        ? Math.max(
            0,
            Math.floor(
              (activeSubscription.currentPeriodEnd - now) / (1000 * 60 * 60)
            )
          )
        : null,

      // 订阅时长统计
      subscribedDays: activeSubscription
        ? Math.floor(
            (now - activeSubscription.createdAt) / (1000 * 60 * 60 * 24)
          )
        : null,

      // 历史统计
      totalSubscriptions: allSubscriptions.length,
      previousSubscriptions: allSubscriptions.filter(
        (sub) => sub.status !== "active"
      ).length,

      // 续费相关
      willRenew: activeSubscription?.cancelAtPeriodEnd === false,
      cancelAtPeriodEnd: activeSubscription?.cancelAtPeriodEnd || false,

      // TODO:试用状态 (暂未实现，预留字段)
      trialStart: null,
      trialEnd: null,
      isTrialing: false,

      // 订阅类型分析
      subscriptionHistory: allSubscriptions.map((sub) => ({
        id: sub.id,
        planId: sub.planId,
        status: sub.status,
        createdAt: sub.createdAt,
        currentPeriodStart: sub.currentPeriodStart,
        currentPeriodEnd: sub.currentPeriodEnd,
        cancelAtPeriodEnd: sub.cancelAtPeriodEnd,
      })),

      // 权益状态
      features: {
        unlimitedShareLinks: !!activeSubscription,
        customBackgrounds: !!activeSubscription,
        advancedAnalytics: !!activeSubscription,
        prioritySupport: !!activeSubscription,
        noExpiration: !!activeSubscription,
      },
    };

    // 5. 构建响应数据
    const responseData = {
      success: true,
      data: subscriptionStatus,
      metadata: {
        userId,
        generatedAt: now,
        cacheExpiry: now + 5 * 60 * 1000, // 5分钟缓存
      },
    };

    // 6. 设置缓存头
    const response = NextResponse.json(responseData);
    response.headers.set(
      "Cache-Control",
      "public, max-age=300, stale-while-revalidate=60"
    ); // 5分钟缓存

    return response;
  } catch (error) {
    console.error("Subscription status API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch subscription status",
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}
