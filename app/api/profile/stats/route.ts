// V5.1 Dashboard Feature - Profile Stats API
// Created: 2025-06-24
// Purpose: 提供用户资料相关的统计数据的独立API端点

import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getDb } from "@/lib/db";
import { users, contributeDatas, userBehaviors } from "@/lib/db/schema";
import { eq, desc, and } from "drizzle-orm";

// 根据环境选择运行时
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

/**
 * Profile Stats API
 * GET /api/profile/stats
 *
 * 功能：提供用户资料相关的统计数据
 * 缓存：10分钟缓存策略
 * 权限：需要用户登录
 */
export async function GET() {
  try {
    // 1. 用户身份验证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await getDb();
    const userId = session.user.id;
    const now = Date.now();

    // 2. 并行获取用户数据
    const [userProfile, githubData, lastSignInBehavior] = await Promise.all([
      // 用户基础信息
      db.select().from(users).where(eq(users.id, userId)).get(),

      // GitHub数据
      db
        .select()
        .from(contributeDatas)
        .where(eq(contributeDatas.userId, userId))
        .get(),

      // 最近一次登录行为
      db
        .select()
        .from(userBehaviors)
        .where(
          and(
            eq(userBehaviors.userId, userId),
            eq(userBehaviors.actionType, "sign_in")
          )
        )
        .orderBy(desc(userBehaviors.performedAt))
        .limit(1)
        .get(),
    ]);

    // 3. 计算统计指标
    const stats = {
      // 用户基础统计
      profile: {
        accountAge: userProfile?.createdAt
          ? Math.floor(
              (now - userProfile.createdAt * 1000) / (1000 * 60 * 60 * 24)
            )
          : 0,
        isVerified: !!userProfile?.email,
        hasGitHubData: !!githubData,
        joinedDate: userProfile?.createdAt
          ? new Date(userProfile.createdAt * 1000).toISOString()
          : null,
        lastSignIn: lastSignInBehavior?.performedAt
          ? new Date(lastSignInBehavior.performedAt).toISOString()
          : null,
        daysSinceLastSignIn: lastSignInBehavior?.performedAt
          ? Math.floor(
              (now - lastSignInBehavior.performedAt) / (1000 * 60 * 60 * 24)
            )
          : null,
      },

      // GitHub数据统计
      github: githubData
        ? {
            totalStars: githubData.totalStars,
            totalForks: githubData.totalForks,
            totalCommits: githubData.commits,
            totalRepos: githubData.publicRepos,
            followers: githubData.followers,
            following: githubData.following,
            contributionScore: githubData.contributionScore,
            lastUpdated: githubData.lastUpdated,
            dataFreshness: getDaysSince(githubData.lastUpdated),
            accountAge: getDaysSince(githubData.userCreatedAt),

            // GitHub 活跃度分析
            activity: {
              commitsPerDay:
                githubData.commits > 0 && githubData.userCreatedAt
                  ? Math.round(
                      (githubData.commits /
                        Math.max(1, getDaysSince(githubData.userCreatedAt))) *
                        100
                    ) / 100
                  : 0,
              reposPerYear:
                githubData.publicRepos > 0 && githubData.userCreatedAt
                  ? Math.round(
                      (githubData.publicRepos /
                        Math.max(
                          1,
                          getDaysSince(githubData.userCreatedAt) / 365
                        )) *
                        100
                    ) / 100
                  : 0,
              followersToFollowingRatio:
                githubData.following > 0
                  ? Math.round(
                      (githubData.followers / githubData.following) * 100
                    ) / 100
                  : githubData.followers,
            },

            // 贡献质量指标
            quality: {
              starsPerRepo:
                githubData.publicRepos > 0
                  ? Math.round(
                      (githubData.totalStars / githubData.publicRepos) * 100
                    ) / 100
                  : 0,
              forksPerRepo:
                githubData.publicRepos > 0
                  ? Math.round(
                      (githubData.totalForks / githubData.publicRepos) * 100
                    ) / 100
                  : 0,
              commitsPerRepo:
                githubData.publicRepos > 0
                  ? Math.round(
                      (githubData.commits / githubData.publicRepos) * 100
                    ) / 100
                  : 0,
            },
          }
        : null,

      // 平台使用统计
      platform: {
        membershipDuration: userProfile?.createdAt
          ? getDaysSince(userProfile.createdAt * 1000)
          : 0,
        profileCompleteness: calculateProfileCompleteness({
          hasEmail: !!userProfile?.email,
          hasGitHubData: !!githubData,
          hasName: !!userProfile?.name,
          hasImage: !!userProfile?.image,
        }),
        dataQuality: githubData ? calculateDataQuality(githubData) : 0,
      },

      // 综合评分
      overall: {
        completionScore: calculateCompletionScore({
          hasGitHubData: !!githubData,
          hasEmail: !!userProfile?.email,
          hasRecentActivity: githubData
            ? getDaysSince(githubData.lastUpdated) < 7
            : false,
        }),
        engagementLevel: calculateEngagementLevel({
          githubData,
          accountAge: userProfile?.createdAt
            ? getDaysSince(userProfile.createdAt * 1000)
            : 0,
        }),
      },
    };

    // 4. 构建响应数据
    const responseData = {
      success: true,
      data: stats,
      metadata: {
        userId,
        generatedAt: now,
        cacheExpiry: now + 10 * 60 * 1000, // 10分钟缓存
      },
    };

    // 5. 设置缓存头
    const response = NextResponse.json(responseData);
    response.headers.set(
      "Cache-Control",
      "public, max-age=600, stale-while-revalidate=120"
    ); // 10分钟缓存

    return response;
  } catch (error) {
    console.error("Profile stats API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch profile stats",
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// 辅助函数

function getDaysSince(timestamp: number): number {
  return Math.floor((Date.now() - timestamp) / (1000 * 60 * 60 * 24));
}

function calculateProfileCompleteness(factors: {
  hasEmail: boolean;
  hasGitHubData: boolean;
  hasName: boolean;
  hasImage: boolean;
}): number {
  let score = 0;
  if (factors.hasEmail) score += 25;
  if (factors.hasGitHubData) score += 40;
  if (factors.hasName) score += 20;
  if (factors.hasImage) score += 15;
  return score;
}

function calculateDataQuality(githubData: any): number {
  let score = 0;

  // 数据新鲜度 (40%)
  const daysSinceUpdate = getDaysSince(githubData.lastUpdated);
  if (daysSinceUpdate <= 1) score += 40;
  else if (daysSinceUpdate <= 7) score += 30;
  else if (daysSinceUpdate <= 30) score += 20;
  else score += 10;

  // 数据完整性 (30%)
  if (githubData.totalStars >= 0) score += 10;
  if (githubData.commits > 0) score += 10;
  if (githubData.publicRepos > 0) score += 10;

  // 活跃度 (30%)
  if (githubData.commits > 100) score += 15;
  else if (githubData.commits > 10) score += 10;
  else if (githubData.commits > 0) score += 5;

  if (githubData.publicRepos > 10) score += 15;
  else if (githubData.publicRepos > 1) score += 10;
  else if (githubData.publicRepos > 0) score += 5;

  return Math.min(100, score);
}

function calculateCompletionScore(factors: {
  hasGitHubData: boolean;
  hasEmail: boolean;
  hasRecentActivity: boolean;
}): number {
  let score = 0;
  if (factors.hasGitHubData) score += 50;
  if (factors.hasEmail) score += 25;
  if (factors.hasRecentActivity) score += 25;
  return score;
}

function calculateEngagementLevel(context: {
  githubData: any;
  accountAge: number;
}): string {
  if (!context.githubData) return "inactive";

  const { githubData, accountAge } = context;
  const commitsPerDay = accountAge > 0 ? githubData.commits / accountAge : 0;

  if (commitsPerDay >= 1) return "very_active";
  if (commitsPerDay >= 0.5) return "active";
  if (commitsPerDay >= 0.1) return "moderate";
  if (githubData.commits > 0) return "light";
  return "inactive";
}
