import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { userDataService } from "@/lib/services/user-data-service";

// 根据环境选择运行时
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

/**
 * Phase 3.3: 统一数据服务API - 使用UserDataService重构
 * 保持API契约不变，内部切换到统一服务层
 */
export async function GET(request: NextRequest) {
  const session = await auth();
  // 简化API日志

  if (!session?.user?.id) {
    console.log(`❌ [API] Unauthorized request - no session or user ID`);
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const invalidateCache = searchParams.get("invalidateCache") === "true";
    const userId = session.user.id;
    const startTime = Date.now();

    // 简化API日志

    // 获取Cloudflare Worker的waitUntil函数（如果可用）
    const waitUntil =
      (request as any).waitUntil ||
      ((promise: Promise<any>) => {
        // 在非Worker环境中，我们仍然启动Promise但不等待
        promise.catch((error) =>
          console.warn("Background task failed:", error)
        );
      });

    // Phase 3.3: 使用统一数据服务 (重构后)
    const response = await userDataService.getUserCompleteData(userId, {
      forceRefresh: invalidateCache,
      waitUntil,
    });
    const responseTime = Date.now() - startTime;

    // 简化API响应日志

    if (!response.success || !response.data) {
      throw new Error(
        response.error || "Failed to fetch user data from unified service"
      );
    }

    // 新的统一API响应格式：移除重复的github字段，使用统一的userData
    const apiResponse = NextResponse.json({
      success: true,
      data: response.data,
    });

    // 设置缓存头
    apiResponse.headers.set(
      "Cache-Control",
      "public, max-age=300, s-maxage=300"
    );
    console.log(
      `✅ Unified GitHub data served for ${userId} in ${responseTime}ms (cached: ${response.data.metadata.cached})`
    );
    return apiResponse;
  } catch (error: any) {
    console.error("GitHub data API error:", error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to fetch GitHub data: ${error.message}`,
      },
      { status: 500 }
    );
  }
}

/**
 * POST: 刷新缓存 - 使用统一缓存失效
 */
export async function POST(request: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const userId = session.user.id;
    const invalidationStart = Date.now();
    await userDataService.invalidateUserCaches(userId);
    const invalidationTime = Date.now() - invalidationStart;

    console.log(
      `🔄 Unified cache refreshed for ${userId}: ${invalidationTime}ms`
    );

    return NextResponse.json({
      success: true,
      message: "Cache refreshed successfully",
      metadata: {
        invalidationTime,
      },
    });
  } catch (error: any) {
    console.error("Cache refresh error:", error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to refresh cache: ${error.message}`,
      },
      { status: 500 }
    );
  }
}
