// app/api/admin/sync-plans/route.ts
import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { stripe } from "@/lib/stripe";
import { getDb } from "@/lib/db";
import { subscriptionPlans } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import Stripe from "stripe";

export const dynamic = "force-dynamic";

export async function GET() {
  console.log("=== 开始同步 Stripe 订阅计划 ===");

  try {
    // 1. 验证用户会话
    const session = await auth();
    console.log("用户会话信息:", {
      userId: session?.user?.id,
      email: session?.user?.email,
      isAuthenticated: !!session?.user,
    });

    if (!session?.user) {
      console.error("未授权访问: 用户未登录");
      return NextResponse.json(
        { success: false, error: "未授权访问: 请先登录" },
        { status: 401 }
      );
    }

    // 2. 初始化数据库连接
    let db;
    try {
      db = await getDb();
      console.log("数据库连接成功");
    } catch (dbError) {
      console.error("数据库连接失败:", dbError);
      return NextResponse.json(
        { success: false, error: "数据库连接失败" },
        { status: 500 }
      );
    }

    // 3. 从 Stripe 获取价格和产品信息
    console.log("正在从 Stripe 获取价格信息...");
    let prices;
    try {
      // 添加请求超时
      const controller = new AbortController();
      const timeout = setTimeout(() => controller.abort(), 25000); // 25秒超时

      prices = await stripe.prices.list({
        expand: ["data.product"],
        active: true,
        limit: 100,
      }, {
        // 添加请求超时和重试配置
        timeout: 20000, // 20秒超时
        maxNetworkRetries: 2, // 最多重试2次
      });
      
      clearTimeout(timeout);
      console.log(`成功获取 ${prices.data.length} 个价格信息`);
    } catch (err) {
      // 类型断言为 Error 和 Stripe.StripeError
      const error = err as Error & {
        type?: string;
        code?: string;
        statusCode?: number;
        requestId?: string;
      };

      console.error("获取 Stripe 价格信息失败:", {
        name: error.name,
        message: error.message,
        code: error.code,
        type: error.type,
        statusCode: error.statusCode,
        requestId: error.requestId,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      });

      let statusCode = 500;
      let errorMessage = "获取 Stripe 价格信息失败";
      
      if (error.type === 'StripeConnectionError') {
        statusCode = 503; // 服务不可用
        errorMessage = "无法连接到 Stripe 服务，请稍后重试";
      } else if (error.type === 'StripeAPIError') {
        statusCode = error.statusCode || 500;
        errorMessage = `Stripe 服务错误: ${error.message}`;
      } else if (error.name === 'AbortError') {
        statusCode = 504; // 网关超时
        errorMessage = "请求超时，请稍后重试";
      }

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          code: error.code || 'stripe_error',
          ...(error.requestId && { requestId: error.requestId }),
          ...(process.env.NODE_ENV === 'development' && { details: error.message }),
        },
        { status: statusCode }
      );
    }

    // 2. 转换 Stripe 数据为数据库格式
    console.log("开始转换 Stripe 数据...");
    const plansToUpsert = prices.data.map((price) => {
      try {
        const product = price.product as Stripe.Product;
        if (!product) {
          throw new Error(`产品信息缺失 (price: ${price.id})`);
        }

        const planData = {
          id: price.id,
          name: product.name || "未命名计划",
          description: product.description || "",
          price: price.unit_amount || 0,
          currency: price.currency,
          interval: price.recurring?.interval || "month",
          features: product.metadata?.features
            ? product.metadata.features
            : "[]",
          isActive: product.active,
          stripePriceId: price.id,
          stripeProductId: product.id,
          updatedAt: new Date(),
        };

        console.log(`处理订阅计划: ${planData.name} (${planData.id})`);
        return planData;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "未知错误";
        console.error(`处理价格 ${price.id} 时出错:`, errorMessage);
        throw new Error(`处理价格 ${price.id} 失败: ${errorMessage}`);
      }
    });

    // 4. 批量插入或更新数据库
    console.log("开始同步数据到数据库...");
    let added = 0;
    let updated = 0;
    let failed = 0;

    for (const plan of plansToUpsert) {
      try {
        // 检查是否已存在
        const existing = await db
          .select()
          .from(subscriptionPlans)
          .where(eq(subscriptionPlans.id, plan.id))
          .get();

        // 准备更新数据（移除 updatedAt 字段，因为数据库会自动处理）
        const { updatedAt, ...updateData } = plan;

        if (existing) {
          // 更新现有记录
          await db
            .update(subscriptionPlans)
            .set(updateData)
            .where(eq(subscriptionPlans.id, plan.id));
          updated++;
          console.log(`已更新订阅计划: ${plan.name}`);
        } else {
          // 插入新记录
          await db.insert(subscriptionPlans).values(updateData);
          added++;
          console.log(`已添加新订阅计划: ${plan.name}`);
        }
      } catch (dbError) {
        failed++;
        const errorMessage =
          dbError instanceof Error ? dbError.message : "未知错误";
        console.error(`处理订阅计划 ${plan.id} 时出错:`, errorMessage);
      }
    }

    // 5. 返回同步结果
    const result = {
      success: failed === 0,
      message:
        failed === 0 ? "同步成功" : `同步完成，但有 ${failed} 条记录处理失败`,
      stats: {
        total: plansToUpsert.length,
        added,
        updated,
        failed,
        successRate:
          (((added + updated) / plansToUpsert.length) * 100).toFixed(2) + "%",
      },
    };

    console.log("同步完成:", result);
    return NextResponse.json(result);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "未知错误";
    console.error("同步过程中发生错误:", error);
    return NextResponse.json(
      {
        success: false,
        error: "同步失败",
        details: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
