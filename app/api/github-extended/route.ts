/**
 * GitHub扩展数据API端点
 * V5 扩展 - 获取用户的扩展GitHub数据（仓库详情、commit信息、README、时间统计）
 */

import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { githubExtendedService } from "@/lib/services/github-extended-service";
import type { ExtendedDataFetchResponse } from "@/types/github-extended";

export async function GET() {
  try {
    // 1. 验证用户身份
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: "Unauthorized - Please sign in to access extended GitHub data",
        },
        { status: 401 }
      );
    }

    console.log(
      `🚀 [GitHub Extended API] Getting data for user ${session.user.id}`
    );

    const startTime = Date.now();

    // 2. 获取扩展数据
    const extendedData = await githubExtendedService.getExtendedData(
      session.user.id
    );

    const processingTime = Date.now() - startTime;

    if (!extendedData) {
      console.log(
        `ℹ️ [GitHub Extended API] No data available for user ${session.user.id}`
      );
      return NextResponse.json(
        {
          success: false,
          error: "Extended data not available. Please try again later.",
          metadata: {
            processingTime,
            apiCalls: 0,
            dataSize: {
              repositories: 0,
              commits: 0,
              readmes: 0,
              timeStats: 0,
            },
          },
        } as ExtendedDataFetchResponse,
        { status: 404 }
      );
    }

    // 3. 构建响应
    const response: ExtendedDataFetchResponse = {
      success: true,
      data: extendedData,
      metadata: {
        processingTime,
        apiCalls: 0, // TODO: 实际统计API调用次数
        dataSize: {
          repositories: extendedData.repositories.length,
          commits: extendedData.commits.length,
          readmes: extendedData.readmes.length,
          techStackFiles: extendedData.techStackFiles.length,
          timeStats: extendedData.timeStats.length,
        },
      },
    };

    console.log(`✅ [GitHub Extended API] Success for user ${session.user.id}`);
    console.log(`📊 Data summary:`, response.metadata.dataSize);
    console.log(`⏱️ Processing time: ${processingTime}ms`);

    return NextResponse.json(response);
  } catch (error: any) {
    console.error(`❌ [GitHub Extended API] Unexpected error:`, error);

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        metadata: {
          processingTime: 0,
          apiCalls: 0,
          dataSize: {
            repositories: 0,
            commits: 0,
            readmes: 0,
            timeStats: 0,
          },
        },
      } as ExtendedDataFetchResponse,
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    // 1. 验证用户身份
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Unauthorized - Please sign in to refresh extended GitHub data",
        },
        { status: 401 }
      );
    }

    console.log(
      `🔄 [GitHub Extended API] Triggering refresh for user ${session.user.id}`
    );

    const startTime = Date.now();

    try {
      // 2. 执行条件更新
      console.log(
        `🚀 [GitHub Extended API] Starting conditional update for ${session.user.id}`
      );

      await githubExtendedService.silentCheckAndUpdate(session.user.id);

      const processingTime = Date.now() - startTime;
      console.log(
        `✅ [GitHub Extended API] Update completed for ${session.user.id} in ${processingTime}ms`
      );

      // 3. 检查更新后的状态
      const status = await githubExtendedService.checkExtendedDataStatus(
        session.user.id
      );

      // 4. 返回详细的成功响应
      return NextResponse.json({
        success: true,
        message: "Data refresh completed successfully",
        status: status,
        metadata: {
          processingTime,
          userId: session.user.id,
          timestamp: new Date().toISOString(),
          updateMethod: "synchronous",
        },
      });
    } catch (updateError) {
      const processingTime = Date.now() - startTime;
      console.error(
        `❌ [GitHub Extended API] Update failed for ${session.user.id}:`,
        updateError
      );

      // 检查失败后的状态
      const status = await githubExtendedService.checkExtendedDataStatus(
        session.user.id
      );

      return NextResponse.json(
        {
          success: false,
          error: `Data refresh failed: ${
            updateError instanceof Error
              ? updateError.message
              : String(updateError)
          }`,
          status: status,
          metadata: {
            processingTime,
            userId: session.user.id,
            timestamp: new Date().toISOString(),
            errorDetails:
              updateError instanceof Error ? updateError.stack : undefined,
          },
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error(`❌ [GitHub Extended API] Unexpected refresh error:`, error);

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error during refresh trigger",
        metadata: {
          processingTime: 0,
          timestamp: new Date().toISOString(),
          errorDetails: error instanceof Error ? error.stack : undefined,
        },
      },
      { status: 500 }
    );
  }
}
