import { NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { auth } from "@/auth";
import { getDb } from "@/lib/db";
import { userSubscriptions } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import <PERSON><PERSON> from "stripe";

// Helper function to handle errors
const handleError = (error: unknown, context: string) => {
  console.error(`[${new Date().toISOString()}] ${context}:`, error);
  return NextResponse.json({ error: `Failed to ${context}` }, { status: 500 });
};

interface CreateCheckoutBody {
  priceId: string;
  successUrl?: string;
  cancelUrl?: string;
  interval?: string;
}

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { priceId, successUrl, cancelUrl, interval }: CreateCheckoutBody =
      await req.json();

    if (!priceId) {
      return NextResponse.json(
        { error: "Price ID is required" },
        { status: 400 }
      );
    }

    // Validate price exists and is active
    try {
      const price = await stripe.prices.retrieve(priceId);
      if (!price.active) {
        return NextResponse.json(
          { error: "Selected plan is not available" },
          { status: 400 }
        );
      }
    } catch (error) {
      return NextResponse.json({ error: "Invalid price ID" }, { status: 400 });
    }

    const db = await getDb();

    // Check for existing subscription
    const existingSubscription = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.userId, session.user.id),
          eq(userSubscriptions.status, "active")
        )
      )
      .get();

    // If user has an active subscription, we can't create a new one
    if (existingSubscription) {
      return NextResponse.json(
        { error: "You already have an active subscription" },
        { status: 400 }
      );
    }

    // For new customers, we'll create a customer in Stripe during checkout
    // by providing the email in the checkout session

    // Create checkout session
    try {
      const sessionData: Stripe.Checkout.SessionCreateParams = {
        mode: "subscription",
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        success_url: `${
          successUrl || process.env.NEXTAUTH_URL
        }/dashboard?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: cancelUrl || `${process.env.NEXTAUTH_URL}/pricing`,
        client_reference_id: session.user.id,
        subscription_data: {
          metadata: {
            userId: session.user.id,
            planId: priceId,
            interval: interval || "month",
          },
        },
      };

      // Add customer email if available and valid
      if (session.user.email && !session.user.email.includes("@example.com")) {
        sessionData.customer_email = session.user.email;
      }

      const checkoutSession = await stripe.checkout.sessions.create(
        sessionData
      );

      if (!checkoutSession.url) {
        throw new Error("Failed to create checkout session: No URL returned");
      }

      return NextResponse.json({
        url: checkoutSession.url,
        sessionId: checkoutSession.id,
      });
    } catch (error) {
      console.error("Error creating checkout session:", error);
      return handleError(error, "create checkout session");
    }
  } catch (error) {
    return handleError(error, "process checkout request");
  }
}
