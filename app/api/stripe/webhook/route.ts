import { NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { headers } from "next/headers";
import { getDb } from "@/lib/db";
import { userSubscriptions, payments, NewPayment } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import type <PERSON><PERSON> from "stripe";
import { SubscriptionService } from "@/lib/subscription/service";
import { SUBSCRIPTION_STATUS, type SubscriptionStatus } from "@/lib/subscription/state";

// Extended types for Stripe objects
type StripeWebhookEvent = Stripe.Event;
type StripeCheckoutSession = Stripe.Checkout.Session & {
  subscription?: string | { id: string };
  client_reference_id?: string;
  customer?: string | { id: string };
};

// Stripe subscription with expanded price data
type StripeSubscription = {
  id: string;
  status: string;
  current_period_start: number;
  current_period_end: number;
  cancel_at: number | null;
  canceled_at: number | null;
  cancel_at_period_end: boolean;
  items: {
    data: Array<{
      id: string;
      price: Stripe.Price;
    }>;
  };
  metadata: {
    userId?: string;
    [key: string]: string | undefined;
  };
};

type StripeInvoice = Stripe.Invoice & {
  subscription?: string | { id: string };
  id: string;
  payment_intent?: string | { id: string; [key: string]: any };
  metadata?: {
    userId?: string;
    [key: string]: string | undefined;
  };
  last_payment_error?: {
    code?: string;
    message?: string;
    [key: string]: any;
  };
  [key: string]: any;
};

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

if (!webhookSecret) {
  throw new Error("STRIPE_WEBHOOK_SECRET is not set in environment variables");
}

// Helper to handle errors consistently
const handleError = (error: unknown, context: string, status = 400) => {
  const errorMessage = error instanceof Error ? error.message : "Unknown error";
  console.error(
    `[${new Date().toISOString()}] Webhook Error (${context}):`,
    error
  );
  return NextResponse.json(
    { error: `Webhook Error: ${errorMessage}` },
    { status }
  );
};

// Helper to verify webhook signature
const verifyWebhook = async (
  req: Request,
  secret: string
): Promise<StripeWebhookEvent> => {
  const body = await req.text();
  const signature = headers().get("stripe-signature");

  if (!signature) {
    throw new Error("Missing Stripe signature");
  }

  try {
    return stripe.webhooks.constructEvent(body, signature, secret);
  } catch (err) {
    console.error("Webhook signature verification failed:", err);
    throw new Error("Invalid signature");
  }
};

// Handle checkout.session.completed event
const handleCheckoutSessionCompleted = async (
  session: StripeCheckoutSession
) => {
  if (!session.subscription || !session.client_reference_id) {
    throw new Error("Missing subscription or client reference ID");
  }

  const db = await getDb();
  const subscriptionId =
    typeof session.subscription === "string"
      ? session.subscription
      : session.subscription.id;

  // Retrieve the subscription with expanded price data
  const subscription = (await stripe.subscriptions.retrieve(subscriptionId, {
    expand: ["items.data.price"],
  })) as unknown as StripeSubscription;

  const priceId = subscription.items.data[0].price.id;
  const userId = session.client_reference_id;

  // Create or update user subscription
  await db
    .insert(userSubscriptions)
    .values({
      id: subscription.id,
      userId,
      planId: priceId,
      status: subscription.status,
      priceId,
      currentPeriodStart: subscription.current_period_start * 1000,
      currentPeriodEnd: subscription.current_period_end * 1000,
      cancelAt: subscription.cancel_at ? subscription.cancel_at * 1000 : null,
      canceledAt: subscription.canceled_at
        ? subscription.canceled_at * 1000
        : null,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    })
    .onConflictDoUpdate({
      target: userSubscriptions.id,
      set: {
        status: subscription.status,
        currentPeriodStart: subscription.current_period_start * 1000,
        currentPeriodEnd: subscription.current_period_end * 1000,
        cancelAt: subscription.cancel_at ? subscription.cancel_at * 1000 : null,
        canceledAt: subscription.canceled_at
          ? subscription.canceled_at * 1000
          : null,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        updatedAt: Date.now(),
      },
    });
};

// Handle subscription updates and deletions
const handleSubscriptionUpdated = async (subscription: StripeSubscription) => {
  if (!subscription.metadata?.userId) {
    console.error("Missing userId in subscription metadata");
    return;
  }

  try {
    await SubscriptionService.updateSubscriptionStatus(
      subscription.id,
      subscription.status as SubscriptionStatus,
      {
        userId: subscription.metadata.userId,
        subscriptionId: subscription.id,
        reason: "subscription_updated",
        metadata: {
          currentPeriodStart: subscription.current_period_start,
          currentPeriodEnd: subscription.current_period_end,
          cancelAt: subscription.cancel_at,
          canceledAt: subscription.canceled_at,
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
        },
      }
    );

    // 更新其他订阅字段
    const db = await getDb();
    await db
      .update(userSubscriptions)
      .set({
        currentPeriodStart: subscription.current_period_start * 1000,
        currentPeriodEnd: subscription.current_period_end * 1000,
        cancelAt: subscription.cancel_at ? subscription.cancel_at * 1000 : null,
        canceledAt: subscription.canceled_at
          ? subscription.canceled_at * 1000
          : null,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
      })
      .where(eq(userSubscriptions.id, subscription.id));
  } catch (error) {
    console.error("Error handling subscription update:", error);
    throw error;
  }
};

// Handle successful invoice payment
const handleInvoicePaid = async (invoice: StripeInvoice) => {
  if (!invoice.subscription) {
    console.log("No subscription found in invoice");
    return;
  }

  const db = await getDb();
  const subscriptionId =
    typeof invoice.subscription === "string"
      ? invoice.subscription
      : invoice.subscription.id;

  // Record the successful payment
  const paymentId = `pay_${Date.now()}_${Math.random()
    .toString(36)
    .substring(2, 9)}`;
  const now = new Date().getTime();

  if (invoice.metadata?.userId) {
    const paymentData = {
      id: paymentId,
      userId: invoice.metadata.userId,
      subscriptionId: subscriptionId,
      amount: invoice.amount_paid,
      currency: invoice.currency || "USD",
      status: "succeeded" as const,
      paymentMethod: invoice.payment_intent
        ? String(invoice.payment_intent)
        : undefined,
      receiptUrl: invoice.invoice_pdf || undefined,
    };

    await db.insert(payments).values(paymentData);
  }
};

// Handle failed invoice payment
const handlePaymentFailed = async (invoice: StripeInvoice) => {
  if (!invoice.subscription) {
    console.log("No subscription found in failed payment invoice");
    return;
  }

  const subscriptionId =
    typeof invoice.subscription === "string"
      ? invoice.subscription
      : invoice.subscription.id;

  try {
    const db = await getDb();
    const now = Date.now();

    // 记录失败的支付
    if (invoice.payment_intent && invoice.metadata?.userId) {
      const paymentIntentId =
        typeof invoice.payment_intent === "string"
          ? invoice.payment_intent
          : invoice.payment_intent.id;

      const paymentData: NewPayment = {
        id: paymentIntentId,
        userId: invoice.metadata.userId,
        subscriptionId,
        amount: invoice.amount_due,
        currency: invoice.currency || "USD",
        status: "failed",
        paymentMethod: invoice.payment_intent
          ? String(invoice.payment_intent)
          : undefined,
        receiptUrl: invoice.invoice_pdf || undefined,
        createdAt: now,
      };

      await db.insert(payments).values(paymentData);
    }

    // 更新订阅状态为逾期
    if (invoice.metadata?.userId) {
      await SubscriptionService.updateSubscriptionStatus(
        subscriptionId,
        SUBSCRIPTION_STATUS.PAST_DUE,
        {
          userId: invoice.metadata.userId,
          subscriptionId,
          reason: "payment_failed",
          metadata: {
            invoiceId: invoice.id,
            amountDue: invoice.amount_due,
            currency: invoice.currency,
            attempt: invoice.attempt_count,
            nextPaymentAttempt: invoice.next_payment_attempt,
            lastPaymentError: invoice.last_payment_error,
          },
        }
      );
    }
  } catch (error) {
    console.error("Error handling failed payment:", error);
    throw error;
  }
};

// Process different event types
const processEvent = async (event: StripeWebhookEvent) => {
  try {
    switch (event.type) {
      case "checkout.session.completed":
        return await handleCheckoutSessionCompleted(
          event.data.object as StripeCheckoutSession
        );

      case "customer.subscription.updated":
      case "customer.subscription.deleted": {
        const subscription = event.data.object as Stripe.Subscription & {
          current_period_start: number;
          current_period_end: number;
          cancel_at: number | null;
          canceled_at: number | null;
          cancel_at_period_end: boolean;
          metadata: {
            userId?: string;
          };
        };

        const subscriptionData: StripeSubscription = {
          id: subscription.id,
          status:
            event.type === "customer.subscription.deleted"
              ? "canceled"
              : subscription.status || "active",
          current_period_start: subscription.current_period_start,
          current_period_end: subscription.current_period_end,
          cancel_at: subscription.cancel_at || null,
          canceled_at: subscription.canceled_at || null,
          cancel_at_period_end: subscription.cancel_at_period_end || false,
          items: {
            data: subscription.items.data.map((item) => ({
              id: item.id,
              price: item.price as Stripe.Price,
            })),
          },
          metadata: {
            userId: subscription.metadata?.userId,
          },
        };
        return await handleSubscriptionUpdated(subscriptionData);
      }

      case "invoice.paid":
        return await handleInvoicePaid(event.data.object as StripeInvoice);

      case "invoice.payment_failed":
        return await handlePaymentFailed(event.data.object as StripeInvoice);

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error("Error processing event:", error);
    throw error;
  }
};

export async function POST(req: Request) {
  try {
    // Verify the webhook signature
    const event = await verifyWebhook(req, webhookSecret as string);

    // Process the event
    await processEvent(event);

    return NextResponse.json({ received: true });
  } catch (error) {
    return handleError(error, "Webhook handler");
  }
}
