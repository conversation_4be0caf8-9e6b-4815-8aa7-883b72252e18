import { NextRequest } from "next/server";
import { auth } from "@/auth";
import { cacheManager } from "@/lib/cloudflare/kv-cache-manager";
import { userDataService } from "@/lib/services/user-data-service";
import {
  getUserDescriptionCached,
  getDashboardOverviewCached,
  getShareStatsCached,
  getMultiDimensionScoreCached,
  getGitHubDataCached,
} from "@/lib/github/api";

/**
 * 缓存状态监控 API - Phase 3.3 统一数据服务版本
 * GET /api/cache/status
 *
 * 功能：检查统一缓存系统的健康状态和性能指标
 * 权限：需要用户登录
 */
export async function GET(request: NextRequest) {
  try {
    // 认证检查
    const session = await auth();
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const testCache = url.searchParams.get("test") === "true";
    const testUnified = url.searchParams.get("unified") === "true";
    const username = session.user.name || session.user.email || "unknown";
    const userId = session.user.id;

    console.log(`🔍 Cache status check for user: ${username} (Phase 3.3)`);

    const results = {
      timestamp: new Date().toISOString(),
      user: {
        id: session.user.id,
        username: username,
      },
      cacheSystem: {
        version: "Phase 3.3 - 统一数据服务缓存系统",
        kvAvailable: false,
        multiLayerEnabled: true,
        reactCacheEnabled: true,
        unifiedSystemEnabled: true,
        unifiedDataServiceEnabled: true,
        multiDimensionCacheEnabled: true,
        githubDataCacheEnabled: true,
      },
      performance: {
        unifiedDataServiceFetch: null as any,
        cacheTest: null as any,
        unifiedCacheTests: null as any,
      },
      recommendations: [] as string[],
    };

    // 1. 测试统一数据服务性能（新的统一缓存机制）
    const unifiedServiceStart = Date.now();
    try {
      const response = await userDataService.getUserCompleteData(
        session.user.id
      );
      const unifiedServiceTime = Date.now() - unifiedServiceStart;
      const { success, data } = response;

      if (success && data) {
        results.performance.unifiedDataServiceFetch = {
          success: true,
          fetchTime: unifiedServiceTime,
          dataAvailable: !!data.userData,
          username: data.userData?.username || null,
          cached: data.metadata.cached,
          cacheSource: data.metadata.cached
            ? "unified_cache_hit"
            : "fresh_fetch",
          dataVersion: data.metadata.dataVersion,
          serviceVersion: data.metadata.version,
        };

        console.log(
          `✅ Unified data service fetch successful in ${unifiedServiceTime}ms (cached: ${data.metadata.cached})`
        );
      } else {
        throw new Error(
          response.error || "Failed to fetch data from unified service"
        );
      }

      if (unifiedServiceTime > 200) {
        results.recommendations.push(
          "Unified data service response time > 200ms - consider cache optimization"
        );
      }
    } catch (error) {
      results.performance.unifiedDataServiceFetch = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        fetchTime: Date.now() - unifiedServiceStart,
      };
      results.recommendations.push(
        "Unified data service fetch error - check service configuration"
      );
    }

    // 2. 测试KV缓存直接访问（如果需要）
    if (testCache) {
      const cacheTestStart = Date.now();
      try {
        const testKey = `cache-health-test-${Date.now()}`;
        const testValue = { test: true, timestamp: Date.now() };

        // 测试 SET
        await cacheManager.set(testKey, testValue, { expirationTtl: 60 });

        // 测试 GET
        const retrieved = await cacheManager.get(testKey);

        // 清理
        await cacheManager.delete(testKey);

        const cacheTestTime = Date.now() - cacheTestStart;

        results.performance.cacheTest = {
          success: !!retrieved,
          testTime: cacheTestTime,
          roundTripWorking:
            JSON.stringify(retrieved) === JSON.stringify(testValue),
        };

        results.cacheSystem.kvAvailable = !!retrieved;

        if (retrieved) {
          console.log(`✅ KV cache test successful in ${cacheTestTime}ms`);
        } else {
          console.log(`❌ KV cache test failed`);
          results.recommendations.push(
            "KV cache not responding - check Cloudflare KV configuration"
          );
        }
      } catch (error) {
        results.performance.cacheTest = {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          testTime: Date.now() - cacheTestStart,
        };
        results.recommendations.push(
          "KV cache error - check environment configuration"
        );
      }
    }

    // 3. Phase 3.3: 测试统一缓存系统兼容性
    if (testUnified) {
      console.log("🧪 Testing Phase 3.3 unified cache system compatibility...");
      const unifiedTestStart = Date.now();

      const unifiedTests = {
        userDescription: {
          success: false,
          time: 0,
          error: null as string | null,
        },
        dashboardOverview: {
          success: false,
          time: 0,
          error: null as string | null,
        },
        shareStats: { success: false, time: 0, error: null as string | null },
        legacyGithubDataCache: {
          success: false,
          time: 0,
          error: null as string | null,
        },
        overallTime: 0,
        overallSuccess: 0,
      };

      // 测试用户描述缓存
      try {
        const userDescStart = Date.now();
        const userDesc = await getUserDescriptionCached(userId);
        unifiedTests.userDescription = {
          success: true,
          time: Date.now() - userDescStart,
          error: null,
        };
        unifiedTests.overallSuccess++;
        console.log(
          `  ✅ User description cache: ${unifiedTests.userDescription.time}ms`
        );
      } catch (error) {
        unifiedTests.userDescription.error =
          error instanceof Error ? error.message : "Unknown error";
        console.log(`  ❌ User description cache failed: ${error}`);
      }

      // 测试Dashboard概览缓存
      try {
        const dashboardStart = Date.now();
        const dashboard = await getDashboardOverviewCached(userId);
        unifiedTests.dashboardOverview = {
          success: !!dashboard,
          time: Date.now() - dashboardStart,
          error: null,
        };
        if (dashboard) unifiedTests.overallSuccess++;
        console.log(
          `  ✅ Dashboard overview cache: ${unifiedTests.dashboardOverview.time}ms`
        );
      } catch (error) {
        unifiedTests.dashboardOverview.error =
          error instanceof Error ? error.message : "Unknown error";
        console.log(`  ❌ Dashboard overview cache failed: ${error}`);
      }

      // 测试分享统计缓存
      try {
        const shareStart = Date.now();
        const shareStats = await getShareStatsCached(userId);
        unifiedTests.shareStats = {
          success: !!shareStats,
          time: Date.now() - shareStart,
          error: null,
        };
        if (shareStats) unifiedTests.overallSuccess++;
        console.log(
          `  ✅ Share stats cache: ${unifiedTests.shareStats.time}ms`
        );
      } catch (error) {
        unifiedTests.shareStats.error =
          error instanceof Error ? error.message : "Unknown error";
        console.log(`  ❌ Share stats cache failed: ${error}`);
      }

      unifiedTests.overallTime = Date.now() - unifiedTestStart;

      results.performance.unifiedCacheTests = unifiedTests;

      // Phase 3.3 性能评估
      const avgResponseTime =
        (unifiedTests.userDescription.time +
          unifiedTests.dashboardOverview.time +
          unifiedTests.shareStats.time) /
        3;

      if (avgResponseTime < 100 && unifiedTests.overallSuccess >= 2) {
        results.recommendations.push(
          "✅ Excellent unified cache performance - all systems operating optimally"
        );
      } else if (avgResponseTime < 200 && unifiedTests.overallSuccess >= 1) {
        results.recommendations.push(
          "✅ Good unified cache performance - some optimization opportunities"
        );
      } else {
        results.recommendations.push(
          "⚠️ Unified cache performance needs attention - check system health"
        );
      }
    }

    // 4. 整体性能评估（增强版）
    const unifiedServiceTime =
      results.performance.unifiedDataServiceFetch?.fetchTime;
    if (unifiedServiceTime) {
      if (unifiedServiceTime < 100) {
        results.recommendations.push(
          "✅ Excellent cache performance - data likely served from cache"
        );
      } else if (unifiedServiceTime < 500) {
        results.recommendations.push(
          "✅ Good performance - mix of cached and fresh data"
        );
      } else if (unifiedServiceTime < 2000) {
        results.recommendations.push(
          "⚠️ Moderate performance - consider checking GitHub API rate limits"
        );
      } else {
        results.recommendations.push(
          "❌ Slow performance - check network connectivity and GitHub API status"
        );
      }
    }

    // 5. 整体健康状态（Phase 3.3版本）
    const healthStatus = {
      overall: "healthy" as "healthy" | "degraded" | "unhealthy",
      issues: [] as string[],
      phase33Status: "operational" as "operational" | "degraded" | "critical",
    };

    if (!results.performance.unifiedDataServiceFetch?.success) {
      healthStatus.overall = "unhealthy";
      healthStatus.issues.push("Unified data service fetch failed");
    } else if (unifiedServiceTime && unifiedServiceTime > 2000) {
      healthStatus.overall = "degraded";
      healthStatus.issues.push("Slow Unified data service fetch performance");
    }

    if (testCache && !results.performance.cacheTest?.success) {
      healthStatus.overall =
        healthStatus.overall === "unhealthy" ? "unhealthy" : "degraded";
      healthStatus.issues.push("KV cache test failed");
    }

    // Phase 3.3 健康评估
    if (testUnified && results.performance.unifiedCacheTests) {
      const unifiedTests = results.performance.unifiedCacheTests;
      if (unifiedTests.overallSuccess === 0) {
        healthStatus.phase33Status = "critical";
        healthStatus.issues.push("All unified cache tests failed");
      } else if (unifiedTests.overallSuccess < 3) {
        healthStatus.phase33Status = "degraded";
        healthStatus.issues.push("Some unified cache tests failed");
      }
    }

    console.log(
      `📊 Cache health check completed: ${healthStatus.overall} (Phase 3.3: ${healthStatus.phase33Status})`
    );

    return Response.json({
      success: true,
      status: healthStatus,
      data: results,
      summary: {
        cacheSystemHealthy: healthStatus.overall !== "unhealthy",
        unifiedDataServiceAvailable:
          results.performance.unifiedDataServiceFetch?.success || false,
        averageResponseTime: unifiedServiceTime || -1,
        recommendationsCount: results.recommendations.length,
        phase33Operational: healthStatus.phase33Status === "operational",
        unifiedCacheEnabled: testUnified,
      },
    });
  } catch (error) {
    console.error("❌ Cache status check failed:", error);

    return Response.json(
      {
        success: false,
        error: "Failed to check cache status",
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
