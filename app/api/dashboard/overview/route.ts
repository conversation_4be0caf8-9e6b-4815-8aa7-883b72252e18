// V5.1 Dashboard Feature - Overview API Endpoint
// Created: 2025-01-30
// Purpose: Dashboard概览数据API，使用统一缓存系统

import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getDashboardOverviewCached } from "@/lib/github/api";

// 根据环境选择运行时
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";

/**
 * Dashboard 概览数据API
 * GET /api/dashboard/overview
 *
 * 功能：获取用户Dashboard概览数据，包含GitHub、AI、分享等核心信息
 * 缓存：5分钟缓存策略（复用统一缓存系统）
 * 权限：需要用户登录
 */
export async function GET() {
  try {
    // 1. 用户身份验证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    console.log(`📊 Dashboard overview requested for user: ${userId}`);

    // 2. 使用统一缓存系统获取数据
    const overviewData = await getDashboardOverviewCached(userId);

    // 3. 增强用户信息
    const enhancedOverview = {
      ...overviewData,
      user: {
        ...overviewData.user,
        username: session.user.name || session.user.email || "unknown",
        name: session.user.name,
        email: session.user.email,
        image: session.user.image,
        displayName:
          session.user.name ||
          session.user.email ||
          `User ${userId.slice(0, 8)}`,
        avatarUrl: session.user.image,
        createdAt: session.user.createdAt,
      },
    };

    // 4. 构建响应数据
    const responseData = {
      success: true,
      data: enhancedOverview,
      metadata: {
        userId,
        generatedAt: Date.now(),
        cacheSource: "unified_cache_system",
        cacheExpiry: enhancedOverview.cacheExpiry,
        performance: {
          cachingEnabled: true,
          expectedResponseTime: "< 100ms",
        },
      },
    };

    // 5. 设置缓存头
    const response = NextResponse.json(responseData);
    response.headers.set(
      "Cache-Control",
      "public, max-age=300, stale-while-revalidate=60"
    ); // 5分钟缓存

    console.log(`✅ Dashboard overview served for user ${userId}`);
    return response;
  } catch (error) {
    console.error("Dashboard overview API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch dashboard overview",
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}
