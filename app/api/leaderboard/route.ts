import { NextRequest, NextResponse } from "next/server";
import { cache } from "react";
import { getFullLeaderboard } from "@/lib/leaderboard";
import { auth } from "@/auth";
import { getDb } from "@/lib/db";

// 根据环境选择运行时：开发环境使用 nodejs (支持 better-sqlite3)，生产环境使用 edge (Cloudflare D1)
export const runtime =
  process.env.NODE_ENV === "production" ? "edge" : "nodejs";
// 标记为动态路由，不进行静态生成
export const dynamic = "force-dynamic";

// Cached function to get leaderboard data
const getCachedLeaderboardData = cache(
  async (
    limit: number = 20,
    page: number = 1,
    db: Awaited<ReturnType<typeof getDb>>,
    currentUserId?: string
  ) => {
    return await getFullLeaderboard(limit, page, db, currentUserId);
  }
);

// Get leaderboard data
export async function GET(request: NextRequest) {
  try {
    const db = await getDb();
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "20", 10);
    const page = parseInt(searchParams.get("page") || "1", 10);

    // Get current user
    const session = await auth();
    const currentUserId = session?.user?.id;

    // Get leaderboard data with caching
    const leaderboardData = await getCachedLeaderboardData(
      limit,
      page,
      db,
      currentUserId
    );

    return NextResponse.json(leaderboardData);
  } catch (error) {
    console.error("Error fetching leaderboard data:", error);
    return NextResponse.json(
      { error: "Failed to fetch leaderboard data", message: String(error) },
      { status: 500 }
    );
  }
}
