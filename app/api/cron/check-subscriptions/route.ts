import { NextResponse } from "next/server";
import { getDb } from "@/lib/db";
import { userSubscriptions } from "@/lib/db/schema";
import { lt, eq, and, gte, lte } from "drizzle-orm";
import { SUBSCRIPTION_STATUS } from "@/lib/subscription/state";
import { SubscriptionService } from "@/lib/subscription/service";

/**
 * 检查并更新即将到期或已过期的订阅
 * 此端点应由定时任务调用，例如每6小时一次
 */
export async function GET() {
  try {
    const db = await getDb();
    const now = Math.floor(Date.now() / 1000);
    const oneDayInSeconds = 24 * 60 * 60;
    
    // 1. 查找即将在24小时内到期的活跃订阅
    const expiringSoon = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.status, SUBSCRIPTION_STATUS.ACTIVE),
          lte(userSubscriptions.currentPeriodEnd, now + oneDayInSeconds),
          gte(userSubscriptions.currentPeriodEnd, now)
        )
      );

    // 2. 查找已过期的活跃订阅
    const expiredSubscriptions = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.status, SUBSCRIPTION_STATUS.ACTIVE),
          lt(userSubscriptions.currentPeriodEnd, now)
        )
      );

    // 3. 更新过期订阅状态
    const updateResults = [];
    for (const sub of expiredSubscriptions) {
      try {
        await SubscriptionService.updateSubscriptionStatus(
          sub.id,
          SUBSCRIPTION_STATUS.PAST_DUE, // 将过期订阅标记为逾期未付
          {
            userId: sub.userId,
            subscriptionId: sub.id,
            reason: 'subscription_expired',
            metadata: {
              originalStatus: sub.status,
              expiredAt: now
            }
          }
        );
        updateResults.push({
          subscriptionId: sub.id,
          status: 'updated',
          fromStatus: sub.status,
          toStatus: SUBSCRIPTION_STATUS.PAST_DUE
        });
      } catch (error) {
        console.error(`Failed to update subscription ${sub.id}:`, error);
        updateResults.push({
          subscriptionId: sub.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      stats: {
        totalChecked: expiringSoon.length + expiredSubscriptions.length,
        expiringSoon: expiringSoon.length,
        expired: expiredSubscriptions.length,
        updated: updateResults.filter(r => r.status === 'updated').length,
        failed: updateResults.filter(r => r.status === 'failed').length
      },
      expiringSoon: expiringSoon.map(sub => ({
        id: sub.id,
        userId: sub.userId,
        expiresIn: sub.currentPeriodEnd - now
      })),
      updatedSubscriptions: updateResults
    });
  } catch (error) {
    console.error("Error checking subscriptions:", error);
    return NextResponse.json(
      { 
        success: false,
        error: "Failed to check subscriptions", 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// 添加 OPTIONS 方法处理预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
