import { NextResponse } from "next/server";
import { getDb } from "@/lib/db";
import { shareLinks, userSubscriptions } from "@/lib/db/schema";
import { lt, eq, and, inArray, gt } from "drizzle-orm";
import { imageQueueManager } from "@/lib/image-queue-manager";

// 每批处理的链接数量
const BATCH_SIZE = 1000;

/**
 * 批量处理数组
 */
async function processInBatches<T, R>(
  items: T[],
  batchSize: number,
  processBatch: (batch: T[]) => Promise<R>
): Promise<R[]> {
  const results: R[] = [];
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResult = await processBatch(batch);
    results.push(batchResult);
  }
  return results;
}

// 处理定时任务请求
export async function GET() {
  try {
    console.time("cleanup-expired-links");
    const db = await getDb();
    const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）

    let updatedCount = 0;

    // 1. 首先获取所有过期的活跃链接
    console.time("fetch-expired-links");
    const expiredLinks = await db
      .select()
      .from(shareLinks)
      .where(and(lt(shareLinks.expiresAt, now), eq(shareLinks.isActive, true)))
      .limit(10000); // 限制最大处理数量，防止内存溢出
    console.timeEnd("fetch-expired-links");
    console.log(`Found ${expiredLinks.length} expired links to process`);

    if (expiredLinks.length > 0) {
      // 2. 获取有有效订阅的用户ID
      console.time("fetch-active-subscribers");
      const activeSubscribers = await db
        .selectDistinct()
        .from(userSubscriptions)
        .where(
          and(
            inArray(userSubscriptions.status, ["active", "trialing"]),
            gt(userSubscriptions.currentPeriodEnd, now)
          )
        );
      console.timeEnd("fetch-active-subscribers");

      // 使用 Set 提高查找性能
      const activeSubscriberIds = new Set(
        activeSubscribers.map((s) => s.userId)
      );

      // 3. 过滤出需要更新的链接（非订阅用户或订阅已过期的用户）
      const linksToUpdate = expiredLinks
        .filter((link) => !activeSubscriberIds.has(link.userId))
        .map((link) => link.id);

      console.log(`Found ${linksToUpdate.length} links to deactivate`);

      // 4. 批量更新
      if (linksToUpdate.length > 0) {
        console.time("update-expired-links");
        // 分批处理，避免 SQL 参数过多
        await processInBatches(linksToUpdate, BATCH_SIZE, async (batch) => {
          const result = await db
            .update(shareLinks)
            .set({ isActive: false })
            .where(inArray(shareLinks.id, batch));
          // D1Result has meta.changes, RunResult has changes
          const rowsAffected =
            "meta" in result
              ? result.meta.changes
              : (result as any).changes ?? 0;
          updatedCount += rowsAffected;
          return result;
        });
        console.timeEnd("update-expired-links");
      }
    }

    // 5. 清理R2图片队列中的过期数据
    console.time("cleanup-r2-queue");
    let r2CleanupResult: {
      removedFromQueue: number;
      removedFromR2: number;
      freedSpace: number;
      errors: string[];
    } = {
      removedFromQueue: 0,
      removedFromR2: 0,
      freedSpace: 0,
      errors: [],
    };

    try {
      r2CleanupResult = await imageQueueManager.cleanup();
      console.log(
        `R2 cleanup completed: ${
          r2CleanupResult.removedFromQueue
        } from queue, ${
          r2CleanupResult.removedFromR2
        } from R2, freed ${Math.round(
          r2CleanupResult.freedSpace / 1024 / 1024
        )}MB`
      );

      if (r2CleanupResult.errors.length > 0) {
        console.warn(`R2 cleanup errors:`, r2CleanupResult.errors);
      }
    } catch (error) {
      console.error("Error during R2 cleanup:", error);
      r2CleanupResult.errors.push(`R2 cleanup failed: ${error}`);
    }

    console.timeEnd("cleanup-r2-queue");

    console.timeEnd("cleanup-expired-links");

    return NextResponse.json({
      success: true,
      message: `Updated ${updatedCount} expired share links (excluding active subscribers), and R2 cleanup: ${r2CleanupResult.removedFromQueue} queue items, ${r2CleanupResult.removedFromR2} R2 objects`,
      details: {
        expiredLinksUpdated: updatedCount,
        r2Cleanup: {
          removedFromQueue: r2CleanupResult.removedFromQueue,
          removedFromR2: r2CleanupResult.removedFromR2,
          freedSpaceMB: Math.round(r2CleanupResult.freedSpace / 1024 / 1024),
          errors: r2CleanupResult.errors,
        },
      },
    });
  } catch (error) {
    console.error("Error cleaning expired data:", error);
    return NextResponse.json(
      { error: "Failed to clean expired data", message: String(error) },
      { status: 500 }
    );
  }
}
