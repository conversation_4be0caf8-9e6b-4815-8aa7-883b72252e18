import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { syncUserToDB } from "@/lib/user-management";

// 获取会话信息
export async function GET(req: NextRequest) {
  const session = await auth();
  // console.log("session", session);

  // 如果用户已登录，同步数据到数据库
  if (session?.user) {
    try {
      await syncUserToDB({
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        username: session.user.username as string,
        githubId: session.user.githubId as string,
        avatarUrl: session.user.avatarUrl as string,
        image: session.user.image as string,
      });
    } catch (error) {
      console.error("同步用户数据失败:", error);
      // 不阻断会话响应
    }
  }

  return new NextResponse(JSON.stringify(session), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
      "Access-Control-Allow-Credentials": "true",
    },
  });
}

// 处理OPTIONS请求
export async function OPTIONS(req: NextRequest) {
  const response = new NextResponse(null, { status: 204 });

  // 添加CORS头
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type");
  response.headers.set("Access-Control-Allow-Credentials", "true");
  response.headers.set("Access-Control-Max-Age", "86400");

  return response;
}
