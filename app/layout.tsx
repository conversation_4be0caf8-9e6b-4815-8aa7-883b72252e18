import type { Metadata } from "next";
import "./globals.css";
import "./liquid-glass.css";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/components/auth/auth-provider";
import { auth } from "@/auth";
import { syncUserToDB } from "@/lib/user-management";
import { BackgroundProvider } from "@/contexts/background-context";
import { ShareLinksProvider } from "@/contexts/sharelinks-context";


export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NEXTAUTH_URL || "https://github-card.refined-x.workers.dev"
  ),
  title: {
    default: "Github Card",
    template: "%s | Github Card",
  },
  description:
    "Create beautiful cards showcasing your GitHub stats and contributions.",
  keywords: ["Github Card", "Github Stats", "Github Contributions"],
  icons: {
    icon: "/favicon/favicon.ico",
    apple: "/favicon/apple-touch-icon.png",
  },
  openGraph: {
    title: "Github Card",
    description:
      "Create beautiful cards showcasing your GitHub stats and contributions.",
    url:
      process.env.NEXTAUTH_URL || "https://github-card.refined-x.workers.dev",
    siteName: "Github Card",
    images: [
      {
        url: `${process.env.NEXTAUTH_URL ||
          "https://github-card.refined-x.workers.dev"
          }/og.png`,
        width: 1400,
        height: 735,
        alt: "Github Card Preview",
      },
    ],
    locale: "zh_CN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Github Card",
    description:
      "Create beautiful cards showcasing your GitHub stats and contributions.",
    creator: "@tower1229",
    site: "@tower1229",
    images: [
      `${process.env.NEXTAUTH_URL || "https://github-card.refined-x.workers.dev"
      }/og.png`,
    ],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  // 如果用户已登录，同步用户数据
  if (session?.user) {
    await syncUserToDB(session.user as any);
  }

  return (
    <html lang="en">
      <body>
        <AuthProvider>
          <BackgroundProvider>
            <ShareLinksProvider>
              {children}
            </ShareLinksProvider>
          </BackgroundProvider>
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  );
}
