"use client";

import React, { useState } from "react";
import { usePathname } from "next/navigation";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { DashboardMobileMenu } from "@/components/dashboard/DashboardMobileMenu";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  return (
    <div className="bg-background flex h-screen">
      {/* PC端侧边栏 */}
      <DashboardSidebar
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        currentPath={pathname}
      />

      {/* 主内容区域 */}
      <div className="flex flex-col flex-1 overflow-hidden">
        <DashboardHeader
          onMobileMenuToggle={() => setMobileMenuOpen(!mobileMenuOpen)}
        />
        <main className="flex-1 p-3 pb-16 overflow-auto sm:p-4 sm:pb-20 md:p-6 md:pb-6 lg:pb-6">
          {children}
        </main>
      </div>

      {/* 移动端抽屉菜单 */}
      <DashboardMobileMenu
        open={mobileMenuOpen}
        onOpenChange={setMobileMenuOpen}
        currentPath={pathname}
      />
    </div>
  );
}
