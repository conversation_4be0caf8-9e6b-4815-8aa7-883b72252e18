"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { ShareCenter } from "@/components/dashboard/ShareCenter";
import { Button } from "@/components/ui/button";
import { Share2, RefreshCw } from "lucide-react";
import { useSubscription } from "@/hooks/use-subscription";
import { useShareStats } from "@/hooks/use-dashboard-data";
import Loading from "@/components/loading";

export default function DashboardSharePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { isPro } = useSubscription();
  const [isRefreshing, setIsRefreshing] = useState(false);



  // 使用现有的 hook 获取分享统计数据
  const { data: shareStats, error: shareError, isLoading: shareLoading, refresh: refreshShareStats } = useShareStats();

  // 临时调试：监控数据状态
  useEffect(() => {
    console.log("Share page - useShareStats result:", {
      shareStats,
      shareError,
      shareLoading,
      sessionStatus: status,
      hasUserId: !!session?.user?.id
    });
  }, [shareStats, shareError, shareLoading, status, session?.user?.id]);



  // 分离加载状态：只有在 session 还在加载时才显示全屏 loading
  const isSessionLoading = status === "loading";
  const isDataLoading = shareLoading;
  const error = shareError;

  // 刷新所有数据
  const refreshAll = async () => {
    try {
      setIsRefreshing(true);
      await refreshShareStats();
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // 认证检查
  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/auth/signin");
      return;
    }
  }, [session, status, router]);



  // 只有在 session 加载时才显示全屏 loading
  if (isSessionLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loading />
      </div>
    );
  }

  // 未认证状态
  if (!session) {
    return null;
  }

  return (
    <div className="space-y-8">
      {/* 页面标题区域 */}
      <div className="mb-8">
        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary">
              <Share2 className="w-6 h-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Share Center
              </h1>
              <p className="text-muted-foreground">
                Manage your sharing links and templates
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">

            <Button
              variant="outline"
              size="sm"
              onClick={() => refreshAll()}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </div>
      </div>


      {/* 主要分享面板 */}
      <ShareCenter
        data={shareStats ? {
          sharing: {
            totalLinks: shareStats.totalLinks,
            activeLinks: shareStats.activeLinks,
            expiredLinks: shareStats.expiredLinks,
            mostUsedTemplate: shareStats.mostUsedTemplate,
            oldestLink: shareStats.oldestLink,
            newestLink: shareStats.newestLink,
          }
        } : null}
        loading={isDataLoading}
        error={!!error}
        onRefresh={refreshAll}
      />


    </div>
  );
}
