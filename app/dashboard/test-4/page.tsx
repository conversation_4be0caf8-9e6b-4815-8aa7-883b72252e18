"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSubscription } from "@/hooks/use-subscription";
import { useDashboardData } from "@/hooks/use-dashboard-data";
import { Crown, RefreshCw, Settings, TestTube } from "lucide-react";
import Loading from "@/components/loading";
import { DebugControlCenter } from "@/components/debug-platform";

export default function TestFourPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { isLoading: subscriptionLoading, isPro } = useSubscription();
  const { overview, isLoading: dataLoading, refreshAll } = useDashboardData();

  // 认证检查
  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/auth/signin");
      return;
    }
  }, [session, status, router]);

  // 加载状态
  if (status === "loading" || subscriptionLoading || dataLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loading />
      </div>
    );
  }

  // 未认证状态
  if (!session) {
    return null;
  }

  const isProUser = isPro || false;

  return (
    <div className="space-y-8">
      {/* Test Platform Header */}
      <div className="mb-8">
        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
          <div className="flex gap-3 items-center">
            <div className="bg-gradient-to-br rounded-xl flex from-purple-500 to-pink-600 h-12 shadow-lg text-white w-12 items-center justify-center">
              <TestTube className="h-7 w-7" />
            </div>
            <div>
              <div className="flex gap-2 items-center">
                <h1 className="font-bold tracking-tight text-3xl">
                  V5 AI Debug Platform
                </h1>
                {isProUser && (
                  <Badge
                    variant="secondary"
                    className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
                  >
                    <Crown className="h-3 mr-1 w-3" />
                    Pro
                  </Badge>
                )}
                <Badge
                  variant="outline"
                  className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                >
                  Beta
                </Badge>
              </div>
              <p className="text-muted-foreground">
                Interactive debugging and optimization platform for AI modules
              </p>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshAll}
              disabled={dataLoading}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${dataLoading ? "animate-spin" : ""}`}
              />
              Refresh Data
            </Button>
          </div>
        </div>
      </div>

      {/* Debug Control Center */}
      <DebugControlCenter />
    </div>
  );
}
