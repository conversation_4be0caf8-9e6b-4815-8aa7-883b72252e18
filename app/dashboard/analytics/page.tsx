"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { AnalyticsPanel } from "@/components/dashboard/AnalyticsPanel";
import { useDashboardData } from "@/hooks/use-dashboard-data";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BarChart3, TrendingUp, RefreshCw, Download } from "lucide-react";
import Loading from "@/components/loading";

export default function DashboardAnalyticsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 获取分析数据
  const { analytics, isAnalyticsLoading, error, refreshAll } =
    useDashboardData();

  // 包装的刷新函数，添加本地loading状态
  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await refreshAll();
    } catch (error) {
      console.error('Failed to refresh analytics data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // 认证检查
  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/auth/signin");
      return;
    }
  }, [session, status, router]);

  // 加载状态
  if (status === "loading" || isAnalyticsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loading />
      </div>
    );
  }

  // 未认证状态
  if (!session) {
    return null;
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <p className="text-destructive">Failed to load analytics data</p>
          <Button onClick={handleRefresh} variant="outline" disabled={isRefreshing}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 页面标题区域 */}
      <div className="mb-8">
        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary">
              <BarChart3 className="w-6 h-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
              <p className="text-muted-foreground">
                Advanced GitHub data analysis and insights
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""
                  }`}
              />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* 分析概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <TrendingUp className={`w-4 h-4 ${analytics?.overallMetrics?.growthTrend?.trend === "up"
                ? "text-green-500"
                : analytics?.overallMetrics?.growthTrend?.trend === "down"
                  ? "text-red-500"
                  : "text-yellow-500"
                }`} />
              Growth Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${analytics?.overallMetrics?.growthTrend?.trend === "up"
              ? "text-green-500"
              : analytics?.overallMetrics?.growthTrend?.trend === "down"
                ? "text-red-500"
                : "text-yellow-500"
              }`}>
              {analytics?.overallMetrics?.growthTrend?.percentage !== undefined
                ? `${analytics.overallMetrics.growthTrend.percentage > 0 ? '+' : ''}${analytics.overallMetrics.growthTrend.percentage}%`
                : "0%"
              }
            </div>
            <div className="text-xs text-muted-foreground">vs average user</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-blue-500" />
              Total Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">
              {analytics?.overallMetrics?.contributionScore
                ? Number(analytics.overallMetrics.contributionScore).toFixed(2)
                : "0.00"
              }
            </div>
            <div className="text-xs text-muted-foreground">contribution score</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-orange-500" />
              Rank Position
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-500">
              #{analytics?.overallMetrics?.rank || "N/A"}
            </div>
            <div className="text-xs text-muted-foreground">global ranking</div>
          </CardContent>
        </Card>
      </div>

      {/* 主要分析面板 */}
      <AnalyticsPanel
        data={analytics}
        loading={isAnalyticsLoading}
        error={!!error}
      />

      {/* 详细分析报告 */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Analysis Report</CardTitle>
          <p className="text-sm text-muted-foreground">
            Comprehensive breakdown of your GitHub activity and performance
            metrics
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Strengths</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• High commit frequency</li>
                  <li>• Diverse technology stack</li>
                  <li>• Active in open source</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Areas for Improvement</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Increase collaboration activities</li>
                  <li>• Improve documentation</li>
                  <li>• Engage more with community</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
