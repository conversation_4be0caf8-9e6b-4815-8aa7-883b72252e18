// V5.1 Dashboard Feature - Main Dashboard Page (Profile Integration)
// Created: 2025-01-30
// Updated: 2025-06-22 - Integrated profile content into main dashboard
// Purpose: 用户Dashboard主页面，整合用户资料和核心模块

"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { ProfileCard } from "@/components/dashboard/ProfileCard";
import { useDashboardData } from "@/hooks/use-dashboard-data";
import { calculateMultiDimensionScores } from "@/lib/github/score";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, Edit, RefreshCw, Github, Calendar, MapPin } from "lucide-react";
import { useSubscription } from "@/hooks/use-subscription";
import Loading from "@/components/loading";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const {
    isPro,
    subscription,
    isLoading: subscriptionLoading,
  } = useSubscription();

  // 获取Dashboard数据
  const { overview, isLoading, error, criticalError, refreshAll } =
    useDashboardData();

  // 认证检查
  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/auth/signin");
      return;
    }
  }, [session, status, router]);

  // 加载状态
  if (status === "loading" || isLoading || subscriptionLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loading />
      </div>
    );
  }

  // 未认证状态
  if (!session) {
    return null;
  }

  // 错误状态
  if (criticalError) {
    return (
      <div className="flex min-h-[400px] px-4 items-center justify-center">
        <div className="space-y-4 text-center">
          <p className="text-destructive">Failed to load dashboard data</p>
          <Button onClick={() => refreshAll()} variant="outline">
            <RefreshCw className="h-4 mr-2 w-4" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // 数据适配
  const userProfile = overview?.profile
    ? {
        username: overview.profile.username || overview.profile.login,
        login: overview.profile.login,
        displayName: overview.profile.name || overview.profile.login,
        name: overview.profile.name || overview.profile.login,
        avatarUrl: overview.profile.avatarUrl,
        bio: overview.profile.bio || "",
        location: overview.profile.location || "",
        blog: overview.profile.blog || "",
        twitterUsername: overview.profile.twitterUsername || "",
        followers: overview.profile.followers,
        following: overview.profile.following,
        publicRepos: overview.profile.publicRepos,
        totalStars: overview.profile.totalStars,
        totalForks: overview.profile.totalForks,
        contributionScore: overview.profile.contributionScore,
        commits: overview.profile.commits,
        pullRequests: overview.profile.pullRequests,
        issues: overview.profile.issues,
        reviews: overview.profile.reviews,
        contributedRepos: overview.profile.contributedRepos,
        languageStats: overview.profile.languageStats,
        createdAt: overview.profile.createdAt,
        updatedAt: overview.profile.updatedAt || Date.now(),
      }
    : null;
  const multiDimensionScore = overview?.profile
    ? calculateMultiDimensionScores({
        commits: overview.profile.commits,
        contributedRepos: overview.profile.contributedRepos,
        pullRequests: overview.profile.pullRequests,
        reviews: overview.profile.reviews,
        issues: overview.profile.issues,
        totalStars: overview.profile.totalStars,
        totalForks: overview.profile.totalForks,
        followers: overview.profile.followers,
        languageDiversity: overview.profile.languageStats?.totalLanguages || 0,
        publicRepos: overview.profile.publicRepos,
        following: overview.profile.following,
        createdAt: overview.profile.createdAt,
      })
    : null;

  return (
    <div className="space-y-4 md:space-y-6 lg:space-y-8">
      {/* 页面标题区域 */}
      <div className="mb-4 md:mb-6 lg:mb-8">
        <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
          <div className="flex gap-3 items-center">
            <div className="rounded-lg flex bg-primary/10 h-8 text-primary w-8 items-center justify-center md:h-10 md:w-10">
              <User className="h-4 w-4 md:h-6 md:w-6" />
            </div>
            <div>
              <h1 className="font-bold tracking-tight text-2xl md:text-3xl">
                Profile
              </h1>
              <p className="text-muted-foreground text-sm md:text-base">
                Manage your profile settings and GitHub information
              </p>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Button variant="outline" size="sm" className="text-xs md:text-sm">
              <Edit className="h-3 mr-1 w-3 md:h-4 md:mr-2 md:w-4" />
              <span className="hidden sm:inline">Edit Profile</span>
              <span className="sm:hidden">Edit</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refreshAll()}
              disabled={isLoading}
              className="text-xs md:text-sm"
            >
              <RefreshCw
                className={`w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2 ${
                  isLoading ? "animate-spin" : ""
                }`}
              />
              <span className="hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>
      </div>

      {/* 移动端优先布局 */}
      <div className="space-y-4 md:space-y-6 lg:space-y-0 lg:grid lg:gap-8 lg:grid-cols-3">
        {/* 移动端头像和基本信息卡片 - 在移动端显示在顶部 */}
        <div className="lg:hidden">
          <Card>
            <CardContent className="text-center p-4">
              <div className="mb-3">
                <img
                  src={
                    session.user?.image ||
                    overview?.profile?.avatarUrl ||
                    "/default-avatar.png"
                  }
                  alt="Profile Avatar"
                  className="border-background rounded-full mx-auto border-2 h-16 shadow-lg w-16 md:h-20 md:w-20"
                />
              </div>
              <h3 className="font-semibold text-base md:text-lg">
                {session.user?.name}
              </h3>
              <p className="text-muted-foreground text-xs md:text-sm">
                @{overview?.profile?.login}
              </p>
              <Button className="mt-3 text-xs w-full md:text-sm">
                <Edit className="h-3 mr-1 w-3 md:h-4 md:mr-2 md:w-4" />
                Edit Avatar
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 主要资料区域 */}
        <div className="space-y-4 md:space-y-6 lg:col-span-2">
          {/* 用户资料卡片 */}
          {userProfile && multiDimensionScore && (
            <ProfileCard
              userProfile={userProfile}
              multiDimensionScore={multiDimensionScore}
              loading={isLoading}
              error={error}
            />
          )}

          {/* GitHub信息 */}
          <Card>
            <CardHeader className="pb-3 md:pb-6">
              <CardTitle className="flex text-base gap-2 items-center md:text-lg">
                <Github className="h-4 w-4 md:h-5 md:w-5" />
                GitHub Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-0">
              {overview?.profile && (
                <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 md:gap-4">
                  <div className="space-y-3">
                    <div>
                      <div className="font-medium text-xs text-muted-foreground md:text-sm">
                        Username
                      </div>
                      <div className="font-medium text-sm break-all md:text-base">
                        {overview.profile.login}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-xs text-muted-foreground md:text-sm">
                        Display Name
                      </div>
                      <div className="font-medium text-sm break-words md:text-base">
                        {overview.profile.name || "Not set"}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-xs text-muted-foreground md:text-sm">
                        Bio
                      </div>
                      <div className="font-medium text-sm break-words md:text-base">
                        {overview.profile.bio || "No bio available"}
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <div className="font-medium text-xs text-muted-foreground md:text-sm">
                        Public Repos
                      </div>
                      <div className="font-medium text-sm md:text-base">
                        {overview.profile.publicRepos}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-xs text-muted-foreground md:text-sm">
                        Followers
                      </div>
                      <div className="font-medium text-sm md:text-base">
                        {overview.profile.followers}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-xs text-muted-foreground md:text-sm">
                        Following
                      </div>
                      <div className="font-medium text-sm md:text-base">
                        {overview.profile.following}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 账户信息 */}
          <Card>
            <CardHeader className="pb-3 md:pb-6">
              <CardTitle className="text-base md:text-lg">
                Account Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-0">
              <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 md:gap-4">
                <div className="space-y-3">
                  <div>
                    <div className="font-medium text-xs text-muted-foreground md:text-sm">
                      Email
                    </div>
                    <div className="font-medium text-sm break-all md:text-base">
                      {session.user?.email}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-xs text-muted-foreground md:text-sm">
                      Plan
                    </div>
                    <div className="flex font-medium text-sm gap-2 items-center md:text-base">
                      {isPro ? "Pro" : "Free"}
                      {isPro && (
                        <span className="rounded bg-primary/10 text-xs text-primary py-1 px-2">
                          Pro
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <div className="font-medium text-xs text-muted-foreground md:text-sm">
                      Member Since
                    </div>
                    <div className="flex font-medium text-sm gap-2 items-center md:text-base">
                      <Calendar className="h-3 w-3 md:h-4 md:w-4" />
                      {overview?.profile?.createdAt
                        ? new Date(
                            overview.profile.createdAt
                          ).toLocaleDateString()
                        : "Unknown"}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-xs text-muted-foreground md:text-sm">
                      Location
                    </div>
                    <div className="flex font-medium text-sm gap-2 items-center break-words md:text-base">
                      <MapPin className="flex-shrink-0 h-3 w-3 md:h-4 md:w-4" />
                      <span className="break-words">
                        {overview?.profile?.location || "Not specified"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏 - 桌面端显示 */}
        <div className="space-y-4 hidden md:space-y-6 lg:block">
          {/* 头像和基本信息 */}
          <Card>
            <CardContent className="text-center p-6">
              <div className="mb-4">
                <img
                  src={
                    session.user?.image ||
                    overview?.profile?.avatarUrl ||
                    "/default-avatar.png"
                  }
                  alt="Profile Avatar"
                  className="border-background rounded-full mx-auto border-4 h-24 shadow-lg w-24"
                />
              </div>
              <h3 className="font-semibold text-lg">{session.user?.name}</h3>
              <p className="text-muted-foreground text-sm">
                @{overview?.profile?.login}
              </p>
              <Button className="mt-4 w-full">
                <Edit className="h-4 mr-2 w-4" />
                Edit Avatar
              </Button>
            </CardContent>
          </Card>

          {/* 统计信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Profile Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex text-sm justify-between">
                <span className="text-muted-foreground">Profile Views</span>
                <span className="font-medium">1,234</span>
              </div>
              <div className="flex text-sm justify-between">
                <span className="text-muted-foreground">Cards Generated</span>
                <span className="font-medium">56</span>
              </div>
              <div className="flex text-sm justify-between">
                <span className="text-muted-foreground">Links Shared</span>
                <span className="font-medium">23</span>
              </div>
              <div className="flex text-sm justify-between">
                <span className="text-muted-foreground">Last Updated</span>
                <span className="font-medium">2 hours ago</span>
              </div>
            </CardContent>
          </Card>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Github className="h-4 mr-2 w-4" />
                Sync GitHub Data
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <User className="h-4 mr-2 w-4" />
                Update Profile
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <RefreshCw className="h-4 mr-2 w-4" />
                Refresh Cache
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 移动端快速操作 - 显示在底部 */}
        <div className="lg:hidden">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 pt-0">
              <div className="grid gap-2 grid-cols-1 sm:grid-cols-3">
                <Button variant="outline" className="text-xs justify-start">
                  <Github className="h-3 mr-2 w-3" />
                  Sync Data
                </Button>
                <Button variant="outline" className="text-xs justify-start">
                  <User className="h-3 mr-2 w-3" />
                  Update Profile
                </Button>
                <Button variant="outline" className="text-xs justify-start">
                  <RefreshCw className="h-3 mr-2 w-3" />
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
