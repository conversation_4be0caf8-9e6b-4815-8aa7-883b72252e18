"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Settings,
  Bell,
  Shield,
  Palette,
  Database,
  RefreshCw,
} from "lucide-react";
import { useSubscription } from "@/hooks/use-subscription";
import Loading from "@/components/loading";

export default function DashboardSettingsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { isPro } = useSubscription();

  // 认证检查
  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/auth/signin");
      return;
    }
  }, [session, status, router]);

  // 加载状态
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loading />
      </div>
    );
  }

  // 未认证状态
  if (!session) {
    return null;
  }

  return (
    <div className="space-y-8">
      {/* 页面标题区域 */}
      <div className="mb-8">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary">
            <Settings className="w-6 h-6" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">
              Manage your account and application preferences
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 通知设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-sm">Email notifications</div>
                <div className="text-xs text-muted-foreground">
                  Receive updates about your account
                </div>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-sm">AI generation alerts</div>
                <div className="text-xs text-muted-foreground">
                  Notify when AI descriptions are ready
                </div>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-sm">Sharing updates</div>
                <div className="text-xs text-muted-foreground">
                  Updates about your shared links
                </div>
              </div>
              <Switch />
            </div>
          </CardContent>
        </Card>

        {/* 隐私设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Privacy & Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-sm">Public profile</div>
                <div className="text-xs text-muted-foreground">
                  Allow others to find your profile
                </div>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-sm">Analytics tracking</div>
                <div className="text-xs text-muted-foreground">
                  Help improve the service
                </div>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-sm">Data sharing</div>
                <div className="text-xs text-muted-foreground">
                  Share anonymous usage data
                </div>
              </div>
              <Switch />
            </div>
          </CardContent>
        </Card>

        {/* 外观设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              Appearance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="font-medium text-sm">Theme</div>
              <div className="grid grid-cols-3 gap-2">
                <Button variant="outline" size="sm">
                  Light
                </Button>
                <Button variant="outline" size="sm">
                  Dark
                </Button>
                <Button variant="outline" size="sm">
                  System
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <div className="font-medium text-sm">Card Style</div>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm">
                  Modern
                </Button>
                <Button variant="outline" size="sm">
                  Classic
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 数据管理 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Data Management
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh GitHub Data
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Database className="w-4 h-4 mr-2" />
                Export My Data
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                <Database className="w-4 h-4 mr-2" />
                Delete Account
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 订阅管理 */}
      <Card>
        <CardHeader>
          <CardTitle>Subscription Management</CardTitle>
          <p className="text-sm text-muted-foreground">
            Manage your subscription plan and billing
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">
                Current Plan: {isPro ? "Pro" : "Free"}
              </div>
              <div className="text-sm text-muted-foreground">
                {isPro
                  ? "Enjoy all premium features"
                  : "Upgrade to unlock premium features"}
              </div>
            </div>
            <Button>{isPro ? "Manage Subscription" : "Upgrade to Pro"}</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
