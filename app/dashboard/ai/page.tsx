"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSubscription } from "@/hooks/use-subscription";
import { useDashboardData } from "@/hooks/use-dashboard-data";
import { Bot, Crown, RefreshCw } from "lucide-react";
import Loading from "@/components/loading";
import { AIDescriptionEngine } from "@/components/ai-description/AIDescriptionEngine";

export default function DashboardAIPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { isLoading: subscriptionLoading, isPro } = useSubscription();
  const { overview, isLoading: dataLoading, refreshAll } = useDashboardData();

  // 认证检查
  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/auth/signin");
      return;
    }
  }, [session, status, router]);

  // 加载状态
  if (status === "loading" || subscriptionLoading || dataLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loading />
      </div>
    );
  }

  // 未认证状态
  if (!session) {
    return null;
  }

  const isProUser = isPro || false;

  return (
    <div className="space-y-8">
      {/* AI Control Panel Header */}
      <div className="mb-8">
        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
          <div className="flex gap-3 items-center">
            <div className="bg-gradient-to-br rounded-xl flex from-blue-500 to-purple-600 h-12 shadow-lg text-white w-12 items-center justify-center">
              <Bot className="h-7 w-7" />
            </div>
            <div>
              <div className="flex gap-2 items-center">
                <h1 className="font-bold tracking-tight text-3xl">
                  AI Control Panel
                </h1>
                {isProUser && (
                  <Badge
                    variant="secondary"
                    className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
                  >
                    <Crown className="h-3 mr-1 w-3" />
                    Pro
                  </Badge>
                )}
              </div>
              <p className="text-muted-foreground">
                V5 AI Architecture Under Development - Coming Soon!
              </p>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshAll}
              disabled={dataLoading}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${dataLoading ? "animate-spin" : ""}`}
              />
              Refresh Data
            </Button>
          </div>
        </div>
      </div>

      {/* V5 AI Description Engine */}
      <AIDescriptionEngine
        userId={session.user.id}
        username={session.user.name || "User"}
        isProUser={isProUser}
        onDescriptionUpdate={(description) => {
          // Handle description update
          console.log("New description:", description);
        }}
      />

      {/* Temporary Stats Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex gap-2 items-center">
            <Bot className="h-5 w-5" />
            Current AI Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="bg-gradient-to-br rounded-lg from-blue-50 to-blue-100 p-4 dark:from-blue-950 dark:to-blue-900">
              <div className="font-bold text-2xl text-blue-600 dark:text-blue-400">
                0
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Total Generations
              </p>
            </div>

            <div className="bg-gradient-to-br rounded-lg from-purple-50 to-purple-100 p-4 dark:from-purple-950 dark:to-purple-900">
              <div className="font-bold text-2xl text-purple-600 dark:text-purple-400">
                {overview?.ai?.lastGenerated ? "Active" : "Ready"}
              </div>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                System Status
              </p>
            </div>

            <div className="bg-gradient-to-br rounded-lg from-green-50 to-green-100 p-4 dark:from-green-950 dark:to-green-900">
              <div className="font-bold text-2xl text-green-600 dark:text-green-400">
                {isProUser ? "Unlimited" : "Limited"}
              </div>
              <p className="text-sm text-green-700 dark:text-green-300">
                Usage Plan
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
