"use client";

import { createContext, useContext, useState, ReactNode, useMemo } from "react";
import { useBackground } from "./background-context";
import { fetchWithAuth } from "@/utils";
import { ShareLinkResponse } from "@/app/api/share-links/route";

type ShareLinksContextType = {
  shareLink: ShareLinkResponse | undefined;
  generateShareLink: (
    templateType: string,
    forceUpdate?: boolean
  ) => Promise<any>;
  backgroundId: string | null;
  loading: boolean;
};

const ShareLinksContext = createContext<ShareLinksContextType | undefined>(
  undefined
);

export function ShareLinksProvider({ children }: { children: ReactNode }) {
  const [shareLink, setShareLink] = useState<ShareLinkResponse | undefined>(
    undefined
  );

  const [loading, setLoading] = useState<boolean>(false);
  const { backgroundId } = useBackground();

  // 使用 useMemo 来避免在每次渲染时都创建新的 contextValue
  const contextValue = useMemo(() => {
    const generateShareLink = async (
      templateType: string,
      forceUpdate = false
    ) => {
      try {
        setLoading(true);
        console.log("Generating share link with:", {
          templateType,
          backgroundId,
          forceUpdate,
        });
        const response = await fetchWithAuth("/api/share-links", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ templateType, backgroundId, forceUpdate }),
        });

        if (!response.ok) {
          const error = await response.text();
          throw new Error(`Failed to generate share link: ${error}`);
        }

        const data = (await response.json()) as ShareLinkResponse;
        console.log("Share link generated:", data.shareLink);
        setShareLink(data);
        setLoading(false);
        return data;
      } catch (error) {
        console.error("Error in generateShareLink:", error);
        setShareLink(undefined);
        setLoading(false);
        throw error;
      }
    };

    return {
      shareLink,
      generateShareLink,
      backgroundId,
      loading,
    };
  }, [shareLink, backgroundId]); // 添加所有依赖项

  return (
    <ShareLinksContext.Provider value={contextValue}>
      {children}
    </ShareLinksContext.Provider>
  );
}

export function useShareLinks() {
  const context = useContext(ShareLinksContext);
  if (context === undefined) {
    throw new Error("useShareLinks must be used within a ShareLinksProvider");
  }
  return context;
}
