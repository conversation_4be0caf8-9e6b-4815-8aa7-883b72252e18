"use client";

import { createContext, useContext, useCallback, useState, useEffect, useRef, ReactNode } from 'react';

type BackgroundContextType = {
  refreshBackground: () => void;
  version: number;
  backgroundId: string | null;
  setCurrentBackgroundId: (id: string | null) => void;
};

const BackgroundContext = createContext<BackgroundContextType | undefined>(undefined);

export function BackgroundProvider({ children }: { children: ReactNode }) {
  const [version, setVersion] = useState(0);
  const isInitialMount = useRef(true);
  const [backgroundId, setBackgroundId] = useState<string | null>(null);

  const refreshBackground = useCallback(() => {
    setVersion(prev => prev + 1);
  }, []);

  // Skip the first effect on mount to prevent double fetch
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
  }, [version]);

  const setCurrentBackgroundId = useCallback((id: string | null) => {
    setBackgroundId(id);
  }, []);

  return (
    <BackgroundContext.Provider value={{ refreshBackground, version, backgroundId, setCurrentBackgroundId   }}>
      {children}
    </BackgroundContext.Provider>
  );
}

export function useBackground() {
  const context = useContext(BackgroundContext);
  if (context === undefined) {
    throw new Error('useBackground must be used within a BackgroundProvider');
  }
  return context;
}
