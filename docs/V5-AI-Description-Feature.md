# GitHub Card V5 - AI Description Feature

## 产品升级方案文档

_Version: 1.0 | Created: 2024-12-22 | Status: Planning_

---

## 📋 功能概述

V5 版本将引入 AI 驱动的个性化描述生成功能，基于用户独特的 GitHub 数据生成具有吸引力的技术档案描述，激发用户分享欲望，提升平台活跃度和影响力。

### 🎯 核心价值主张

- **个性化技术档案**: 基于多维度数据分析生成独特描述
- **增强分享意愿**: 吸引力文案激发用户主动分享
- **智能内容策略**: 视角独特且具有说服力的表达方式
- **Pro 用户增值**: 高级定制化功能驱动付费转化

---

## 🏗️ 技术架构设计

### 🔧 AI 集成架构

```mermaid
graph TB
    A[GitHub Data] --> B[Data Processor]
    B --> C[AI Prompt Generator]
    C --> D[DeepSeek API]
    D --> E[Response Parser]
    E --> F[Description Storage]
    F --> G[Frontend Display]

    H[User Action] --> I[Description Manager]
    I --> J[Custom Editor]
    J --> K[Apply Changes]
    K --> F

    subgraph "AI Core Service"
        C
        D
        E
    end

    subgraph "User Control"
        I
        J
        K
    end
```

### 🎨 组件架构图

```mermaid
graph LR
    A[ProfileCard] --> B[AIDescriptionSection]
    B --> C[DescriptionDisplay]
    B --> D[RegenerateButton]
    B --> E[CustomizeButton]

    E --> F[DescriptionEditor]
    F --> G[MarkdownPreview]
    F --> H[ApplyButton]
    F --> I[CancelButton]

    subgraph "Pro Features"
        E
        F
        G
    end
```

---

## 🧠 AI 实现方案

### 1. DeepSeek 集成策略

#### 🔌 API 集成方式

```typescript
// 简单且稳健的HTTP客户端
class DeepSeekClient {
  private apiKey: string;
  private baseUrl = "https://api.deepseek.com/v1";

  async generateDescription(prompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 300,
        temperature: 0.8,
      }),
    });

    const data = await response.json();
    return data.choices[0].message.content;
  }
}
```

#### 📝 智能 Prompt 设计

```typescript
interface PromptTemplate {
  generatePrompt(userData: GitHubAnalysisData): string;
}

class PersonalityDrivenPrompt implements PromptTemplate {
  generatePrompt(data: GitHubAnalysisData): string {
    const { scores, profile, expertise } = data;

    return `
    Create an engaging developer description for ${profile.username} based on:

    **Technical Profile:**
    - ${scores.commitScore}/100 Code Creation Excellence
    - ${scores.collaborationScore}/100 Community Collaboration
    - ${scores.influenceScore}/100 Open Source Impact
    - ${scores.explorationScore}/100 Technology Exploration

    **Key Achievements:**
    - ${profile.totalStars} total stars across projects
    - ${profile.commits} commits showing consistent development
    - ${expertise.languages} programming languages mastered
    - ${expertise.bestDimension} as strongest technical dimension

    **Style Requirements:**
    - Professional yet approachable tone
    - Highlight unique strengths and technical personality
    - Focus on impact and potential rather than just metrics
    - 2-3 sentences maximum, compelling and shareable
    - Avoid clichés, be specific and authentic

    Generate a description that makes other developers want to connect and collaborate.
    `;
  }
}
```

### 2. 多样化描述策略

#### 🎭 描述风格类型

```typescript
enum DescriptionStyle {
  TECHNICAL_EXPERT = "technical-expert", // 技术专家型
  COMMUNITY_BUILDER = "community-builder", // 社区建设者
  INNOVATION_PIONEER = "innovation-pioneer", // 创新先锋
  LEARNING_ENTHUSIAST = "learning-enthusiast", // 学习者
}

class StyleBasedPromptGenerator {
  generateByStyle(data: GitHubAnalysisData, style: DescriptionStyle): string {
    const basePrompt = this.getBasePrompt(data);

    switch (style) {
      case DescriptionStyle.TECHNICAL_EXPERT:
        return `${basePrompt}\nFocus: Deep technical expertise, code quality, architectural thinking.`;

      case DescriptionStyle.COMMUNITY_BUILDER:
        return `${basePrompt}\nFocus: Collaboration, mentoring, open source contributions, community impact.`;

      case DescriptionStyle.INNOVATION_PIONEER:
        return `${basePrompt}\nFocus: Cutting-edge technology adoption, creative solutions, trendsetting.`;

      case DescriptionStyle.LEARNING_ENTHUSIAST:
        return `${basePrompt}\nFocus: Continuous learning, technology exploration, growth mindset.`;
    }
  }
}
```

---

## 🗄️ 数据存储设计

### 📊 数据库扩展

```sql
-- AI描述存储表
CREATE TABLE ai_descriptions (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

  -- 生成的描述内容
  generated_description TEXT NOT NULL,
  description_style TEXT NOT NULL, -- technical-expert, community-builder, etc.

  -- 生成时的数据快照
  data_snapshot TEXT NOT NULL, -- JSON格式的GitHub数据
  prompt_used TEXT NOT NULL,
  ai_model_version TEXT NOT NULL DEFAULT 'deepseek-chat',

  -- 用户自定义内容
  custom_description TEXT, -- 用户编辑后的版本
  is_custom_applied BOOLEAN NOT NULL DEFAULT FALSE,

  -- 元数据
  generated_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL,
  expires_at INTEGER NOT NULL, -- 描述过期时间，需要重新生成

  -- 性能优化索引
  UNIQUE(user_id) -- 每个用户只有一条最新记录
);

-- 描述生成历史表（可选，用于分析和改进）
CREATE TABLE description_history (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  description_content TEXT NOT NULL,
  generation_type TEXT NOT NULL, -- 'ai-generated', 'user-customized'
  created_at INTEGER NOT NULL
);
```

### 📋 TypeScript 类型定义

```typescript
// AI描述相关类型
export interface AIDescription {
  id: string;
  userId: string;
  generatedDescription: string;
  descriptionStyle: DescriptionStyle;
  dataSnapshot: string; // JSON string
  promptUsed: string;
  aiModelVersion: string;
  customDescription?: string;
  isCustomApplied: boolean;
  generatedAt: number;
  updatedAt: number;
  expiresAt: number;
}

export interface DescriptionGenerationRequest {
  userId: string;
  forceRegenerate?: boolean;
  preferredStyle?: DescriptionStyle;
}

export interface DescriptionUpdateRequest {
  userId: string;
  customDescription: string;
  applyChanges: boolean;
}
```

---

## 🔄 业务流程设计

### 1. 自动生成流程

```mermaid
sequenceDiagram
    participant U as User
    participant API as GitHub Data API
    participant AI as AI Service
    participant DB as Database
    participant UI as Frontend

    U->>API: Fetch GitHub Data
    API->>AI: Generate Description Request
    AI->>AI: Analyze Multi-dimension Scores
    AI->>AI: Generate Prompt Based on Data
    AI->>AI: Call DeepSeek API
    AI->>DB: Store Generated Description
    API->>UI: Return Complete Profile Data
    UI->>U: Display Profile with AI Description
```

### 2. 用户自定义流程

```mermaid
sequenceDiagram
    participant U as User (Pro)
    participant UI as Frontend
    participant API as Backend API
    participant DB as Database

    U->>UI: Click "Customize Description"
    UI->>API: Get Current Description
    API->>DB: Fetch AI Description
    DB->>UI: Return Description Data
    U->>UI: Edit in Markdown Editor
    UI->>U: Show Live Preview
    U->>UI: Click "Apply Changes"
    UI->>API: Update Description Request
    API->>DB: Save Custom Description
    DB->>UI: Confirm Update
    UI->>U: Show Updated Profile
```

---

## 🛠️ 核心 API 设计

### 1. 描述生成 API

```typescript
// API路由: /api/ai-description/generate
export async function POST(request: Request) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { forceRegenerate, preferredStyle } = await request.json();

    // 检查现有描述是否过期
    const existingDescription = await getAIDescription(session.user.id);
    if (
      existingDescription &&
      !isExpired(existingDescription) &&
      !forceRegenerate
    ) {
      return Response.json({
        description: existingDescription.isCustomApplied
          ? existingDescription.customDescription
          : existingDescription.generatedDescription,
        source: existingDescription.isCustomApplied ? "custom" : "ai",
      });
    }

    // 获取最新GitHub数据
    const githubData = await fetchGitHubContributions(session.user.username);
    const multiDimensionScores = calculateMultiDimensionScores(githubData);

    // 生成AI描述
    const aiDescription = await generateAIDescription({
      githubData,
      multiDimensionScores,
      style: preferredStyle,
    });

    // 存储到数据库
    await saveAIDescription({
      userId: session.user.id,
      description: aiDescription,
      dataSnapshot: JSON.stringify({ githubData, multiDimensionScores }),
      style: preferredStyle || determineOptimalStyle(multiDimensionScores),
    });

    return Response.json({
      description: aiDescription,
      source: "ai",
      style: preferredStyle,
    });
  } catch (error) {
    console.error("AI Description generation failed:", error);
    return Response.json(
      {
        error: "Failed to generate description",
        fallback: generateFallbackDescription(session.user.username),
      },
      { status: 500 }
    );
  }
}
```

### 2. 描述定制 API

```typescript
// API路由: /api/ai-description/customize
export async function POST(request: Request) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 检查Pro权限
    const subscription = await getUserSubscription(session.user.id);
    if (!subscription || subscription.status !== "active") {
      return Response.json(
        {
          error: "Pro subscription required",
          upgradeUrl: "/subscription",
        },
        { status: 403 }
      );
    }

    const { customDescription, applyChanges } = await request.json();

    // 验证描述长度和内容
    if (!validateDescription(customDescription)) {
      return Response.json(
        {
          error: "Invalid description format",
        },
        { status: 400 }
      );
    }

    // 更新数据库
    await updateAIDescription(session.user.id, {
      customDescription,
      isCustomApplied: applyChanges,
      updatedAt: Date.now(),
    });

    return Response.json({
      success: true,
      description: applyChanges ? customDescription : null,
    });
  } catch (error) {
    console.error("Description customization failed:", error);
    return Response.json(
      {
        error: "Failed to update description",
      },
      { status: 500 }
    );
  }
}
```

---

## 🎨 前端组件设计

### 1. AI 描述展示组件

```typescript
interface AIDescriptionSectionProps {
  userId: string;
  username: string;
  currentDescription?: string;
  isProUser: boolean;
  onDescriptionUpdate?: (description: string) => void;
}

export function AIDescriptionSection({
  userId,
  username,
  currentDescription,
  isProUser,
  onDescriptionUpdate,
}: AIDescriptionSectionProps) {
  const [description, setDescription] = useState(currentDescription);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const regenerateDescription = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/ai-description/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ forceRegenerate: true }),
      });

      const data = await response.json();
      setDescription(data.description);
      onDescriptionUpdate?.(data.description);
    } catch (error) {
      toast.error("Failed to regenerate description");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="ai-description-section">
      <div className="description-header">
        <h3 className="font-semibold text-lg">AI-Generated Profile</h3>
        <div className="description-actions">
          <Button
            variant="outline"
            size="sm"
            onClick={regenerateDescription}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 animate-spin w-4" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Regenerate
          </Button>

          {isProUser && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Edit3 className="h-4 w-4" />
              Customize
            </Button>
          )}
        </div>
      </div>

      <div className="description-content">
        {description ? (
          <p className="text-sm text-muted-foreground leading-relaxed">
            {description}
          </p>
        ) : (
          <div className="description-placeholder">
            <Sparkles className="h-8 text-muted-foreground/50 w-8" />
            <p className="text-sm text-muted-foreground">
              AI description will be generated based on your GitHub activity
            </p>
          </div>
        )}
      </div>

      {isEditing && isProUser && (
        <DescriptionEditor
          initialDescription={description || ""}
          onSave={(newDescription, apply) => {
            // Handle save logic
            setDescription(apply ? newDescription : description);
            setIsEditing(false);
          }}
          onCancel={() => setIsEditing(false)}
        />
      )}
    </div>
  );
}
```

### 2. 描述编辑器组件

```typescript
interface DescriptionEditorProps {
  initialDescription: string;
  onSave: (description: string, apply: boolean) => void;
  onCancel: () => void;
}

export function DescriptionEditor({
  initialDescription,
  onSave,
  onCancel,
}: DescriptionEditorProps) {
  const [content, setContent] = useState(initialDescription);
  const [isPreview, setIsPreview] = useState(false);

  const handleSave = async (apply: boolean) => {
    try {
      const response = await fetch("/api/ai-description/customize", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          customDescription: content,
          applyChanges: apply,
        }),
      });

      if (response.ok) {
        onSave(content, apply);
        toast.success(apply ? "Description updated" : "Draft saved");
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to save description");
      }
    } catch (error) {
      toast.error("Failed to save description");
    }
  };

  return (
    <div className="description-editor">
      <div className="editor-header">
        <div className="editor-tabs">
          <Button
            variant={!isPreview ? "default" : "ghost"}
            size="sm"
            onClick={() => setIsPreview(false)}
          >
            Edit
          </Button>
          <Button
            variant={isPreview ? "default" : "ghost"}
            size="sm"
            onClick={() => setIsPreview(true)}
          >
            Preview
          </Button>
        </div>
      </div>

      <div className="editor-content">
        {isPreview ? (
          <div className="preview-pane">
            <p className="text-sm leading-relaxed">{content}</p>
          </div>
        ) : (
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Customize your description..."
            className="min-h-[120px] resize-none"
            maxLength={280}
          />
        )}
      </div>

      <div className="editor-footer">
        <div className="character-count">
          <span
            className={`text-xs ${
              content.length > 250
                ? "text-destructive"
                : "text-muted-foreground"
            }`}
          >
            {content.length}/280
          </span>
        </div>

        <div className="editor-actions">
          <Button variant="outline" size="sm" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleSave(false)}>
            Save Draft
          </Button>
          <Button size="sm" onClick={() => handleSave(true)}>
            Apply Changes
          </Button>
        </div>
      </div>
    </div>
  );
}
```

---

## 🚀 实施计划

### Phase 1: 基础架构搭建 (1-2 周)

- [ ] 数据库 schema 扩展
- [ ] DeepSeek API 集成和测试
- [ ] 基础 AI 描述生成逻辑
- [ ] 简单的前端展示组件

### Phase 2: 核心功能开发 (2-3 周)

- [ ] 智能 Prompt 设计和优化
- [ ] 多样化描述风格实现
- [ ] 缓存和性能优化
- [ ] 错误处理和降级策略

### Phase 3: Pro 功能开发 (1-2 周)

- [ ] 描述定制编辑器
- [ ] 实时预览功能
- [ ] 权限验证和付费墙
- [ ] 用户体验优化

### Phase 4: 测试和优化 (1 周)

- [ ] A/B 测试不同描述风格
- [ ] 性能监控和优化
- [ ] 用户反馈收集
- [ ] 最终调优和发布

---

## 💰 成本估算

### 🔧 技术成本

- **DeepSeek API**: ~$0.002/1K tokens，预估月成本 $50-200
- **额外存储**: 描述数据存储成本忽略不计
- **计算资源**: 现有 Cloudflare Workers 足够

### 🎯 预期 ROI

- **Pro 订阅转化率提升**: +15-25%
- **用户分享活跃度**: +30-40%
- **平台影响力扩展**: 通过高质量描述吸引更多开发者

---

## 🛡️ 风险控制

### 🚨 技术风险缓解

1. **API 限流**: 实施请求频率限制，避免过度调用
2. **降级策略**: AI 服务不可用时提供模板化描述
3. **内容审核**: 实施基础的内容过滤机制
4. **缓存策略**: 30 天有效期，减少重复请求

### 📋 质量保证

1. **人工 review 样本**: 定期抽查生成描述质量
2. **用户反馈机制**: 收集用户对描述的满意度
3. **多轮优化**: 基于数据不断改进 Prompt 策略

---

## 📊 监控指标

### 🎯 关键指标

- **生成成功率**: >95%
- **用户满意度**: >4.0/5.0
- **描述分享率**: 监控包含 AI 描述的分享次数
- **Pro 转化提升**: 对比引入前后的付费转化率

### 📈 业务指标

- **用户留存**: 有 AI 描述的用户 7 日留存率
- **分享活跃度**: 描述相关的社交分享增长
- **平台影响力**: 通过高质量描述吸引的新用户数

---

## 🎨 设计原则

### 💡 用户体验原则

1. **渐进式增强**: AI 描述作为锦上添花，不影响现有功能
2. **简单易用**: 一键生成，Pro 用户可深度定制
3. **透明可控**: 用户始终知道内容来源，可随时修改
4. **价值导向**: 聚焦于提升用户的技术形象和分享欲望

### 🔧 技术实现原则

1. **稳健优先**: 降级策略确保功能可用性
2. **性能第一**: 合理缓存，避免重复计算
3. **扩展性强**: 为未来更多 AI 功能预留架构空间
4. **成本控制**: 智能的 API 调用策略控制运营成本

---

## 📊 Pro 用户功能增强

### 📊 用户 Dashboard

```mermaid
graph TB
    A[User Dashboard] --> B[Profile Overview]
    A --> C[AI Description Panel]
    A --> D[Stats & Analytics]
    A --> E[Sharing Center]

    B --> B1[Basic Profile Info]
    B --> B2[Multi-dimension Scores]

    C --> C1[Description Generator]
    C --> C2[Style Mixer]
    C --> C3[Length Adapter]
    C --> C4[History & Versions]

    D --> D1[Engagement Metrics]
    D --> D2[Profile Views]
    D --> D3[Share Analytics]

    E --> E1[Share Templates]
    E --> E2[Social Platforms]
    E --> E3[Link Management]

    subgraph "Pro Features"
        C1
        C2
        C3
        C4
    end
```

#### 🎛️ Dashboard 核心功能

1. **个人资料概览**: 展示用户基本信息和多维度评分（所有用户可用）
2. **AI 描述面板**: 集成所有 AI 描述相关功能（仅限 Pro 用户）
3. **统计与分析**: 提供用户参与度和分享数据分析（所有用户可用）
4. **分享中心**: 管理和优化社交媒体分享（所有用户可用）

#### 📱 响应式设计

```typescript
// Dashboard布局组件
export function UserDashboard({ userId, username, isPro }: UserDashboardProps) {
  return (
    <div className="dashboard-container">
      <DashboardHeader username={username} isPro={isPro} />

      <div className="dashboard-grid">
        <ProfileOverviewCard />
        {isPro ? <AIDescriptionPanel /> : <AIDescriptionUpgradeCard />}
        <StatsAnalyticsCard />
        <SharingCenterCard />
      </div>

      <DashboardFooter />
    </div>
  );
}
```

### 🔒 非 Pro 用户升级提示卡

```typescript
// 升级提示卡组件
export function AIDescriptionUpgradeCard() {
  return (
    <div className="ai-description-upgrade-card">
      <div className="card-header">
        <h3 className="font-semibold text-lg">AI Profile Description</h3>
        <Badge variant="premium">Pro Feature</Badge>
      </div>

      <div className="card-content">
        <div className="feature-preview">
          <Sparkles className="h-12 text-amber-500/80 w-12" />
          <p className="mt-4 text-sm text-muted-foreground">
            Enhance your GitHub profile with AI-generated descriptions tailored
            to your unique contributions and coding style.
          </p>
        </div>

        <div className="mt-6 feature-highlights">
          <h4 className="font-medium text-sm mb-2">Pro Features Include:</h4>
          <ul className="space-y-1 text-xs">
            <li className="flex items-center">
              <CheckCircle className="h-3 mr-2 text-green-500 w-3" />
              Personalized AI descriptions based on your GitHub data
            </li>
            <li className="flex items-center">
              <CheckCircle className="h-3 mr-2 text-green-500 w-3" />
              Mix different personality styles to match your preferences
            </li>
            <li className="flex items-center">
              <CheckCircle className="h-3 mr-2 text-green-500 w-3" />
              Optimize description length for different platforms
            </li>
            <li className="flex items-center">
              <CheckCircle className="h-3 mr-2 text-green-500 w-3" />
              Custom editing and fine-tuning capabilities
            </li>
          </ul>
        </div>
      </div>

      <div className="mt-6 card-footer">
        <Button className="w-full" variant="premium">
          <Sparkles className="h-4 mr-2 w-4" />
          Upgrade to Pro
        </Button>
      </div>
    </div>
  );
}
```

### 🎭 增强版 AI 描述生成面板 (Pro 专享)

```mermaid
graph LR
    A[AI Description Panel] --> B[Generated Description]
    A --> C[Style Controls]
    A --> D[Length Controls]
    A --> E[Action Buttons]

    C --> C1[Style Mixer Sliders]
    C --> C2[Style Presets]

    D --> D1[Context Selector]
    D --> D2[Length Preview]

    E --> E1[Regenerate]
    E --> E2[Edit]
    E --> E3[Save]
    E --> E4[Share]

    subgraph "Pro Features"
        C
        D
        E2
        E3
    end
```

#### 1. 交互式风格混合器

```typescript
// 风格权重类型定义
interface StyleWeights {
  technicalExpert: number; // 技术专家型权重 (0-100)
  communityBuilder: number; // 社区建设者权重 (0-100)
  innovationPioneer: number; // 创新先锋权重 (0-100)
  learningEnthusiast: number; // 学习者权重 (0-100)
}

// 风格混合器组件
export function StyleMixer({ weights, onChange }: StyleMixerProps) {
  return (
    <div className="style-mixer">
      <h4 className="font-medium text-sm">Personality Mix</h4>

      <div className="style-sliders">
        <StyleSlider
          label="Technical Expert"
          value={weights.technicalExpert}
          onChange={(val) => onChange({ ...weights, technicalExpert: val })}
          color="orange"
        />

        <StyleSlider
          label="Community Builder"
          value={weights.communityBuilder}
          onChange={(val) => onChange({ ...weights, communityBuilder: val })}
          color="amber"
        />

        <StyleSlider
          label="Innovation Pioneer"
          value={weights.innovationPioneer}
          onChange={(val) => onChange({ ...weights, innovationPioneer: val })}
          color="cyan"
        />

        <StyleSlider
          label="Learning Enthusiast"
          value={weights.learningEnthusiast}
          onChange={(val) => onChange({ ...weights, learningEnthusiast: val })}
          color="blue"
        />
      </div>

      <div className="style-presets">
        <Button
          size="xs"
          variant="outline"
          onClick={() => onChange(PRESETS.balanced)}
        >
          Balanced
        </Button>
        <Button
          size="xs"
          variant="outline"
          onClick={() => onChange(PRESETS.technical)}
        >
          Technical
        </Button>
        <Button
          size="xs"
          variant="outline"
          onClick={() => onChange(PRESETS.community)}
        >
          Community
        </Button>
        <Button
          size="xs"
          variant="outline"
          onClick={() => onChange(PRESETS.innovative)}
        >
          Innovative
        </Button>
      </div>
    </div>
  );
}
```

#### 2. 自适应描述长度控制

```typescript
// 描述上下文类型
enum DescriptionContext {
  PROFILE_CARD = "profile-card", // 简短 1-2 句
  EXPANDED_VIEW = "expanded-view", // 中等 3-4 句
  FULL_PROFILE = "full-profile", // 完整描述
  SOCIAL_SHARE = "social-share", // 社交媒体优化
}

// 长度控制组件
export function LengthController({ context, onChange }: LengthControllerProps) {
  return (
    <div className="length-controller">
      <h4 className="font-medium text-sm">Description Length</h4>

      <div className="context-selector">
        <div className="flex space-x-2 items-center">
          <RadioGroup value={context} onValueChange={onChange}>
            <div className="flex flex-wrap gap-2">
              <div className="flex space-x-2 items-center">
                <RadioGroupItem value="profile-card" id="profile-card" />
                <Label htmlFor="profile-card">Brief</Label>
              </div>
              <div className="flex space-x-2 items-center">
                <RadioGroupItem value="expanded-view" id="expanded-view" />
                <Label htmlFor="expanded-view">Standard</Label>
              </div>
              <div className="flex space-x-2 items-center">
                <RadioGroupItem value="full-profile" id="full-profile" />
                <Label htmlFor="full-profile">Detailed</Label>
              </div>
              <div className="flex space-x-2 items-center">
                <RadioGroupItem value="social-share" id="social-share" />
                <Label htmlFor="social-share">Social</Label>
              </div>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="mt-2 text-xs text-muted-foreground length-preview">
        {context === "profile-card" &&
          "1-2 sentences, perfect for profile cards"}
        {context === "expanded-view" &&
          "3-4 sentences, ideal for expanded profiles"}
        {context === "full-profile" &&
          "5-7 sentences, comprehensive description"}
        {context === "social-share" &&
          "2-3 sentences, optimized for social sharing"}
      </div>
    </div>
  );
}
```

### 🧩 整合的 AI 描述面板

```typescript
// 增强的 AI 描述面板组件
export function EnhancedAIDescriptionPanel({
  userId,
  username,
  currentDescription,
  onDescriptionUpdate,
}: EnhancedAIDescriptionPanelProps) {
  // 状态管理
  const [description, setDescription] = useState(currentDescription);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [styleWeights, setStyleWeights] = useState(PRESETS.balanced);
  const [descriptionContext, setDescriptionContext] =
    useState<DescriptionContext>(DescriptionContext.EXPANDED_VIEW);

  // 生成描述的处理函数
  const generateDescription = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/ai-description/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          forceRegenerate: true,
          styleWeights,
          context: descriptionContext,
        }),
      });

      const data = await response.json();
      setDescription(data.description);
      onDescriptionUpdate?.(data.description);
    } catch (error) {
      toast.error("Failed to generate description");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="ai-description-panel">
      <div className="panel-header">
        <h3 className="font-semibold text-lg">AI Profile Description</h3>
      </div>

      <div className="description-display">
        {description ? (
          <p className="text-sm leading-relaxed">{description}</p>
        ) : (
          <div className="description-placeholder">
            <Sparkles className="h-8 text-muted-foreground/50 w-8" />
            <p className="text-sm text-muted-foreground">
              Generate your AI-powered GitHub profile description
            </p>
          </div>
        )}
      </div>

      <div className="controls-section">
        <StyleMixer weights={styleWeights} onChange={setStyleWeights} />
        <LengthController
          context={descriptionContext}
          onChange={setDescriptionContext}
        />
      </div>

      <div className="action-buttons">
        <Button
          onClick={generateDescription}
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <Loader2 className="h-4 mr-2 animate-spin w-4" />
          ) : (
            <Sparkles className="h-4 mr-2 w-4" />
          )}
          {description ? "Regenerate Description" : "Generate Description"}
        </Button>

        <div className="flex space-x-2 mt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            disabled={!description}
            className="flex-1"
          >
            <Edit3 className="h-4 mr-2 w-4" />
            Customize
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              /* 分享逻辑 */
            }}
            disabled={!description}
            className="flex-1"
          >
            <Share2 className="h-4 mr-2 w-4" />
            Share
          </Button>
        </div>
      </div>

      {isEditing && (
        <DescriptionEditor
          initialDescription={description || ""}
          onSave={(newDescription, apply) => {
            setDescription(apply ? newDescription : description);
            setIsEditing(false);
          }}
          onCancel={() => setIsEditing(false)}
        />
      )}
    </div>
  );
}
```

### 📊 API 扩展

```typescript
// 扩展的描述生成请求接口
export interface EnhancedDescriptionGenerationRequest
  extends DescriptionGenerationRequest {
  styleWeights?: StyleWeights;
  context?: DescriptionContext;
}

// 扩展的 API 路由
export async function POST(request: Request) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 检查Pro权限
    const subscription = await getUserSubscription(session.user.id);
    if (!subscription || subscription.status !== "active") {
      return Response.json(
        {
          error: "Pro subscription required",
          upgradeUrl: "/subscription",
        },
        { status: 403 }
      );
    }

    const { forceRegenerate, styleWeights, context } =
      (await request.json()) as EnhancedDescriptionGenerationRequest;

    // 获取最新GitHub数据
    const githubData = await fetchGitHubContributions(session.user.username);
    const multiDimensionScores = calculateMultiDimensionScores(githubData);

    // 生成增强版AI描述
    const aiDescription = await generateEnhancedAIDescription({
      githubData,
      multiDimensionScores,
      styleWeights,
      context,
    });

    // 存储到数据库
    await saveAIDescription({
      userId: session.user.id,
      description: aiDescription,
      dataSnapshot: JSON.stringify({
        githubData,
        multiDimensionScores,
        styleWeights,
        context,
      }),
      style: determineMainStyle(styleWeights),
    });

    return Response.json({
      description: aiDescription,
      source: "ai",
      styleWeights,
      context,
    });
  } catch (error) {
    console.error("Enhanced AI Description generation failed:", error);
    return Response.json(
      {
        error: "Failed to generate description",
        fallback: generateFallbackDescription(session.user.username),
      },
      { status: 500 }
    );
  }
}
```

### 📱 移动端适配

```typescript
// 移动端优化的面板组件
export function MobileAIDescriptionPanel(
  props: EnhancedAIDescriptionPanelProps
) {
  const [activeTab, setActiveTab] = useState<
    "description" | "style" | "length"
  >("description");

  return (
    <div className="mobile-ai-panel">
      <div className="tabs">
        <Button
          variant={activeTab === "description" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("description")}
        >
          Description
        </Button>
        <Button
          variant={activeTab === "style" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("style")}
        >
          Style
        </Button>
        <Button
          variant={activeTab === "length" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("length")}
        >
          Length
        </Button>
      </div>

      <div className="tab-content">
        {activeTab === "description" && <DescriptionTab {...props} />}
        {activeTab === "style" && <StyleTab {...props} />}
        {activeTab === "length" && <LengthTab {...props} />}
      </div>
    </div>
  );
}
```

---

_本文档将随着实施进展持续更新和完善_
