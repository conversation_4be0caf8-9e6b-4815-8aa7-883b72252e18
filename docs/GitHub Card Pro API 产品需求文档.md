# GitHub Card Pro API 产品需求文档

## 产品概述

为GitHub Card项目新增Pro用户专属的卡片图片API功能，基于Cloudflare Browser Rendering技术，提供可直接嵌入到Markdown文档、博客、README等场景的动态卡片图片服务。

## 核心价值

### 用户价值
- **Pro用户专享**：高级订阅用户的专属功能
- **实时数据**：基于最新GitHub数据生成卡片
- **便捷嵌入**：一个URL即可在任何支持图片的地方展示GitHub卡片
- **高质量渲染**：服务端渲染，确保跨平台一致性

### 商业价值
- **订阅转化**：吸引用户升级到Pro订阅
- **差异化服务**：与免费版形成明确功能区分
- **API经济**：为开发者提供集成能力

## 功能需求

### 1. 核心功能架构

```
用户请求 → API验证 → 生成页面渲染 → Browser截图 → 图片存储 → 重定向返回
```

### 2. API端点设计

#### 主要端点
```
GET /api/card/{username}?token={pro_token}&style={style}&theme={theme}
```

#### 参数说明
- `username`: GitHub用户名（必需）
- `token`: Pro用户API Token（必需）
- `style`: 卡片样式（可选，默认为用户偏好）
- `theme`: 主题色彩（可选，默认为用户偏好）
- `refresh`: 强制刷新（可选，忽略缓存）

#### 响应行为
- **成功**: 302重定向到图片URL
- **失败**: 返回错误占位图片或JSON错误信息

### 3. 技术实现流程

#### 3.1 请求验证
1. 验证Pro用户身份和API Token
2. 检查请求频率限制
3. 验证参数有效性

#### 3.2 页面渲染准备
1. 构造内部/generate页面URL
2. 添加特殊参数标识API模式
3. 页面检测到API模式时：
   - 隐藏所有操作按钮和菜单
   - 移除交互元素
   - 优化布局用于截图

#### 3.3 Browser Rendering
1. 使用Durable Object启动浏览器会话
2. 访问准备好的/generate页面
3. 等待页面完全加载
4. 生成高质量截图

#### 3.4 图片处理与存储
1. 截图优化（压缩、格式转换）
2. 上传到R2存储
3. 生成带缓存的公开URL
4. 记录生成日志

#### 3.5 缓存策略
- **缓存时间**: 1小时（可配置）
- **缓存键**: `api_card:{username}:{style}:{theme}:{data_hash}`
- **强制刷新**: Pro用户可通过refresh参数强制更新

## 用户体验设计

### 1. API Token管理
- **生成位置**: Pro用户Dashboard
- **Token格式**: `ghc_` + 32位随机字符串
- **权限控制**: 仅限卡片API访问
- **使用统计**: 显示API调用次数和频率

### 2. 使用指南
在用户Dashboard提供：
- API使用文档
- 代码示例（Markdown、HTML）
- 实时预览工具
- 使用统计图表

### 3. 错误处理
- **Token无效**: 返回错误提示图片
- **用户不存在**: 返回默认占位图片
- **服务异常**: 返回系统错误图片
- **频率超限**: 返回限流提示图片

## 技术规格

### 1. 性能要求
- **响应时间**: 首次生成 < 5秒，缓存命中 < 500ms
- **并发处理**: 支持100个并发请求
- **可用性**: 99.9%服务可用性

### 2. 限制规格
- **频率限制**: 每用户每分钟60次请求
- **图片规格**: 1200x800px, PNG格式, < 500KB
- **缓存时间**: 1小时默认，最短5分钟

### 3. 安全要求
- **Token验证**: 每次请求验证Pro用户身份
- **防滥用**: IP和用户双重频率限制
- **数据安全**: 不存储敏感GitHub数据

## 实现优先级

### P0 (核心功能)
- [ ] Pro用户API Token生成和管理
- [ ] 基础API端点实现
- [ ] Browser Rendering集成
- [ ] 图片生成和存储
- [ ] 基础缓存机制

### P1 (重要功能)
- [ ] 频率限制和防滥用
- [ ] 错误处理和占位图片
- [ ] 使用统计和监控
- [ ] API文档和使用指南

### P2 (增强功能)
- [ ] 多种卡片样式支持
- [ ] 自定义主题配置
- [ ] 批量生成API
- [ ] Webhook通知机制

## 商业模式

### 1. 订阅限制
- **免费用户**: 无API访问权限
- **Pro用户**: 每月10,000次API调用
- **Enterprise用户**: 无限制API调用

### 2. 使用场景
- **个人博客**: 在博客中展示GitHub状态
- **项目文档**: README中的贡献者卡片
- **团队展示**: 公司网站的开发团队介绍
- **社交媒体**: 自动化的GitHub状态分享

## 成功指标

### 1. 技术指标
- API响应时间 < 5秒
- 缓存命中率 > 80%
- 服务可用性 > 99.9%
- 错误率 < 1%

### 2. 业务指标
- Pro用户API使用率 > 60%
- 月均API调用量增长 > 20%
- 用户满意度评分 > 4.5/5
- Pro订阅转化率提升 > 15%

## 风险评估

### 1. 技术风险
- **Browser Rendering稳定性**: 中等风险，有官方支持
- **性能瓶颈**: 低风险，可通过缓存和优化解决
- **成本控制**: 中等风险，需要监控使用量

### 2. 业务风险
- **用户接受度**: 低风险，满足真实需求
- **竞争压力**: 低风险，差异化功能
- **合规问题**: 低风险，不涉及敏感数据

## 实施计划

### 阶段一：基础实现（2-3周）
1. Pro用户API Token系统
2. 基础API端点开发
3. Browser Rendering集成
4. 基础测试和调试

### 阶段二：完善功能（1-2周）
1. 缓存和性能优化
2. 错误处理完善
3. 监控和日志系统
4. 安全加固

### 阶段三：用户体验（1周）
1. Dashboard集成
2. 使用文档编写
3. 用户测试和反馈
4. 正式发布

## 附录

### API使用示例

#### Markdown中使用
```markdown
![GitHub Card](https://github-card.refined-x.workers.dev/api/card/username?token=ghc_xxx)
```

#### HTML中使用
```html
<img src="https://github-card.refined-x.workers.dev/api/card/username?token=ghc_xxx" alt="GitHub Card" />
```

#### 动态参数示例
```
/api/card/username?token=ghc_xxx&style=modern&theme=dark&refresh=true
```

---

*文档版本：v1.0*  
*创建时间：2025-01-24*  
*最后更新：2025-01-24*
