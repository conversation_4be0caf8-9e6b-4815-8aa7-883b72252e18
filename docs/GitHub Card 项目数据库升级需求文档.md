# GitHub Card 项目数据库升级需求文档

## 项目概述

目前运行在 Vercel 上的 GitHub Card 项目需要升级数据存储能力，使用 Neon 数据库。该升级将实现用户信息存储、行为数据记录以及实现限时分享链接功能。

## 技术栈

- **托管平台**：Vercel
- **数据库**：Neon (PostgreSQL)
- **包管理器**：yarn

## 数据库需求

### 数据库架构

#### 1. 用户表

```
users
- id (UUID, 主键)
- github_id (字符串, 唯一)
- username (字符串)
- avatar_url (字符串)
- email (字符串, 可为空)
- created_at (时间戳)
- updated_at (时间戳)
```

#### 2. 用户行为表

```
user_behaviors
- id (UUID, 主键)
- user_id (UUID, 外键关联 users.id)
- action_type (字符串) - 例如：'login', 'generate_link', 'view'
- action_data (JSON, 可为空) - 与行为相关的额外数据
- performed_at (时间戳)
```

#### 3. 分享链接表

```
share_links
- id (UUID, 主键)
- user_id (UUID, 外键关联 users.id)
- link_token (字符串, 唯一) - 用于生成URL
- card_data (JSON) - 要显示的GitHub卡片数据
- created_at (时间戳)
- expires_at (时间戳) - 设置为created_at + 3天
- is_active (布尔值) - 标记链接是否当前有效
```

## 功能需求

### 1. 数据库连接设置

- 使用环境变量实现 Neon 数据库连接
- 设置数据库迁移和架构管理
- 实现连接池以高效访问数据库

### 2. 用户管理

- 认证时存储基本用户信息
- 信息变更时更新用户信息
- 跟踪用户认证事件

### 3. 分享链接生成

- 创建生成分享链接的 API 端点
- 为每个分享链接生成唯一、安全的令牌
- 在数据库中存储 GitHub 卡片数据
- 设置 3 天后的过期时间
- 将生成的链接返回给用户

### 4. 分享链接访问

- 创建访问共享卡片数据的 API 端点
- 验证链接有效性和过期状态
- 对有效链接返回卡片数据
- 对过期链接返回适当的错误信息

### 5. 链接过期管理

- 实现 3 天后自动链接过期
- 允许用户重新生成过期链接
- 实现后台任务或访问时检查以验证链接过期状态

## API 端点

### 1. 用户 API

- `POST /api/users` - 创建或更新用户信息
- `GET /api/users/:id` - 获取用户信息

### 2. 分享链接 API

- `POST /api/share-links` - 生成新的分享链接
- `GET /api/share-links/:token` - 访问分享链接的数据
- `GET /api/share-links/user/:userId` - 列出用户的所有分享链接

## 安全考虑

- 实施适当的身份验证和授权
- 使用环境变量存储数据库凭证
- 在数据库操作前净化所有用户输入
- 为 API 端点实现速率限制

## 性能考虑

- 优化数据库查询
- 适当实现缓存
- 考虑对经常查询的字段建立索引

## 测试需求

- 数据库模型和实用函数的单元测试
- API 端点的集成测试
- 链接过期功能的测试

## 部署计划

1. 在 Vercel 环境中设置 Neon 数据库
2. 实现数据库架构和迁移
3. 在本地开发和测试 API 端点
4. 部署到测试环境进行测试
5. 部署到生产环境

## 监控与维护

- 设置数据库性能监控
- 为关键操作实现日志记录
- 定期清理过期的分享链接

此需求文档为 GitHub Card 项目实现数据库升级和分享链接功能提供了全面的指导。概述的规范将作为开发路线图，确保所有必要功能都能正确实现。
