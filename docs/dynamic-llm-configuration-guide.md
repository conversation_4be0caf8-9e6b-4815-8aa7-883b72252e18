# 动态 LLM 配置指南

GitHub Card 项目现已支持**动态 LLM 配置**，允许在每次 AI 调用时单独指定提供商（provider）和模型，而无需依赖全局环境变量配置。

## 🎯 核心特性

- **每次调用动态指定**：单次调用可指定不同的 provider 和 model
- **多提供商混合使用**：同一项目中可混合使用 Doubao 和 OpenRouter
- **零业务逻辑变更**：现有 AI 模块代码无需大幅修改
- **类型安全**：完整的 TypeScript 类型支持
- **性能监控**：按 provider 分组的统计和监控

## 🚀 快速开始

### 基础使用示例

```typescript
import { createLLMClient } from "@/lib/ai/core/clients/LLMClientFactory";

const llmClient = createLLMClient();

// 使用Doubao生成文本
const doubaoResponse = await llmClient.chat({
  provider: "doubao",
  model: "ep-**************-hrc7t", // Doubao Standard
  messages: [{ role: "user", content: "Hello" }],
  temperature: 0.7,
  max_tokens: 1000,
});

// 使用OpenRouter智能路由
const openRouterResponse = await llmClient.chat({
  provider: "openrouter",
  model: "openai/gpt-4o-mini",
  messages: [{ role: "user", content: "Hello" }],
  enableRouting: true, // 启用智能路由
  temperature: 0.7,
  max_tokens: 1000,
});

// 使用Vercel AI Gateway统一接入
const aiGatewayResponse = await llmClient.chat({
  provider: "aigateway",
  model: "openai/gpt-4o", // 默认模型
  messages: [{ role: "user", content: "Hello" }],
  temperature: 0.7,
  max_tokens: 1000,
});

// AI Gateway支持多种模型
const claudeResponse = await llmClient.chat({
  provider: "aigateway",
  model: "anthropic/claude-sonnet-4",
  messages: [{ role: "user", content: "Hello" }],
  temperature: 0.7,
  max_tokens: 1000,
});
```

### 流式调用示例

```typescript
// 流式生成 - Doubao Flash模型
for await (const chunk of llmClient.chatStream({
  provider: "doubao",
  model: "ep-**************-str8m", // Doubao Flash
  messages: [{ role: "user", content: "Write a story" }],
  stream: true,
  max_tokens: 800,
})) {
  console.log(chunk.choices[0]?.delta?.content || "");
}
```

## 📋 动态配置选项

### DynamicChatOptions 配置

```typescript
interface DynamicChatOptions {
  // 必需配置
  provider: "doubao" | "openrouter"; // LLM提供商
  model: string; // 模型名称
  messages: ChatMessage[]; // 对话消息

  // 可选配置
  temperature?: number; // 温度参数 (0-1)
  max_tokens?: number; // 最大token数
  timeout?: number; // 超时时间(ms)

  // 结构化输出
  response_format?: { type: "json_object" };

  // 流式配置
  stream?: boolean;

  // OpenRouter特有配置
  enableRouting?: boolean; // 智能路由
  models?: string[]; // 候选模型列表

  // 认证配置（可选，使用环境变量默认值）
  apiKey?: string; // 自定义API密钥
  baseURL?: string; // 自定义API端点
  appName?: string; // 应用名称
}
```

## 🎨 在 AI 模块中使用

### 基础模块方法

新架构在`BaseModule`中提供了便捷的动态 LLM 调用方法：

```typescript
export abstract class BaseModule {
  // 动态LLM调用 - 普通模式
  protected async callLLM(llmConfig: DynamicChatOptions) {
    return await this.llmClient.chat(llmConfig);
  }

  // 动态LLM调用 - 流式模式
  protected async *callLLMStream(llmConfig: DynamicChatOptions) {
    for await (const chunk of this.llmClient.chatStream(llmConfig)) {
      yield chunk;
    }
  }

  // 健康检查
  protected async checkLLMHealth(
    provider: "doubao" | "openrouter"
  ): Promise<boolean> {
    return await this.llmClient.healthCheck(provider);
  }

  // 性能统计
  protected getLLMStats(provider?: "doubao" | "openrouter") {
    return this.llmClient.getPerformanceStats(provider);
  }
}
```

### 在具体模块中的使用

```typescript
export class AnalyzerModule extends BaseModule {
  private async generateAnalysis(input: string): Promise<AnalysisResult> {
    // 使用Doubao Standard模型进行分析
    const response = await this.callLLM({
      provider: "doubao",
      model: "ep-**************-hrc7t",
      messages: [
        {
          role: "system",
          content: "你是一位数据分析专家...",
        },
        {
          role: "user",
          content: input,
        },
      ],
      response_format: { type: "json_object" },
      temperature: 0.6,
      max_tokens: 2048,
    });

    return JSON.parse(response.choices[0].message.content);
  }

  private async generateStreamingNarrative(input: string): Promise<string> {
    let result = "";

    // 使用流式生成创造性内容
    for await (const chunk of this.callLLMStream({
      provider: "doubao",
      model: "ep-**************-hrc7t",
      messages: [{ role: "user", content: input }],
      temperature: 0.8, // 提高创意性
      max_tokens: 1024,
      stream: true,
    })) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        result += content;
      }
    }

    return result;
  }
}
```

## 🔧 模型选择指南

### Doubao 模型选择

```typescript
import { SUPPORTED_MODELS } from "@/lib/ai/core/clients/ILLMClient";

// 根据场景选择模型
const modelConfig = {
  // 复杂推理任务 - 使用Thinking模型
  thinking: {
    provider: "doubao" as const,
    model: SUPPORTED_MODELS.DOUBAO.THINKING,
    temperature: 0.6,
  },

  // 通用文本生成 - 使用Standard模型
  standard: {
    provider: "doubao" as const,
    model: SUPPORTED_MODELS.DOUBAO.STANDARD,
    temperature: 0.7,
  },

  // 快速响应 - 使用Flash模型
  flash: {
    provider: "doubao" as const,
    model: SUPPORTED_MODELS.DOUBAO.FLASH,
    temperature: 0.8,
  },
};
```

### OpenRouter 模型选择

```typescript
const openRouterConfigs = {
  // 成本优化 - DeepSeek Chat
  costEffective: {
    provider: "openrouter" as const,
    model: SUPPORTED_MODELS.OPENROUTER.DEEPSEEK_CHAT,
    enableRouting: true,
  },

  // 高质量生成 - Claude 3.5 Sonnet
  highQuality: {
    provider: "openrouter" as const,
    model: SUPPORTED_MODELS.OPENROUTER.CLAUDE_3_SONNET,
    temperature: 0.6,
  },

  // 快速响应 - Claude 3 Haiku
  fastResponse: {
    provider: "openrouter" as const,
    model: SUPPORTED_MODELS.OPENROUTER.CLAUDE_3_HAIKU,
    enableRouting: true,
  },
};
```

### AI Gateway 模型选择

```typescript
const aiGatewayConfigs = {
  // 默认推荐 - GPT-4o
  default: {
    provider: "aigateway" as const,
    model: SUPPORTED_MODELS.AIGATEWAY.GPT_4O,
    temperature: 0.7,
  },

  // 成本优化 - GPT-4o Mini
  costEffective: {
    provider: "aigateway" as const,
    model: SUPPORTED_MODELS.AIGATEWAY.GPT_4O_MINI,
    temperature: 0.7,
  },

  // 高质量推理 - Claude Sonnet 4
  highQuality: {
    provider: "aigateway" as const,
    model: SUPPORTED_MODELS.AIGATEWAY.CLAUDE_SONNET_4,
    temperature: 0.6,
  },

  // 创新思维 - Grok 3
  creative: {
    provider: "aigateway" as const,
    model: SUPPORTED_MODELS.AIGATEWAY.GROK_3,
    temperature: 0.8,
  },

  // 开源选择 - Llama 3.1 70B
  opensource: {
    provider: "aigateway" as const,
    model: SUPPORTED_MODELS.AIGATEWAY.LLAMA_3_1_70B,
    temperature: 0.7,
  },
};
```

## 🔄 混合使用策略

### 按任务类型分配模型

```typescript
class AITaskRouter {
  async processAnalysis(data: any) {
    // 数据分析使用Doubao Standard - 稳定可控
    return await this.callLLM({
      provider: "doubao",
      model: SUPPORTED_MODELS.DOUBAO.STANDARD,
      messages: [{ role: "user", content: JSON.stringify(data) }],
      response_format: { type: "json_object" },
    });
  }

  async generateCreativeContent(prompt: string) {
    // 创意生成使用OpenRouter Claude - 更强的创意能力
    return await this.callLLM({
      provider: "openrouter",
      model: SUPPORTED_MODELS.OPENROUTER.CLAUDE_3_SONNET,
      messages: [{ role: "user", content: prompt }],
      temperature: 0.8,
    });
  }

  async quickResponse(query: string) {
    // 快速响应优先使用Flash模型
    return await this.callLLM({
      provider: "doubao",
      model: SUPPORTED_MODELS.DOUBAO.FLASH,
      messages: [{ role: "user", content: query }],
      max_tokens: 200,
    });
  }
}
```

### 成本优化策略

```typescript
class CostOptimizedAI {
  async intelligentRouting(task: AITask) {
    // 根据任务复杂度智能选择模型
    if (task.complexity === "simple") {
      // 简单任务使用成本最低的模型
      return await this.callLLM({
        provider: "openrouter",
        model: SUPPORTED_MODELS.OPENROUTER.DEEPSEEK_CHAT,
        messages: task.messages,
        enableRouting: true,
      });
    } else if (task.complexity === "complex") {
      // 复杂任务使用高质量模型
      return await this.callLLM({
        provider: "doubao",
        model: SUPPORTED_MODELS.DOUBAO.THINKING,
        messages: task.messages,
      });
    } else {
      // 中等任务使用标准模型
      return await this.callLLM({
        provider: "doubao",
        model: SUPPORTED_MODELS.DOUBAO.STANDARD,
        messages: task.messages,
      });
    }
  }
}
```

## 📊 性能监控

### 按提供商获取统计

```typescript
// 获取所有提供商的统计
const allStats = llmClient.getPerformanceStats();
console.log("总体统计:", allStats);

// 获取特定提供商的统计
const doubaoStats = llmClient.getPerformanceStats("doubao");
console.log("Doubao统计:", doubaoStats);

const openRouterStats = llmClient.getPerformanceStats("openrouter");
console.log("OpenRouter统计:", openRouterStats);

const aiGatewayStats = llmClient.getPerformanceStats("aigateway");
console.log("AI Gateway统计:", aiGatewayStats);
```

### 请求指标监控

```typescript
// 获取所有请求指标
const allMetrics = llmClient.getRequestMetrics();

// 获取特定提供商的请求指标
const doubaoMetrics = llmClient.getRequestMetrics(undefined, "doubao");

// 获取特定请求的指标
const specificMetric = llmClient.getRequestMetrics("request-id-123");
```

## 🔍 健康检查

```typescript
// 检查单个提供商
const doubaoHealth = await llmClient.healthCheck("doubao");
const openRouterHealth = await llmClient.healthCheck("openrouter");
const aiGatewayHealth = await llmClient.healthCheck("aigateway");

// 检查所有提供商
async function checkAllProviders() {
  const results = await Promise.allSettled([
    llmClient.healthCheck("doubao"),
    llmClient.healthCheck("openrouter"),
    llmClient.healthCheck("aigateway"),
  ]);

  return {
    doubao: results[0].status === "fulfilled" ? results[0].value : false,
    openrouter: results[1].status === "fulfilled" ? results[1].value : false,
    aigateway: results[2].status === "fulfilled" ? results[2].value : false,
  };
}
```

## ⚠️ 环境配置

虽然支持动态配置，但仍需要设置基础的 API 密钥：

```bash
# .env.local
DOUBAO_API_KEY=your-doubao-uuid-key
OPENROUTER_API_KEY=sk-or-v1-your-key-here
AI_GATEWAY_API_KEY=your-vercel-ai-gateway-key

# 可选配置
OPENROUTER_APP_NAME=GitHub-Card
OPENROUTER_DEFAULT_MODEL=openai/gpt-4o-mini

# AI Gateway 可选配置
AI_GATEWAY_DEFAULT_MODEL=openai/gpt-4o
AI_GATEWAY_APP_NAME=GitHub-Card
# AI_GATEWAY_BASE_URL=https://ai-gateway.vercel.sh/v1  # 自定义网关地址
```

## 🎉 迁移指南

### 从旧版本迁移

**之前的代码（环境变量配置）:**

```typescript
// 旧版本 - 依赖环境变量
const response = await this.llmClient.chat({
  messages: [{ role: "user", content: "Hello" }],
  temperature: 0.7,
});
```

**新版本（动态配置）:**

```typescript
// 新版本 - 动态指定provider和model
const response = await this.callLLM({
  provider: "doubao",
  model: "ep-**************-hrc7t",
  messages: [{ role: "user", content: "Hello" }],
  temperature: 0.7,
});
```

### 便捷迁移工具

```typescript
// 创建便捷配置函数
const createDoubaoConfig = (model?: string) => ({
  provider: "doubao" as const,
  model: model || SUPPORTED_MODELS.DOUBAO.STANDARD,
});

const createOpenRouterConfig = (model?: string) => ({
  provider: "openrouter" as const,
  model: model || SUPPORTED_MODELS.OPENROUTER.GPT_4O_MINI,
  enableRouting: true,
});

// 使用便捷函数
const response = await this.callLLM({
  ...createDoubaoConfig(),
  messages: [{ role: "user", content: "Hello" }],
  temperature: 0.7,
});
```

## 🛠️ 最佳实践

1. **根据任务选择模型**：分析任务用 Standard，创意任务用高 temperature 配置
2. **合理使用缓存**：相同配置的客户端会被自动缓存
3. **监控成本**：使用性能统计监控 token 使用情况
4. **错误处理**：始终包含 try-catch 处理 LLM 调用
5. **类型安全**：充分利用 TypeScript 类型定义

## 🔮 未来扩展

动态配置架构为未来集成更多 AI 提供商预留了空间：

- Anthropic Claude API
- Google Gemini
- Azure OpenAI
- 自定义模型端点

---

通过动态 LLM 配置，GitHub Card 项目现在具备了**最大的灵活性**，可以在同一个项目中根据具体需求选择最适合的 AI 模型，实现**成本优化**和**质量最大化**的平衡！
