# Analyzer 模块使用说明

## 概述

新的 Analyzer 模块基于 `analyzer-map.json` 配置，实现了12项 GitHub 数据分析功能，采用算法分析 + LLM分析的混合模式。

## 主要特性

- **类型安全**：完整的 TypeScript 类型定义
- **混合分析**：算法分析（快速、确定性）+ LLM分析（智能、语义化）
- **流式输出**：支持实时进度反馈
- **可配置**：灵活的分析项选择和配置

## 分析项列表

### 算法分析项（10项）
- `commit_density` - 提交密度分析
- `commit_consistency` - 提交一致性分析  
- `commit_length` - 提交消息长度分析
- `active_hours` - 活跃时间分析
- `weekend_dev` - 周末开发习惯分析
- `long_pause_gaps` - 长时间暂停间隔分析
- `language_diversity` - 编程语言多样性分析
- `framework_focus` - 框架专注度分析
- `experimental_ratio` - 实验性项目比例分析
- `star_to_follower_ratio` - 星标与关注者比例分析

### LLM分析项（2项）
- `commit_sentiment` - 提交情感分析
- `repo_naming_style` - 仓库命名风格分析

## 使用方法

### 1. 基本用法

```typescript
import { AnalyzerModule } from '@/lib/ai/core/modules/AnalyzerModule';
import type { AnalyzerInput } from '@/lib/ai/types';

const analyzer = new AnalyzerModule();

const input: AnalyzerInput = {
  userData: {
    username: "example",
    // ... 其他用户数据
  },
  extendedData: {
    repositoriesData: [...],
    commitsData: [...],
    timeStatsData: {...},
    // ... 其他扩展数据
  },
  config: {
    enableLLM: true,
    includeItems: [], // 空数组表示包含所有项
    excludeItems: [], // 排除特定项
  }
};

const result = await analyzer.analyzeWithStreaming(input);
```

### 2. 配置选项

```typescript
interface AnalyzerConfig {
  includeItems?: AnalysisItemName[];  // 要包含的分析项
  excludeItems?: AnalysisItemName[];  // 要排除的分析项
  enableLLM?: boolean;                // 是否启用LLM分析
  llmTimeout?: number;                // LLM调用超时时间（毫秒）
  minConfidence?: number;             // 最小置信度阈值
}
```

### 3. 流式输出

```typescript
const result = await analyzer.analyzeWithStreaming(
  input,
  config,
  (message, chunk, isComplete, stage) => {
    console.log(`[${stage}] ${message}`);
    if (chunk) {
      console.log('数据:', chunk);
    }
  }
);
```

## 输出格式

```typescript
interface AnalyzerOutput {
  status: "success" | "partial" | "error";
  content: {
    results: AnalysisItem[];           // 分析结果列表
    summary: {                         // 整体摘要
      successCount: number;
      totalCount: number;
      overallConfidence: number;
      dataQuality: "excellent" | "good" | "fair" | "poor";
    };
    metadata: {                        // 执行元数据
      totalProcessingTime: number;
      dataVersion: string;
      llmCallCount: number;
      timestamp: number;
      errors?: string[];
    };
  };
}

interface AnalysisItem {
  name: string;                        // 分析项名称
  value: string;                       // 分析结果值
  confidence: number;                  // 置信度 (0-1)
  source: string;                      // 数据来源
  usedLLM: boolean;                    // 是否使用了LLM
  rawData?: any;                       // 原始数据（调试用）
  metadata?: {                         // 分析元数据
    processingTime: number;
    dataCompleteness: number;
    error?: string;
  };
}
```

## 调试平台使用

1. 打开调试平台的 Analyzer 标签页
2. 输入或选择 GitHub 基础数据
3. 系统会自动获取扩展数据
4. 配置分析参数（可选）
5. 点击执行按钮开始分析
6. 查看实时分析进度和最终结果

## 错误处理

- 数据验证失败：检查输入数据格式和完整性
- LLM调用失败：检查网络连接和API配置
- 分析项执行失败：查看具体错误信息和原始数据

## 性能优化

- 算法分析项响应时间：< 100ms
- LLM分析项响应时间：< 5s
- 总体分析时间：< 30s（包含所有12项）
- 支持并发执行以提高效率

## 扩展开发

要添加新的分析项：

1. 在 `analyzer-map.json` 中添加配置
2. 在 `types/analyzer.ts` 中添加类型定义
3. 在相应的分析器类中实现分析逻辑
4. 更新测试和文档

## 注意事项

- 确保扩展数据的完整性和准确性
- LLM分析项需要网络连接
- 大量数据可能影响分析性能
- 定期更新分析器映射配置
