# GitHub Card 项目数据库梳理及改进方案

## 当前数据库表设计与目的

项目当前使用 SQLite 数据库（在 Cloudflare Workers 环境中），包含以下主要表：

### 1. 用户相关表

- **users**: 存储用户基本信息和 GitHub 账户信息

  - 目的：作为用户身份标识，存储 GitHub 用户基本资料
  - 主要字段：id, name, email, githubId, username, avatarUrl 等

- **accounts**: 存储 OAuth 授权账户信息

  - 目的：记录用户通过 GitHub 授权的账户信息，包括 access token 等
  - 主要字段：userId, provider, providerAccountId, access_token, expires_at 等

- **sessions**: 存储用户会话信息

  - 目的：管理用户登录状态
  - 主要字段：sessionToken, userId, expires

- **verificationTokens**: 存储邮箱验证等令牌信息
  - 目的：用于邮箱验证等功能，目前可能未实际使用

### 2. GitHub 数据相关表

- **contributeDatas**: 存储 GitHub 用户贡献数据
  - 目的：缓存 GitHub API 获取的用户数据和贡献数据，减少对 GitHub API 的请求
  - 主要字段：username, githubData(JSON), lastUpdated, createdAt
  - 数据有效期：24 小时

### 3. 功能相关表

- **shareLinks**: 存储分享链接信息

  - 目的：生成并管理用户创建的分享链接
  - 主要字段：userId, linkToken, githubUsername, expiresAt, isActive, templateType
  - 链接有效期：3 天

- **userBehaviors**: 记录用户行为
  - 目的：跟踪用户活动，如登录、创建分享链接、查看链接等
  - 主要字段：userId, actionType, actionData(JSON), performedAt

## 当前存在的问题

### 1. 用户认证与数据存储问题

- **主要问题**：用户登录后，accounts 表和 users 表没有插入数据
- **原因分析**：
  - 当前使用 NextAuth 的 JWT 策略而非数据库策略
  - auth.ts 中配置为"strategy: jwt"，而不是"strategy: database"
  - 缺少适配器配置，未将认证数据保存到数据库

### 2. 数据结构问题

- **contributeDatas 表**：

  - 使用了`text("github_data", { mode: "json" })`存储所有 GitHub 数据，难以进行单项查询和更新
  - 缺少对用户与 contributeDatas 的有效关联机制

- **shareLinks 表**：
  - 与用户表的关联依赖于用户认证正常工作，而目前认证机制有问题

### 3. 数据管理问题

- 缺少定期清理过期数据的机制
- 缺少对 contributeDatas 表过期数据的主动更新机制

## 改进方案

### 1. 认证机制改进

#### 1.1 认证机制核心原则

- **只用 JWT 策略**：认证完全依赖 JWT，所有用户信息都通过 token 传递，极简、无数据库依赖。
- **用户数据持久化**：登录后通过自定义逻辑（如 API 或中间件）将用户信息同步到数据库，便于后续扩展（如付费、行为分析等）。
- **认证与数据解耦**：认证流程和用户数据存储完全分离，认证不依赖数据库，数据持久化不影响认证流程。

---

#### 1.2 认证配置代码示例

```typescript
// auth.ts
import NextAuth from "next-auth";
import GitHubProvider from "next-auth/providers/github";
import type { D1Database } from "@cloudflare/workers-types";

// 声明 globalThis.DB 类型
declare global {
  // eslint-disable-next-line no-var
  var DB: D1Database;
}

// 配置 Auth.js - 使用纯JWT策略
export const { auth, handlers, signIn, signOut } = NextAuth({
  providers: [
    GitHubProvider({
      clientId: process.env.GITHUB_ID as string,
      clientSecret: process.env.GITHUB_SECRET as string,
      profile(profile) {
        return {
          id: profile.id.toString(),
          name: profile.name ?? profile.login,
          email: profile.email,
          image: profile.avatar_url,
          username: profile.login,
          githubId: profile.id.toString(),
          avatarUrl: profile.avatar_url,
        };
      },
    }),
  ],
  // 禁用数据库适配器，纯使用JWT
  adapter: undefined,
  // 使用JWT策略
  session: {
    strategy: "jwt",
    maxAge: 3 * 24 * 60 * 60, // 3天
  },
  callbacks: {
    async jwt({ token, user, account, profile }) {
      if (account && profile && user) {
        token.userId = user.id;
        token.sub = user.id;
        token.username = (user as any).username || (profile as any).login;
        token.githubId = (profile as any).id?.toString();
        token.avatarUrl = (profile as any).avatar_url;
        token.name =
          user.name || (profile as any).name || (profile as any).login;
        token.email = user.email || (profile as any).email;
        token.picture = user.image || (profile as any).avatar_url;
        console.log(`[auth] 用户登录成功，GitHub ID: ${(profile as any).id}`);
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = (token.userId as string) || (token.sub as string);
        session.user.name = (token.name as string) || session.user.name;
        session.user.email = (token.email as string) || session.user.email;
        session.user.image = (token.picture as string) || session.user.image;
        session.user.username = token.username as string;
        session.user.githubId = token.githubId as string;
        session.user.avatarUrl = token.avatarUrl as string;
      }
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV !== "production",
  trustHost: true,
  cookies: {
    sessionToken: {
      name: "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    callbackUrl: {
      name: "next-auth.callback-url",
      options: {
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    csrfToken: {
      name: "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
});
```

---

#### 1.3. 用户数据同步机制

- **同步时机**：每次用户登录后，或每次获取会话信息时（如`/api/auth/session`），调用自定义同步函数将用户信息写入数据库。
- **同步实现**：推荐在 API 路由或中间件中调用 `syncUserToDB`，如：

```typescript
// app/api/auth/session/route.ts
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { syncUserToDB } from "@/lib/user-management";

export async function GET(req: NextRequest) {
  const session = await auth();
  if (session?.user) {
    await syncUserToDB({
      id: session.user.id,
      name: session.user.name,
      email: session.user.email,
      username: session.user.username as string,
      githubId: session.user.githubId as string,
      avatarUrl: session.user.avatarUrl as string,
      image: session.user.image as string,
    });
  }
  return new NextResponse(JSON.stringify(session), { status: 200 });
}
```

---

#### 1.4. 方案优势

- **极简认证**：无数据库依赖，认证流程极其稳定。
- **灵活扩展**：后续可随时扩展用户表、行为表、订阅表等，支持付费、行为分析等功能。
- **易于维护**：认证和数据持久化解耦，出错时互不影响。
- **适配 Cloudflare D1/Serverless**：无状态认证+按需持久化，最适合边缘环境。

---

#### 1.5. 未来扩展建议

- 付费功能、行为分析、用户画像等全部基于数据库表扩展，认证流程无需变动。
- 可根据业务需要，灵活切换为数据库适配器方案（如 D1Adapter），只需调整`adapter`配置和 session 策略。

### 2. 数据结构优化

#### 1) contributeDatas 表改进

```typescript
// 将大型JSON数据拆分为多个字段
export const contributeDatas = sqliteTable("contribute_datas", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  username: text("username").notNull().unique(),
  // 基本用户数据
  login: text("login").notNull(),
  name: text("name"),
  avatarUrl: text("avatar_url").notNull(),
  bio: text("bio"),
  blog: text("blog"),
  location: text("location"),
  twitterUsername: text("twitter_username"),
  publicRepos: integer("public_repos").notNull(),
  followers: integer("followers").notNull(),
  following: integer("following").notNull(),
  createdAt: integer("created_at").notNull(),
  // 贡献数据
  totalStars: integer("total_stars").notNull(),
  contributionScore: integer("contribution_score").notNull(),
  contributionGrade: text("contribution_grade").notNull(),
  commits: integer("commits").notNull(),
  pullRequests: integer("pull_requests").notNull(),
  issues: integer("issues").notNull(),
  reviews: integer("reviews").notNull(),
  // 元数据
  lastUpdated: integer("last_updated")
    .notNull()
    .default(sql`(unixepoch())`),
  recordCreatedAt: integer("record_created_at")
    .notNull()
    .default(sql`(unixepoch())`),
  userId: text("user_id").references(() => users.id), // 添加明确的用户关联
});
```

#### 2) 添加数据清理机制

添加定期任务，清理过期数据：

```typescript
// 新建 app/api/cron/clean-expired-data/route.ts
import { NextResponse } from "next/server";
import { getDb } from "@/lib/db";
import { shareLinks } from "@/lib/db/schema";
import { lt, eq } from "drizzle-orm";

// 处理定时任务请求
export async function GET() {
  try {
    const db = getDb();
    const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）

    // 将过期的shareLinks标记为非活动
    const result = await db
      .update(shareLinks)
      .set({ isActive: false })
      .where(and(lt(shareLinks.expiresAt, now), eq(shareLinks.isActive, true)));

    return NextResponse.json({
      success: true,
      message: `Updated ${result.count} expired share links`,
    });
  } catch (error) {
    console.error("Error cleaning expired data:", error);
    return NextResponse.json(
      { error: "Failed to clean expired data" },
      { status: 500 }
    );
  }
}
```

#### A3) 添加用户操作存储逻辑

添加在用户登录后保存用户信息的逻辑：

```typescript
// 添加到 lib/user-management.ts
"use server";

import { getDb } from "@/lib/db";
import { users, userBehaviors } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function ensureUserRecord(session: any) {
  if (!session?.user) return null;

  const db = getDb();
  const userId = session.user.id;
  const username = session.user.username;

  // 查找用户
  const existingUser = await db.query.users.findFirst({
    where: eq(users.id, userId),
  });

  // 如果用户不存在，则创建
  if (!existingUser) {
    await db.insert(users).values({
      id: userId,
      name: session.user.name,
      email: session.user.email,
      image: session.user.image,
      githubId: userId, // 假设id就是githubId
      username: username,
      avatarUrl: session.user.image,
      createdAt: Math.floor(Date.now() / 1000),
      updatedAt: Math.floor(Date.now() / 1000),
    });

    // 记录用户行为
    await db.insert(userBehaviors).values({
      userId: userId,
      actionType: "user_created",
      performedAt: Math.floor(Date.now() / 1000),
    });
  }

  // 记录登录行为
  await db.insert(userBehaviors).values({
    userId: userId,
    actionType: "login",
    performedAt: Math.floor(Date.now() / 1000),
  });

  return userId;
}
```

### 3. 数据架构调整建议

#### 1) 改为 PostgreSQL 数据库

考虑从 SQLite 转为 PostgreSQL：

- 更好的并发处理能力
- 更丰富的数据类型和查询功能
- 更好的大规模数据处理能力

#### 2) 添加索引优化

```sql
-- 为常用查询添加索引
CREATE INDEX idx_contribute_datas_username ON contribute_datas(username);
CREATE INDEX idx_share_links_user_id ON share_links(userId);
CREATE INDEX idx_share_links_link_token ON share_links(linkToken);
CREATE INDEX idx_user_behaviors_user_id ON user_behaviors(userId);
CREATE INDEX idx_user_behaviors_action_type ON user_behaviors(actionType);
```

#### 3) 添加定期数据更新机制

创建定时任务，定期更新活跃用户的 GitHub 数据：

```typescript
// app/api/cron/update-github-data/route.ts
import { NextResponse } from "next/server";
import { getDb } from "@/lib/db";
import { contributeDatas } from "@/lib/db/schema";
import { lt } from "drizzle-orm";
import { getUserGitHubData } from "@/lib/server-github";

export async function GET() {
  try {
    const db = getDb();
    const oneDayAgo = Math.floor(Date.now() / 1000) - 24 * 60 * 60;

    // 获取超过24小时未更新的数据
    const outdatedData = await db.query.contributeDatas.findMany({
      where: lt(contributeDatas.lastUpdated, oneDayAgo),
      limit: 50, // 限制每次处理的数量
    });

    // 更新每个过期数据
    const updateResults = await Promise.allSettled(
      outdatedData.map((data) => getUserGitHubData(data.username))
    );

    const successCount = updateResults.filter(
      (result) => result.status === "fulfilled" && result.value.success
    ).length;

    return NextResponse.json({
      success: true,
      message: `Updated ${successCount} of ${outdatedData.length} GitHub data records`,
    });
  } catch (error) {
    console.error("Error updating GitHub data:", error);
    return NextResponse.json(
      { error: "Failed to update GitHub data" },
      { status: 500 }
    );
  }
}
```

## 综合建议

1. **修改认证策略**：从 JWT 转为数据库策略，确保用户数据正确保存
2. **优化数据结构**：拆分 JSON 数据为独立字段，方便查询和更新
3. **增加数据管理**：添加过期数据清理和定期更新机制
4. **优化数据库类型**：考虑从 SQLite 迁移到 PostgreSQL
5. **添加监控和诊断**：增加数据库操作日志，方便排查问题
6. **优化查询性能**：添加必要的索引，优化常用查询
7. **完善用户行为记录**：系统化记录用户行为，便于后续分析

通过以上改进，可以解决当前项目中用户认证和数据存储的问题，并优化数据库设计，提高系统性能和可维护性。
