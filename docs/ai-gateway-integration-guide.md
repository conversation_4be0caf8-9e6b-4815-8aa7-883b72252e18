# Vercel AI Gateway 集成指南

GitHub Card 项目现已集成 **Vercel AI Gateway**，提供统一的多模型接入平台，支持 OpenAI、Anthropic、xAI、Groq 等主流 AI 提供商。

## 🌟 核心优势

### 🎯 统一接入
- **一个 API Key**：访问多个 AI 提供商
- **OpenAI 兼容**：完全兼容 OpenAI API 规范
- **无缝切换**：动态选择不同模型和提供商
- **智能路由**：自动负载均衡和故障转移

### 💰 成本优化
- **透明定价**：按提供商原价计费，无额外加价
- **免费层**：每月 $5 免费额度
- **按需付费**：无最低消费要求
- **成本监控**：内置使用量统计

### 🔧 技术特性
- **流式响应**：支持 Server-Sent Events (SSE)
- **多模态支持**：文本、图片、PDF 文档处理
- **工具调用**：Function Calling 支持
- **错误重试**：指数退避重试机制
- **性能监控**：完整的可观测性

## 🚀 快速开始

### 环境配置

```bash
# .env.local
AI_GATEWAY_API_KEY=your-vercel-ai-gateway-key

# 可选配置
AI_GATEWAY_DEFAULT_MODEL=openai/gpt-4o
AI_GATEWAY_APP_NAME=GitHub-Card
# AI_GATEWAY_BASE_URL=https://ai-gateway.vercel.sh/v1  # 自定义网关地址
```

### 基础使用

```typescript
import { createLLMClient } from "@/lib/ai/core/clients/LLMClientFactory";

const llmClient = createLLMClient();

// 基础文本对话
const response = await llmClient.chat({
  provider: "aigateway",
  model: "openai/gpt-4o",
  messages: [{ role: "user", content: "Hello, world!" }],
  temperature: 0.7,
  max_tokens: 1000,
});

console.log(response.choices[0].message.content);
```

### 流式响应

```typescript
// 流式生成
for await (const chunk of llmClient.chatStream({
  provider: "aigateway",
  model: "anthropic/claude-sonnet-4",
  messages: [{ role: "user", content: "Write a story about AI" }],
  stream: true,
})) {
  const content = chunk.choices[0]?.delta?.content;
  if (content) {
    process.stdout.write(content);
  }
}
```

## 📋 支持的模型

### OpenAI 模型
```typescript
const openaiModels = {
  GPT_4O: "openai/gpt-4o",           // 最新 GPT-4o 模型
  GPT_4O_MINI: "openai/gpt-4o-mini", // 轻量版 GPT-4o
};
```

### Anthropic 模型
```typescript
const anthropicModels = {
  CLAUDE_SONNET_4: "anthropic/claude-sonnet-4", // Claude 最新版本
  CLAUDE_HAIKU: "anthropic/claude-3-haiku",     // 快速响应版本
};
```

### xAI 模型
```typescript
const xaiModels = {
  GROK_3: "xai/grok-3", // Grok 3 模型
};
```

### Meta 模型
```typescript
const metaModels = {
  LLAMA_3_1_70B: "meta-llama/llama-3.1-70b-instruct", // Llama 3.1 70B
};
```

## 🎨 高级功能

### 多模态支持

```typescript
// 图片分析
const imageResponse = await llmClient.chat({
  provider: "aigateway",
  model: "openai/gpt-4o",
  messages: [{
    role: "user",
    content: [
      { type: "text", text: "What's in this image?" },
      { 
        type: "image", 
        image_url: { 
          url: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..." 
        } 
      }
    ]
  }],
});
```

### 工具调用

```typescript
// Function Calling
const toolResponse = await llmClient.chat({
  provider: "aigateway",
  model: "openai/gpt-4o",
  messages: [{ role: "user", content: "What's the weather in Beijing?" }],
  tools: [{
    type: "function",
    function: {
      name: "get_weather",
      description: "Get current weather for a location",
      parameters: {
        type: "object",
        properties: {
          location: { type: "string", description: "City name" }
        },
        required: ["location"]
      }
    }
  }],
  tool_choice: "auto"
});
```

### PDF 文档处理

```typescript
// PDF 文档分析
const pdfResponse = await llmClient.chat({
  provider: "aigateway",
  model: "anthropic/claude-sonnet-4",
  messages: [{
    role: "user",
    content: [
      { type: "text", text: "Summarize this PDF document" },
      { 
        type: "file", 
        file: { 
          data: "base64-encoded-pdf-data",
          media_type: "application/pdf",
          filename: "document.pdf"
        } 
      }
    ]
  }],
});
```

## 📊 性能监控

### 统计信息

```typescript
// 获取 AI Gateway 统计
const stats = llmClient.getPerformanceStats("aigateway");
console.log("AI Gateway 统计:", {
  totalRequests: stats.totalRequests,
  totalTokens: stats.totalTokens,
  avgTokensPerRequest: stats.avgTokensPerRequest,
  lastUpdated: new Date(stats.lastUpdated)
});
```

### 健康检查

```typescript
// 检查 AI Gateway 健康状态
const isHealthy = await llmClient.healthCheck("aigateway");
console.log("AI Gateway 状态:", isHealthy ? "正常" : "异常");
```

## 🔧 配置选项

### 便捷配置函数

```typescript
import { createAIGatewayConfig } from "@/lib/ai/core/clients/LLMClientFactory";

// 使用默认模型 (openai/gpt-4o)
const defaultConfig = createAIGatewayConfig();

// 指定特定模型
const claudeConfig = createAIGatewayConfig("anthropic/claude-sonnet-4", {
  temperature: 0.6,
  max_tokens: 2000,
});

// 自定义配置
const customConfig = createAIGatewayConfig("xai/grok-3", {
  temperature: 0.8,
  max_tokens: 1500,
  apiKey: "custom-api-key", // 覆盖环境变量
});
```

### 模型选择策略

```typescript
const modelStrategy = {
  // 成本优化
  costEffective: "openai/gpt-4o-mini",
  
  // 高质量推理
  highQuality: "anthropic/claude-sonnet-4",
  
  // 创新思维
  creative: "xai/grok-3",
  
  // 开源选择
  opensource: "meta-llama/llama-3.1-70b-instruct",
};

// 根据任务类型选择模型
function selectModel(taskType: string) {
  switch (taskType) {
    case "analysis":
      return modelStrategy.highQuality;
    case "creative":
      return modelStrategy.creative;
    case "simple":
      return modelStrategy.costEffective;
    default:
      return "openai/gpt-4o"; // 默认模型
  }
}
```

## ⚠️ 最佳实践

### 错误处理

```typescript
import { AIGatewayClientError } from "@/lib/ai/core/clients/AIGatewayClient";

try {
  const response = await llmClient.chat({
    provider: "aigateway",
    model: "openai/gpt-4o",
    messages: [{ role: "user", content: "Hello" }],
  });
} catch (error) {
  if (error instanceof AIGatewayClientError) {
    console.error("AI Gateway 错误:", {
      message: error.message,
      code: error.errorCode,
      retryable: error.retryable,
    });
    
    // 可重试错误的处理
    if (error.retryable) {
      console.log("错误可重试，系统将自动重试...");
    }
  } else {
    console.error("未知错误:", error);
  }
}
```

### 超时配置

```typescript
// 设置请求超时
const response = await llmClient.chat({
  provider: "aigateway",
  model: "anthropic/claude-sonnet-4",
  messages: [{ role: "user", content: "Complex analysis task" }],
  timeout: 120000, // 2分钟超时
});
```

### 成本控制

```typescript
// 限制 token 使用量
const response = await llmClient.chat({
  provider: "aigateway",
  model: "openai/gpt-4o",
  messages: [{ role: "user", content: "Generate a summary" }],
  max_tokens: 500, // 限制输出长度
  temperature: 0.3, // 降低随机性以提高一致性
});
```

## 🔗 相关链接

- [Vercel AI Gateway 官方文档](https://vercel.com/docs/ai-gateway)
- [OpenAI API 兼容性说明](https://vercel.com/docs/ai-gateway/openai-compat)
- [支持的模型和提供商](https://vercel.com/docs/ai-gateway/models-and-providers)
- [定价信息](https://vercel.com/docs/ai-gateway/pricing)

## 📝 更新日志

- **v1.0.0** - 初始集成 AI Gateway 支持
- 支持基础文本对话和流式响应
- 集成多模态内容处理
- 实现工具调用功能
- 添加错误处理和重试机制
- 完整的性能监控和统计
