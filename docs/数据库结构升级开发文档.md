# 数据库结构升级开发文档

## 1. 概述

本文档概述了通过引入新的 `contribute_data` 表来存储 GitHub 用户贡献数据并修改现有 `share_links` 表的必要更改。主要目标是：

1. 创建 `contribute_data` 表以存储有 24 小时有效期的 GitHub 用户数据
2. 修改 `share_links` 表以移除冗余的 `cardData` 字段
3. 更新 API 端点和前端组件以适配新结构

## 2. 数据库架构变更

### 2.1 新表：contribute_data

将创建一个名为 `contribute_data` 的新表，用于存储 GitHub 用户贡献数据，结构如下：

```sql
CREATE TABLE "contribute_data" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "username" VARCHAR(255) NOT NULL UNIQUE,
  "user_data" JSONB NOT NULL,
  "contribution_data" JSONB NOT NULL,
  "last_updated" TIMESTAMP NOT NULL DEFAULT NOW(),
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW()
);
```

该表将：

- 按用户名存储 GitHub 用户数据（唯一）
- 包含基本配置文件数据和贡献统计信息
- 跟踪数据最后更新时间，用于 24 小时新鲜度检查

### 2.2 修改表：share_links

`share_links` 表将被修改，移除 `cardData` JSON 字段并添加 `githubUsername` 字段：

```sql
ALTER TABLE "share_links"
ADD COLUMN "github_username" VARCHAR(255) NOT NULL;

ALTER TABLE "share_links"
DROP COLUMN "card_data";
```

在迁移过程中，将使用现有 `card_data` 字段中的数据填充新的 `github_username` 字段，然后再移除 `card_data` 列。

## 3. 代码更改

### 3.1 数据库架构定义 (`lib/db/schema.ts`)

1. 添加新的 `contributeDatas` 表定义
2. 更新 `shareLinks` 表定义，使用 `githubUsername` 替代 `cardData`
3. 添加用户和 contributeDatas 之间的关系
4. 为新表添加 TypeScript 类型

### 3.2 GitHub API 模块 (`lib/github/api.ts`)

1. 修改 `getGitHubUserData` 和 `getGitHubContributions` 函数以：
   - 首先检查 `contribute_data` 表中是否存在新鲜数据
   - 如果数据存在且少于 1 天，则返回该数据
   - 否则，从 GitHub API 获取数据
   - 将新鲜数据存储在 `contribute_data` 表中以供将来使用
2. 实现新的 `storeContributionDataInDb` 函数来处理数据库操作

### 3.3 服务器操作 (`lib/server-github.ts`)

更新服务器操作以配合修改后的 API 函数工作，并创建格式正确的 `GitHubData` 对象。

### 3.4 API 端点

#### 3.4.1 分享链接 API (`app/api/share-links/route.ts`)

1. 修改 POST 端点：

   - 移除 `cardData` 处理
   - 仅在 `share_links` 表中存储 `githubUsername`
   - 继续更新用户在排行榜中的贡献分数

2. 更新 GET 端点，在响应中包含 `githubUsername` 而非 `cardData`

#### 3.4.2 单个分享链接 API (`app/api/share-links/[token]/route.ts`)

1. 更改端点以：
   - 获取分享链接记录以获取 `githubUsername`
   - 使用该用户名调用 `getUserGitHubData` 获取最新的贡献数据
   - 以与之前相同的格式返回数据

### 3.5 前端组件

#### 3.5.1 共享卡片页面 (`app/shared/[token]/page.tsx`)

前端组件不需要重大更改，因为它期望相同的数据结构。唯一的变更是改进控制台日志记录以便更好地调试。

## 4. 迁移过程

1. 创建迁移脚本 (`lib/db/migrate-01-add-contribute-data.ts`)，该脚本：

   - 创建新的 `contribute_data` 表
   - 向 `share_links` 添加 `github_username` 列
   - 从现有 `card_data` 填充 `github_username`
   - 使 `github_username` 为 NOT NULL
   - 删除 `card_data` 列

2. 在 package.json 中添加运行迁移的脚本：
   ```json
   {
     "scripts": {
       "db:migrate:contribute-data": "tsx lib/db/migrate-01-add-contribute-data.ts"
     }
   }
   ```

## 5. 测试计划

1. **数据库迁移测试**

   - 运行迁移脚本并验证所有表是否正确创建/修改
   - 检查现有数据是否正确迁移

2. **API 测试**

   - 测试带有新数据库缓存的 GitHub 数据获取
   - 验证分享链接 API 在没有 `cardData` 的情况下是否工作
   - 测试基于令牌的 API 以确保它正确获取 GitHub 数据

3. **前端测试**
   - 测试共享卡片页面以确保它仍然正确显示
   - 验证卡片生成和分享是否仍然与新结构一起工作

## 6. 部署过程

1. 创建生产数据库的备份
2. 将代码更改部署到暂存环境
3. 在暂存环境上运行迁移脚本并验证
4. 在暂存环境上彻底测试应用程序
5. 部署到生产环境并运行迁移脚本
6. 验证应用程序在生产环境中是否正常工作

## 7. 回滚计划

如果遇到问题：

1. 从备份恢复数据库
2. 还原代码更改
3. 部署之前的版本

## 8. 其他考虑因素

1. **性能**：新方法通过减少对 GitHub 的冗余 API 调用来提高性能
2. **存储**：通过移除重复的 GitHub 数据来优化数据库大小
3. **API 限制**：通过减少调用次数，更好地管理 GitHub API 速率限制
4. **数据新鲜度**：用户始终会看到最多 24 小时前的数据

## 9. 未来改进

1. 添加后台作业以定期刷新 `contribute_data` 中的数据
2. 实现更复杂的缓存策略，为不同类型的数据设置不同的 TTL
3. 添加数据库与 API 使用情况的指标跟踪

## 10. 结论

此数据库升级将通过有效缓存用户贡献数据，显著提高应用程序的性能并减少 GitHub API 的使用。迁移过程设计为无干扰性，并有明确的回滚计划以应对可能出现的问题。
