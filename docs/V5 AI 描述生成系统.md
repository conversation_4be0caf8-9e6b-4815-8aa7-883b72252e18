# **为个性化 AI 开发者描述生成系统构建的系统架构与提示工程技术方案**

## **1\. 一种用于创意生成的模块化、链式思维系统架构**

为了实现从量化的 GitHub 开发者数据到富有创意和幽默感的个性化描述文本的转化，单一、庞大的提示词（Prompt）难以胜任。此类任务在本质上包含多个逻辑上独立的子任务：数据解读、创意策略构思、文本风格化生成以及质量评估。一个将所有指令糅合在一起的"万能"提示词，不仅会因其复杂性而导致大型语言模型（LLM）性能下降，更会在出现问题时使调试过程变得异常困难，如同一个无法探查内部运作的"黑箱" 1。

因此，本报告提出一个模块化的、基于提示链（Prompt Chaining）的系统架构。该架构将复杂的生成任务分解为一系列更小、更专注的子任务，每个子任务由一个独立的模块（即一次独立的 LLM API 调用）处理 2。这种设计理念的核心优势在于，它将复杂的创意过程拆解为可管理、可独立测试和可调试的单元，显著提升了系统的可控性、可靠性和最终输出质量 4。

### **1.1. 整体工作流程：从 GitHub 统计到喜剧文本**

所设计的系统包含一个四阶段的流水线，每个阶段都扮演着独特的角色：**分析器（Analyzer） \-\> 策略师（Strategist） \-\> 写手（Writer） \-\> 评论家（Critic）**。

1. **分析器（Analyzer）**：接收原始的 GitHub 数据，将其转化为结构化的、带有情感价位（Valence）和初步解读的 JSON 对象。
2. **策略师（Strategist）**：接收分析器输出的结构化数据，并根据整体数据画像和用户偏好，决策采用何种喜剧策略和叙事角度。
3. **写手（Writer）**：接收原始数据、分析结果和策略师的"创意简报"，最终生成符合特定风格和要求的幽默文本。
4. **评论家（Critic）**：一个可选的高级模块，对写手生成的文本进行评估和批判，为自动优化或向用户提供修改建议提供依据。

这种架构将数据分析与创意生成彻底分离，确保每一步都具有极高的精确度。它不仅是组织代码的清晰方式，更是一种内嵌的、低成本的调试和迭代策略，直接响应了对系统可维护性和可测试性的核心需求 2。

### **1.2. 模块一：分析器 - 将原始数据结构化为可行动的洞察**

此模块的核心职责是将用户提供的、非结构化的原始数据（例如 { "commits": 5000, "reviews": 0 }）以及用户设定的参数，转化为一个高度结构化、便于后续模块处理的 JSON 对象。

提示词策略：
此模块的提示词将指示 DeepSeek R1 扮演一名"GitHub 数据分析师"的角色。提示词会提供原始数据，并明确要求模型遵循一个预定义的 JSON Schema 进行输出。为了确保输出的纯净和有效性，提示词会强调"仅返回有效的 JSON，不包含任何解释性文字或 Markdown 标记" 5。
输出格式 (JSON)：
输出应为一个 JSON 数组，其中每个对象代表一个输入的数据指标。这种格式清晰地分离了每个数据点的属性，为后续模块提供了标准化的输入。

JSON

\[
{
"metric": "commits",
"value": 5000,
"valence": "highly_positive",
"analysis": "极高的提交次数，表明开发者异常活跃和投入。"
},
{
"metric": "reviews",
"value": 0,
"valence": "negative",
"analysis": "缺乏代码审查可能意味着独立工作，或在协作流程中存在潜在短板。"
},
{
"metric": "followers",
"value": 1200,
"valence": "positive",
"analysis": "拥有相当数量的关注者，表明在社区中具有一定的影响力。"
}
\]

为了将数据分析这一主观过程标准化，从而确保 AI 行为的可预测性，我们预先定义了一套数据价位映射规则。这套规则不仅为分析器模块提供了清晰的逻辑指引，其分层设计（例如，"积极"与"非常积极"）也为后续的幽默创作提供了天然的素材，使得夸张和讽刺的程度可以依据数据强度进行调整。

**表 1：GitHub 指标价位映射表**

| 指标名称 (Metric Name) | 数值范围 (Value Range) | 指定价位 (Assigned Valence) | 样本解读/论点 (Sample Interpretation/Talking Point) |
| :--------------------- | :--------------------- | :-------------------------- | :-------------------------------------------------- |
| commits                | 0-50                   | Negative                    | 几乎没有活动迹象。这个账户还活跃吗？                |
| commits                | 51-1000                | Neutral/Positive            | 一位活跃的贡献者。                                  |
| commits                | 1001-5000              | Positive                    | 一位非常高产的贡献者。                              |
| commits                | 5001+                  | Highly Positive             | 简直是台代码永动机，怀疑是赛博格。                  |
| reviews                | 0                      | Negative                    | 在真空中工作，一匹孤狼。                            |
| reviews                | 1-100                  | Neutral                     | 参与团队协作，但频率不高。                          |
| reviews                | 101+                   | Positive                    | 积极参与代码审查，是团队协作的核心。                |
| followers              | 0-10                   | Negative                    | 社区影响力有限，可能还是新人。                      |
| followers              | 11-1000                | Positive                    | 在社区中有一定关注度和影响力。                      |
| followers              | 1001+                  | Highly Positive             | 社区中的明星人物，具有显著影响力。                  |
| totalStars             | 0-50                   | Neutral                     | 项目获得的认可度有限。                              |
| totalStars             | 51+                    | Positive                    | 项目受到社区的广泛欢迎和认可。                      |
| pullRequests           | 0-10                   | Negative                    | 对其他项目的贡献较少。                              |
| pullRequests           | 11+                    | Positive                    | 积极为开源社区做贡献。                              |

在 Next.js 后端，应对分析器的输出进行强制校验，使用 json.loads()或类似工具解析，并剥离任何可能存在的 Markdown 代码块标记（如\`\`\`json），以保证数据在模块间传递的健壮性 6。

### **1.3. 模块二：幽默策略师 - 选择喜剧切入点**

此模块扮演"创意总监"或"喜剧编剧室"的角色。它接收来自分析器的结构化 JSON，并基于数据的整体情感价位以及用户指定的任何约束（如情绪：'自嘲'，视角：'第一人称'），决定采用何种喜剧策略。

提示词策略：
这是一个纯粹的推理任务，非常适合发挥 DeepSeek R1 的强大推理能力 7。提示词将采用零样本（Zero-Shot）方式，指示模型"扮演一位脱口秀节目的首席编剧"。模型将被赋予分析器的 JSON 输出，以及一个包含可用喜剧策略和模板的列表。提示词会要求模型"一步一步地思考"（这是激活 DeepSeek R1 深度思考能力的关键技巧 8），以选择最合适的策略。
输出格式 (JSON)：
策略师的输出是一个清晰的"创意简报"，它将指导下一个模块的工作。

JSON

{
"selected_strategy_id": "Humblebrag",
"target_emotion": "feigned_humility",
"perspective": "first_person",
"selected_template_id": "TPL_004",
"key_talking_points": \[
"5000 次 commits 这个数字本身的荒谬性。",
"0 次 reviews 所代表的修道院般的沉寂。"
\],
"justification": "用户数据呈现出极端的两极分化（极高的 commits 和零 reviews），非常适合采用'凡尔赛式自夸'（Humblebrag）策略，通过第一人称视角，以一种看似谦虚实则炫耀的口吻，来营造戏剧性的幽默效果。"
}

将创意策略的决策（决定*说什么*）与最终文本的执行（决定*怎么说*）分离开来，是提升创意任务可控性的核心。专业创意工作流程也遵循类似的模式：先有策略和提纲，再进行具体内容的撰写 9。这种解耦使得整个创意过程更加透明，策略师模块的输出为写手模块提供了明确的指导，极大地增加了最终文本符合预期的可能性。

### **1.4. 模块三：写手 - 精雕细琢的独白创作**

这是核心的文本生成模块。它将原始数据、分析器的 JSON 以及策略师的"创意简报"JSON 三者融会贯通，合成为最终的、充满幽默感的文本。

提示词策略：
此模块的成功在很大程度上依赖于\*\*少样本学习（Few-Shot Learning）\*\*来精确控制输出的风格 10。提示词将动态构建，包含以下几个关键部分：

1. **角色扮演指令**："你是一位言辞犀利、风格诙谐的脱口秀演员，类似于李诞或范志毅" 12。
2. **创意简报**：来自策略师模块的完整 JSON 输出。
3. **核心数据**：与所选策略最相关的几个数据点。
4. **少样本示例**：提供 3-5 个高质量的目标风格范例。这些范例从对中国当代幽默的研究中精心挑选，并使用清晰的"哨兵（Sentinels）"结构来组织，以提高模型学习效率 10。

**少样本示例结构示例**：

: {"metric": "followers", "value": 2, "valence": "negative"}
: {"selected_strategy_id": "Self_Deprecation", "perspective": "first_person"}
: 我在 GitHub 上有两个粉丝，一个是我自己，另一个是我的小号，专门用来提醒我大号该更新了。我们仨，构成了一个稳定而又心酸的开发生态。
\---
: {"metric": "commits", "value": 8000, "valence": "highly_positive"}
: {"selected_strategy_id": "Hyperbolic_Praise", "perspective": "second_person"}
: 你有 8000 次 commit？哥们儿，说真的，你这不是在写代码，你这是在给键盘做物理超度。你家附近的网吧是不是都拉着横幅："庆祝本店键盘成功在\[你的名字\]手下存活超过 24 小时"?"
\---

关于 DeepSeek R1 的提示策略，存在一些看似矛盾的建议。部分资料表明，该模型在零样本推理任务上表现卓越，少样本示例有时甚至可能干扰其强大的逻辑推理能力 8。然而，大量关于风格控制的研究证实，少样本学习是实现特定文体迁移的最有效手段之一 15。

这种矛盾可以通过区分任务类型来调和。对于逻辑推理和分析任务，DeepSeek R1 的零样本能力应被充分利用。但"风格"并非一个纯粹的逻辑问题，它是一个模糊的、高维的模式。模仿李诞的语言节奏、词汇选择和修辞结构，很难仅通过指令来精确描述，它更需要通过*展示*来学习。

因此，本架构采用了一种混合策略：

- **分析器**和**策略师**这两个重推理、轻风格的模块，采用**零样本提示**，以最大化发挥 DeepSeek R1 的内在推理优势。
- **写手**这个重风格、轻推理的模块，采用**少样本提示**，因为这是进行风格迁移和模仿的最可靠方法。

这种精细化的策略选择，解决了表面上的研究矛盾，并为不同类型的任务匹配了最优的提示范式。

### **1.5. 模块四：评论家 - 自我修正与优化循环**

这是一个可选的高级模块，体现了 DeepSeek R1 强大的自我反思能力 8。它接收来自写手模块生成的文本，并根据一系列预设标准对其进行评估。

提示词策略：
提示词将指示模型："你是一位要求严苛的喜剧编辑。以下文本旨在满足这些约束：\[来自策略师的约束条件\]。源数据是：\[相关数据\]。请从三个维度对文本进行批判：1. 幽默性（是否真的好笑？），2. 合规性（是否遵循了所有约束？），3. 原创性（是否落入俗套？）。请给出一句总结性评价，并提出一条具体的改进建议。"
应用场景：
评论家的输出可以直接展示给用户，作为"优化建议"。更进一步，它可以用来自动触发写手模块的第二次运行，并将批判意见作为新的指令加入提示词中（例如，"重新生成这段文本，但让笑点（Punchline）更出人意料"）。这构建了一个强大的、自动化的文本优化循环，能够持续提升输出质量 1。

## **2\. 针对 DeepSeek R1 模型的高级提示工程**

本节将深入探讨为 DeepSeek R1 编写高效提示词的具体技术，将第一节的架构原则转化为可直接应用的提示词结构。

### **2.1. 驾驭 DeepSeek R1 的推理引擎：分步思考与自我反思**

为了充分激活 DeepSeek R1 的深度推理能力，应在提示词中系统性地使用引导性短语。诸如"一步一步地思考"、"分解这个问题"、"首先，分析......然后，总结......"等指令，能有效促使模型采用更系统、更严谨的思维路径，从而提升复杂任务的准确性 8。

此外，可以鼓励模型进行自我反思和多路径探索。例如，在策略师模块中，可以这样提问："针对这份数据，构思两种不同的喜剧角度。然后，评估每种角度的优劣，选择最佳方案并解释你的理由。"这种方法迫使模型进行比较和权衡，往往能产出更具深度的决策 8。

虽然官方文档未详述，但社区实践表明，模拟模型的内部思考过程是一种有效技巧 18。可以通过在提示词中设定结构来实现这一点：

请遵循以下结构进行思考和回答：
\<thinking\>
在这里写下你的分步分析过程、推理和决策依据。
\</thinking\>
\<answer\>
在这里给出最终的、格式化的输出。
\</answer\>

这种结构迫使模型将其"思维链"外化，不仅提升了输出的逻辑性，也为调试提供了极大的便利。当输出不符合预期时，检查\<thinking\>标签内的内容可以快速定位问题所在。

### **2.2. 结构化数据提示：确保稳健的 JSON 交接**

在模块化的工作流中，模块之间通过 JSON 进行通信，因此确保 LLM 稳定输出有效的 JSON 至关重要。综合多方研究，以下是一套确保 JSON 输出可靠性的最佳实践 5：

1. **明确指令**：在提示词中清晰、强硬地声明要求。"你的回答必须且只能是一个有效的 JSON 对象，不要添加任何额外的解释、注释或 Markdown 格式。" 5。
2. **定义 Schema**：在提示词中提供清晰的输出格式定义，可以采用类似 JSON Schema 或 Pydantic 模型的描述方式，明确每个键的名称、数据类型和用途描述 5。
3. **提供范例**：给出一个（且仅一个）格式完美的 JSON 输出示例。模型非常擅长模仿结构，一个好的范例胜过千言万语 5。
4. **预填充回答**：在 API 调用中，可以预先填充助手（Assistant）角色的回答，以一个开括号{开始。这会强烈暗示模型继续完成一个 JSON 对象 6。
5. **使用 XML 标签**：将期望的 JSON 输出包裹在自定义的 XML 标签中，如\<json\>和\</json\>。这使得后端的解析器可以更轻松、更可靠地从模型的完整响应中提取出目标内容，即使模型意外地添加了一些额外文本 6。

### **2.3. 角色扮演与上下文预设的力量**

为模型分配一个具体、专业的角色，能够极大地提升其输出的质量和相关性。相比于一个通用的"助手"，一个"愤世嫉俗的喜剧演员"或"严谨的数据科学家"能更好地理解任务的上下文和隐含要求 7。

在本系统的设计中，每个模块都应被赋予一个独特的角色：

- **模块一（分析器）**：GitHub 数据分析专家
- **模块二（策略师）**：金牌喜剧编剧
- **模块三（写手）**：风格百变的脱口秀演员
- **模块四（评论家）**：毒舌喜剧评论人

在提示词的开头就设定好角色，能为整个生成过程奠定正确的基调。

### **2.4. 设计包含动态参数注入的"主提示词"**

为了实现用户在前端界面上的微调功能（如选择视角、情绪、必须包含或规避的词语），需要设计一个灵活的"主提示词"模板。这个模板主要用于**写手模块**。

该模板将包含多个占位符，例如{{PERSPECTIVE}}, {{TARGET\_EMOTION}}, {{INCLUDE\_WORD}}, {{EXCLUDE\_WORD}}等。Next.js 后端在接收到用户的请求后，会根据用户的选择动态地填充这些占位符，生成最终的、完全定制化的提示词，然后发送给 DeepSeek R1 的 API。

**模板示例**：

你是一位{{PERSONA}}。
你的任务是根据以下创意简报和数据，生成一段文本。

\# 创意简报
{{STRATEGIST\_JSON}}

\# 核心数据
{{ANALYZER\_JSON}}

\# 风格范例
{{FEW\_SHOT\_EXAMPLES}}

\# 额外约束
\- 叙事视角必须是：{{PERSPECTIVE}}
\- 文本必须传达的情绪是：{{TARGET\_EMOTION}}
\- 文本中必须包含词语：{{INCLUDE\_WORD}}
\- 文本中必须避免使用词语：{{EXCLUDE\_WORD}}

请开始你的创作：

这种方法将前端的用户交互与后端的提示工程无缝连接起来，实现了高度的个性化和灵活性。

## **3\. 掌握风格控制：一种基于库的幽默生成方法**

本节直接解决项目的核心挑战：如何让 AI 可靠地生成具有特定、微妙喜剧风格的文本。所采用的方法是将目标风格解构成可量化、可调用的组成部分，并通过一个精心策划的示例库来"教会"模型这些技巧。

### **3.1. 解构中式幽默：构建"修辞与笑话结构"库**

要让 AI 模仿一种风格，首先必须对这种风格进行定义和解构。通过分析《吐槽大会》、《脱口秀大会》等节目以及李诞等喜剧演员的文本，可以识别出一系列反复出现的、有效的喜剧修辞手法和笑话结构 12。

这些技巧构成了我们的"喜剧策略与修辞库"，它将抽象的"幽默感"转化为一个可供**策略师模块**选择的"菜单"。

**核心喜剧技巧分析**：

- **铺垫与笑点 (Setup/Punchline)**：所有笑话的基础结构。铺垫建立一个预期，笑点则以一种意想不到但又合乎逻辑的方式打破这个预期 24。
- **夸张 (Hyperbole)**：将某一特征或事实放大到荒谬的程度，以产生喜剧效果。例如，范志毅吐槽周琦发球失误："我上去用脚都能传给别人，你用手都不行！" 13。
- **反语与讽刺 (Irony & Sarcasm)**：用与本意相反的话语来表达真实意图，常用于吐槽和批判。例如，范志毅："真没想到，（中国）足球还能有鞭策别人的一天。" 13。
- **譬喻 (Analogy & Metaphor)**：通过比喻将不相关的事物联系起来，使观点更生动、更具攻击性或更易于理解。例如，杨鸣评价某种打法："跟一渣男似的，不主动不拒绝，我还不负责。" 13。
- **自嘲 (Self-Deprecation)**：通过调侃自身的缺点或尴尬处境来拉近与观众的距离，建立亲和力。这是许多脱口秀演员的核心技巧 23。
- **预期违背与身份反转**：创造与常识或身份不符的情境。例如，一个地位较低的人（范志毅代表的国足）去"教育"一个当时成绩更好的领域（中国男篮），这种错位本身就构成了巨大的喜剧张力 13。
- **金句式观察 (Witty Observation)**：从平凡的生活或数据中提炼出精辟、带有哲理或忧伤色彩的幽默评论，这是李诞的标志性风格 25。例如，小佳的段子："我们都有病，只是我的比较明显。" 26。

将这些技巧系统化，就形成了一个强大的策略库。

**表 2：喜剧策略与修辞库**

| 策略名称 (Strategy Name)             | 描述 (Description)                                         | 典型用例 (Data Valence)                             | 经典示例 (Illustrative Example) \[来源\]                                                                    |                                                                               |
| :----------------------------------- | :--------------------------------------------------------- | :-------------------------------------------------- | :---------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------- |
| **自嘲 (Self-Deprecation)**          | 突出并调侃自身的缺点或尴尬，以获得共鸣和幽默。             | 主要用于处理用户的**负面**数据。                    | "我在 GitHub 上有两个粉丝，一个是我自己，另一个是我的小号，专门用来提醒我大号该更新了。" \[脱口秀通用原则\] |                                                                               |
| **凡尔赛式自夸 (Humblebrag)**        | 以抱怨或谦虚的口吻，不经意地炫耀自己的优点。               | 用于处理用户的**非常积极 (Highly Positive)** 数据。 | "最近太累了，写了 5000 次 commit，感觉手指头都快成机械的了，真羡慕那些有时间休息的人。" 27                  |                                                                               |
| **夸张攻击 (Hyperbolic Roast)**      | 将对方的某个负面特征或失误进行极度夸张的描绘。             | 用于处理**负面**数据，尤其是在第二/三人称视角下。   | "（对周琦）那个发球我看了好几遍，我上去拿脚都能传给别人，你拿手都不行！" 13                                 |                                                                               |
| **身份反转式说教 (Ironic Reversal)** | 利用身份或地位的错位，以一种不合常理的姿态进行"说教"。     | 用于处理混合数据，营造戏剧性冲突。                  | "（范志毅谈男篮）真没想到，足球还能有鞭策别人的一天。" 13                                                   |                                                                               |
| **荒诞类比 (Absurd Analogy)**        | 用一个风马牛不相及但又在某个点上极其贴切的比喻来描述事物。 | 适用于任何数据，能创造强烈的画面感。                | "（杨鸣评周琦）上场叉着腰溜达，球进了你鼓掌，你是教练啊！以后咱打球，娘们一点，别老爷们儿唧唧的。" 13       |                                                                               |
| **金句式总结 (Philosophical Quip)**  | 从具体事件中提炼出带有普遍性、甚至一丝忧伤的精辟短句。     | 适用于任何数据，是提升文本格调的关键。              | "我们都有病，只是我的比较明显。" 26 或                                                                      | "现代生活就是一场茫茫黑夜漫游，我们看到的往往只是另一个人晃来晃去的手电。" 25 |

### **3.2. 通过少样本学习实现风格植入：策划范例集**

拥有了策略库之后，下一步是创建高质量的少样本范例集，这是将风格"教给"**写手模块**的关键。

**范例集策划步骤**：

1. **素材来源**：从《吐槽大会》、李诞的播客和表演、以及其他相关文化产品中，转录和分析经典台词 23。
2. **抽象模式**：对每一句金句，分析其背后的模式：它是在回应什么样的数据/情境？它运用了哪种修辞手法？它的句式结构是怎样的？
3. **创造合成范例**：基于抽象出的模式，使用项目的具体 GitHub 指标作为输入，编写全新的、原创的范例。例如，将范志毅吐槽周琦"1 分不容易"的模式，应用到 pullRequests 数据上："整个月就 1 个 PR，拿 1 个 PR 不容易的！我写个文档的字数都比你这代码多！"
4. **结构化组织**：使用前文提到的"哨兵"格式（如:...:...:...）来组织所有范例，确保模型能够清晰地理解输入、策略和期望输出之间的关系 10。

这个范例集是系统的核心资产之一，需要持续维护和扩充。

### **3.3. 控制叙事视角与情感基调**

用户的微调需求可以通过在提示词中加入明确的指令来满足。

- **叙事视角**：在**写手模块**的提示词中直接规定："请使用**第一人称（我）进行叙述"、"请直接对用户说话，使用第二人称（你）**"、"请以**第三人称（他/她）**来描述这位开发者"。
- **情感基调**：情感基调是**策略师模块**输出的"创意简报"中的一个关键字段。**写手模块**的提示词将包含这样的指令："文本的整体情感基调必须是**{{TARGET\_EMOTION}}**，例如，要体现出**讽刺/骄傲/谦逊**的感觉。" 同时，少样本范例集中也应包含对应不同情绪的例子，以提供更具体的风格参考。

## **4\. 一种用于迭代测试与调试的低成本框架**

本节将提供一个实用的、成本效益高的质量保证（QA）策略，直接回应关于如何调试和优化 AI 输出的问题。该框架充分利用了第一节中建立的模块化架构。

### **4.1. 提示词的单元测试：隔离并验证每个模块**

模块化架构的最大好处之一是它允许对每个提示词进行"单元测试"。这意味着可以独立地测试和验证链条中的每一个环节，这远比每次都运行完整的端到端流程要便宜和高效得多 4。

- **测试分析器模块**：创建一个测试套件，包含各种边界情况的输入数据（例如，全零数据、极大值数据、部分缺失数据）。断言其输出始终是有效的 JSON，并且 valence 字段的分类符合**表 1**中定义的逻辑。
- **测试策略师模块**：使用经过验证的分析器输出来测试策略师。断言其 selected_strategy_id 的选择在逻辑上是合理的（例如，对于纯负面数据，不应选择"凡尔赛式自夸"）。
- **测试写手模块**：使用固定的"创意简报"和数据来测试写手。主要评估其输出是否遵循了所有的风格、视角和情绪约束。

这种方法可以快速定位问题，例如，如果最终文本不好笑，单元测试可以帮助确定是策略选择错误，还是文本生成环节的风格模仿失败。

### **4.2. 构建并利用"黄金集"进行端到端评估**

对于创意生成任务，需要一个超越简单单元测试的评估方法。为此，建议构建一个包含 20-30 个多样化开发者画像的"黄金测试集"。这些画像应覆盖各种典型场景，如：高提交的独行侠、低代码但高影响力的管理者、刚入门的新贡献者等。

对于这个黄金集，我们不定义一个精确的、逐字匹配的期望输出字符串，因为这对于创意任务来说过于僵化。取而代之的是，为每个画像定义一套"验收标准（Acceptance Criteria）"。

**黄金集条目示例**：

- **画像**：高 commits (10000+), 零 reviews, 高 followers (5000+)。
- **验收标准**：
  1. 输出必须是第一人称。
  2. 必须采用"凡尔赛式自夸"或"金句式总结"策略。
  3. 必须同时提及极高的 commits 和零 reviews 这两个矛盾点。
  4. 文本必须被至少 3 名内部评估员中的 2 名评为"有趣"或"非常有趣"。
  5. 文本长度应在 50 到 100 个汉字之间。

这个黄金集构成了应用的回归测试套件。每当对提示词或模型进行重大更新时，运行此测试集可以确保新的改动没有破坏已有的、表现良好的行为。

### **4.3. 实施人机回圈的反馈系统进行定性分析**

自动化测试无法完全衡量创意输出的质量。最终的评判标准来自真实用户。因此，在 Next.js 应用中集成一个简单的用户反馈机制至关重要。

可以在每个生成的描述旁边放置一个简单的"赞/踩"按钮或 1-5 星的评分系统。当用户提交反馈时，系统应记录以下完整信息：

- 用户的评分。
- 用户的原始输入数据。
- 链条中每个模块（分析器、策略师）的完整 JSON 输出。
- 发送给写手模块的最终完整提示词。
- 写手模块返回的最终文本。

这些数据是无价之宝。通过分析低分案例，可以快速发现提示词库的薄弱环节、不受欢迎的喜剧策略，或模型在处理某些特定数据组合时的"幻觉"。

### **4.4. 全面日志记录的重要性：用完整上下文进行调试**

为了实现高效的故障排查，必须对每一次生成请求的完整"对话"进行日志记录。这包括从用户最初的输入到最终输出的每一个中间步骤 1。当用户报告一个不满意的结果时，开发人员可以调阅这份完整的日志，从而精确地复现问题场景，而无需猜测是链条中的哪个环节出了问题。这极大地降低了维护成本，并加速了系统的迭代周期。

## **结论与建议**

本项目旨在构建一个技术上具有挑战性但商业上富有吸引力的 AI 应用。要成功实现这一目标，必须超越简单的单次提示生成，采用一个更系统、更精密的工程化方法。

本报告提出的核心解决方案是一个**模块化的、四阶段的提示链架构**。该架构将复杂的创意任务分解为可控的、可测试的逻辑单元，这是确保系统稳定性和输出质量的基石。

在此架构之上，报告详细阐述了三项关键技术策略：

1. **混合式提示范式**：针对 DeepSeek R1 的特性，对重推理的分析和策略模块采用**零样本提示**，而对重风格的写作模块采用**少样本提示**。这种精细化的方法能够最大限度地发挥模型的综合能力。
2. **严格的结构化数据传递**：通过一系列确保 LLM 输出有效 JSON 的技术，保证了模块间信息流的稳定可靠，这是整个自动化流程能够顺利运作的前提。
3. **基于解构的风格控制**：通过建立一个包含中式幽默核心修辞手法的"策略库"，并将这些策略通过精心策划的少样本范例"教给"模型，实现了对输出文本风格的精确、可预测的控制。

最后，报告提出了一套结合了**单元测试、黄金集回归测试和人机回圈反馈**的低成本测试框架。该框架与模块化架构相辅相成，为应用的持续迭代和质量提升提供了坚实保障。

综上所述，通过采纳本报告中详述的系统架构、提示工程技术和测试框架，开发团队将能够构建一个功能强大、输出质量高且易于维护的个性化 AI 描述生成系统，成功地将枯燥的开发者数据转化为具有高度社交传播潜力的幽默内容。

#### **引用的著作**

1. Prompt Chaining Guide \- PromptHub, 访问时间为 六月 30, 2025， [https://www.prompthub.us/blog/prompt-chaining-guide](https://www.prompthub.us/blog/prompt-chaining-guide)
2. What is prompt chaining? \- IBM, 访问时间为 六月 30, 2025， [https://www.ibm.com/think/topics/prompt-chaining](https://www.ibm.com/think/topics/prompt-chaining)
3. Prompt Chaining Guide For Beginners: All You Need To Know, 访问时间为 六月 30, 2025， [https://www.godofprompt.ai/blog/prompt-chaining-guide](https://www.godofprompt.ai/blog/prompt-chaining-guide)
4. Learn Prompt Chaining: Simple Explanations and Examples \- Vellum AI, 访问时间为 六月 30, 2025， [https://www.vellum.ai/blog/what-is-prompt-chaining](https://www.vellum.ai/blog/what-is-prompt-chaining)
5. How To Write AI Prompts That Output Valid JSON Data | Build5Nines, 访问时间为 六月 30, 2025， [https://build5nines.com/how-to-write-ai-prompts-that-output-valid-json-data/](https://build5nines.com/how-to-write-ai-prompts-that-output-valid-json-data/)
6. Crafting Structured {JSON} Responses: Ensuring Consistent Output from any LLM \- DEV Community, 访问时间为 六月 30, 2025， [https://dev.to/rishabdugar/crafting-structured-json-responses-ensuring-consistent-output-from-any-llm-l9h](https://dev.to/rishabdugar/crafting-structured-json-responses-ensuring-consistent-output-from-any-llm-l9h)
7. Deepseek R1 is here. This is how you can use it properly | by Gergely Rabb \- Medium, 访问时间为 六月 30, 2025， [https://medium.com/@rbbgrgly/deepseek-r1-is-here-this-is-how-you-can-use-it-properly-83979e2a2e09](https://medium.com/@rbbgrgly/deepseek-r1-is-here-this-is-how-you-can-use-it-properly-83979e2a2e09)
8. Tips for Prompting deepseek-R1 to Improve Accuracy and Encourage Deep Thinking, 访问时间为 六月 30, 2025， [https://www.reddit.com/r/DeepSeek/comments/1ia6bcg/tips_for_prompting_deepseekr1_to_improve_accuracy/](https://www.reddit.com/r/DeepSeek/comments/1ia6bcg/tips_for_prompting_deepseekr1_to_improve_accuracy/)
9. 6 Prompt Chaining Examples \- AirOps, 访问时间为 六月 30, 2025， [https://www.airops.com/blog/prompt-chaining-examples](https://www.airops.com/blog/prompt-chaining-examples)
10. Story Centaur: Large Language Model Few Shot Learning as a Creative Writing Tool \- ACL Anthology, 访问时间为 六月 30, 2025， [https://aclanthology.org/2021.eacl-demos.29.pdf](https://aclanthology.org/2021.eacl-demos.29.pdf)
11. Few-Shot Learning for Clinical Natural Language Processing Using Siamese Neural Networks: Algorithm Development and Validation Study \- JMIR AI, 访问时间为 六月 30, 2025， [https://ai.jmir.org/2023/1/e44293/](https://ai.jmir.org/2023/1/e44293/)
12. 脱口秀的文本与行动--理论评论 \- 中国作家网, 访问时间为 六月 30, 2025， [http://www.chinawriter.com.cn/n1/2022/1216/c407521-32588314.html](http://www.chinawriter.com.cn/n1/2022/1216/c407521-32588314.html)
13. 中国男足教育中国男篮？范志毅竟是被耽误的脱口秀演员，金句让李诞笑趴在地上, 访问时间为 六月 30, 2025， [http://sh.people.com.cn/n2/2021/0315/c350122-34621937.html](http://sh.people.com.cn/n2/2021/0315/c350122-34621937.html)
14. how to prompt the DeepSeek-R1 model : r/artificial \- Reddit, 访问时间为 六月 30, 2025， [https://www.reddit.com/r/artificial/comments/1ijas54/how_to_prompt_the_deepseekr1_model/](https://www.reddit.com/r/artificial/comments/1ijas54/how_to_prompt_the_deepseekr1_model/)
15. Few-shot Style-Conditioned LLM Text Generation via Latent Interpolation | OpenReview, 访问时间为 六月 30, 2025， [https://openreview.net/forum?id=kVcEiWtld9](https://openreview.net/forum?id=kVcEiWtld9)
16. Few-Shot Detection of Machine-Generated Text using Style Representations \- OpenReview, 访问时间为 六月 30, 2025， [https://openreview.net/forum?id=cWiEN1plhJ](https://openreview.net/forum?id=cWiEN1plhJ)
17. Few-Shot Detection of Machine-Generated Text using Style Representations \- arXiv, 访问时间为 六月 30, 2025， [https://arxiv.org/html/2401.06712v1](https://arxiv.org/html/2401.06712v1)
18. How to Master DeepSeek R1 Prompt Engineering \- YouTube, 访问时间为 六月 30, 2025， [https://www.youtube.com/watch?v=kRXfddrtrmM](https://www.youtube.com/watch?v=kRXfddrtrmM)
19. Enhancing JSON Output with Large Language Models: A Comprehensive Guide \- Medium, 访问时间为 六月 30, 2025， [https://medium.com/@dinber19/enhancing-json-output-with-large-language-models-a-comprehensive-guide-f1935aa724fb](https://medium.com/@dinber19/enhancing-json-output-with-large-language-models-a-comprehensive-guide-f1935aa724fb)
20. imaurer/awesome-llm-json: Resource list for generating JSON using LLMs via function calling, tools, CFG. Libraries, Models, Notebooks, etc. \- GitHub, 访问时间为 六月 30, 2025， [https://github.com/imaurer/awesome-llm-json](https://github.com/imaurer/awesome-llm-json)
21. The Ultimate Fucking Guide to Prompt Engineering : r/PromptEngineering \- Reddit, 访问时间为 六月 30, 2025， [https://www.reddit.com/r/PromptEngineering/comments/1j8m0rs/the_ultimate_fucking_guide_to_prompt_engineering/](https://www.reddit.com/r/PromptEngineering/comments/1j8m0rs/the_ultimate_fucking_guide_to_prompt_engineering/)
22. 《奇葩说•第七季》修辞策略与言语幽默研究 \- hanspub.org, 访问时间为 六月 30, 2025， [https://www.hanspub.org/journal/paperinformation?paperid=68290](https://www.hanspub.org/journal/paperinformation?paperid=68290)
23. 中国篮球玻璃心？《吐槽大会》宣布节目无法上线球迷等不来周琦道歉, 访问时间为 六月 30, 2025， [https://kbsapp.sports.qq.com/article/20210321A03W9W](https://kbsapp.sports.qq.com/article/20210321A03W9W)
24. 脱口秀观看指南：如何成为一名专业的野生"喜剧人"？ \- Xmind, 访问时间为 六月 30, 2025， [https://xmind.app/cn/blog/how-to-be-a-professional-comedian/](https://xmind.app/cn/blog/how-to-be-a-professional-comedian/)
25. 李诞- Apple 播客, 访问时间为 六月 30, 2025， [https://podcasts.apple.com/cn/podcast/%E6%9D%8E%E8%AF%9E/id1729723546](https://podcasts.apple.com/cn/podcast/%E6%9D%8E%E8%AF%9E/id1729723546)
26. 脱口秀演员小佳 \- 中青在线, 访问时间为 六月 30, 2025， [http://m.cyol.com/gb/articles/2022-01/09/content_K8a5PIBAn.html](http://m.cyol.com/gb/articles/2022-01/09/content_K8a5PIBAn.html)
27. 新媒体视野下网络语言的语体特征, 访问时间为 六月 30, 2025， [https://www.js-skl.org.cn/uploads/Files/2015-10/19/1-1445237072-302.pdf](https://www.js-skl.org.cn/uploads/Files/2015-10/19/1-1445237072-302.pdf)
28. 轻松讲书一窥百部心理学佳作为生活中的种种困惑探寻答案 \- Apple Podcasts, 访问时间为 六月 30, 2025， [https://podcasts.apple.com/cn/browse?l=en](https://podcasts.apple.com/cn/browse?l=en)

---

## **附录：V5 AI 描述生成系统数据库设计方案**

### **A. 数据库架构概述**

为了支撑四模块链式生成架构，数据库设计需要捕获每个模块的中间结果，实现完整的处理链路追踪和调试能力。

### **B. 核心设计原则**

1. **过程透明化**：存储每个模块的输入输出，支持链路追踪
2. **调试友好**：快速定位问题模块，支持 A/B 测试
3. **性能优化**：合理索引和分区，支持高并发查询
4. **数据完整性**：保证四模块数据的一致性和关联性

### **C. 方案一：扩展现有表结构（推荐）**

**1. AI 描述主表增强**

```sql
-- 扩展 ai_descriptions 表
ALTER TABLE ai_descriptions ADD COLUMN generation_request_id TEXT;
ALTER TABLE ai_descriptions ADD COLUMN analyzer_output TEXT; -- JSON格式
ALTER TABLE ai_descriptions ADD COLUMN strategist_output TEXT; -- JSON格式
ALTER TABLE ai_descriptions ADD COLUMN writer_output TEXT; -- 最终文本
ALTER TABLE ai_descriptions ADD COLUMN critic_output TEXT; -- JSON格式，可选
ALTER TABLE ai_descriptions ADD COLUMN processing_status TEXT DEFAULT 'completed';
ALTER TABLE ai_descriptions ADD COLUMN error_step TEXT; -- 出错的步骤
ALTER TABLE ai_descriptions ADD COLUMN total_processing_time INTEGER; -- 总处理时间(ms)
ALTER TABLE ai_descriptions ADD COLUMN model_costs TEXT; -- JSON格式的成本信息
```

**2. 新增生成请求表**

```sql
-- AI描述生成请求表（支持流式生成）
CREATE TABLE ai_generation_requests (
  id TEXT PRIMARY KEY NOT NULL,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

  -- 请求参数
  preferred_style TEXT,
  context_type TEXT,
  stream_response BOOLEAN DEFAULT FALSE,
  force_regenerate BOOLEAN DEFAULT FALSE,

  -- 处理状态
  status TEXT NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
  current_step TEXT, -- analyzer, strategist, writer, critic
  progress_percentage INTEGER DEFAULT 0,

  -- 时间统计
  step_timings TEXT, -- JSON: {analyzer: 1200ms, strategist: 800ms, ...}
  started_at INTEGER NOT NULL,
  completed_at INTEGER,

  -- 错误处理
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,

  created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

**3. 模块处理日志表**

```sql
-- 四模块处理详细日志
CREATE TABLE ai_module_logs (
  id TEXT PRIMARY KEY NOT NULL,
  request_id TEXT NOT NULL REFERENCES ai_generation_requests(id) ON DELETE CASCADE,
  module_name TEXT NOT NULL, -- analyzer, strategist, writer, critic

  -- 输入输出
  input_data TEXT NOT NULL, -- JSON格式
  output_data TEXT, -- JSON格式
  prompt_used TEXT,

  -- 执行信息
  status TEXT NOT NULL, -- success, failed, retrying
  processing_time INTEGER, -- 毫秒
  token_usage TEXT, -- JSON: {prompt_tokens: 100, completion_tokens: 50}
  model_version TEXT,

  -- 错误信息
  error_code TEXT,
  error_message TEXT,

  -- 质量指标
  quality_score REAL, -- 0-1分数
  confidence_score REAL, -- 0-1分数

  created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

**4. 喜剧策略库表**

```sql
-- 喜剧策略配置表
CREATE TABLE comedy_strategies (
  id TEXT PRIMARY KEY NOT NULL,
  strategy_name TEXT NOT NULL UNIQUE,
  strategy_type TEXT NOT NULL, -- self_deprecation, humblebrag, hyperbolic_roast, etc.

  -- 策略配置
  description TEXT NOT NULL,
  target_emotions TEXT, -- JSON数组
  suitable_data_patterns TEXT, -- JSON: 适用的数据模式
  template_examples TEXT, -- JSON: 模板示例

  -- 使用统计
  usage_count INTEGER DEFAULT 0,
  success_rate REAL DEFAULT 0.0,
  average_rating REAL DEFAULT 0.0,

  -- 元数据
  is_active BOOLEAN DEFAULT TRUE,
  difficulty_level INTEGER DEFAULT 1, -- 1-5
  created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
  updated_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

**5. 少样本示例库表**

```sql
-- 少样本学习示例库
CREATE TABLE few_shot_examples (
  id TEXT PRIMARY KEY NOT NULL,
  strategy_id TEXT REFERENCES comedy_strategies(id) ON DELETE CASCADE,

  -- 示例内容
  input_pattern TEXT NOT NULL, -- JSON: 输入数据模式
  example_output TEXT NOT NULL, -- 示例输出文本
  style_tags TEXT, -- JSON数组: 风格标签

  -- 质量评估
  quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
  humor_rating INTEGER CHECK (humor_rating >= 1 AND humor_rating <= 5),
  appropriateness_rating INTEGER CHECK (appropriateness_rating >= 1 AND appropriateness_rating <= 5),

  -- 使用统计
  selection_count INTEGER DEFAULT 0,
  success_rate REAL DEFAULT 0.0,

  -- 元数据
  source TEXT, -- 示例来源
  author TEXT, -- 作者/风格来源
  language TEXT DEFAULT 'zh-CN',
  is_active BOOLEAN DEFAULT TRUE,

  created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
  updated_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

### **D. 方案二：全新模块化表结构（备选）**

如果希望完全重新设计，可以创建独立的模块表：

```sql
-- 分析器结果表
CREATE TABLE analyzer_results (
  id TEXT PRIMARY KEY,
  request_id TEXT NOT NULL,
  raw_github_data TEXT NOT NULL, -- JSON
  structured_analysis TEXT NOT NULL, -- JSON数组，每个指标的分析
  valence_mapping TEXT NOT NULL, -- JSON
  processing_time INTEGER,
  created_at INTEGER NOT NULL
);

-- 策略师结果表
CREATE TABLE strategist_results (
  id TEXT PRIMARY KEY,
  request_id TEXT NOT NULL,
  analyzer_result_id TEXT REFERENCES analyzer_results(id),
  selected_strategy TEXT NOT NULL,
  target_emotion TEXT,
  perspective TEXT,
  key_talking_points TEXT, -- JSON数组
  justification TEXT,
  processing_time INTEGER,
  created_at INTEGER NOT NULL
);

-- 写手结果表
CREATE TABLE writer_results (
  id TEXT PRIMARY KEY,
  request_id TEXT NOT NULL,
  strategist_result_id TEXT REFERENCES strategist_results(id),
  generated_text TEXT NOT NULL,
  used_examples TEXT, -- JSON: 使用的少样本示例
  final_prompt TEXT,
  processing_time INTEGER,
  created_at INTEGER NOT NULL
);

-- 评论家结果表
CREATE TABLE critic_results (
  id TEXT PRIMARY KEY,
  request_id TEXT NOT NULL,
  writer_result_id TEXT REFERENCES writer_results(id),
  humor_score REAL,
  compliance_score REAL,
  originality_score REAL,
  overall_assessment TEXT,
  improvement_suggestions TEXT, -- JSON数组
  processing_time INTEGER,
  created_at INTEGER NOT NULL
);
```

### **E. 索引和性能优化**

```sql
-- 关键索引
CREATE INDEX idx_ai_requests_user_status ON ai_generation_requests(user_id, status);
CREATE INDEX idx_ai_requests_created ON ai_generation_requests(created_at DESC);
CREATE INDEX idx_module_logs_request ON ai_module_logs(request_id, module_name);
CREATE INDEX idx_module_logs_status ON ai_module_logs(status, created_at);
CREATE INDEX idx_comedy_strategies_active ON comedy_strategies(is_active, success_rate DESC);
CREATE INDEX idx_few_shot_examples_strategy ON few_shot_examples(strategy_id, is_active);

-- 分区建议（如果数据量大）
-- 按月分区 ai_module_logs 表
-- 按用户分区 ai_generation_requests 表
```

### **F. 数据迁移策略**

**1. 现有数据保留**

```sql
-- 保留现有 ai_descriptions 和 description_history 表
-- 添加 generation_request_id 字段关联新架构
-- 为现有记录生成虚拟的四模块数据
```

**2. 渐进式迁移**

```sql
-- 步骤1：创建新表结构
-- 步骤2：新请求使用新架构
-- 步骤3：历史数据按需迁移
-- 步骤4：废弃旧字段（可选）
```

### **G. 监控和分析支持**

```sql
-- 系统性能视图
CREATE VIEW ai_generation_performance AS
SELECT
  DATE(started_at/1000, 'unixepoch') as generation_date,
  COUNT(*) as total_requests,
  AVG(completed_at - started_at) as avg_processing_time,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as success_count,
  COUNT(CASE WHEN status = 'failed' THEN 1 END) as failure_count
FROM ai_generation_requests
GROUP BY DATE(started_at/1000, 'unixepoch');

-- 模块性能分析视图
CREATE VIEW module_performance_analysis AS
SELECT
  module_name,
  COUNT(*) as execution_count,
  AVG(processing_time) as avg_time,
  COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
  AVG(quality_score) as avg_quality
FROM ai_module_logs
GROUP BY module_name;

-- 策略效果分析视图
CREATE VIEW strategy_effectiveness AS
SELECT
  cs.strategy_name,
  cs.usage_count,
  cs.success_rate,
  AVG(ml.quality_score) as recent_quality
FROM comedy_strategies cs
LEFT JOIN ai_module_logs ml ON ml.output_data LIKE '%' || cs.id || '%'
WHERE ml.module_name = 'strategist'
GROUP BY cs.id, cs.strategy_name;
```

**结论：推荐采用方案一（扩展现有表），原因如下：**

1. **向后兼容**：保留现有功能不中断
2. **渐进式升级**：可以逐步迁移到新架构
3. **开发效率**：减少重复代码和数据迁移工作
4. **数据完整性**：避免数据丢失和不一致问题

这种设计既支持新架构的调试需求，又保持了系统的稳定性和可维护性。
