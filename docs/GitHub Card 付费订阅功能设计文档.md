# GitHub Card 付费订阅功能设计文档

## 1. 概述

本文档详细描述了如何为 GitHub Card 应用实现付费订阅功能，包括数据库设计、API 接口、前端组件和部署要求。

## 2. 功能需求

### 2.1 用户故事

- 作为免费用户，我可以创建有效期为 3 天的分享链接
- 作为付费用户，我可以创建永久有效的分享链接
- 作为付费用户，我可以管理我的订阅
- 作为系统管理员，我可以管理订阅计划

### 2.2 功能边界

- 付费用户创建的分享链接永久有效
- 用户降级为免费用户后，已创建的分享链接在下次清理时会被移除
- 订阅状态变更通过 Webhook 实时更新

## 3. 数据库设计

### 3.1 新增表

#### 3.1.1 订阅计划表 (subscription_plans)

```sql
CREATE TABLE subscription_plans (
id TEXT PRIMARY KEY,
name TEXT NOT NULL,
description TEXT NOT NULL,
price INTEGER NOT NULL, -- 单位：分
currency TEXT NOT NULL DEFAULT 'USD',
interval TEXT NOT NULL, -- 'month' 或 'year'
features TEXT NOT NULL, -- JSON 数组
is_active BOOLEAN NOT NULL DEFAULT true,
created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

#### 3.1.2 用户订阅表 (user_subscriptions)

```sql
CREATE TABLE user_subscriptions (
id TEXT PRIMARY KEY,
user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
plan_id TEXT NOT NULL REFERENCES subscription_plans(id),
status TEXT NOT NULL,
price_id TEXT NOT NULL,
current_period_start INTEGER NOT NULL,
current_period_end INTEGER NOT NULL,
cancel_at INTEGER,
canceled_at INTEGER,
cancel_at_period_end BOOLEAN NOT NULL DEFAULT false,
created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
updated_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

#### 3.1.3 支付记录表 (payments)

```sql
CREATE TABLE payments (
id TEXT PRIMARY KEY,
user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
subscription_id TEXT REFERENCES user_subscriptions(id),
amount INTEGER NOT NULL,
currency TEXT NOT NULL DEFAULT 'USD',
status TEXT NOT NULL,
payment_method TEXT,
receipt_url TEXT,
created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

#### 3.1.4 索引优化

```sql
-- 用户订阅表索引
CREATE INDEX idx_user_subscriptions_user_status ON user_subscriptions(user_id, status);

-- 支付记录表索引
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_subscription_id ON payments(subscription_id);
CREATE INDEX idx_payments_created_at ON payments(created_at);

-- 分享链接表索引（如果不存在）
CREATE INDEX idx_share_links_user_id ON share_links(user_id);
CREATE INDEX idx_share_links_expires_at ON share_links(expires_at) WHERE expires_at IS NOT NULL;
```

#### 3.1.5 外键约束

```sql
-- 确保所有外键关系完整
PRAGMA foreign_keys = ON;

-- 用户订阅表外键
ALTER TABLE user_subscriptions
ADD CONSTRAINT fk_user_subscriptions_plan
FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT;

-- 支付记录表外键
ALTER TABLE payments
ADD CONSTRAINT fk_payments_subscription
FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL;
```

## 4. API 设计

### 4.1 订阅相关 API

#### 4.1.1 获取订阅计划

```http
GET /api/subscription/plans
```

响应：

```json
{
  "plans": [
    {
      "id": "pro-monthly",
      "name": "Pro Monthly",
      "price": 999,
      "currency": "USD",
      "interval": "month",
      "features": ["永久链接", "高级模板", "高级分析"]
    }
  ]
}
```

#### 4.1.2 创建结账会话

```http
POST /api/stripe/create-checkout
```

请求体：

```json
{
  "priceId": "price_xxx",
  "successUrl": "https://example.com/success",
  "cancelUrl": "https://example.com/cancel"
}
```

响应：

```json
{
  "sessionId": "cs_test_xxx",
  "url": "https://checkout.stripe.com/c/pay/xxx"
}
```

#### 4.1.3 创建客户门户会话

```http
POST /api/stripe/create-portal
```

请求体：

```json
{
  "returnUrl": "https://example.com/account"
}
```

响应：

```json
{
  "url": "https://billing.stripe.com/session/xxx"
}
```

#### 4.1.4 获取用户订阅状态

```http
GET /api/subscription
```

响应：

```json
{
  "subscription": {
    "id": "sub_xxx",
    "status": "active",
    "planId": "pro-monthly",
    "currentPeriodEnd": **********,
    "cancelAtPeriodEnd": true
  }
}
```

#### 4.2 Webhook 端点

```http
POST /api/stripe/webhook
```

处理 Stripe 事件：

- checkout.session.completed
- customer.subscription.updated
- customer.subscription.deleted
- invoice.paid
- invoice.payment_failed

### 4.3 安全与限流

#### 4.3.1 速率限制

所有支付相关 API 实施以下速率限制：

- 认证用户：每分钟 60 次请求
- 未认证用户：每分钟 10 次请求
- Webhook 端点：每分钟 100 次请求（基于 IP）

#### 4.3.2 错误处理

标准错误响应格式：

```json
{
  "error": {
    "code": "error_code",
    "message": "Human readable error message",
    "details": {
      "field": "additional error details"
    },
    "request_id": "req_12345"
  }
}
```

常见错误码：

| 状态码 | 错误码            | 描述           |
| ------ | ----------------- | -------------- |
| 400    | INVALID_REQUEST   | 请求参数无效   |
| 401    | UNAUTHORIZED      | 未授权访问     |
| 403    | FORBIDDEN         | 权限不足       |
| 404    | NOT_FOUND         | 资源不存在     |
| 429    | TOO_MANY_REQUESTS | 请求过于频繁   |
| 500    | INTERNAL_ERROR    | 服务器内部错误 |

#### 4.3.3 Webhook 重试机制

- 初始失败后等待 1 分钟重试
- 后续重试间隔依次为：5 分钟、15 分钟、60 分钟、3 小时、6 小时、12 小时
- 重试 24 小时后仍失败则标记为需要人工干预
- 记录所有重试历史和原因

## 5. 前端实现

### 5.1 订阅状态管理

```typescript
// contexts/SubscriptionContext.tsx
"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useSession } from "next-auth/react";

type Subscription = {
  id: string;
  status: string;
  planId: string;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
};

type SubscriptionContextType = {
  subscription: Subscription | null;
  isLoading: boolean;
  isPro: boolean;
  refreshSubscription: () => Promise<void>;
};

const SubscriptionContext = createContext<SubscriptionContextType | null>(null);

export function SubscriptionProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchSubscription = async () => {
    if (status !== "authenticated") {
      setIsLoading(false);
      return;
    }

    try {
      const res = await fetch("/api/subscription");
      const data = await res.json();
      setSubscription(data.subscription);
    } catch (error) {
      console.error("Error fetching subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [status]);

  return (
    <SubscriptionContext.Provider
      value={{
        subscription,
        isLoading,
        isPro: !!subscription && subscription.status === "active",
        refreshSubscription: fetchSubscription,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
}

export function useSubscription() {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error(
      "useSubscription must be used within a SubscriptionProvider"
    );
  }
  return context;
}
```

### 5.2 分享链接组件修改

在分享按钮组件中，为非付费用户添加提示，说明链接将在 3 天后过期。

```tsx
// components/share-button.tsx
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function ShareButton({ isProUser }: { isProUser: boolean }) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline" size="icon">
          <Share2 className="h-4 w-4" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        {isProUser ? "点击复制分享链接" : "分享链接将在3天后过期"}
      </TooltipContent>
    </Tooltip>
  );
}
```

#### 说明

- 对于付费用户，显示简单的"点击复制分享链接"提示
- 对于非付费用户，显示"分享链接将在 3 天后过期"提示
- 保持界面简洁，不增加额外操作步骤
- 实际的链接创建和复制逻辑保持不变

## 6. 后端实现

### 6.1 获取 share link 接口判断所属用户订阅状态

在 `app/api/share-links/[token]/route.ts` 中，我们修改了分享链接的访问逻辑，增加了对链接所有者订阅状态的检查：

1. **主要修改点**:

   - 查询分享链接时，检查链接所有者是否有有效的订阅
   - 如果用户有活跃的订阅，则跳过链接过期检查
   - 保持非订阅用户的 3 天过期逻辑不变

2. **关键代码**:

   ```typescript
   // 查询用户是否有活跃的订阅
   const activeSubscription = await db.query.userSubscriptions.findFirst({
     where: (sub, { eq, and, gt }) =>
       and(eq(sub.userId, shareLink.userId), eq(sub.status, "active")),
   });

   const isProUser = !!activeSubscription;

   // 修改过期检查逻辑，如果是Pro用户则跳过过期检查
   if (!shareLink.isActive || (!isProUser && now > expiresAtDate)) {
     // ... 过期处理逻辑
   }
   ```

## 7. 部署要求

### 7.1 环境变量

```bash
# Stripe

STRIPE_SECRET_KEY=sk_test*...
STRIPE_WEBHOOK_SECRET=whsec*...
STRIPE_PRICE_ID_PRO_MONTHLY=price*...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test*...

```

### 7.2 数据库迁移

创建数据库迁移脚本：

```typescript
// drizzle/migrations/0005_add_subscription_tables.ts
import { sql } from "drizzle-orm";

export async function up(db) {
  // 创建订阅计划表
  await db.run(sql`  CREATE TABLE subscription_plans (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      price INTEGER NOT NULL,
      currency TEXT NOT NULL DEFAULT 'USD',
      interval TEXT NOT NULL,
      features TEXT NOT NULL,
      is_active BOOLEAN NOT NULL DEFAULT true,
      created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
    )`);

  // 创建用户订阅表
  await db.run(sql`  CREATE TABLE user_subscriptions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      plan_id TEXT NOT NULL REFERENCES subscription_plans(id),
      status TEXT NOT NULL,
      price_id TEXT NOT NULL,
      current_period_start INTEGER NOT NULL,
      current_period_end INTEGER NOT NULL,
      cancel_at INTEGER,
      canceled_at INTEGER,
      cancel_at_period_end BOOLEAN NOT NULL DEFAULT false,
      created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
      updated_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
    )`);

  // 创建支付记录表
  await db.run(sql`  CREATE TABLE payments (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      subscription_id TEXT REFERENCES user_subscriptions(id),
      amount INTEGER NOT NULL,
      currency TEXT NOT NULL DEFAULT 'USD',
      status TEXT NOT NULL,
      payment_method TEXT,
      receipt_url TEXT,
      created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
    )`);

  // 添加索引
  await db.run(
    sql`CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id)`
  );
  await db.run(
    sql`CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status)`
  );
  await db.run(sql`CREATE INDEX idx_payments_user_id ON payments(user_id)`);
  await db.run(
    sql`CREATE INDEX idx_payments_subscription_id ON payments(subscription_id)`
  );
}

export async function down(db) {
  await db.run(sql`DROP TABLE IF EXISTS payments`);
  await db.run(sql`DROP TABLE IF EXISTS user_subscriptions`);
  await db.run(sql`DROP TABLE IF EXISTS subscription_plans`);
}
```

## 8. 测试计划

### 8.1 单元测试

#### 分享链接创建测试

- 测试免费用户创建的链接有 3 天有效期
- 测试付费用户创建的链接永不过期
- 测试用户降级后，新创建的链接有 3 天有效期

#### 订阅状态测试

- 测试订阅状态的正确更新
- 测试订阅取消后的行为
- 测试支付失败的处理

### 8.2 集成测试

#### 结账流程测试

- 测试成功订阅
- 测试取消订阅
- 测试支付失败场景

#### Webhook 测试

- 测试各种 Stripe 事件的处理
- 测试重试机制

## 9. 安全考虑

### 9.1 敏感信息处理

- 日志脱敏：

  - 信用卡号：只显示最后 4 位
  - API 密钥：只显示前 4 位和后 4 位
  - 邮箱：对@前的内容进行部分隐藏

- 管理端访问控制：
  - 仅允许特定 IP 地址访问管理 API
  - 实现基于角色的访问控制 (RBAC)

### 9.2 审计日志

记录以下操作：

- 用户订阅状态变更
- 支付成功/失败
- 退款处理
- 优惠码使用
- 管理操作

日志格式：

```json
{
  "timestamp": "2024-05-27T09:00:00Z",
  "event_type": "subscription.updated",
  "user_id": "user_123",
  "subscription_id": "sub_123",
  "old_status": "trialing",
  "new_status": "active",
  "ip_address": "***********",
  "user_agent": "Mozilla/5.0...",
  "metadata": {}
}
```

## 10. 业务逻辑

### 10.1 订阅状态转换

```mermaid
stateDiagram-v2
    [*] --> incomplete
    incomplete --> trialing: 首次支付成功
    trialing --> active: 试用期结束
    trialing --> canceled: 试用期取消
    active --> past_due: 扣款失败
    active --> canceled: 用户取消
    past_due --> active: 成功扣款
    past_due --> canceled: 多次扣款失败
    canceled --> [*]
```

状态说明：

- `incomplete`: 初始状态，支付未完成
- `trialing`: 试用中
- `active`: 活跃订阅
- `past_due`: 扣款失败
- `canceled`: 已取消
- `unpaid`: 多次扣款失败

### 10.2 试用期设计（未来扩展）

预留字段：

```sql
ALTER TABLE user_subscriptions
ADD COLUMN trial_start INTEGER,
ADD COLUMN trial_end INTEGER;
```

试用期规则：

- 首次订阅可享受 14 天试用
- 试用期内可随时取消，不产生费用
- 试用期结束前 3 天发送提醒邮件
- 试用期结束后自动转为正式订阅并扣款

## 6.3 分享链接处理逻辑

#### 6.3.1 创建分享链接

- 所有用户都可以创建分享链接
- 所有用户的分享链接 3 天后过期

#### 6.3.2 访问分享链接

当访问分享链接时（`GET /api/share-links/[token]`），按以下逻辑处理：

1. 查找对应的分享链接
2. 检查链接是否已被标记为不可用（isActive = false）
3. 如果用户是付费订阅用户，则忽略过期检查
4. 如果是免费用户，检查链接是否已过期
5. 返回对应的 GitHub 用户数据

```typescript
// 伪代码：分享链接访问逻辑
async function handleShareLinkAccess(token: string) {
  const shareLink = await db.shareLinks.findByToken(token);

  if (!shareLink || !shareLink.isActive) {
    return { error: "分享链接不存在或已失效" };
  }

  // 获取链接所有者信息
  const owner = await db.users.findById(shareLink.userId);

  // 如果是付费用户，忽略过期检查
  const isProUser = owner?.subscription?.status === "active";
  const isExpired = shareLink.expiresAt && shareLink.expiresAt < Date.now();

  if (!isProUser && isExpired) {
    // 标记为过期
    await db.shareLinks.update(shareLink.id, { isActive: false });
    return { error: "分享链接已过期" };
  }

  // 返回分享的数据
  return {
    githubUsername: shareLink.githubUsername,
    templateType: shareLink.templateType,
    backgroundId: shareLink.backgroundId,
    // 其他必要数据
  };
}
```
