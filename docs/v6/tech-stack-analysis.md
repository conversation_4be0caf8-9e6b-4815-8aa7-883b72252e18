# 技术栈文件获取与分析功能

## 概述

GitHub Card 项目现已支持获取和分析用户仓库中的技术栈文件，用于更深入的用户行为分析。该功能扩展了现有的 GitHub 扩展数据获取能力，可以获取各种编程语言的依赖配置文件。

## 支持的技术栈文件类型

| 文件名             | 语言/框架    | 用途               |
| ------------------ | ------------ | ------------------ |
| `package.json`     | Node.js      | NPM 依赖管理       |
| `pyproject.toml`   | Python       | Poetry 依赖管理    |
| `requirements.txt` | Python       | pip 依赖管理       |
| `Pipfile`          | Python       | pipenv 依赖管理    |
| `Cargo.toml`       | Rust         | Cargo 依赖管理     |
| `go.mod`           | Go           | Go 模块管理        |
| `composer.json`    | PHP          | Composer 依赖管理  |
| `pom.xml`          | Java         | Maven 依赖管理     |
| `build.gradle`     | Java/Kotlin  | Gradle 构建配置    |
| `Gemfile`          | Ruby         | Bundler 依赖管理   |
| `pubspec.yaml`     | Dart/Flutter | Pub 依赖管理       |
| `mix.exs`          | Elixir       | Mix 依赖管理       |
| `project.clj`      | Clojure      | Leiningen 项目配置 |
| `stack.yaml`       | Haskell      | Stack 构建工具     |
| `deno.json`        | Deno         | Deno 配置文件      |
| `bun.lockb`        | Bun          | Bun 锁定文件       |

## 数据存储结构

### 数据库 Schema 更新

对于生产环境，运行以下命令更新数据库结构：

```bash
# 生成迁移文件
yarn db:generate

# 推送到数据库
yarn db:push
```

对于本地开发环境，可以重置数据库：

```bash
# 重置开发数据库（会清空所有数据）
yarn db:reset-dev
```

### TypeScript 类型定义

```typescript
export interface TechStackFileData {
  repository: string; // 仓库名称
  name: string; // 文件名
  fileType: TechStackFileType; // 文件类型
  path: string; // 文件路径
  content: string; // Base64编码的文件内容
  size: number; // 文件大小
  encoding: string; // 编码方式
  sha: string; // Git SHA值
  downloadUrl: string; // 下载URL
}
```

## 使用方法

### 1. 获取技术栈文件

```typescript
import { fetchExtendedGitHubData } from "@/lib/github/extended-fetch";

const config = {
  maxRepositories: 10,
  includeTechStackFiles: true, // 启用技术栈文件获取
  // ... 其他配置
};

const extendedData = await fetchExtendedGitHubData(username, repos, config);
console.log(`获取到 ${extendedData.techStackFiles.length} 个技术栈文件`);
```

### 2. 分析技术栈

```typescript
import { TechStackAnalyzer } from "@/lib/ai/analyzers/TechStackAnalyzer";

const analysis = TechStackAnalyzer.analyze(extendedData);
console.log(`框架专注度: ${analysis.framework_focus}`);
console.log(`检测到的框架: ${analysis.detectedFrameworks.join(", ")}`);
```

### 3. 在行为分析中使用

技术栈分析结果可以用于实现原始需求中的以下分析内容：

- **framework_focus**: 分析是否专注于特定技术栈/框架
  - `react_purist`: React 生态专家
  - `rustacean`: Rust 语言专家
  - `scatter`: 技术栈分散

## API 缓存策略

- **缓存时间**: 30 天（技术栈文件变化频率较低）
- **请求频率控制**: 每个文件获取间隔 100ms
- **失败处理**: 单个文件获取失败不影响整体流程

## 性能考虑

1. **按需获取**: 通过配置项`includeTechStackFiles`控制是否获取
2. **仓库优先级**: 按 star 数排序，优先获取热门仓库的技术栈文件
3. **文件过滤**: 只获取根目录下的技术栈文件，避免深度遍历
4. **内容限制**: 自动跳过过大的文件（通过 GitHub API size 字段判断）

## 测试

运行测试脚本验证功能：

```bash
npx ts-node scripts/test-tech-stack-fetch.ts
```

## 扩展计划

### 短期计划

- [x] 基础文件获取功能
- [x] 简单框架检测
- [ ] 更多文件类型支持

### 长期计划

- [ ] 依赖解析器实现
- [ ] 版本兼容性分析
- [ ] 技术栈演进趋势分析
- [ ] 安全漏洞检测集成

## 注意事项

1. **API 限制**: 每个技术栈文件都需要一次 GitHub API 调用，注意 rate limit
2. **存储空间**: Base64 编码会增加约 33%的存储空间
3. **隐私考虑**: 只获取公开仓库的文件，不涉及私有代码
4. **解析精度**: 当前为基础文本匹配，后续可增强为完整解析器

## 相关文件

- `types/github-extended.ts` - 类型定义
- `lib/github/extended-fetch.ts` - 数据获取逻辑
- `lib/ai/analyzers/TechStackAnalyzer.ts` - 分析器实现
- `lib/services/github-extended-service.ts` - 服务层集成
- `lib/db/schema.ts` - 数据库 Schema
- `scripts/test-tech-stack-fetch.ts` - 测试脚本
