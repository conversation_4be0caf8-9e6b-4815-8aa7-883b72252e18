fcb567b0-eaa1-4d67-8edf-e1e7751184ee	188b48c6-f65f-440b-979d-28d03fd8737e	"[{""name"":""Vue-Access-Control"",""fullName"":""tower1229/Vue-Access-Control"",""description"":"":gem: Frontend access control framework based Vue"",""private"":false,""language"":""Vue"",""stargazersCount"":1076,""forksCount"":205,""createdAt"":""2017-10-21T01:06:21Z"",""updatedAt"":""2025-07-07T09:39:34Z"",""pushedAt"":""2024-03-08T10:30:39Z"",""size"":6781,""defaultBranch"":""master"",""topics"":[""access-control"",""access-management"",""authorization"",""vue"",""vue-router""],""hasReadme"":true,""readmeContent"":""English | [中文](README_CN.md)\n\n# vue-access-control\n\n[![Build Status](https://travis-ci.com/tower1229/Vue-Access-Control.svg?branch=master)](https://travis-ci.com/tower1229/Vue-Access-Control)  [![license](https://img.shields.io/github/license/tower1229/Vue-Access-Control.svg)]()\n\n> :gem: Frontend access control framework based Vue\n\n![logo](https://refined-x.com/asset/vsc-logo.png)\n\n## [A new version](https://github.com/tower1229/Vue-Access-Control/tree/v2) is ready, with a modular design that is less invasive to the business, fully compatible with V1 version interface data\n\n-----\n\n## Introduction\n\nVue-Access-Control is a solution of front-end user rights control based on Vue/Vue-Router/axios,through the control of three levels of routing, view and request, the developer can realize the user authority control of any granularity.\n\nThe management function can refer to [CuttingMat](https://github.com/cutting-mat) project.\n\n## Documentation\n\n[Vue2.0用户权限控制解决方案](http://refined-x.com/2017/11/28/Vue2.0%E7%94%A8%E6%88%B7%E6%9D%83%E9%99%90%E6%8E%A7%E5%88%B6%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88/)\n\n[基于Vue实现后台系统权限控制](http://refined-x.com/2017/08/29/%E5%9F%BA%E4%BA%8EVue%E5%AE%9E%E7%8E%B0%E5%90%8E%E5%8F%B0%E7%B3%BB%E7%BB%9F%E6%9D%83%E9%99%90%E6%8E%A7%E5%88%B6/)\n\n[用addRoutes实现动态路由](http://refined-x.com/2017/09/01/%E7%94%A8addRoutes%E5%AE%9E%E7%8E%B0%E5%8A%A8%E6%80%81%E8%B7%AF%E7%94%B1/)\n\n## Download\n\nhomepage: http://refined-x.com/Vue-Access-Control/\n\ngit: `git clone https://github.com/tower1229/Vue-Access-Control.git`\n\n\n## Live Example\n\ntest account:\n\n``` bash\n1. username: root\n   password: any password\n2. username: client\n   password: any password\n```\n\nlive example:\n\n[http://refined-x.com/Vue-Access-Control/](http://refined-x.com/Vue-Access-Control/)\n\n## Build Setup\n\n``` bash\n# install dependencies\nnpm install\n\n# serve with hot reload at localhost:8080\nnpm run serve\n\n# build for production with minification\nnpm run build\n\n```\n\n## License\n\n[![FOSSA Status](https://app.fossa.io/api/projects/git%2Bgithub.com%2Ftower1229%2FVue-Access-Control.svg?type=large)](https://app.fossa.io/projects/git%2Bgithub.com%2Ftower1229%2FVue-Access-Control?ref=badge_large)\n\nCopyright (c) 2017-present, [refined-x.com](http://refined-x.com)\n\n"",""readmeSize"":2303},{""name"":""Vue-Giant-Tree"",""fullName"":""tower1229/Vue-Giant-Tree"",""description"":""🌳 巨树：基于ztree封装的Vue树形组件，轻松实现海量数据的高性能渲染。"",""private"":false,""language"":""Vue"",""stargazersCount"":675,""forksCount"":168,""createdAt"":""2019-08-07T07:43:52Z"",""updatedAt"":""2025-07-17T16:04:26Z"",""pushedAt"":""2024-03-08T10:39:21Z"",""size"":2375,""defaultBranch"":""master"",""topics"":[""vue-ztree"",""ztree"",""ztree-vue""],""hasReadme"":true,""readmeContent"":""# vue-giant-tree\n\n[![npm](https://img.shields.io/npm/v/vue-giant-tree.svg)](https://www.npmjs.com/package/vue-giant-tree/) [![license](https://img.shields.io/github/license/tower1229/vue-giant-tree.svg)]()\n\n> :deciduous_tree: 巨树：基于[ztree](https://github.com/zTree/zTree_v3)封装的 Vue 树形组件，轻松实现海量数据的高性能渲染。\n\n![logo](https://refined-x.com/asset/vgt-preview.png)\n\nVue3.x 版本[在这](https://github.com/tower1229/Vue-Giant-Tree/tree/vue3)\n\n## 为什么需要 vue-giant-tree\n\nVue 的数据监听机制决定了在大数据量场景下的渲染性能非常低下，基于 Vue 实现的常规树组件几乎无法胜任上万条数据的高性能渲染，在 IE 浏览器（即便是 IE11）中很容易导致页面卡死甚至浏览器崩溃。\n\n> 不服气可以试试这份数据 [big-tree.json](http://refined-x.com/Vue-Giant-Tree/mock/big-tree.json)\n\n为了摆脱数据监听，只能放弃通过 Vue 渲染，采用常规 DOM 操作的方式。在这个领域[ztree](https://github.com/zTree/zTree_v3)是当之无愧最成熟的方案，因此 vue-giant-tree 直接基于 ztree 做上层封装，以组件的形式将 ztree 的配置和事件暴露出来，使其可以方便的在 Vue 项目中安装使用。\n\nvue-giant-tree 仅仅是给 ztree 套了一层 Vue 组件的壳，顺便提供了一套更现代化的皮肤，因为主打大数据量场景，所以取名**巨树**。\n\nztree 在性能优化方面已经做到了近乎极致，感谢 ztree 作者的工作，向您致敬！\n\n## 安装\n\n```bash\nnpm i vue-giant-tree --save\n```\n\n**注意：组件依赖 jQuery，务必在页面中提前加载 jQuery**\n\n```\n<script src=\""https://code.jquery.com/jquery-3.6.0.min.js\""\n    integrity=\""sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\"" crossorigin=\""anonymous\""></script>\n```\n\n## 使用\n\nin script:\n\n```javascript\nimport tree from \""vue-giant-tree\"";\n\nexport default {\n\tcomponents: {\n          tree\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tnodes: [\n                    { id:1, pid:0, name:\""随意勾选 1\"", open:true},\n                    { id:11, pid:1, name:\""随意勾选 1-1\"", open:true},\n                    { id:111, pid:11, name:\""随意勾选 1-1-1\""},\n                    { id:112, pid:11, name:\""随意勾选 1-1-2\""},\n                    { id:12, pid:1, name:\""随意勾选 1-2\"", open:true},\n                    { id:121, pid:12, name:\""随意勾选 1-2-1\""},\n                    { id:122, pid:12, name:\""随意勾选 1-2-2\""},\n                    { id:2, pid:0, name:\""随意勾选 2\"", checked:true, open:true},\n                    { id:21, pid:2, name:\""随意勾选 2-1\""},\n                    { id:22, pid:2, name:\""随意勾选 2-2\"", open:true},\n                    { id:221, pid:22, name:\""随意勾选 2-2-1\"", checked:true},\n                    { id:222, pid:22, name:\""随意勾选 2-2-2\""},\n                    { id:23, pid:2, name:\""随意勾选 2-3\""}\n                ]\n\t\t}\n\t},\n    methods: {\n        onClick(evt, treeId, treeNode) {\n\n        },\n        onCheck(evt, treeId, treeNode) {\n\n        },\n        handleCreated(ztreeObj) {\n\n        }\n    }\n\t...\n```\n\nin template:\n\n```html\n<tree\n  :nodes=\""nodes\""\n  @onClick=\""onClick\""\n  @onCheck=\""onCheck\""\n  @onCreated=\""handleCreated\""\n/>\n```\n\n## 属性\n\n| 参数    | 说明       | 类型   | 默认值                      |\n| ------- | ---------- | ------ | --------------------------- |\n| setting | ztree 配置 | Object | `{view: {showIcon: false}}` |\n| nodes   | ztree 数据 | Array  | `[]`                        |\n\n## 事件\n\n完全移植[zTree API](http://www.treejs.cn/v3/api.php)中`callback`支持的事件，除了：\n\n- 不支持所有 `before` 开头的事件。这类事件的主要作用是根据返回值决定是否阻止后续的`on`事件，这种判断可以在`on`事件中实现；当然，你也可以通过`setting.callback.beforeXXX`自行配置\n- 不支持 `onNodeCreated` 事件。因为在大数据量下很耗性能，如果需要可以通过 `setting.callback.onNodeCreated` 自行传入\n- 增加 `onCreated` 事件。每次实例初始化完成时触发，回调参数接收 ztree 实例，通过 ztree 实例可以使用所有实例方法\n\n| 事件名称       | 说明                                              |\n| -------------- | ------------------------------------------------- |\n| onAsyncError   | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onAsyncSuccess | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onCheck        | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onClick        | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onCollapse     | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onDblClick     | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onDrag         | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onDragMove     | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onDrop         | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onExpand       | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onMouseDown    | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onMouseUp      | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onRemove       | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onRename       | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onRightClick   | 参考 [zTree API](http://www.treejs.cn/v3/api.php) |\n| onCreated      | 初始化渲染完成后触发，回调参数接收 ztree 实例     |\n\n## 扩展\n\nzTree 没有提供给整个实例更新数据的方法，vue-giant-tree 基于 Vue 的组件通信机制扩展实现了*响应式数据*特性，只要`nodes`的值发生变化，ztree 实例就会随之更新。\n\n[项目 DEMO](https://github.com/tower1229/Vue-Giant-Tree/blob/master/src/App.vue)里演示了 vue-giant-tree 的响应式数据特性。\n\n## 演示\n\n- 线上演示：\n\n> http://refined-x.com/Vue-Giant-Tree/\n\n- 本地演示：\n\n```\nnpm i\nnpm run serve\n```\n\n## License\n\nMIT\n\nCopyright (c) 2019-present, [前端路上](http://refined-x.com)\n"",""readmeSize"":6062},{""name"":""Vue-Tree-Chart"",""fullName"":""tower1229/Vue-Tree-Chart"",""description"":""A Vue component to display tree chart"",""private"":false,""language"":""Vue"",""stargazersCount"":594,""forksCount"":173,""createdAt"":""2018-08-03T01:30:49Z"",""updatedAt"":""2025-07-14T06:26:23Z"",""pushedAt"":""2024-02-08T16:05:02Z"",""size"":865,""defaultBranch"":""master"",""topics"":[""chart"",""tree"",""tree-chart"",""vue"",""vue-component""],""hasReadme"":true,""readmeContent"":""English | [中文](README_CN.md)\n\n# vue-tree-chart\n\n[![npm](https://img.shields.io/npm/v/vue-tree-chart.svg)](https://www.npmjs.com/package/vue-tree-chart/) [![license](https://img.shields.io/github/license/tower1229/Vue-Tree-Chart.svg)]()\n\n> :deciduous_tree: A Vue component to display tree chart\n\n![logo](https://refined-x.com/asset/vtc-logo.png)\n\nVue3.x version [is here](https://github.com/tower1229/Vue-Tree-Chart/tree/vue3)\n\n## Install\n\n```bash\nnpm i vue-tree-chart --save\n```\n\n## Usage\n\nin template:\n\n```html\n<TreeChart :json=\""treeData\"" />\n```\n\nin script:\n\n```js\nimport TreeChart from \""vue-tree-chart\"";\n\nexport default {\n\tcomponents: {\n    \tTreeChart\n\t},\n\tdata() {\n\t\treturn {\n\t\t\ttreeData: {\n\t\t\t\t...\n\t\t\t}\n\t\t}\n\t}\n\t...\n```\n\n## Prop\n\n### json\n\nComponent data to support those field：\n\n```text\n- name[String] to display a node name\n- image_url[String] to display a node image\n- children[Array] node`s children\n- mate[Array] node`s mate\n- class[Array] node`s class\n- extend[Boolean] show/hide node`s children, default True\n```\n\nExample：\n\n```js\n  {\n    name: 'root',\n    image_url: \""https://static.refined-x.com/avat.jpg\"",\n    class: [\""rootNode\""],\n    children: [\n      {\n        name: 'children1',\n        image_url: \""https://static.refined-x.com/avat1.jpg\""\n      },\n      {\n        name: 'children2',\n        image_url: \""https://static.refined-x.com/avat2.jpg\"",\n        mate: [{\n          name: 'mate',\n          image_url: \""https://static.refined-x.com/avat3.jpg\""\n        }],\n        children: [\n          {\n            name: 'grandchild',\n            image_url: \""https://static.refined-x.com/avat.jpg\""\n          },\n          {\n            name: 'grandchild2',\n            image_url: \""https://static.refined-x.com/avat1.jpg\""\n          },\n          {\n            name: 'grandchild3',\n            image_url: \""https://static.refined-x.com/avat2.jpg\""\n          }\n        ]\n      },\n      {\n        name: 'children3',\n        image_url: \""https://static.refined-x.com/avat.jpg\""\n      }\n    ]\n  }\n```\n\n## Event\n\n### click-node(node)\n\nClick on the node triggered, receive the current node data as a parameter\n\n## Run a demo\n\n```bash\nnpm run serve\n```\n\n## Build\n\n```bash\nnpm run build-bundle\n```\n\nCopyright (c) 2017-present, [前端路上](http://refined-x.com)\n"",""readmeSize"":2259},{""name"":""WidgetsPlayground"",""fullName"":""tower1229/WidgetsPlayground"",""description"":""前端组件管理系统"",""private"":false,""language"":""Vue"",""stargazersCount"":164,""forksCount"":57,""createdAt"":""2016-12-10T02:49:00Z"",""updatedAt"":""2025-07-17T07:41:11Z"",""pushedAt"":""2018-07-04T01:42:30Z"",""size"":775,""defaultBranch"":""master"",""topics"":[""playground"",""vue2"",""widget""],""hasReadme"":true,""readmeContent"":""# WidgetsPlayground\n\n[![compatibility](https://img.shields.io/badge/compatibility-MicrosoftEdge%2B-orange.svg)]() [![GitHub release](https://img.shields.io/github/release/tower1229/WidgetsPlayground.svg)]() [![license](https://img.shields.io/github/license/tower1229/WidgetsPlayground.svg)]()\n\n## 介绍\n\n前端组件管理系统，前端基于Vue2/Vue-router/Vuex实现，界面基于[Flow-UI](http://flow-ui.refined-x.com/)实现，后端基于野狗云实现。\n\n除了组件管理以外，本项目同时演示了一种不依赖构建工具开发Vue项目的思路，详细介绍参见[如何不用构建工具开发Vue全家桶项目](https://refined-x.com/2017/10/28/%E5%A6%82%E4%BD%95%E4%B8%8D%E7%94%A8%E6%9E%84%E5%BB%BA%E5%B7%A5%E5%85%B7%E5%BC%80%E5%8F%91Vue%E5%85%A8%E5%AE%B6%E6%A1%B6%E9%A1%B9%E7%9B%AE/)\n\n## 功能\n\n### 组件管理\n- 二级分类\n- 标签筛选\n- 时间/名称排序\n- 关键词搜索\n\n### 组件演示\n- 实时编辑\n- 多组件组合\n- 所见即所得\n\n### 组件应用\n- HTML/CSS/JS代码一键复制\n- 还可以将编辑结果生成配置代码，实现编辑结果一键再现\n\n### 用户管理\n- 接入野狗云后端，实现用户管理\n- 用户使用痕迹数据统计\n\n## 配置\n- 项目直接丢进服务器环境就可以运行\n- 运行前，先修改`index.html`底部脚本中的`seajs.root`为`\""\""`（以项目所在的服务路径为准）\n- `seajs.widgetRootPath`变量是演示组件库（`/widgets`）的位置，通常不需要修改\n- `seajs.config.base`是Flow-UI模块库的地址，通常不需要修改\n\n## 演示 \nhttp://refined-x.com/WidgetsPlayground/\n"",""readmeSize"":1607},{""name"":""HybridStart"",""fullName"":""tower1229/HybridStart"",""description"":""基于apicloud的混合应用开发框架"",""private"":false,""language"":""JavaScript"",""stargazersCount"":107,""forksCount"":24,""createdAt"":""2016-12-10T08:17:23Z"",""updatedAt"":""2024-09-11T09:43:00Z"",""pushedAt"":""2019-07-15T03:39:27Z"",""size"":2303,""defaultBranch"":""master"",""topics"":[""apicloud"",""hybrid-apps"",""hybrid-start""],""hasReadme"":true,""readmeContent"":""\n# HybridStart\n\n基于[apicloud](http://www.apicloud.com/)的混合应用开发框架，可能是开发这一类混合应用的最佳实践。\n\n[![npm](https://img.shields.io/npm/v/hybridstart.svg)](https://www.npmjs.com/package/hybridstart/) [![GitHub release](https://img.shields.io/github/release/tower1229/HybridStart.svg)]() [![license](https://img.shields.io/github/license/tower1229/HybridStart.svg)]()\n\n## 提供\n- 完全独立可剥离的UI，便于风格定制\n- 清晰的开发模式，做APP从未这么简单\n- 井然有序的代码组织，页面再多也不乱\n- 熟悉的模块化开发体验，用了就回不去了\n- 丰富的示例，助你快速上手\n\n## 快速开始 \n- 在在APICloud平台创建新项目，添加[默认集成插件](#默认集成插件)\n- 将代码检出到本地，备份config.xml中的`appid`信息并清空所有文件\n- 将[Hybrid Start项目](https://github.com/tower1229/HybridStart.git)除\""/docs\""以外文件拷贝进项目文件夹，修改新config.xml里的`appid`\n- 提交代码，平台打包\n\n## 默认集成插件\n- [UIPullRefreshFlash](http://docs.apicloud.com/Client-API/UI-Layout/UIPullRefreshFlash)自定义下拉刷新\n- [ajpush](http://docs.apicloud.com/Client-API/Open-SDK/ajpush)极光推送\n- [bMap](http://docs.apicloud.com/Client-API/Open-SDK/bMap)百度地图\n- [mam](http://docs.apicloud.com/Client-API/Cloud-Service/mam)版本管理必备\n- [zip](https://docs.apicloud.com/Client-API/Func-Ext/zip)扩展插件解压\n\n## 文档 \n[HybridStart Documention](http://refined-x.com/HybridStart/docs/)\n\n[HybridStart专题文章](http://refined-x.com/tags/HybridStart/)\n\n## 课程\n\n[《Hybrid App 开发快速指南》](https://gitbook.cn/gitchat/column/5b679a1d201ffa4ab88e7d5d)\n\n## 下载\n\n项目主页：http://refined-x.com/HybridStart/\n\ngit：`git clone https://github.com/tower1229/HybridStart.git`\n\nnpm：`npm i hybridstart`\n\n## 资源\n[体验APP](http://app.mi.com/details?id=com.apicloud.A6997660453388)\n\n[代码仓库](https://github.com/tower1229/HybridStart)\n\n[源码下载](https://github.com/tower1229/HybridStart/archive/master.zip)\n\n## 更多\n> [前端路上](http://refined-x.com)\n\n<br /><br />\n"",""readmeSize"":2173},{""name"":""frontend-weekly"",""fullName"":""tower1229/frontend-weekly"",""description"":""前端周刊，给前端同学准备的每周1小时阅读清单"",""private"":false,""language"":""TypeScript"",""stargazersCount"":46,""forksCount"":5,""createdAt"":""2018-02-25T13:37:31Z"",""updatedAt"":""2025-04-30T02:08:09Z"",""pushedAt"":""2025-04-30T02:08:05Z"",""size"":45914,""defaultBranch"":""master"",""topics"":[],""hasReadme"":false},{""name"":""weapp-star"",""fullName"":""tower1229/weapp-star"",""description"":""微信小程序上手项目-星座配对"",""private"":false,""language"":""JavaScript"",""stargazersCount"":44,""forksCount"":20,""createdAt"":""2017-07-20T01:27:13Z"",""updatedAt"":""2024-09-24T00:53:36Z"",""pushedAt"":""2017-07-20T01:31:04Z"",""size"":19,""defaultBranch"":""master"",""topics"":[],""hasReadme"":true,""readmeContent"":""# weapp-star\n微信小程序上手项目-星座配对\n\n## 更多\n\n> [前端路上](http://refined-x.com)\n\n<br /><br />"",""readmeSize"":118},{""name"":""weapp-plugin-dashboard"",""fullName"":""tower1229/weapp-plugin-dashboard"",""description"":""微信小程序动态仪表盘组件"",""private"":false,""language"":""JavaScript"",""stargazersCount"":23,""forksCount"":7,""createdAt"":""2019-07-19T07:55:35Z"",""updatedAt"":""2022-06-28T09:44:36Z"",""pushedAt"":""2019-07-24T08:11:21Z"",""size"":20,""defaultBranch"":""master"",""topics"":[],""hasReadme"":true,""readmeContent"":""# weapp-plugin-dashboard\n\n[![npm](https://img.shields.io/npm/v/weapp-plugin-dashboard.svg)](https://www.npmjs.com/package/weapp-plugin-dashboard/)  [![license](https://img.shields.io/github/license/tower1229/weapp-plugin-dashboard.svg)]()\n\n> 微信小程序仪表盘组件\n\n![](https://refined-x.com/asset/a/weapp-plugin-dashboard.gif)\n\n\n## 安装与使用\n\n1. 在小程序根目录（project.config.json中`miniprogramRoot`配置的目录）中依次执行\n```\nnpm init\nnpm i weapp-plugin-dashboard -S --production\n```\n2. 微信开发者工具，项目配置开启**使用npm模块**，并执行“工具-构建npm”\n3. 在小程序页面json文件中配置\n```\n\""usingComponents\"": {\n    \""weapp-plugin-dashboard\"": \""weapp-plugin-dashboard\""\n}\n```\n4. 在小程序页面中使用组件\n```\n<weapp-plugin-dashboard />\n```\n\n## 配置参数\n\n完整的配置项及默认值如下：\n\n```\n<weapp-plugin-dashboard \n    min=\""0\""                 // 最小值\n    max=\""100\""               // 最大值\n    val=\""50\""                // 当前值\n    width=\""750\""             // 组件宽度，单位rpx\n    height=\""400\""            // 组件高度，单位rpx\n    colors=\""{{myColors}}\""   // 仪表盘颜色分布\n    >\n</weapp-plugin-dashboard>\n```\n\n```\n...\ndata: {\n    myColors: [{\n        percent: 50,\n        color: '#67C23A'\n    }, {\n        percent: 80,\n        color: '#E6A23C'\n    }, {\n        percent: 100,\n        color: '#F56C6C'\n    }]\n}\n...\n```\n\n## 关于作者\n\n[前端路上](https://refined-x.com/)\n\n"",""readmeSize"":1496},{""name"":""frontend-face-detection"",""fullName"":""tower1229/frontend-face-detection"",""description"":""a demo of frontend-face-detection"",""private"":false,""language"":""HTML"",""stargazersCount"":23,""forksCount"":2,""createdAt"":""2018-04-09T00:50:35Z"",""updatedAt"":""2021-01-10T04:20:43Z"",""pushedAt"":""2018-08-02T05:55:02Z"",""size"":12,""defaultBranch"":""master"",""topics"":[""face-detection""],""hasReadme"":true,""readmeContent"":""# frontend-face-detection\n\n## 介绍\n\n本项目是一个基于前端人脸识别技术实现的照片合成示例，人脸识别基于[trackingjs](https://trackingjs.com/)实现，照片合成基于[AlloyImage](http://alloyteam.github.io/AlloyImage/)实现。\n\n支持上传本地图片/内置图片库/远程图片三种模式的图片识别，详细介绍参见[纯前端实现人脸识别-提取-合成](https://refined-x.com/2017/09/06/%E7%BA%AF%E5%89%8D%E7%AB%AF%E5%AE%9E%E7%8E%B0%E4%BA%BA%E8%84%B8%E8%AF%86%E5%88%AB-%E6%8F%90%E5%8F%96-%E5%90%88%E6%88%90/)\n\n## 演示\n\nhttps://refined-x.com/frontend-face-detection/\n\n## 更新记录\n\n【2018-08-02】 增加自动匹配功能，匹配到人脸自动停止\n\n## 许可证\n\n[MIT](http://opensource.org/licenses/MIT)\n\nCopyright (c) 2017-present, [refined-x.com](http://refined-x.com)\n"",""readmeSize"":834},{""name"":""AJAX-Cache"",""fullName"":""tower1229/AJAX-Cache"",""description"":"":tophat:The best jQuery-ajax-cache plugin"",""private"":false,""language"":""JavaScript"",""stargazersCount"":21,""forksCount"":5,""createdAt"":""2018-03-06T05:11:19Z"",""updatedAt"":""2023-11-07T12:48:06Z"",""pushedAt"":""2019-01-17T06:41:57Z"",""size"":16,""defaultBranch"":""master"",""topics"":[""ajax"",""cache"",""jquery-plugin""],""hasReadme"":true,""readmeContent"":""English | [中文](README_CN.md)\n\n# AJAX-Cache\n\n[![npm](https://img.shields.io/npm/v/ajax-cache.svg)](https://www.npmjs.com/package/@tower1229/AJAX-Cache) [![GitHub release](https://img.shields.io/github/release/tower1229/AJAX-Cache.svg)]() [![license](https://img.shields.io/github/license/tower1229/AJAX-Cache.svg)]()\n\n> :tophat:The best jQuery-ajax-cache plugin\n\n## Introduction\n\nAJAX-Cache is a jQuery plug-in. It implements asynchronous request caching based on localStorage/sessionStorage, and provides two cache modes: snapshot and timer.\n\n## Install\n\n### npm\n\n`npm i ajax-cache  --save`\n\n### Download\n\nhttps://github.com/tower1229/AJAX-Cache\n\n## Usage\n\nYou only need to add a `localCache` configuration for jQuery.ajax ()\n\n### Open the snapshot cache\n\n```\n$.ajax({\n    url: \""http://rapapi.org/mockjsdata/9195/common/getRandom\"",\n    dataType:'json',\n    localCache: 'snapshot',\n    success: function(res) {\n        if (res.snapshot) {\n            console.log('[snapshot] ' + res.data);\n        } else if (res.snapshootEqual) {\n            console.log('remote data is equal snapshot');\n        } else {\n            console.log('[remote data] ' + res.data);\n        }\n    }\n});\n```\n\n### Open the timing caching\n\n```\n$.ajax({\n    url: \""http://rapapi.org/mockjsdata/9195/common/getRandom\"",\n    dataType:'json',\n    localCache: 5000,\n    success: function(res) {\n        console.log('\\n[Caching for 5 seconds] ' + res.data);\n    }\n});\n```\n\n### Scavenging caching\n\n```\n$.ajax({\n    url: \""http://rapapi.org/mockjsdata/9195/common/getRandom\"",\n    dataType:'json',\n    localCache: false,\n    success: function(res) {\n    \tconsole.log('Cache has been cleared');\n        console.log(res.data);\n    }\n});\n```\n\n### Scavenging all caches\n\n```\n$.ajaxCache.clear();\n```\n\n### Configuration\n\n```\n$.ajaxCache.set({\n\tstorage: 'localStorage', \t\t//Storage mode, default \""localStorage\"", optional \""sessionStorage\""\n\tcacheNamePrefix: '_ajaxcache'\t//Storage prefix, usually without modification\n});\n```\n\n## Live Example\n\nhttp://refined-x.com/AJAX-Cache/test/\n\n## License\n\n[MIT](http://opensource.org/licenses/MIT)\n\nCopyright (c) 2017-present, [refined-x.com](http://refined-x.com)\n\n"",""readmeSize"":2162}]"	"[{""sha"":""597904debc0fec01f5fdd4f252e9edd168c5cb93"",""message"":""Update project configuration and remove unused components\n\n- Added iOS and Android directories to .gitignore.\n- Updated app.json to include bundle identifiers for iOS and package name for Android.\n- Added expo-dev-client dependency in package.json and package-lock.json.\n- Refactored app layout by removing unused components and simplifying the structure.\n- Deleted several components and screens that are no longer needed, including NotFoundScreen, TabLayout, and Explore screen.\n- Removed color scheme and themed components to streamline the project."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T01:23:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T01:23:05Z""},""repository"":""tower1229/react-native-app""},{""sha"":""60a5d4769f95e137aa692ef28aa4c8dd710f6b6e"",""message"":""Initial commit\n\nGenerated by create-expo-app 3.3.0."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-08T03:06:34Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-08T03:06:34Z""},""repository"":""tower1229/react-native-app""},{""sha"":""74f67b175dd49de8a9e5f1d3f9749af14651e2a0"",""message"":""Merge pull request #9 from tower1229/dev\n\nImplement update route and enhance token creation: Add '/update-spl' …"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-23T04:22:37Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-23T04:22:37Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""cf13a9613b46344753cb066523b2b93288b06040"",""message"":""Implement update route and enhance token creation: Add '/update-spl' route for updating tokens, including necessary imports and route definitions. Update create-spl component to allow token mutability and add a checkbox for enabling/disabling this feature. Modify index component to link to the new update route, improving user navigation. Enhance mint-spl component with mint authority checks and refactor amount calculations for better precision."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T04:21:28Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T04:21:28Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""a1016ff20a7905f4c64c0e9b90b0996132e1eb7b"",""message"":""Merge pull request #8 from tower1229/dev\n\nRefactor environment variable usage: replace import.meta.env with pro…"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:11:48Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:11:48Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""69042f39b48ab60df34edac77018d91b25ff362e"",""message"":""Refactor environment variable usage: replace import.meta.env with process.env for PUBLIC_BACKEND_SERVICE, IMGBB_API_KEY, and LIGHTHOUSE_API_KEY in various files. Enhance error handling by adding checks for required API keys in image upload functions. This change improves consistency in environment variable management across the application."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:11:03Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:11:03Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""7c08b36c08cb04c6f5b4fb52ef64d10b90ed4ec8"",""message"":""Merge pull request #7 from tower1229/dev\n\nDev"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:03:28Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:03:28Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""0ed916b899fa7eb962dd51a39fd6cd97f241ca46"",""message"":""Refactor environment variable handling and update API key references: rename LIGHTHOUSE_API_KEY and IMGBB_API_KEY to PUBLIC_LIGHTHOUSE_API_KEY and PUBLIC_IMGBB_API_KEY for clarity. Adjust image upload utilities to utilize new environment variable names. Update Header component styling and footer content for improved user experience."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:02:57Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T02:02:57Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""b8e8f7d90cabf929842291afd139c6ed19e76879"",""message"":""Refactor environment variables and remove unused configurations: clear PUBLIC_BACKEND_SERVICE and update API key references in environment files. Remove obsolete GitHub Actions workflow and streamline build configuration in rsbuild.config.ts. Update image upload utilities to use new environment variable names for improved clarity and consistency."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:52:22Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:52:22Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""31791a4be7cf99a9b75820da28b2e6718f11ad2b"",""message"":""Merge pull request #6 from tower1229/dev\n\nAdd disclaimer route and update HomeComponent: include '/disclaimer' …"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:40:43Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:40:43Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""1301c3c30b3f526a455bd3cc43010b57037b15d8"",""message"":""Add disclaimer route and update HomeComponent: include '/disclaimer' route in routing structure, create DisclaimerRoute, and integrate a link to the disclaimer in the HomeComponent for improved user navigation."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:40:10Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:40:10Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""52e4ddb54b4b72e855c024983878ee8b73e8c678"",""message"":""Merge pull request #5 from tower1229/dev\n\nEnhance token creation and routing features: add '/mint-spl' route fo…"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:11:18Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:11:18Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""59d158fd2858397b1bc6ba6a7e43845965588a9f"",""message"":""Enhance token creation and routing features: add '/mint-spl' route for minting tokens, update ProgressModal to display token and transaction details, and refactor create-spl route to include new token information fields. Improve user feedback with enhanced logging and success information handling during token creation process."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:10:17Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-23T01:10:17Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""6103d659553d31ddfc4c3787f51e2128901272d4"",""message"":""Merge pull request #4 from tower1229/dev\n\nDev"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:56:42Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:56:42Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""15a0cd035c96d739964671525d51e36a54a53583"",""message"":""Update Header component: change title to emoji and adjust styling for improved visual appeal"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:55:52Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:55:52Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""56341b3fcc87d746c6f72e1612570446a058531f"",""message"":""Rename project to \""create-token-on-solana\"" and update Tailwind CSS theme colors. Refactor routing to include a new \""/create-spl\"" route for token creation, enhancing the user interface with a new header and improved layout in the HomeComponent. Update the Header component for better styling and user experience."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:49:15Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:49:15Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""65736b6f15931d9c8829ef39101919d5043f9670"",""message"":""Merge pull request #3 from tower1229/dev\n\nUpdate GitHub Actions workflow to use Yarn for dependency management …"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:21:31Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:21:31Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""6b5f0e51d75a931ebf50ce370d51452d52d73025"",""message"":""Update GitHub Actions workflow to use Yarn for dependency management and build process, replacing npm commands for improved consistency and performance."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:21:00Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:21:00Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""60fe35610fd74563d71a7dd44fbda801074b4229"",""message"":""Merge pull request #2 from tower1229/dev\n\nUpdate Node.js version in GitHub Actions workflow from 18 to 22 for i…"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:20:03Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:20:03Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""c1dbf3656224daf8dce0afcdce6d26988311f75f"",""message"":""Update Node.js version in GitHub Actions workflow from 18 to 22 for improved compatibility and performance."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:19:21Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:19:21Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""de7fc94253da91a0c58b6e13a181779ef92372e3"",""message"":""Merge pull request #1 from tower1229/dev\n\nDev"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:18:06Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:18:06Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""c485d82ba6bd1c63546515c92c15013fd297d1ef"",""message"":""Enhance build configuration and streamline HomeComponent: add assetPrefix for production environment in rsbuild.config.ts and remove unused explorerUrl state in HomeComponent for improved clarity."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:17:40Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:17:40Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""68522223fb6a86a2dd756c52e2d2b5c6d5e84ee0"",""message"":""Update token creation feature: change page title, enhance network selection in the wallet connection, and improve progress modal to display transaction details. Refactor SolanaProvider to dynamically set RPC endpoint based on selected network. Add success information handling in HomeComponent for better user feedback."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:12:52Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T11:12:52Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""565e41325f6820a68216082229c2a534f089c668"",""message"":""Refactor HomeComponent to streamline token creation process by consolidating transaction instructions into a single transaction. Enhance logging for better user feedback during account creation, token minting, and metadata upload. Update associated token account creation and metadata handling for improved clarity and efficiency."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T07:29:11Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T07:29:11Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""494dcd755967e59b1304dabc5f84adf748b64547"",""message"":""Update environment variables for Lighthouse and Imgbb API keys, enhance token creation process with metadata upload, and improve component structure. Add new utility functions for IPFS and image uploads. Refactor HomeComponent to include additional token information fields and logging for better user feedback."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T07:27:49Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T07:27:49Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""ed7779d2f880123996e1ad75b958ae66b03ac983"",""message"":""Rename project to \""solana-create-token\"" and update development port to 3002. Remove unused data store and fetch functions, and implement a new token creation feature in the homepage component. Update README to reflect project purpose."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T02:18:35Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T02:18:35Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""7fc7147d2a661d6ed5e53cbefab8988fee7c8e2d"",""message"":""init"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T02:08:08Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-22T02:08:08Z""},""repository"":""tower1229/create-token-on-solana""},{""sha"":""aa41bfcdc9e36dfdac788612487bf5233862c2a3"",""message"":""update link"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:47:54Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:47:54Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""f879e8501027b4b97430a59830ef7eb49a04b2bf"",""message"":""Merge branch 'master' of github.com:tower1229/tower1229.github.io"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:46:34Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:46:34Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""b2a3faeea0d2f93137151dfa727c3c0d53aa64a2"",""message"":""add new"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:46:30Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:46:30Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""977d767242cd548bbd21bdbfb39477cfe93c0684"",""message"":""Add 'more' tag to New Countdown Timer post for improved content organization and readability."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-14T01:53:48Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-14T01:53:48Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""d99f27999b4cd2560c1c97c3d82f59bac4e7ee20"",""message"":""Refine language and clarity in the About section of the documentation, enhancing readability and consistency throughout the text."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-12T11:33:15Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-12T11:33:15Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""e2c421943022db7c567b84ddfb0000b51b6fe3f0"",""message"":""Enhance layout of interface screenshots in New Countdown Timer post by enabling flex-wrap for better responsiveness."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:15:02Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:15:02Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""184bc1ce0047d4819d0aeed90654dbe88fc52f9a"",""message"":""Add interface screenshots to New Countdown Timer post for enhanced visual representation"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:11:45Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:11:45Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""d0a8c796ca2394616410423d9badda73e9a1832b"",""message"":""Update countdown timer images: adjust dimensions for better display on Chrome and Edge installation links."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:04:42Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:04:42Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""7d05bf089e49894ac4219fd822700a263beb3abc"",""message"":""Merge branch 'master' of github.com:tower1229/tower1229.github.io"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:00:55Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:00:55Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""451a0c36f65b8ff99498353ab513490a768ca0cb"",""message"":""Update countdown timer image: replace s3.png for enhanced visual quality and consistency with previous updates."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:00:50Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T10:00:50Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""2609acf2b323217ab4b15a259b2e54fa0bda7776"",""message"":""Update New-Countdown-Timer——AI全流程驱动的Chrome扩展开发实录.md"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-05-01T05:31:03Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-05-01T05:31:03Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""7ef8005a74ca5cce8e1396bb7dd32fa503f7c64e"",""message"":""Update countdown timer image: replace s2.png for improved visual quality."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T01:07:52Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T01:07:52Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""a47d7c244f507a23d668aa97a6989afc5c9b132d"",""message"":""Update countdown timer image: replace existing s1.png with a new version for improved visual quality."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T01:04:21Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T01:04:21Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""d3eaa2e4922ba125eefddb8d9b3fa07d46420081"",""message"":""Update blog post \""New Countdown Timer\"": refine text for clarity, correct formatting issues, and add additional plugin screenshots for better visual representation."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T01:00:41Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T01:00:41Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""33d7baf4b85c241ddadb39340d360f384b77f826"",""message"":""Update New-Countdown-Timer——AI全流程驱动的Chrome扩展开发实录.md"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-04-29T16:02:04Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-04-29T16:02:04Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""40f41101a64d3192c0a5c68d51791164933224ce"",""message"":""Update New-Countdown-Timer——AI全流程驱动的Chrome扩展开发实录.md"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-04-29T15:52:24Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-04-29T15:52:24Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""1d6a1f8280e7ed6da0fe4e69c86680dccd6c3e46"",""message"":""Refactor Algolia search functionality: update search attributes, enhance hit formatting, and improve event handling for better performance and user experience. Adjust code style for consistency."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T13:28:34Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T13:28:34Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""359cc50d39953d6ae090ec4219b95ede82cc8128"",""message"":""Update blog post \""New Countdown Timer\"": replace image for better representation, enhance text clarity, and refine development process description. Adjust formatting for improved readability."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T11:12:42Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T11:12:42Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""dab52c0b1349e7d8f3a5cc379c30ace9610b2917"",""message"":""Refine blog post on \""New Countdown Timer\"": improve text clarity and flow, update tea brewing instructions, and enhance the description of the development process. Adjust image formatting for better presentation."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T11:00:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T11:00:12Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""c69b9331114366ba574e55e2af72d0c3adca2add"",""message"":""Update favicon paths in _config.yml: change image locations to the /asset/favicon directory for better organization and consistency."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T09:27:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T09:27:05Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""678d81e7967c1e144614c4c03056ba35de6490e4"",""message"":""Update blog post \""Hello Hexo Again\"": add date, improve formatting, and enhance readability by adjusting text spacing and structure. Document backup strategies and customizations for Hexo, including layout modifications and plugin recommendations."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T09:18:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T09:18:05Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""3e9462ff504efeabec44acd4a63975d6349ebe6b"",""message"":""Remove unused files and configurations from the next-backup theme, including CSS, JavaScript, images, and documentation. This cleanup enhances maintainability and optimizes the theme's performance."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T09:13:31Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T09:13:31Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""5811ca5d0ed40a8d104a75309c359848f6e9676b"",""message"":""Merge pull request #8 from tower1229/update-next\n\nUpdate next"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-04-29T08:55:18Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-04-29T08:55:18Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""55c4a84cdbfa791d5b1930be96f3d88c9ebe7b8f"",""message"":""Update menu icons in _config.yml: replace text labels with Font Awesome icons for improved visual representation and consistency across the theme."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T08:54:43Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T08:54:43Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""48ab3abf4a3c239c41495731d4e4c73bdbf449ae"",""message"":""Refactor theme structure: remove unused files and configurations, update CSS styles, and enhance language support across various components. This cleanup improves maintainability and optimizes the theme for better performance."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T07:25:19Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T07:25:19Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""56355fbbd94007354b0602cd1e824136caaac3ec"",""message"":""Update Algolia configuration in _config.yml: change index name, add attributes to display, specify image attribute, and set noInteractive option."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T06:55:34Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T06:55:34Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""c7f6221f1b68ecc025ce5f7865c03bbfb5d4a2cd"",""message"":""Merge pull request #7 from tower1229/update-hexo\n\nUpdate configuration and dependencies: change site title and descript…"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-04-29T06:47:50Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-04-29T06:47:50Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""295b81d08d4cafad215cd371a160dd349cd245d9"",""message"":""Update configuration and dependencies: change site title and description in _config.yml, upgrade Hexo and related packages in package.json, and add Algolia crawler verification in robots.txt. Additionally, refine post content for better readability and structure."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T06:47:27Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T06:47:27Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""3e448d4bf9fdd10e52425cbe403c56496ca55aaa"",""message"":""Update configuration settings: disable Baidu push for SEO and switch Algolia script sources to CDN links"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-17T10:01:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-17T10:01:12Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""cee0c286b28a24d09d45975fef46131a9d4ee55d"",""message"":""Enable motion animations and update Pangu script source in configuration file"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-17T09:50:55Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-17T09:50:55Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""c6897928df1fde022685778a8dcf3f464a274f3c"",""message"":""Enhance project index with new entries and improved descriptions for Github Card and Tracesr, while maintaining existing content for 宝贝成长助理. This update enriches the project showcase with detailed information and visual elements."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-17T08:57:42Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-17T08:57:42Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""f47474388c8a3a7a5818d53b56162e89822229f6"",""message"":""update about"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-08-21T06:42:06Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-08-21T06:42:06Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""011b54205e802a526f3c3a688496c95336cc06a6"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-07-08T07:58:01Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-07-08T07:58:01Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""7a4e872a3cd3a7e1a8d90bb1f62376494d9b1888"",""message"":""update about"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-05-06T06:35:29Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-05-06T06:35:29Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""2dbcccc3443920614ef9ab97f1003cbe4d0a12d9"",""message"":""update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-04-05T04:41:37Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-04-05T04:41:37Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""518a67a6b8f2a0ee6cf09c9addabbb63d742a3a5"",""message"":""update about"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2022-12-13T03:25:55Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2022-12-13T03:25:55Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""ff49c9dccf8263b08ce4a84d8c69445cd139d87c"",""message"":""update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2022-12-08T04:52:58Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2022-12-08T04:52:58Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""0c8de18602c299fc1a5adf46a2e634ca27c11929"",""message"":""beian"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:36:30Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:36:30Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""88f0ea30e9c2abc218dcfb14ac5e0035e6ebb41b"",""message"":""update about"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-07T12:19:48Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-07T12:19:48Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""b6826deb6a3dd7855203b70b4518c878a8a58102"",""message"":""update about"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-23T02:48:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-23T02:48:05Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""fbeccdc4e3bc48b520fbb05a7ac662d25ba8ccba"",""message"":""update post"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-01T01:33:15Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-01T01:33:15Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""0f1db84a89fb0f06a0ef3536bbdac3fd27d7a6e3"",""message"":""更新项目连接"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-30T02:08:39Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-30T02:08:39Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""17afdeb1d3e5971f804ba79985eb4d578b6a8c59"",""message"":""update post"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-30T01:12:24Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-30T01:12:24Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""28c8746c6ed205ae81791087e4fa7188f190331a"",""message"":""update-前端导航"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-29T01:59:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-29T01:59:12Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""e007133bd667b4885fd442fb3020ff826d081c94"",""message"":""update about"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-23T02:20:57Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-23T02:20:57Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""a71675c708d87e3756efb9f9f7b802882a87c768"",""message"":""链接错误"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-22T05:17:32Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-22T05:17:32Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""411dad59402fbbb837263cb70c90d8e276337e12"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-26T08:06:44Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-26T08:06:44Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""21fc71a0660d0802b2b3f36ea64b4b51748c852c"",""message"":""post"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-26T08:02:22Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-26T08:02:22Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""57a3ae2921371e6282d302b21b6b12632644d610"",""message"":""add jsDelivr"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-23T09:41:03Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-23T09:41:03Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""7d6f1f28fdd63230ef1a838b541352edf4c5dd09"",""message"":""更新工具箱"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-19T09:12:58Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-19T09:12:58Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""c4175df320231c927e43c6b0efe7622e862b8e26"",""message"":""类库本地化"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-17T01:36:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-17T01:36:12Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""a55a59a03fa11389520aa9b8a71053ab892c45ee"",""message"":""add 正则大全"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-16T09:58:58Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-16T09:58:58Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""ae93ce3fa978c6cf66103188420d66c058b5ecb7"",""message"":""添加lottie 动画库"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-12T03:23:48Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-12T03:23:48Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""10561affef45d75d3ca33d67cf2c58d6d99e4fe8"",""message"":""更新项目"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-12T01:17:31Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-12T01:17:31Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""1627870edf9af1bdfff297baa42ab27dd1961f4e"",""message"":""update navi intro"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T05:59:50Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T05:59:50Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""a3cf5fd5ffc2402b14f93e1ae39b307ec007fe19"",""message"":""前端工具箱"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T05:54:45Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T05:54:45Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""fc6169621f9403d431b036a2a50e310d14914108"",""message"":""bug fix"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-07T02:49:34Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-07T02:49:34Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""0e87ec2ca22ac3dced227b1231a0d0e9eeac82ac"",""message"":""bug fix"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-07T02:42:35Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-07T02:42:35Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""73477520a347780d8ca2166e9c50311168de614f"",""message"":""移除 .refined-x.com 域名"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-07T02:38:42Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-07T02:38:42Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""f80337168d7039a55fd07583de7ee7c83810e17b"",""message"":""update about"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-12T01:59:15Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-12T01:59:15Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""70ee72b01cf2d0da8b3a8fa1b41bde9d43254858"",""message"":""update project"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-12T01:31:33Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-12T01:31:33Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""30f9cbc37822de5d53d44e703749c64a630e197e"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:21:06Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:21:06Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""90535f9ddcade970f29cb03909cf3dc9085a0c2b"",""message"":""Merge branch 'master' of github.com:tower1229/tower1229.github.io"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:20:25Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:20:25Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""b0db8c3421f8024989ac8c0664d50e0a90cb8374"",""message"":""update minimist"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:20:20Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:20:20Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""63bbd9abc82c6df037466092d089e9797503553f"",""message"":""Merge pull request #1 from tower1229/dependabot/npm_and_yarn/prismjs-1.27.0\n\nBump prismjs from 1.25.0 to 1.27.0"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:18:23Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:18:23Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""427b7fd698322d714eac255126f3024f772faf2e"",""message"":""Merge pull request #3 from tower1229/dependabot/npm_and_yarn/moment-2.29.2\n\nBump moment from 2.29.1 to 2.29.2"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:18:11Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:18:11Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""b1dca5b86d81e78bf8f11b07801e21be4118035d"",""message"":""update package"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:17:41Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-10T01:17:41Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""bce0725c3ba1385ffa2bbcc31d57dfe196056b05"",""message"":""接入百度联盟"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-03-04T02:16:32Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-03-04T02:16:32Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""ac82bac45a1640dc714d9a98d253dc973eb9b576"",""message"":""关闭评论"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-03-04T01:09:56Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-03-04T01:09:56Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""c793b691386da0042c3871841ead61c28403e46e"",""message"":""更新样式"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-21T08:16:39Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-21T08:16:39Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""1d0093d717bf51af9bc3ecfe45b778e1b081d4c3"",""message"":""增加侧边栏广告"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-21T07:53:10Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-21T07:53:10Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""d8c301f9b62369970f6b23c106c0e0d7b52f18c6"",""message"":""添加CNAME"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T06:08:22Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T06:08:22Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""ea1121820f314e899c972274fa224c63f84cbbb5"",""message"":""简历添加GitHub统计"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T06:04:20Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T06:04:20Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""2b4b8781ebebfc8eef7f2f5983214be379f36c02"",""message"":""关掉motion效果"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T02:51:40Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T02:51:40Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""dcdf359aca85410bda429dc88ca17a2ed877d90a"",""message"":""404 no layout"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T02:30:23Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-22T02:30:23Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""3d34b13da3b52c7653a07f5952717d3beaee2bac"",""message"":""Create README.md"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2021-12-21T05:15:48Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2021-12-21T05:15:48Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""463e535a014af0edec6b1a1ec8cd25481bd25c67"",""message"":""修改git地址"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T05:12:31Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T05:12:31Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""809991238b408a6f9f321fa2ad9f423dfe11f9d2"",""message"":""add nojekyll"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T03:19:11Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T03:19:11Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""9c99191f4fe80e585919e8f0fcc5f5cfccd42164"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T03:09:45Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T03:09:45Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""ad3d3a76ea553e72656c472930a49971484f4b39"",""message"":""fix github"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T03:08:06Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T03:08:06Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""5da8aac19f4c24a8d13e48c1a3939290bf339ac3"",""message"":""create gh-pages"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T02:51:16Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T02:51:16Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""4a33bee81aa19e79441085e057d97ab551104674"",""message"":""友情链接内联样式"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T09:52:48Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T09:52:48Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""49443556a87d6d8c39bdd6ecdde36f9047d0958a"",""message"":""删除rss2.xml"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T08:58:26Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T08:58:26Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""be8b345d0bd7946fa2044017b06b84c2eae9f183"",""message"":""完成"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T08:55:13Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T08:55:13Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""92dc985d51940ede0b3ed5f64f733a6f74af6716"",""message"":""保存一下"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T06:59:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T06:59:12Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""0090456f5fe5b006a70419cd9104ea8e2e108bb8"",""message"":""ignore"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:57:02Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:57:02Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""2ed75fa28db37b5cdd4cb412a3e9d528f872fa61"",""message"":""renew"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:56:13Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:56:13Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""2832889e92b3c87ed99f853188fcaee1ee542d1d"",""message"":""Merge branch 'master' of github.com:tower1229/tower1229.github.io"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:54:17Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:54:17Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""4b75f2e4373a74e3afb34d06bcf54db1b0b3e24e"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:52:09Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:52:09Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""65d4da918573cec07713d73edf18948892bbdd0f"",""message"":""保存一下"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:40:50Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T09:40:50Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""d15ff06b89d971d71fcafc70fd423c81fa04c85b"",""message"":""暂存一下"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T07:44:17Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T07:44:17Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""8d6eda4040452c84d1791fd4a93d36efe6683ca5"",""message"":""单页+评论"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T04:03:03Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-16T04:03:03Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""bef65d48fdddf3d2e396db76d80741bfa5066919"",""message"":""init"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-15T10:06:14Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-15T10:06:14Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""2f55142b5a0df55a72a6414da07c9f315845c4b4"",""message"":""Site updated: 2021-11-12 09:29:52"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-12T01:29:53Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-12T01:29:53Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""2bb46ad5d16b640a5b9de86cc0b9d3b08d71b49d"",""message"":""Site updated: 2021-11-12 09:29:24"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-12T01:29:25Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-12T01:29:25Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""171cde0c5ebd26f4549d0ba874b0e1e31ad79467"",""message"":""Site updated: 2021-09-24 09:28:35"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-09-24T01:28:36Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-09-24T01:28:36Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""d6ff629b793341abacccb0724cb6342a18416e2b"",""message"":""Site updated: 2021-08-31 13:10:57"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-31T05:10:58Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-31T05:10:58Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""071cb3ac40c4396b49814f7c5f8f827bcca77235"",""message"":""Site updated: 2021-08-17 09:09:13"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-17T01:09:15Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-17T01:09:15Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""5de3c7b3939c27cb718de45e20a82c00d4d442b1"",""message"":""Site updated: 2021-08-12 15:28:34"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-12T07:28:35Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-12T07:28:35Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""e1d9bd049d750f53dc6cfb6388863f8241e3f306"",""message"":""Site updated: 2021-08-12 15:26:57"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-12T07:26:58Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-08-12T07:26:58Z""},""repository"":""tower1229/tower1229.github.io""},{""sha"":""faee058e00ec29c3ce86d44e79bb64908b3dfd0f"",""message"":""add readme"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:21:09Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:21:09Z""},""repository"":""tower1229/GitHub-Card-Community""},{""sha"":""c77a0bd7abbe668c407a88ec94f5a953e5847383"",""message"":""Initial commit"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:19:46Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-05-28T12:19:46Z""},""repository"":""tower1229/GitHub-Card-Community""},{""sha"":""8d3655e53e32b323bd59ab62ce580c83502e60ad"",""message"":""update to 1.2.4"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-26T01:12:36Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-26T01:12:36Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""75d320ce4e269d904aad54e07350a1a2bdd1810c"",""message"":""update Icons"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-26T01:12:01Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-26T01:12:01Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""0b5e7ef7279c193414ede07134fb51326295281c"",""message"":""docs: update README with direct links to Chrome and EDGE app stores for easier access"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T09:53:46Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T09:53:46Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""79e14b20820b957314ba87020acf49624717c5cb"",""message"":""chore: update dependencies in package.json and yarn.lock for improved stability and performance\n\n- Added rimraf as a dependency.\n- Updated @tailwindcss/postcss and @tailwindcss/vite to version 4.1.6.\n- Updated types for chrome, react, and react-dom to their latest versions.\n- Updated eslint and daisyui to their latest versions.\n- Updated vite to version 6.3.5.\n- Updated various Babel packages to their latest versions for better compatibility."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T09:48:58Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-11T09:48:58Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""dd26c7b075e91b2f67e06c3ba5f5d6b05df8ad22"",""message"":""chore: update version to 1.2.3 in package.json and manifest.json; adjust styles in TimerItem and TimerList components for improved layout"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-09T12:52:20Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-09T12:52:20Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""d376291ddda616c0666487ce19f4d1087a0b1411"",""message"":""chore: update version to 1.2.2 in package.json and manifest.json; minor style adjustments in popup and CountdownView components"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-07T13:45:40Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-07T13:45:40Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""210834f57864185dec0f5fb7afbff8876cf5c97f"",""message"":""fix: sync"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-07T02:13:32Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-07T02:13:32Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""83ad4ddea27e50e6572daec8057df191fb521ce0"",""message"":""style fix"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-07T01:15:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-07T01:15:05Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""0559c06cd0d96f5f9645014220a0c92fae837196"",""message"":""Update version number in manifest.json from 1.1.0 to 1.2.0"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-05T07:13:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-05T07:13:05Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""cfff221f77cfa3d6f7f8486ba9f8538561ac5de9"",""message"":""Update version number in package.json from 1.0.0 to 1.2.0"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-05T07:12:17Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-05T07:12:17Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""cdfe4afc600cd5d7bbd771a4416a10fe37fcd3a5"",""message"":""Remove outdated screenshot files from the documentation directory"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T13:32:06Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T13:32:06Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""e457a910c27c1978fda62c341fb9ba5438f32402"",""message"":""Enhance Timer components and icon handling for improved consistency\n\n- Added aria-label and title attributes to the sound preview button in TimerForm for better accessibility.\n- Updated TimerItem component to include titles for drag-and-drop functionality and timer start action.\n- Improved icon display logic in setExtensionIcon to maintain consistency with countdown display.\n- Adjusted createIconText function to round milliseconds to full seconds, aligning with CountdownView behavior."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T13:01:22Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T13:01:22Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""9aabc0f001a5eb05071a0633523a3ab5c8522884"",""message"":""Refactor Countdown and TimerList components for improved clarity and styling\n\n- Removed isCountingDown prop from TimerListPage and Popup components to streamline state management.\n- Updated button style in CountdownView for a more consistent appearance."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T12:45:25Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T12:45:25Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""5883a0d7e766c92f5fce7d778228d4848723c958"",""message"":""Refactor TimerForm and chrome-theme.css for improved styling consistency\n\n- Reduced padding in TimerForm component for a more compact layout.\n- Adjusted padding in chrome-theme.css for better alignment of sound items."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T12:34:55Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T12:34:55Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""460bca7a1a8c72d1375e5e9a21011690976afb61"",""message"":""Update TimerItem component and chrome-theme.css for improved styling\n\n- Adjusted opacity of the TimerItem background for better visual consistency.\n- Removed unnecessary margin from chrome-theme.css to streamline layout."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T12:27:30Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T12:27:30Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""75ade27d973f829bf82017fe008a0dad737ebfe7"",""message"":""Refactor TimerItem component for improved UI consistency and accessibility\n\n- Simplified background color handling to use a consistent theme color.\n- Updated button styles for better alignment and user interaction.\n- Replaced SVG icons with a more streamlined design for clarity.\n- Enhanced layout spacing for a cleaner appearance."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T08:36:31Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T08:36:31Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""980cc3a7a33a93ce7078439556b2ca6748ce010c"",""message"":""Enhance UI and theme integration for Countdown Timer extension\n\n- Updated Tailwind CSS configuration to include custom colors for a Chrome-inspired theme.\n- Refined popup layout and styles for improved user experience, including consistent dimensions and overflow handling.\n- Integrated new ChromeLayout component for better structure in TimerEditPage and TimerListPage.\n- Updated TimerForm and TimerItem components to utilize theme colors and improve accessibility.\n- Enhanced TimerList and CountdownView components with responsive design and improved styling.\n- Added chrome-theme.css for centralized theme management and styling consistency across components."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T08:21:40Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T08:21:40Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""6af43fca75c3f1fda0854535d24cdbb811d43a36"",""message"":""Merge pull request #2 from tower1229/feat-sync\n\nFeat sync"",""author"":{""name"":""🦄9527"",""email"":""<EMAIL>"",""date"":""2025-05-02T07:06:05Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2025-05-02T07:06:05Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""a5f56586458d68b8d1591bccd3983f5161f8e22f"",""message"":""Update privacy policy and manifest permissions for Countdown Timer extension\n\n- Added 'activeTab' and 'offscreen' permissions to the manifest.json for enhanced functionality.\n- Updated privacy-policy.md to reflect the new permissions and clarify data collection practices."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T06:59:26Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-05-02T06:59:26Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""5b0a00b6545cf68000f6c8a43fb28670d0b06c67"",""message"":""Update store description to reflect branding change from Chrome to Browser extension\n\n- Revised short and detailed descriptions for clarity and consistency.\n- Updated references to cloud sync and toolbar display to align with new terminology."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T14:20:43Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T14:20:43Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""0ca0ed579857539c24ea0feb9bfcd9279aafe7c4"",""message"":""Refactor project to rename Chrome extension to Browser extension\n\n- Updated package.json to reflect the new name and description.\n- Revised README.md to change references from Chrome to Browser extension.\n- Modified .gitignore to clarify the purpose of ignored files.\n- Removed deprecated test.sh script.\n- Updated manifest.json description for consistency with new branding."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T14:06:06Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T14:06:06Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""fbff049853e6f4ded0e14088adfdb9526f8df5ca"",""message"":""Remove accessibility support section from README.md"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T13:22:21Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T13:22:21Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""403a4839b3dc2fd4791d1f9b6aade1abe18cb7b3"",""message"":""Revise README.md for clarity and feature updates\n\n- Updated the title and description to better reflect the purpose of the Countdown Timer Chrome extension.\n- Enhanced the introduction and features sections for improved readability and detail.\n- Reorganized installation instructions and usage guidelines for better user experience.\n- Added a privacy and security section to emphasize data protection measures.\n- Included a support and contribution section to encourage user engagement."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T13:12:31Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T13:12:31Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""4c78e8ceb1432b2996221dd7d8c0797d617355cc"",""message"":""Update store description and features for Countdown Timer extension\n\n- Revised short and detailed descriptions to emphasize enhanced functionality and user experience.\n- Expanded key features section to include custom timers, cloud sync, and an intuitive interface.\n- Updated use cases to reflect broader applications of the timer tool.\n- Improved privacy and efficiency statements for clarity and user assurance."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T13:03:33Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T13:03:33Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""b3a62a2830da5f86ed0f6e93fe27babbcec60a50"",""message"":""Enhance logging for cloud synchronization process\n\n- Added logging to indicate when cloud synchronization is triggered and when it succeeds.\n- Improved error handling logging for cloud sync failures to aid in debugging."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:59:04Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:59:04Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""2130e2828a8ef573a50acf1c340fe9afeb39a99f"",""message"":""Refactor cloud synchronization logging and debounce timing\n\n- Updated logging messages in service worker and sync manager for better clarity and localization.\n- Reduced debounce delay for cloud synchronization from 10 minutes to 1 minute to improve responsiveness.\n- Simplified cloud data retrieval and saving processes by removing redundant logs."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:54:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:54:12Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""326db05eb6971f84055b9b8d5230ce42836eab7b"",""message"":""Enhance cloud synchronization and localization features\n\n- Implemented pre-unload synchronization of custom timers to ensure data consistency.\n- Updated service worker to handle both synchronous and asynchronous cloud sync methods.\n- Improved logging for cloud data retrieval and synchronization processes.\n- Translated user-facing messages in TimerList component to English for better accessibility."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:52:25Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:52:25Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""a6db99b8863731ac8364dbbb610f2ed8e8fc019d"",""message"":""Implement cloud synchronization feature for Countdown Timer extension\n\n- Added functionality for seamless cloud synchronization of timer data across devices.\n- Introduced a SyncManager to handle local and cloud data interactions, including conflict resolution.\n- Updated service worker to initialize cloud sync on startup and force sync on extension unload.\n- Enhanced storage utility to trigger cloud sync upon saving custom timers.\n- Documented cloud sync features and considerations in the project documentation."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:24:52Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T05:24:52Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""b73f795742f4ddb2d83cbf79ed4eac433fd4ccd8"",""message"":""Remove unused timer-template.svg icon from the project and update documentation to reflect the change."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T02:32:01Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T02:32:01Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""d896681baaaf696e6dd987732c903f77a1b518b5"",""message"":""Remove front-end cursor rules and update TypeScript guidelines with best practices and localization for improved clarity and structure."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T02:29:09Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T02:29:09Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""0db0eff92462f93f54c3ecc750d522fdb073a802"",""message"":""Update icons and images for improved visual assets"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T03:26:09Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-29T03:26:09Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""80c5b93649c4d3fe57922fac35f788c2512c350a"",""message"":""Add GitHub link to README.md for easier access to the repository"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T13:18:40Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T13:18:40Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""419b1b510d16a6ac3ac218e583cbc9fbb618847a"",""message"":""Update README.md for Chinese localization and feature enhancements\n\n- Translated the README to Chinese, providing a localized overview of the Countdown Timer Chrome extension.\n- Revised sections to reflect the plugin's purpose, use cases, and features in Chinese.\n- Improved installation instructions and added a FAQ section for better user guidance.\n- Enhanced the overall structure and clarity of the documentation to cater to Chinese-speaking users."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T12:08:47Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T12:08:47Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""4c7580a8d2264d962267c909f766a9681c5c6625"",""message"":""Update README.md to enhance documentation and feature descriptions for Countdown Timer extension\n\n- Revamped the README to provide a clearer overview of the Countdown Timer Chrome extension, including its purpose and use cases.\n- Expanded the features section to detail multiple custom timers, drag-and-drop sorting, and accessibility options.\n- Improved installation instructions and added a FAQ section for better user guidance.\n- Updated technical stack and development setup instructions to reflect the use of Yarn as the package manager."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T11:55:28Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T11:55:28Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""b5d6cb01f90e227853387ba74bbc86da67421b34"",""message"":""Refactor sound playback logic and enhance service worker communication\n\n- Improved sound playback handling in the service worker to better manage active content scripts.\n- Enhanced error handling for message sending to ensure reliability during playback requests.\n- Updated content script to confirm loading status and respond effectively to sound playback commands."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T10:26:32Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T10:26:32Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""e7fdbb0655876e3850dcaa974a7e38931dcac752"",""message"":""Update manifest and service worker for improved permissions and sound playback\n\n- Expanded permissions in the manifest to include \""activeTab\"" for better access to the current tab.\n- Modified content script matching patterns to be more specific, enhancing security.\n- Refactored sound playback logic in the service worker to target the active tab directly, improving user experience during sound playback."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T10:17:59Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T10:17:59Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""45c1932e3dba4d133c93b90d2c0abed45fff96ee"",""message"":""Update store description and manifest for \""New Countdown Timer\""\n\n- Translated store description from Chinese to English, enhancing accessibility for a broader audience.\n- Updated key features and use cases sections to reflect English terminology.\n- Modified the manifest file to restrict web accessible resources to \""self\"" for improved security."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T10:06:30Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T10:06:30Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""b52dd6bde84f569c68073dc3c8f8a162268585b1"",""message"":""Refactor and enhance codebase with English translations and improved comments\n\n- Updated comments in service worker and popup components to provide English translations for better clarity.\n- Improved variable and function descriptions to enhance code readability and maintainability.\n- Adjusted UI elements in the popup and timer components to ensure consistent language usage across the application.\n- Enhanced error handling and logging for better debugging and user experience."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T09:18:56Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T09:18:56Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""00b619d228067d9311c78cd32a35c6fe5d4ee98a"",""message"":""Update project name and related documentation to reflect new branding as \""New Countdown Timer\""\n\n- Changed the project name from \""Tab Countdown Timer\"" to \""New Countdown Timer\"" in package.json, popup.html, and various documentation files.\n- Updated README, privacy policy, and store description to align with the new name.\n- Ensured consistency across all references to the timer in the codebase."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T09:04:33Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T09:04:33Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""d9501ddad44f2b66dea92b0e234ad31cf17aedc8"",""message"":""更新 .gitignore，排除 node_modules 目录"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T09:04:07Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T09:04:07Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""abd6de6c7bb5a17ba446fb7f538e3c3ffdf7f9c1"",""message"":""Enhance service worker and content script communication for sound playback\n\n- Implemented tracking of active content scripts in the service worker to manage sound playback more effectively.\n- Added error handling for message sending to ensure robustness when no active listeners are present.\n- Updated content script to confirm its loading status and respond to sound playback requests.\n- Improved audio playback logic to retry sending messages if initial attempts fail, enhancing user experience during timer completion."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T07:25:17Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T07:25:17Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""78d9bebcf78973dbad4945c517c2da79371318e2"",""message"":""Update audio files and enhance popup component layout\n\n- Replaced alarm sound file with a new version.\n- Adjusted popup component styles for better height management and overflow handling.\n- Improved CountdownView to dynamically adjust height based on content.\n- Updated TimerForm to display sound options more clearly.\n- Refactored sound path retrieval to simplify audio file management."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T07:14:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T07:14:12Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""c343f8eb1ee2831e565c4ed75b927b05f8232fd0"",""message"":""Enhance countdown timer UI and state management\n\n- Added total time and current color state to the popup component for improved countdown display.\n- Updated CountdownView to accept color and total time props, enhancing visual feedback during countdown.\n- Refactored rendering logic in the popup component for better structure and readability.\n- Removed unused cancel edit functionality to streamline the component."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T06:58:48Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T06:58:48Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""efd8cf7e710072228a3332bbabf27b73dcd26141"",""message"":""Update package dependencies and enhance countdown timer functionality\n\n- Added daisyui as a new dependency for improved UI components.\n- Refactored timer state management to use isCountingDown instead of isRunning for better clarity.\n- Updated popup and service worker components to reflect changes in timer state handling.\n- Enhanced styling and layout in various components for a more cohesive user experience."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T06:52:08Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T06:52:08Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""960dbe330949f4c80f528bab0429d144fac197f2"",""message"":""Implement routing and editing functionality in popup component\n\n- Added routing state management to handle different views (timer list and timer edit).\n- Introduced state for editing timers and creating new timers.\n- Updated the popup component to display TimerEditPage when editing or creating a timer.\n- Implemented save and cancel functionality for timer edits, enhancing user experience."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:58:53Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:58:53Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""803fddfc2b2b58a2e71649e23be58ac548ffa582"",""message"":""Enhance countdown functionality and UI integration\n\n- Added countdown status management in the service worker to track if a timer is running.\n- Updated the popup component to retrieve and display countdown status, integrating a new CountdownView for active timers.\n- Removed unused state management related to timer editing and creation to streamline the popup component.\n- Improved message handling between the service worker and popup for better synchronization of countdown states."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:56:26Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:56:26Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""b02cb2e24650915da73e50f153b2a03ec4b0668f"",""message"":""Refactor TimerForm and TimerItem components to remove name field and improve sound selection UI\n\n- Removed the name field from TimerForm and TimerItem components to simplify the timer creation process.\n- Updated sound selection in TimerForm to use buttons instead of a dropdown for better user interaction.\n- Adjusted related types to reflect the removal of the name property from CustomTimer interface.\n- Cleaned up unused code and improved overall component structure."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:46:16Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:46:16Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""ce210c4daabc1b9dd154efb5168e56460fe030b4"",""message"":""更新文档 2.1"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:37:27Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:37:27Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""8f48e56c175df3a21275910ca356c786c971a4a3"",""message"":""Refactor TimerForm component to use TimeInput for time fields and improve styling\n\n- Replaced input fields for hours, minutes, and seconds with a new TimeInput component for better encapsulation and functionality.\n- Removed redundant input change handler and adjusted increment/decrement logic.\n- Enhanced styling for labels and buttons for consistency and improved user experience."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:34:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:34:05Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""720ed167246e32af135a467786926a9782707d55"",""message"":""Update version to 1.1.0 and enhance timer functionality\n\n- Updated manifest.json to version 1.1.0 with a new description for customizable timer presets.\n- Improved service worker to retrieve and utilize current timer sound settings.\n- Refactored popup component to manage custom timers and application state more effectively.\n- Added functionality for saving, deleting, and editing custom timers.\n- Enhanced audio handling to support different notification sounds based on user selection."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:27:13Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:27:13Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""d4c4e29beef2eb5f0f978c526ecd3a40db9a3c39"",""message"":""更新 2.0 文档"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:00:53Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T05:00:53Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""162a2d0c9c44f21d14498aaf366abab2b4d43642"",""message"":""Fix parameter name in audio playback function for clarity"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T04:44:29Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T04:44:29Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""607bd31077e5a83ef114b98b2eb11acab31b5c84"",""message"":""Enhance timer and audio functionality with offscreen support\n\n- Introduced a Vite plugin to handle manifest file issues for Vite 5+.\n- Updated manifest.json to include \""offscreen\"" permission.\n- Refactored service worker to utilize offscreen document for audio playback.\n- Improved audio handling in playSound and added support for custom sound paths.\n- Enhanced icon generation logic for better text display.\n- Cleaned up and optimized timer display format in createIconText function."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T03:31:24Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T03:31:24Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""4c8475a7b90303a6c683b3f8202f534ad8904079"",""message"":""Remove unnecessary .DS_Store files from the repository"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:53:47Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:53:47Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""c0584f4191a88e0645b59196986c9965b4dd2c5c"",""message"":""Refactor timer functionality and improve audio handling\n\n- Replaced formatTime with createIconText for icon display in service worker.\n- Updated playNotificationSound to accept a sound parameter and handle errors.\n- Introduced calculateTotalSeconds and millisecondsToSeconds functions for better time management.\n- Enhanced icon generation to support OffscreenCanvas for Service Worker compatibility.\n- Cleaned up unused code and improved error handling in icon setting."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:42:30Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:42:30Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""c2b1c28f797f9630c6246192d3f1c5bdbe01cc26"",""message"":""Add playSound functionality and update manifest for content scripts\n\n- Added playSound script to Vite configuration.\n- Updated manifest.json to include tabs permission and register playSound as a content script.\n- Enhanced service worker to send play sound messages to all open tabs.\n- Updated binary icon files."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:30:09Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:30:09Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""873e71fc68f45dd17e1b05f5f428668bc3cf5854"",""message"":""Update package.json to include Tailwind CSS dependencies and set module type; remove postcss.config.js; modify vite.config.ts to integrate Tailwind CSS; update yarn.lock for new dependencies; adjust service worker message listener parameters."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:16:48Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T02:16:48Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""2e0496dc38f6b57925a8c7f33248d5496561bfc3"",""message"":""init"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T01:58:08Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-28T01:58:08Z""},""repository"":""tower1229/countdown-chrome""},{""sha"":""9a020d38a0399cce98d4b8e012fad057e94996ed"",""message"":""docs(2024): add Zustand and PrimeVue sections to phase-6.md\n\n- Introduce Zustand as a small, fast, and scalable state management solution\n- Include a brief description and link to the Zustand demo\n- Add PrimeVue as a popular Vue UI component library with a 25-year history\n- Provide a brief description and link to the PrimeVue website"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T02:07:55Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-30T02:07:55Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""4cef7f5c5d0f3ddf19fff28b2906525a2dc257b3"",""message"":""docs(2024): add Framer section to phase-6.md\n\n- Introduce Framer as a website builder favored by designers\n- Include a brief description and link to the Framer website"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T05:50:18Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T05:50:18Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""df14f9277a039415c3733d5568cd3cdbb97cf086"",""message"":""docs(2024): add Expo-ReactNative framework section to phase-6.md\n\n- Introduce Expo as a framework that lowers the barrier for ReactNative development\n- Include a brief description and link to the Expo documentation"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T01:22:34Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T01:22:34Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""ff1fd172a33167023721b0ba6262ce01c447b73f"",""message"":""docs(2024): add new section and resource to phase-6.md\n\n- Introduce Anime.js as a lightweight JavaScript animation engine\n- Include a brief description and link to the Anime.js documentation\n- Update navigation to include link to phase-6"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T01:16:58Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-04-11T01:16:58Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""eb32712771d5c6cd9ade3dd7daa5b2e940584376"",""message"":""docs(2024): update phase-6.md with new content and image\n\n- Replace cover image with a new one for the 2024-6 issue\n- Add a new section featuring a GitHub Card generator tool\n- Include a brief description of the tool's functionality"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-03T10:08:30Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2025-01-03T10:08:30Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""f72c95ced4f996cefdb62be92520765460b1f2e0"",""message"":""docs(2024): add Markdown conversion tool to phase-6.md\n\n- Introduce MarkItDown, a utility for converting various file formats to Markdown\n- Provide a link to the online version for easy access and usage"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-12-31T02:07:53Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-12-31T02:07:53Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""0501930d7ea47847029b82ba69685dbd75d99734"",""message"":""docs: update for 2024 Phase 5 release\n\n- Add new phase to documentation\n- Update cover image for Phase 5\n- Include new sections on modern CSS and typography\n- Add links to relevant resources"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-11-26T02:08:56Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-11-26T02:08:56Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""b3b40c45b4063064383d08f21a6251d2ea584bf6"",""message"":""docs(2024): update phase-5.md with new frontend resources\n\n- Add 2024 State of Frontend report link\n- Include date-fns library for date handling\n- Add Modern CSS One-Line Upgrades article"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-11-25T04:33:04Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-11-25T04:33:04Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""40e257ed62eff8d61c9b6813a450ae3a2f2f41b5"",""message"":""docs"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-27T06:33:51Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-27T06:33:51Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""45c5d15c35c8819387d8bb641006b3e2c3c72b4d"",""message"":""fix(public): replace missing daily Bing image with placeholder"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-20T02:02:18Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-20T02:02:18Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""0b5fbd5cb447563557dac491a7179299f4bf6e51"",""message"":""docs: add 2024/phase-4 to nav and create phase-4.md with View Transitions API intro"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-20T02:00:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-20T02:00:12Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""7a16429f5aecd2cc450294ae5f9f39ab2221a05c"",""message"":""docs: add View Transitions API article in phase-4.md\n\nIntroduce the View Transitions API in the phase-4.md document, which provides\ndetails about achieving visual animations similar to Keynote's magic move. This\nupdate includes information on how the API can automatically identify differences\nbetween scenes and generate animations suitable for complex page-level transitions."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-08-14T03:05:17Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-08-14T03:05:17Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""aadefee46200229f3672f5726119cc91d5366124"",""message"":""fix: link"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-05-08T07:35:10Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-05-08T07:35:10Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""04c511ddcf29f45737e6951cab9d1d07a43fa3af"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-19T07:04:23Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-19T07:04:23Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""ff2466ed2ff44bbfcbdcad54bce350812a084e24"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-18T07:07:42Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-18T07:07:42Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""4720f5f2ac351662d6fdc0bbd1c3c8fb9c13bf47"",""message"":""fix: baidu"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-10T05:44:41Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-10T05:44:41Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""5a608c1c0648758db10120e935c2efad7ef4a3a8"",""message"":""feat: add baidu"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-10T05:35:51Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-10T05:35:51Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""fce8ce7e7131674bbf6cb22169e32c8af28b3f44"",""message"":""build: update img"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T02:18:10Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T02:18:10Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""0f5a3eab393e8be8ba3cd3850b4c5b7ce234e17a"",""message"":""build: sitemap"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T02:05:54Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T02:05:54Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""f13c9f4d46ae4083e129af2f7ebd7dc339991512"",""message"":""build: update public"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:54:15Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:54:15Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""a3c665a0b8ebdef81309436515c81705da47701c"",""message"":""build: action"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:39:54Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:39:54Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""bf9f07990a3e7d22136dca74cd68cd881002faab"",""message"":""Merge pull request #10 from tower1229/dev-vitepress\n\nDev vitepress"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:35:52Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:35:52Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""3e9f415b7ab0bcfe96af4cbd4010c7cb0693fa7a"",""message"":""build: fix"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:34:55Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:34:55Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""f447fbc2d0a5dc1b2bf6f6ed825c117533b24d56"",""message"":""build: vitepress"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:27:24Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-09T01:27:24Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""9c998fd13fcf1c54720037c9948e9bde4c4373b4"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-08T07:17:21Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-04-08T07:17:21Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""54e0c251688f168cb29cf50fc76fea36810e4578"",""message"":""Merge branch 'master' of github.com:tower1229/frontend-weekly"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-03-29T02:02:31Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-03-29T02:02:31Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""309dc2eb07ebba816281a3c1154f083841b20c08"",""message"":""docs: update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-03-29T02:01:04Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-03-29T02:01:04Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""a8d3d94bc1e10c847c0bb7023ce61ecd76bca4fe"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-20T06:03:29Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-20T06:03:29Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""a0339661707e59c5c02693a7ca57f1be600295f3"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-20T04:24:14Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-20T04:24:14Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""4b95370cf03388d8e7817283ecf6e846618beea3"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-18T04:32:22Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-18T04:32:22Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""c2ffed6284bbea57bbddcb7aa85a4548389e2b24"",""message"":""feat: add 2024-2"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-18T01:14:12Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-18T01:14:12Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""c8c8adb0b51ee80ac84713fb0c60e79b95b2d921"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-11T05:36:40Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-11T05:36:40Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""81ce9592747981f7f2a498ddb0a5f3bc34d932c3"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-08T00:47:58Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-03-08T00:47:58Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""5b0d771235d013f071032f10efba0e1a00bbdfdc"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-27T01:00:55Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-27T01:00:55Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""37c506f0fbc775e7f78f56c636dd35d5bfd57f4c"",""message"":""docs: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-26T07:02:59Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-26T07:02:59Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""cedbd863f010ef0dc219b5ab1a06296e24f01be1"",""message"":""feat: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-22T06:50:08Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-22T06:50:08Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""2197c9dcb97d5b9424fd73843b9b78e6681d306e"",""message"":""Merge branch 'master' of github.com:tower1229/frontend-weekly"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-01-11T00:49:04Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-01-11T00:49:04Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""00ae7f187f655742a232d2f8929e63498eb10701"",""message"":""feat: add 2024-1"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-01-11T00:48:41Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-01-11T00:48:41Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""236f87b51f5703dbef852185f241ea121cd3f530"",""message"":""feat: update"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T01:36:29Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T01:36:29Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""57e423281b86960d74009a1a0d18ae9c4d117ef0"",""message"":""fix: fix image url"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-19T11:07:38Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-19T11:07:38Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""c44a88918989bf056f5b88a8f43566df8cca45b6"",""message"":""feat: support search"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-19T10:47:23Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-19T10:47:23Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""2cf8bde4692bb3ab6f9bb20b24d2db16fe940f15"",""message"":""build: ignre"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T05:15:17Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T05:15:17Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""6139c43e346b552cd3f38b54b71e4d341a74a567"",""message"":""build: remove dist"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T05:14:29Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T05:14:29Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""b75b17bbb62d889ec349cb9f70a2cbfad02f1678"",""message"":""build: ignore"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T05:14:22Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T05:14:22Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""0ef65fbb52396b0b6f4156793d2fdf95bdfbe10f"",""message"":""fix: readme"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T04:54:15Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T04:54:15Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""dadf68cb1f47a3460bbdbffe2f735eb219bc3da1"",""message"":""build: update target branch"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T04:49:03Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T04:49:03Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""8743760cd2f99bdb1e43ba35d01952f185964a44"",""message"":""build"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T04:44:13Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T04:44:13Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""717bc4e9603b997e19d496f8718794a2a666e305"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T01:42:46Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T01:42:46Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""6bb95e59b3b32a1474b3832b2f1234cf6fb602b1"",""message"":""build: update to vuepress"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T01:21:38Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-10-18T01:21:38Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""9ef7a5405e0f91230ff6b0987b62d5b9ae7c047e"",""message"":""feat: new"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-08-12T01:06:53Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2023-08-12T01:06:53Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""fca04c76d940b007c0f506bfd3a2a9c81696509f"",""message"":""2023-3"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-04-18T03:52:02Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-04-18T03:52:02Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""e0f6b54d9be6ba4197bc8f2babdf2183f142a0af"",""message"":""2023-2"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-03-04T03:08:29Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-03-04T03:08:29Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""0b4e617ae665ad125cfaca9300b7a90059bd163e"",""message"":""update ad link"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-01-02T09:15:54Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-01-02T09:15:54Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""f5a0dc22f07bbebc4041a31826e27d40ae17125b"",""message"":""20230102"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-01-02T09:02:23Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-01-02T09:02:23Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""764104877ca4409fa51b577fce77ccf9f2bf89e8"",""message"":""备案号"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:42:52Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:42:52Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""6e8ef5a26f2bbeb3f35139f7084e3a4bb2f30569"",""message"":""备案号"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:39:39Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:39:39Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""4cb08b9d8c6080fb1535cd4e8d6281deff6731c9"",""message"":""不给俺"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:38:29Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-11-28T06:38:29Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""2f10a137dfba1c3f21d0285efe8c40c18dfb22fb"",""message"":""update link"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-27T09:26:01Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-27T09:26:01Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""c8fbaa3c37dd75a140040abddaa8a0dc6be7d098"",""message"":""update ink"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-27T09:25:01Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-27T09:25:01Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""6c8e12eefe600dd77e2295d47dd1275c8b760330"",""message"":""2022-14"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-08T06:56:09Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-10-08T06:56:09Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""41d900bffeb94658f33c400fd03241352b1c7d27"",""message"":""update link"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-08-17T02:39:44Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-08-17T02:39:44Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""7020c3475a3024f10d049a7fb42edb641d68f76a"",""message"":""2022-13"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-08-17T02:30:42Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-08-17T02:30:42Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""607dade51632cf80beca03d0059257feae8cd34d"",""message"":""update 12"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-18T02:40:34Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-18T02:40:34Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""aeb53b1462e8d3e0b6d4780f9f283fa38f20974b"",""message"":""12"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-18T01:30:19Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-18T01:30:19Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""49ecf4f1e2a732ca23710d0dec604fc5fc98254a"",""message"":""bing image"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-22T06:52:09Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-22T06:52:09Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""47d13a8317e00dc4b73278cd5d4a2b9ca78cdf32"",""message"":""2022-11"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-22T06:48:44Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-22T06:48:44Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""9ccb96210697f571b90b749d5101c76f89028016"",""message"":""publish"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-07T01:50:55Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-06-07T01:50:55Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""53ac0f98f5ae440d52564d104379271f78f512ef"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-30T01:02:20Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-30T01:02:20Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""117d160ddb67dad539df5b742cba7c2996ff5649"",""message"":""第 8 期"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-24T02:06:04Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-24T02:06:04Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""cce206f1e490e367dfa8151638cc4cc6d7ea6a04"",""message"":""7"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-16T10:59:47Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-16T10:59:47Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""242f087b59dea1e4d8fa4f0e51e313b49cc132d6"",""message"":""min"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T01:05:42Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T01:05:42Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""c77eed2335ebb547e3d7e3929273012f1a2c6263"",""message"":""update index"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T01:01:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T01:01:12Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""fdf922fd1614ec0b8a9a2ccf704d0903abd131b0"",""message"":""update 6"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T00:54:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-05-09T00:54:12Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""363cefb8f7270d547da010414b1fe5e45394787e"",""message"":""update ad"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-25T01:45:19Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-25T01:45:19Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""65863aa5fa927425c0ccebde15323559563ec747"",""message"":""2022-05"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-25T01:28:33Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-25T01:28:33Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""8c8b4dd8370f5415afe899f902f08f1747927f16"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-12T01:41:32Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-12T01:41:32Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""54bdbbdf9426d4637fc9bf8eefbc3395824b0288"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-11T04:25:47Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-11T04:25:47Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""6d587241f504e4cea1f2ff5c03da2c5412ebe77c"",""message"":""update package"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-11T03:54:21Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-11T03:54:21Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""dc7745838d1d2a36bfd24b4b976001088b5f464a"",""message"":""Merge pull request #6 from tower1229/dependabot/npm_and_yarn/minimist-1.2.6\n\nBump minimist from 1.2.5 to 1.2.6"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2022-04-11T03:52:47Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2022-04-11T03:52:47Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""29f38cd081b4867dec72c7109706ca99fe300f4e"",""message"":""updte"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-11T03:51:39Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-04-11T03:51:39Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""626b6a8319e40f938e3553f1eef24571dbc73beb"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-03-07T08:42:36Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-03-07T08:42:36Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""3b348f3164871c4755a6d0a7f88a364ae7025d6d"",""message"":""更新广告"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-21T08:21:33Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-21T08:21:33Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""d123b1e5c30009d7e6e8c7801046004f9c75475f"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-07T01:14:05Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-02-07T01:14:05Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""f88ef10fe05aba29789d5f795386f17d4a8407fe"",""message"":""存一下啊"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-28T04:02:21Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-28T04:02:21Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""0e1b6b37d7fe834a050b683a39c7d9084fa6f736"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T06:40:32Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T06:40:32Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""2da2c27abb3fe6e0f62343734a368030a1c66377"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T06:38:01Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T06:38:01Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""103fb99ea0433a4e74bce2167580907cc199d7f5"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T06:34:54Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T06:34:54Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""ccafba2ad444ad3459f76cff4ca970f847819e90"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T02:46:33Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-01-21T02:46:33Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""04a55b5301800bac557a2317e2062f6ebeca71d1"",""message"":""Merge branch 'master' of github.com:tower1229/frontend-weekly"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-23T03:54:47Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-23T03:54:47Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""4ada7a7dbaf93da18da65181d20f2a0ef5b92086"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-23T03:54:41Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-23T03:54:41Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""0986464709b3c70846133698758081723fe03f77"",""message"":""Create CNAME"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2021-12-22T03:05:51Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2021-12-22T03:05:51Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""2d0e85084d33d8c826b3cda888c6eb06d49855a2"",""message"":""Delete CNAME"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2021-12-22T03:04:25Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2021-12-22T03:04:25Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""742741aa0011e1261ea3db846c1ddfcfcb91f53a"",""message"":""update travis"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T06:36:56Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T06:36:56Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""a96d3c2cd278e946697ed98a7f0bd1a9327d709f"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T06:29:37Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-21T06:29:37Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""24b0c9b5b72be537f5c3b08aa8e69f8266a47ee6"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T09:44:42Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T09:44:42Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""fdbbdc83371050f072ac93b30189a3eab1cd980d"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T09:39:50Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-12-17T09:39:50Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""d184e5eef7fc4083859333d3075c76611e6320ca"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-22T07:55:20Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-22T07:55:20Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""61e3df1dea25b1a6a112fef380190f95906fbc91"",""message"":""1111"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-02T02:32:30Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-02T02:32:30Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""321d803ba270a45940a8cc0610cb4b55efcc403d"",""message"":""1111"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-02T02:29:07Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-11-02T02:29:07Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""77556217ae97894914aa54b3c30c016636dc2839"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-09-17T08:36:14Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-09-17T08:36:14Z""},""repository"":""tower1229/frontend-weekly""},{""sha"":""d54565f64ae13a7c0f4d5c401070efdf6b72af64"",""message"":""Merge pull request #1 from tower1229/dependabot/npm_and_yarn/next-14.2.10\n\nBump next from 14.2.5 to 14.2.10"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-09-24T06:53:35Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2024-09-24T06:53:35Z""},""repository"":""tower1229/nextjs-todolist""},{""sha"":""fe5a03e4d6296703e6c6b90273cdf574e65d1bae"",""message"":""init"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-24T06:46:52Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-09-24T06:46:52Z""},""repository"":""tower1229/nextjs-todolist""},{""sha"":""6c2eed30199cc332922fea32c6efb8467f60b119"",""message"":""Initial commit"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-09-20T07:22:03Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2024-09-20T07:22:03Z""},""repository"":""tower1229/nextjs-todolist""},{""sha"":""93a6a2cc27ed76af4cb4a59574e11961a4852af6"",""message"":""chore: update project config and dependencies\n\n- Remove husky scripts from .husky directory\n- Update site config description in constant/config.ts\n- Downgrade Next.js, React-Icons, and development dependencies\n- Remove unused husky and related dependencies from package.json"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-08-14T04:00:45Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-08-14T04:00:45Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""598ed8bc792c92efa6306482e303bd4111ae7c2f"",""message"":""refactor: rename CreateStream to NewStream\n\nThe function CreateStream was renamed to NewStream to align with the\nproject's naming conventions. This change was made to ensure consistency\nthroughout the codebase and to enhance code readability."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-08-14T03:35:49Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-08-14T03:35:49Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""ac6dff2bf7d3b9fac2884327d3ff66b97ff92dd8"",""message"":""Merge pull request #1 from tower1229/dev\n\nDev"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:18:16Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:18:16Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""9981511c36d77ca3a7279af508b0eb1e06237105"",""message"":""chore: ad page"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:17:45Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:17:45Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""b086925191f89ddc44135108ab2e28aea7bf94a7"",""message"":""chore: add page"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:17:25Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:17:25Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""0e936c7fb65e456af4bcbfd684d9a11592f60180"",""message"":""fix: commit"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:04:17Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:04:17Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""dcdb250909d8ddff4f815ccba48f9efeee5748d0"",""message"":""feat: init"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:03:52Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T07:03:52Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""ef80e5cf81ae9228cdbcfbd8d428cbc8f906356a"",""message"":""Initial commit"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2024-02-23T02:59:12Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2024-02-23T02:59:12Z""},""repository"":""tower1229/nextjs-daisyui-starter""},{""sha"":""969c91fa83251ebf827daa0a09f36fa8bba06488"",""message"":""feat(fish-pond): add fish pond page to root router\n\nAdd a new FishPond component to the src/pages directory and include its route\nin the root router configuration. This change allows users to navigate to the\nFishPond page from the application's main menu."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-07-26T12:04:53Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-07-26T12:04:53Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""6760639bd39670701a89e0fbc929baa675b16d5f"",""message"":""feat/game-page: implement Pixi.js bunny animation\n\nReplace the existing game components with a new Pixi.js animation featuring a\nbunny sprite. The bunny rotates continuously on the screen as a proof-of-concept\nfor integrating Pixi.js animations into the game page.\n\nRemoved unused hooks, context providers, and configuration files related to the\nprevious game implementation. Also, cleaned up the tailwind.config.js to remove\ncustom colors and themes that were no longer required.\n\nThe package.json has been updated to include newer versions of dependencies and\nremove unused ones such as wagmi and viem.\n\nBREAKING CHANGE: The game page now uses Pixi.js instead of React for rendering\nanimations. This change will require updates to any components that relied on the\nprevious implementation."",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-07-26T11:57:00Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-07-26T11:57:00Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""835f8b9dc930025c91ae6d0c296bc9707db9c3a5"",""message"":""feat: add axios"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-13T01:49:19Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-13T01:49:19Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""25f9498436019b96ca20fbfd6bcfbd6b190fb14f"",""message"":""chore: update route"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-13T01:31:52Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-13T01:31:52Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""7ae25e3a4ab9e4861ec2f07445b3c90698b03631"",""message"":""feat: evm login"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-13T01:27:09Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-13T01:27:09Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""09d813b17854c9fa2351a3f01476e0c46786f81b"",""message"":""chore: todo"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-12T07:45:36Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-12T07:45:36Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""d9d5d5a7c9cb54505b28919a4cc029138e4d012e"",""message"":""feat: game over ui"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-12T03:21:55Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-12T03:21:55Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""53860152f451ba42c582723081c9a6ad5f158d73"",""message"":""feat: update map"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-12T01:28:57Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-12T01:28:57Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""6abf977211f0ebcf6e17f13cf9d74f5bb203177c"",""message"":""feat: component"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-11T07:34:35Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-11T07:34:35Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""e88a8039e0e6119ab9fa9d9a495c112d73388af4"",""message"":""feat: login"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-11T07:28:59Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-11T07:28:59Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""0bd2cba63f1274a8f53b6a8b9eb9f6eee7e55201"",""message"":""chore: add todo"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T07:45:20Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T07:45:20Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""688744830bfe7838400b8023aaefe6504dd36d83"",""message"":""feat: path record"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T06:56:39Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T06:56:39Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""a42ab245921721ad48a5b3358a69a583e9a89e6f"",""message"":""feat: add safeMove()"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T06:23:39Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T06:23:39Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""aed857e45e988f27aabf537f0ec6035495ecca2e"",""message"":""chore: update spirit"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T02:40:22Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-08T02:40:22Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""b99531e59d5a99338e3677bfcd0e10844fcbd9bc"",""message"":""chore: add gameloop"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T07:47:09Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T07:47:09Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""651b00fcf1cb15f536319f4018a995a4b0d51a25"",""message"":""chore: remove react-use"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T07:21:57Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T07:21:57Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""015b243d53228e71930e8d7cf148e72a6025a345"",""message"":""feat: base"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T07:19:40Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T07:19:40Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""53ba5b7a72824cd3fe34ce8b2d49efd9487126ef"",""message"":""build: init"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T01:22:36Z""},""committer"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2023-12-07T01:22:36Z""},""repository"":""tower1229/pixi-playground""},{""sha"":""cc09e32fc1a4addf754e9a299f588ecd18488aae"",""message"":""chore: add tea"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-03-08T10:39:12Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2024-03-08T10:39:12Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""2785d7b98ca9e0a232e49d4333c667b0bd35f8d5"",""message"":""外置jQuery"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-15T02:58:51Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-15T02:58:51Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""dfd006edb4e4d57e765759c203ae5d79397fc4ce"",""message"":""add vue3 link"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-15T02:04:14Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2022-07-15T02:04:14Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""32565420f04f72fd7d2d042d53c575a92fcf2726"",""message"":""https://github.com/tower1229/Vue-Giant-Tree/issues/28"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-05-20T02:40:27Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-05-20T02:40:27Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""a787652b4eb3892bbb114b7f17262d86d73d8e66"",""message"":""docs:\n\ndist/demo可运行"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-01-12T06:14:35Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2021-01-12T06:14:35Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""cf0e2b5bb61f53bdc62a130986d743778e7dff40"",""message"":""📃docs:\n修改错别字，完善事件示例"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-17T02:36:50Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-17T02:36:50Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""61fc2fbafa359179eea0ede33ce2be7f81b78273"",""message"":""✨feat:\n0.1.4"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:28:45Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:28:45Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""363fe911008cdb96207973d7002d9b54b85797dc"",""message"":""🐞fix:\n拖拽样式问题"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:26:15Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:26:15Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""f7becc4dfd2a3428128271388b5bb0de6eed85ee"",""message"":""🐞fix:\n拖拽样式问题"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:25:22Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:25:22Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""88a2ffdf459ce7e4e44f5d7e5cb1d69028576a67"",""message"":""🔧build:\nignore package-lock.json"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:13:33Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:13:33Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""62bfc0bff742c408c9806bf3854d614f77c750ac"",""message"":""🔧build:\nignore package-lock.json"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:13:06Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2020-09-16T03:13:06Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""92c1210325469d3bbb784fb59d3abbd90110d51c"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-10-16T04:09:04Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-10-16T04:09:04Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""28110c37f645c1f54aada6eb48f7bc137d39ef90"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-09-03T09:23:53Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-09-03T09:23:53Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""5b4e9c233579d758cf3123c1743749ba56224277"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-09-03T09:16:28Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-09-03T09:16:28Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""e837e5ab1aaf9367b55ca189ab21a4e4540e418a"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-20T02:25:40Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-20T02:25:40Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""a3bd384c4292ef30cc213b0a2a3faee242aea5d8"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-20T02:06:18Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-20T02:06:18Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""af55f8893a13375db3465f1cd905860abe52552e"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-09T01:46:21Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-09T01:46:21Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""2c3a730eddcf3f8805e122ce751304ba2a487fa4"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-08T07:27:02Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-08T07:27:02Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""2c654f93d5c9e7aaa0a1c3f8a15088e3774b5d81"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T09:25:47Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T09:25:47Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""757cf58d7a6e901d9d61dec4f3968e1a202918be"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T09:06:22Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T09:06:22Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""9e87a6ae780080103142678989f0e370e9587b12"",""message"":""update"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T09:03:24Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T09:03:24Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""3a3db3f5d2cfd2a285921c733041f0d1413d3baf"",""message"":""Merge branch 'master' of github.com:tower1229/Vue-Giant-Tree"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T07:46:01Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T07:46:01Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""d3745b28eac950dfe21425f1bb52e3f5e19aba77"",""message"":""Initial commit"",""author"":{""name"":""refined-x"",""email"":""<EMAIL>"",""date"":""2019-08-07T07:43:53Z""},""committer"":{""name"":""GitHub"",""email"":""<EMAIL>"",""date"":""2019-08-07T07:43:53Z""},""repository"":""tower1229/Vue-Giant-Tree""},{""sha"":""33fe7183dd26a4111e5077793807e4f391f7f2b4"",""message"":""init"",""author"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T02:05:44Z""},""committer"":{""name"":""tower1229"",""email"":""<EMAIL>"",""date"":""2019-08-07T02:05:44Z""},""repository"":""tower1229/Vue-Giant-Tree""}]"	"[{""repository"":""tower1229/Vue-Access-Control"",""name"":""README.md"",""path"":""README.md"",""content"":""RW5nbGlzaCB8IFvkuK3mloddKFJFQURNRV9DTi5tZCkKCiMgdnVlLWFjY2Vz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\neC5jb20pCgo=\n"",""size"":2303,""encoding"":""base64"",""sha"":""25aace4cb35338b3df8fc9ed3433dcff3d851e23"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/Vue-Access-Control/master/README.md""},{""repository"":""tower1229/Vue-Giant-Tree"",""name"":""README.md"",""path"":""README.md"",""content"":""IyB2dWUtZ2lhbnQtdHJlZQoKWyFbbnBtXShodHRwczovL2ltZy5zaGllbGRz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\nq6/ot6/kuIpdKGh0dHA6Ly9yZWZpbmVkLXguY29tKQo=\n"",""size"":6062,""encoding"":""base64"",""sha"":""dcebcc30424bf61168bb413fb943355e7198f3fa"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/Vue-Giant-Tree/master/README.md""},{""repository"":""tower1229/Vue-Tree-Chart"",""name"":""README.md"",""path"":""README.md"",""content"":""RW5nbGlzaCB8IFvkuK3mloddKFJFQURNRV9DTi5tZCkKCiMgdnVlLXRyZWUt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\nZC14LmNvbSkK\n"",""size"":2259,""encoding"":""base64"",""sha"":""7a6d32a10ac3435936795c72ae55b7f6df0ff0e3"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/Vue-Tree-Chart/master/README.md""},{""repository"":""tower1229/WidgetsPlayground"",""name"":""README.md"",""path"":""README.md"",""content"":""IyBXaWRnZXRzUGxheWdyb3VuZAoKWyFbY29tcGF0aWJpbGl0eV0oaHR0cHM6\nLy9pbWcuc2hpZWxkcy5pby9iYWRnZS9jb21wYXRpYmlsaXR5LU1pY3Jvc29m\ndEVkZ2UlMkItb3JhbmdlLnN2ZyldKCkgWyFbR2l0SHViIHJlbGVhc2VdKGh0\ndHBzOi8vaW1nLnNoaWVsZHMuaW8vZ2l0aHViL3JlbGVhc2UvdG93ZXIxMjI5\nL1dpZGdldHNQbGF5Z3JvdW5kLnN2ZyldKCkgWyFbbGljZW5zZV0oaHR0cHM6\nLy9pbWcuc2hpZWxkcy5pby9naXRodWIvbGljZW5zZS90b3dlcjEyMjkvV2lk\nZ2V0c1BsYXlncm91bmQuc3ZnKV0oKQoKIyMg5LuL57uNCgrliY3nq6/nu4Tk\nu7bnrqHnkIbns7vnu5/vvIzliY3nq6/ln7rkuo5WdWUyL1Z1ZS1yb3V0ZXIv\nVnVleOWunueOsO+8jOeVjOmdouWfuuS6jltGbG93LVVJXShodHRwOi8vZmxv\ndy11aS5yZWZpbmVkLXguY29tLynlrp7njrDvvIzlkI7nq6/ln7rkuo7ph47n\ni5fkupHlrp7njrDjgIIKCumZpOS6hue7hOS7tueuoeeQhuS7peWklu+8jOac\nrOmhueebruWQjOaXtua8lOekuuS6huS4gOenjeS4jeS+nei1luaehOW7uuW3\npeWFt+W8gOWPkVZ1ZemhueebrueahOaAnei3r++8jOivpue7huS7i+e7jeWP\nguingVvlpoLkvZXkuI3nlKjmnoTlu7rlt6XlhbflvIDlj5FWdWXlhajlrrbm\nobbpobnnm65dKGh0dHBzOi8vcmVmaW5lZC14LmNvbS8yMDE3LzEwLzI4LyVF\nNSVBNiU4MiVFNCVCRCU5NSVFNCVCOCU4RCVFNyU5NCVBOCVFNiU5RSU4NCVF\nNSVCQiVCQSVFNSVCNyVBNSVFNSU4NSVCNyVFNSVCQyU4MCVFNSU4RiU5MVZ1\nZSVFNSU4NSVBOCVFNSVBRSVCNiVFNiVBMSVCNiVFOSVBMSVCOSVFNyU5QiVB\nRS8pCgojIyDlip/og70KCiMjIyDnu4Tku7bnrqHnkIYKLSDkuoznuqfliIbn\nsbsKLSDmoIfnrb7nrZvpgIkKLSDml7bpl7Qv5ZCN56ew5o6S5bqPCi0g5YWz\n6ZSu6K+N5pCc57SiCgojIyMg57uE5Lu25ryU56S6Ci0g5a6e5pe257yW6L6R\nCi0g5aSa57uE5Lu257uE5ZCICi0g5omA6KeB5Y2z5omA5b6XCgojIyMg57uE\n5Lu25bqU55SoCi0gSFRNTC9DU1MvSlPku6PnoIHkuIDplK7lpI3liLYKLSDo\nv5jlj6/ku6XlsIbnvJbovpHnu5PmnpznlJ/miJDphY3nva7ku6PnoIHvvIzl\nrp7njrDnvJbovpHnu5PmnpzkuIDplK7lho3njrAKCiMjIyDnlKjmiLfnrqHn\nkIYKLSDmjqXlhaXph47ni5fkupHlkI7nq6/vvIzlrp7njrDnlKjmiLfnrqHn\nkIYKLSDnlKjmiLfkvb/nlKjnl5Xov7nmlbDmja7nu5/orqEKCiMjIOmFjee9\nrgotIOmhueebruebtOaOpeS4oui/m+acjeWKoeWZqOeOr+Wig+WwseWPr+S7\npei/kOihjAotIOi/kOihjOWJje+8jOWFiOS/ruaUuWBpbmRleC5odG1sYOW6\nlemDqOiEmuacrOS4reeahGBzZWFqcy5yb290YOS4umAiImDvvIjku6Xpobnn\nm67miYDlnKjnmoTmnI3liqHot6/lvoTkuLrlh4bvvIkKLSBgc2VhanMud2lk\nZ2V0Um9vdFBhdGhg5Y+Y6YeP5piv5ryU56S657uE5Lu25bqT77yIYC93aWRn\nZXRzYO+8ieeahOS9jee9ru+8jOmAmuW4uOS4jemcgOimgeS/ruaUuQotIGBz\nZWFqcy5jb25maWcuYmFzZWDmmK9GbG93LVVJ5qih5Z2X5bqT55qE5Zyw5Z2A\n77yM6YCa5bi45LiN6ZyA6KaB5L+u5pS5CgojIyDmvJTnpLogCmh0dHA6Ly9y\nZWZpbmVkLXguY29tL1dpZGdldHNQbGF5Z3JvdW5kLwo=\n"",""size"":1607,""encoding"":""base64"",""sha"":""719b32dcb60205f349e1305cb4758bb673bec83b"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/WidgetsPlayground/master/README.md""},{""repository"":""tower1229/HybridStart"",""name"":""README.md"",""path"":""README.md"",""content"":""CiMgSHlicmlkU3RhcnQKCuWfuuS6jlthcGljbG91ZF0oaHR0cDovL3d3dy5h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\nPGJyIC8+PGJyIC8+Cg==\n"",""size"":2173,""encoding"":""base64"",""sha"":""02235c5d3eabe9093db430965ba32448141d48a6"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/HybridStart/master/README.md""},{""repository"":""tower1229/weapp-star"",""name"":""README.md"",""path"":""README.md"",""content"":""IyB3ZWFwcC1zdGFyCuW+ruS/oeWwj+eoi+W6j+S4iuaJi+mhueebri3mmJ/l\nuqfphY3lr7kKCiMjIOabtOWkmgoKPiBb5YmN56uv6Lev5LiKXShodHRwOi8v\ncmVmaW5lZC14LmNvbSkKCjxiciAvPjxiciAvPg==\n"",""size"":118,""encoding"":""base64"",""sha"":""d288a79f5088407635e82f5454f1ec80380f748e"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/weapp-star/master/README.md""},{""repository"":""tower1229/weapp-plugin-dashboard"",""name"":""README.md"",""path"":""README.md"",""content"":""IyB3ZWFwcC1wbHVnaW4tZGFzaGJvYXJkCgpbIVtucG1dKGh0dHBzOi8vaW1n\nLnNoaWVsZHMuaW8vbnBtL3Yvd2VhcHAtcGx1Z2luLWRhc2hib2FyZC5zdmcp\nXShodHRwczovL3d3dy5ucG1qcy5jb20vcGFja2FnZS93ZWFwcC1wbHVnaW4t\nZGFzaGJvYXJkLykgIFshW2xpY2Vuc2VdKGh0dHBzOi8vaW1nLnNoaWVsZHMu\naW8vZ2l0aHViL2xpY2Vuc2UvdG93ZXIxMjI5L3dlYXBwLXBsdWdpbi1kYXNo\nYm9hcmQuc3ZnKV0oKQoKPiDlvq7kv6HlsI/nqIvluo/ku6rooajnm5jnu4Tk\nu7YKCiFbXShodHRwczovL3JlZmluZWQteC5jb20vYXNzZXQvYS93ZWFwcC1w\nbHVnaW4tZGFzaGJvYXJkLmdpZikKCgojIyDlronoo4XkuI7kvb/nlKgKCjEu\nIOWcqOWwj+eoi+W6j+agueebruW9le+8iHByb2plY3QuY29uZmlnLmpzb27k\nuK1gbWluaXByb2dyYW1Sb290YOmFjee9rueahOebruW9le+8ieS4reS+neas\noeaJp+ihjApgYGAKbnBtIGluaXQKbnBtIGkgd2VhcHAtcGx1Z2luLWRhc2hi\nb2FyZCAtUyAtLXByb2R1Y3Rpb24KYGBgCjIuIOW+ruS/oeW8gOWPkeiAheW3\npeWFt++8jOmhueebrumFjee9ruW8gOWQryoq5L2/55SobnBt5qih5Z2XKirv\nvIzlubbmiafooYzigJzlt6Xlhbct5p6E5bu6bnBt4oCdCjMuIOWcqOWwj+eo\ni+W6j+mhtemdompzb27mlofku7bkuK3phY3nva4KYGBgCiJ1c2luZ0NvbXBv\nbmVudHMiOiB7CiAgICAid2VhcHAtcGx1Z2luLWRhc2hib2FyZCI6ICJ3ZWFw\ncC1wbHVnaW4tZGFzaGJvYXJkIgp9CmBgYAo0LiDlnKjlsI/nqIvluo/pobXp\nnaLkuK3kvb/nlKjnu4Tku7YKYGBgCjx3ZWFwcC1wbHVnaW4tZGFzaGJvYXJk\nIC8+CmBgYAoKIyMg6YWN572u5Y+C5pWwCgrlrozmlbTnmoTphY3nva7pobnl\nj4rpu5jorqTlgLzlpoLkuIvvvJoKCmBgYAo8d2VhcHAtcGx1Z2luLWRhc2hi\nb2FyZCAKICAgIG1pbj0iMCIgICAgICAgICAgICAgICAgIC8vIOacgOWwj+WA\nvAogICAgbWF4PSIxMDAiICAgICAgICAgICAgICAgLy8g5pyA5aSn5YC8CiAg\nICB2YWw9IjUwIiAgICAgICAgICAgICAgICAvLyDlvZPliY3lgLwKICAgIHdp\nZHRoPSI3NTAiICAgICAgICAgICAgIC8vIOe7hOS7tuWuveW6pu+8jOWNleS9\njXJweAogICAgaGVpZ2h0PSI0MDAiICAgICAgICAgICAgLy8g57uE5Lu26auY\n5bqm77yM5Y2V5L2NcnB4CiAgICBjb2xvcnM9Int7bXlDb2xvcnN9fSIgICAv\nLyDku6rooajnm5jpopzoibLliIbluIMKICAgID4KPC93ZWFwcC1wbHVnaW4t\nZGFzaGJvYXJkPgpgYGAKCmBgYAouLi4KZGF0YTogewogICAgbXlDb2xvcnM6\nIFt7CiAgICAgICAgcGVyY2VudDogNTAsCiAgICAgICAgY29sb3I6ICcjNjdD\nMjNBJwogICAgfSwgewogICAgICAgIHBlcmNlbnQ6IDgwLAogICAgICAgIGNv\nbG9yOiAnI0U2QTIzQycKICAgIH0sIHsKICAgICAgICBwZXJjZW50OiAxMDAs\nCiAgICAgICAgY29sb3I6ICcjRjU2QzZDJwogICAgfV0KfQouLi4KYGBgCgoj\nIyDlhbPkuo7kvZzogIUKClvliY3nq6/ot6/kuIpdKGh0dHBzOi8vcmVmaW5l\nZC14LmNvbS8pCgo=\n"",""size"":1496,""encoding"":""base64"",""sha"":""ceee393ba74a783dbf4b55e0c903de4431404ea2"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/weapp-plugin-dashboard/master/README.md""},{""repository"":""tower1229/frontend-face-detection"",""name"":""README.md"",""path"":""README.md"",""content"":""IyBmcm9udGVuZC1mYWNlLWRldGVjdGlvbgoKIyMg5LuL57uNCgrmnKzpobnn\nm67mmK/kuIDkuKrln7rkuo7liY3nq6/kurrohLjor4bliKvmioDmnK/lrp7n\njrDnmoTnhafniYflkIjmiJDnpLrkvovvvIzkurrohLjor4bliKvln7rkuo5b\ndHJhY2tpbmdqc10oaHR0cHM6Ly90cmFja2luZ2pzLmNvbS8p5a6e546w77yM\n54Wn54mH5ZCI5oiQ5Z+65LqOW0FsbG95SW1hZ2VdKGh0dHA6Ly9hbGxveXRl\nYW0uZ2l0aHViLmlvL0FsbG95SW1hZ2UvKeWunueOsOOAggoK5pSv5oyB5LiK\n5Lyg5pys5Zyw5Zu+54mHL+WGhee9ruWbvueJh+W6ky/ov5znqIvlm77niYfk\nuInnp43mqKHlvI/nmoTlm77niYfor4bliKvvvIzor6bnu4bku4vnu43lj4Lo\np4Fb57qv5YmN56uv5a6e546w5Lq66IS46K+G5YirLeaPkOWPli3lkIjmiJBd\nKGh0dHBzOi8vcmVmaW5lZC14LmNvbS8yMDE3LzA5LzA2LyVFNyVCQSVBRiVF\nNSU4OSU4RCVFNyVBQiVBRiVFNSVBRSU5RSVFNyU4RSVCMCVFNCVCQSVCQSVF\nOCU4NCVCOCVFOCVBRiU4NiVFNSU4OCVBQi0lRTYlOEYlOTAlRTUlOEYlOTYt\nJUU1JTkwJTg4JUU2JTg4JTkwLykKCiMjIOa8lOekugoKaHR0cHM6Ly9yZWZp\nbmVkLXguY29tL2Zyb250ZW5kLWZhY2UtZGV0ZWN0aW9uLwoKIyMg5pu05paw\n6K6w5b2VCgrjgJAyMDE4LTA4LTAy44CRIOWinuWKoOiHquWKqOWMuemFjeWK\nn+iDve+8jOWMuemFjeWIsOS6uuiEuOiHquWKqOWBnOatogoKIyMg6K645Y+v\n6K+BCgpbTUlUXShodHRwOi8vb3BlbnNvdXJjZS5vcmcvbGljZW5zZXMvTUlU\nKQoKQ29weXJpZ2h0IChjKSAyMDE3LXByZXNlbnQsIFtyZWZpbmVkLXguY29t\nXShodHRwOi8vcmVmaW5lZC14LmNvbSkK\n"",""size"":834,""encoding"":""base64"",""sha"":""5e5a8d694bc1446b874a8037f3629f9616cfe2a9"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/frontend-face-detection/master/README.md""},{""repository"":""tower1229/AJAX-Cache"",""name"":""README.md"",""path"":""README.md"",""content"":""RW5nbGlzaCB8IFvkuK3mloddKFJFQURNRV9DTi5tZCkKCiMgQUpBWC1DYWNo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\nCgo=\n"",""size"":2162,""encoding"":""base64"",""sha"":""896d0abd42590a5884a04bb86be9c1dae2b134dd"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/AJAX-Cache/master/README.md""}]"	[]	"{""maxRepositories"":10,""maxCommitsPerRepo"":100,""includeReadme"":true,""includeTechStackFiles"":true,""includeTimeStats"":true,""requestDelay"":100,""cacheValidityHours"":24}"	1.0.0	1753014513036	1753100913036	completed	NULL	10	340	9	0	1753014513838	1753014513036	"[{""repository"":""tower1229/Vue-Access-Control"",""name"":""package.json"",""fileType"":""package.json"",""path"":""package.json"",""content"":""ewogICJuYW1lIjogInZ1ZS1hY2Nlc3MtY29udHJvbCIsCiAgInZlcnNpb24i\nOiAiMS4wLjAiLAogICJkZXNjcmlwdGlvbiI6ICJGcm9udGVuZCBhY2Nlc3Mg\nY29udHJvbCBmcmFtZXdvcmsgYmFzZWQgVnVlIiwKICAia2V5d29yZHMiOiBb\nCiAgICAidnVlIiwKICAgICJhY2Nlc3MtY29udHJvbCIKICBdLAogICJhdXRo\nb3IiOiAidG93ZXIxMjI5QGdtYWlsLmNvbSIsCiAgImxpY2Vuc2UiOiAiTUlU\nIiwKICAicmVwb3NpdG9yeSI6IHsKICAgICJ0eXBlIjogImdpdCIsCiAgICAi\ndXJsIjogImh0dHBzOi8vZ2l0aHViLmNvbS90b3dlcjEyMjkvVnVlLUFjY2Vz\ncy1Db250cm9sIgogIH0sCiAgInNjcmlwdHMiOiB7CiAgICAic2VydmUiOiAi\ndnVlLWNsaS1zZXJ2aWNlIHNlcnZlIiwKICAgICJidWlsZCI6ICJ2dWUtY2xp\nLXNlcnZpY2UgYnVpbGQiCiAgfSwKICAiZGVwZW5kZW5jaWVzIjogewogICAg\nImF4aW9zIjogIl4wLjI2LjEiLAogICAgImNyeXB0by1qcyI6ICJeNC4xLjEi\nLAogICAgImVsZW1lbnQtdWkiOiAiXjIuMTUuNiIsCiAgICAidnVlIjogIl4y\nLjYuMTQiLAogICAgInZ1ZS1yb3V0ZXIiOiAiXjMuNS4yIgogIH0sCiAgImRl\ndkRlcGVuZGVuY2llcyI6IHsKICAgICJAdnVlL2NsaS1wbHVnaW4tYmFiZWwi\nOiAiXjQuNS4xMyIsCiAgICAiQHZ1ZS9jbGktc2VydmljZSI6ICJeNC41LjEz\nIiwKICAgICJjaG9raWRhciI6ICJeMy41LjEiLAogICAgImNvcmUtanMiOiAi\nXjMuMTMuMSIsCiAgICAibW9ja2pzIjogIl4xLjEuMCIsCiAgICAidnVlLXRl\nbXBsYXRlLWNvbXBpbGVyIjogIl4yLjYuMTQiCiAgfQp9Cg==\n"",""size"":799,""encoding"":""base64"",""sha"":""b67764f3b7800f90449beb5428757945072e24f3"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/Vue-Access-Control/master/package.json""},{""repository"":""tower1229/Vue-Giant-Tree"",""name"":""package.json"",""fileType"":""package.json"",""path"":""package.json"",""content"":""ewogICJuYW1lIjogInZ1ZS1naWFudC10cmVlIiwKICAidmVyc2lvbiI6ICIx\nLjAuMCIsCiAgImF1dGhvciI6ICJyZWZpbmVkLXggW3Rvd2VyMTIyOUBmb3ht\nYWlsLmNvbV0iLAogICJtYWluIjogIi4vZGlzdC92dWUtZ2lhbnQtdHJlZS5j\nb21tb24uanMiLAogICJyZXBvc2l0b3J5IjogewogICAgInR5cGUiOiAiZ2l0\nIiwKICAgICJ1cmwiOiAiZ2l0K2h0dHBzOi8vZ2l0aHViLmNvbS90b3dlcjEy\nMjkvVnVlLUdpYW50LVRyZWUuZ2l0IgogIH0sCiAgImZpbGVzIjogWwogICAg\nImRpc3QiCiAgXSwKICAia2V5d29yZHMiOiBbCiAgICAidnVlIiwKICAgICJ0\ncmVlIiwKICAgICJ6dHJlZSIKICBdLAogICJsaWNlbnNlIjogIk1JVCIsCiAg\nImhvbWVwYWdlIjogImh0dHBzOi8vZ2l0aHViLmNvbS90b3dlcjEyMjkvVnVl\nLUdpYW50LVRyZWUvYmxvYi9tYXN0ZXIvUkVBRE1FLm1kIiwKICAicHJpdmF0\nZSI6IGZhbHNlLAogICJzY3JpcHRzIjogewogICAgInNlcnZlIjogInZ1ZS1j\nbGktc2VydmljZSBzZXJ2ZSIsCiAgICAiYnVpbGQiOiAidnVlLWNsaS1zZXJ2\naWNlIGJ1aWxkIiwKICAgICJidWlsZC1idW5kbGUiOiAidnVlLWNsaS1zZXJ2\naWNlIGJ1aWxkIC0tdGFyZ2V0IGxpYiAtLWRlc3QgZGlzdCAtLW5hbWUgdnVl\nLWdpYW50LXRyZWUgLi9zcmMvY29tcG9uZW50cy96dHJlZS52dWUiLAogICAg\nImxpbnQiOiAidnVlLWNsaS1zZXJ2aWNlIGxpbnQiCiAgfSwKICAiZGVwZW5k\nZW5jaWVzIjogewogICAgIkB6dHJlZS96dHJlZV92MyI6ICJeMy41LjQ0Igog\nIH0sCiAgImRldkRlcGVuZGVuY2llcyI6IHsKICAgICJAdnVlL2NsaS1wbHVn\naW4tYmFiZWwiOiAiXjQuNC42IiwKICAgICJAdnVlL2NsaS1wbHVnaW4tZXNs\naW50IjogIl40LjQuNiIsCiAgICAiQHZ1ZS9jbGktc2VydmljZSI6ICJeNC40\nLjYiLAogICAgImJhYmVsLWVzbGludCI6ICJeMTAuMS4wIiwKICAgICJlc2xp\nbnQiOiAiXjYuNC4wIiwKICAgICJlc2xpbnQtcGx1Z2luLXZ1ZSI6ICJeNi4y\nLjIiLAogICAgInZ1ZSI6ICJeMi42LjExIiwKICAgICJ2dWUtdGVtcGxhdGUt\nY29tcGlsZXIiOiAiXjIuNi4xMSIKICB9Cn0=\n"",""size"":1061,""encoding"":""base64"",""sha"":""459fd2d6d4ebc1ea5667f5f71f6dbc1f3f78ee41"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/Vue-Giant-Tree/master/package.json""},{""repository"":""tower1229/Vue-Tree-Chart"",""name"":""package.json"",""fileType"":""package.json"",""path"":""package.json"",""content"":""ewogICJuYW1lIjogInZ1ZS10cmVlLWNoYXJ0IiwKICAidmVyc2lvbiI6ICIx\nLjIuOSIsCiAgImRlc2NyaXB0aW9uIjogIkEgdnVlMiBjb21wb25lbnQgdG8g\nZGlzcGxheSB0cmVlIGNoYXJ0IiwKICAiYXV0aG9yIjogInJlZmluZWQteCBb\ndG93ZXIxMjI5QGZveG1haWwuY29tXSIsCiAgIm1haW4iOiAiLi9kaXN0L1Ry\nZWVDaGFydC5jb21tb24uanMiLAogICJyZXBvc2l0b3J5IjogewogICAgInR5\ncGUiOiAiZ2l0IiwKICAgICJ1cmwiOiAiZ2l0K2h0dHBzOi8vZ2l0aHViLmNv\nbS90b3dlcjEyMjkvVnVlLVRyZWUtQ2hhcnQuZ2l0IgogIH0sCiAgImZpbGVz\nIjogWwogICAgImRpc3QiCiAgXSwKICAia2V5d29yZHMiOiBbCiAgICAidnVl\nIiwKICAgICJ0cmVlIiwKICAgICJjaGFydCIsCiAgICAidHJlZSBjaGFydCIK\nICBdLAogICJsaWNlbnNlIjogIk1JVCIsCiAgImhvbWVwYWdlIjogImh0dHBz\nOi8vZ2l0aHViLmNvbS90b3dlcjEyMjkvVnVlLVRyZWUtQ2hhcnQvYmxvYi9t\nYXN0ZXIvUkVBRE1FLm1kIiwKICAicHJpdmF0ZSI6IGZhbHNlLAogICJzY3Jp\ncHRzIjogewogICAgInNlcnZlIjogInZ1ZS1jbGktc2VydmljZSBzZXJ2ZSIs\nCiAgICAiYnVpbGQiOiAidnVlLWNsaS1zZXJ2aWNlIGJ1aWxkIiwKICAgICJi\ndWlsZC1idW5kbGUiOiAidnVlLWNsaS1zZXJ2aWNlIGJ1aWxkIC0tdGFyZ2V0\nIGxpYiAtLWRlc3QgZGlzdCAtLW5hbWUgVHJlZUNoYXJ0IC4vc3JjL2NvbXBv\nbmVudHMvVHJlZUNoYXJ0LnZ1ZSIsCiAgICAibGludCI6ICJ2dWUtY2xpLXNl\ncnZpY2UgbGludCIKICB9LAogICJkZXBlbmRlbmNpZXMiOiB7CiAgICAidnVl\nIjogIl4yLjYuMTIiCiAgfSwKICAiZGV2RGVwZW5kZW5jaWVzIjogewogICAg\nIkB2dWUvY2xpLXBsdWdpbi1iYWJlbCI6ICJeNC41LjYiLAogICAgIkB2dWUv\nY2xpLXBsdWdpbi1lc2xpbnQiOiAiXjQuNS42IiwKICAgICJAdnVlL2NsaS1z\nZXJ2aWNlIjogIl40LjUuNiIsCiAgICAiYmFiZWwtZXNsaW50IjogIl4xMC4x\nLjAiLAogICAgImVzbGludCI6ICJeNy45LjAiLAogICAgImVzbGludC1wbHVn\naW4tdnVlIjogIl42LjIuMiIsCiAgICAidnVlLXRlbXBsYXRlLWNvbXBpbGVy\nIjogIl4yLjYuMTIiCiAgfQp9Cg==\n"",""size"":1099,""encoding"":""base64"",""sha"":""c8834b792df389ea9288fecc30703b2d9ec481ba"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/Vue-Tree-Chart/master/package.json""},{""repository"":""tower1229/HybridStart"",""name"":""package.json"",""fileType"":""package.json"",""path"":""package.json"",""content"":""ewogICJuYW1lIjogImh5YnJpZHN0YXJ0IiwKICAidmVyc2lvbiI6ICIxLjIu\nNCIsCiAgImRlc2NyaXB0aW9uIjogIkEgaHlicmlkIGFwcGxpY2F0aW9uIGRl\ndmVsb3BtZW50IGZyYW1ld29yayIsCiAgIm1haW4iOiAibGliL2luZGV4Lmpz\nIiwKICAiZGlyZWN0b3JpZXMiOiB7CiAgICAiZG9jIjogImRvY3MiCiAgfSwK\nICAic2NyaXB0cyI6IHsKICAgICJpbml0IjogImFwaWNsb3VkIHdpZmlTdGFy\ndCAtLXBvcnQgODY4NiIsCiAgICAic3luYyI6ICJhcGljbG91ZCB3aWZpU3lu\nYyAtLXByb2plY3QgLi8gLS11cGRhdGVBbGwgZmFsc2UgLS1wb3J0IDg2ODYi\nLAogICAgInN0b3AiOiAiYXBpY2xvdWQgd2lmaVN0b3AgLS1wb3J0IDg2ODYi\nLAogICAgImJ1aWxkIjogImxlc3NjIC0tY2xlYW4tY3NzIHNkay91aS5sZXNz\nIHNkay91aS5jc3MiCiAgfSwKICAicmVwb3NpdG9yeSI6IHsKICAgICJ0eXBl\nIjogImdpdCIsCiAgICAidXJsIjogImdpdCtodHRwczovL2dpdGh1Yi5jb20v\ndG93ZXIxMjI5L0h5YnJpZFN0YXJ0LmdpdCIKICB9LAogICJrZXl3b3JkcyI6\nIFsKICAgICJoeWJyaWRzdGFydCIsCiAgICAiaHlicmlkYXBwIiwKICAgICJh\ncGljbG91ZCIsCiAgICAiaHlicmlkc3RhcnQtY2xpIgogIF0sCiAgImF1dGhv\nciI6ICJ0b3dlcjEyMjlAZ21haWwuY29tIiwKICAibGljZW5zZSI6ICJNSVQi\nLAogICJidWdzIjogewogICAgInVybCI6ICJodHRwczovL2dpdGh1Yi5jb20v\ndG93ZXIxMjI5L0h5YnJpZFN0YXJ0L2lzc3VlcyIKICB9LAogICJob21lcGFn\nZSI6ICJodHRwczovL2dpdGh1Yi5jb20vdG93ZXIxMjI5L0h5YnJpZFN0YXJ0\nI3JlYWRtZSIsCiAgImRlcGVuZGVuY2llcyI6IHsKICAgICJhcGljbG91ZC1j\nbGkiOiAiXjAuMi4wIiwKICAgICJsZXNzIjogIl4yLjcuMyIsCiAgICAibGVz\ncy1wbHVnaW4tY2xlYW4tY3NzIjogIl4xLjUuMSIKICB9Cn0K\n"",""size"":936,""encoding"":""base64"",""sha"":""bc52fb5a476e436d6a199dcf86ef8e01c740e6ca"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/HybridStart/master/package.json""},{""repository"":""tower1229/frontend-weekly"",""name"":""package.json"",""fileType"":""package.json"",""path"":""package.json"",""content"":""ewogICJuYW1lIjogImZyb250ZW5kLXdlZWtseS12aXRlcHJlc3MiLAogICJ2\nZXJzaW9uIjogIjEuMC4wIiwKICAiZGVzY3JpcHRpb24iOiAiIiwKICAibWFp\nbiI6ICJpbmRleC5qcyIsCiAgInNjcmlwdHMiOiB7CiAgICAidGVzdCI6ICJl\nY2hvIFwiRXJyb3I6IG5vIHRlc3Qgc3BlY2lmaWVkXCIgJiYgZXhpdCAxIiwK\nICAgICJkZXYiOiAidml0ZXByZXNzIGRldiIsCiAgICAiYnVpbGQiOiAidml0\nZXByZXNzIGJ1aWxkIiwKICAgICJwcmV2aWV3IjogInZpdGVwcmVzcyBwcmV2\naWV3IgogIH0sCiAgImF1dGhvciI6ICIiLAogICJsaWNlbnNlIjogIklTQyIs\nCiAgImRldkRlcGVuZGVuY2llcyI6IHsKICAgICJ2aXRlcHJlc3MiOiAiXjEu\nMC4yIgogIH0KfQ==\n"",""size"":370,""encoding"":""base64"",""sha"":""a09adaa7243cced1a689f6a504197563f38ac01c"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/frontend-weekly/master/package.json""},{""repository"":""tower1229/weapp-plugin-dashboard"",""name"":""package.json"",""fileType"":""package.json"",""path"":""package.json"",""content"":""ewogICJuYW1lIjogIndlYXBwLXBsdWdpbi1kYXNoYm9hcmQiLAogICJ2ZXJz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\nY2llcyI6IHt9Cn0K\n"",""size"":1857,""encoding"":""base64"",""sha"":""7d490971e1c4d7cea426cd3dfbd14a4fe93c1dae"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/weapp-plugin-dashboard/master/package.json""},{""repository"":""tower1229/AJAX-Cache"",""name"":""package.json"",""fileType"":""package.json"",""path"":""package.json"",""content"":""ewogICJuYW1lIjogImFqYXgtY2FjaGUiLAogICJ2ZXJzaW9uIjogIjEuMC4z\nIiwKICAiZGVzY3JpcHRpb24iOiAiVGhlIGJlc3QgalF1ZXJ5LWFqYXgtY2Fj\naGUgcGx1Z2luIiwKICAibWFpbiI6ICJhamF4LWNhY2hlLmpzIiwKICAic2Ny\naXB0cyI6IHsKICAgICJ0ZXN0IjogImVjaG8gXCJFcnJvcjogbm8gdGVzdCBz\ncGVjaWZpZWRcIiAmJiBleGl0IDEiCiAgfSwKICAicmVwb3NpdG9yeSI6IHsK\nICAgICJ0eXBlIjogImdpdCIsCiAgICAidXJsIjogImh0dHBzOi8vZ2l0aHVi\nLmNvbS90b3dlcjEyMjkvQUpBWC1DYWNoZS5naXQiCiAgfSwKICAia2V5d29y\nZHMiOiBbCiAgICAiYWpheCIsCiAgICAiY2FjaGUiLAogICAgImpxdWVyeS1w\nbHVnaW4iCiAgXSwKICAiYXV0aG9yIjogInRvd2VyMTIyOUBnbWFpbC5jb20i\nLAogICJsaWNlbnNlIjogIk1JVCIsCiAgImJ1Z3MiOiB7CiAgICAidXJsIjog\nImh0dHBzOi8vZ2l0aHViLmNvbS90b3dlcjEyMjkvQUpBWC1DYWNoZS9pc3N1\nZXMiCiAgfSwKICAiaG9tZXBhZ2UiOiAiaHR0cDovL3JlZmluZWQteC5jb20v\nQUpBWC1DYWNoZS8iCn0=\n"",""size"":554,""encoding"":""base64"",""sha"":""0c69037d4a3c01793ba51bac209c92e5f848a843"",""downloadUrl"":""https://raw.githubusercontent.com/tower1229/AJAX-Cache/master/package.json""}]"	7