{"commit_density": {"source": "events, repos.commits", "extractor": "统计每日/每周平均提交量，设定频率区间", "requiresLLM": false, "values": ["low", "medium", "high"]}, "commit_consistency": {"source": "events.timestamp", "extractor": "计算连续提交天数或活跃周期性（如 streak）", "requiresLLM": false, "values": ["random", "streak: x days", "seasonal"]}, "commit_length": {"source": "commits.message", "extractor": "统计 commit message 的平均字符数", "requiresLLM": false, "values": ["terse", "verbose", "poetic"]}, "commit_sentiment": {"source": "commits.message", "extractor": "使用情感分析识别语气（正向/戏谑/负面）", "requiresLLM": true, "values": ["neutral", "joking", "dramatic"]}, "repo_naming_style": {"source": "repos[].name", "extractor": "是否有统一主题、引用文化符号或为随机命名", "requiresLLM": true, "values": ["thematic", "random", "poetic"]}, "active_hours": {"source": "events.timestamp", "extractor": "统计活跃时间分布，按 UTC 映射出开发者本地作息偏好", "requiresLLM": false, "values": ["earlybird", "night_owl", "insomniac"]}, "weekend_dev": {"source": "events.date", "extractor": "周六日提交频率占比", "requiresLLM": false, "values": ["yes", "no", "only_weekends"]}, "long_pause_gaps": {"source": "events[].timestamp", "extractor": "计算最近一次提交与前一次的间隔是否异常长", "requiresLLM": false, "values": ["no_gap", "occasional", "huge_gap"]}, "language_diversity": {"source": "repos[].language", "extractor": "统计出现过的语言种类数量", "requiresLLM": false, "values": ["mono", "multi:3", "multi:5", "chaotic"]}, "framework_focus": {"source": "repo topics、描述、技术栈文件（package.json, pyproject.toml, Cargo.toml等）", "extractor": "基于技术栈文件内容分析框架专注度，已实现TechStackAnalyzer", "requiresLLM": false, "analyzer": "TechStackAnalyzer.analyze()", "values": ["react_purist", "rustacean", "scatter"]}, "experimental_ratio": {"source": "repo name, description, tags", "extractor": "含 experimental、test、sandbox 等关键词的项目比例", "requiresLLM": false, "values": ["low", "moderate", "high"]}, "star_to_follower_ratio": {"source": "user.followers, total stars (repos[].stargazers_count)", "extractor": "计算总 star 与粉丝比，评估影响力结构", "requiresLLM": false, "values": ["stealthy", "popular", "asymmetrical"]}}