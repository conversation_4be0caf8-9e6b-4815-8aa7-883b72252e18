# V5.3 调试平台功能增强 - 详细实施计划

## 📋 项目概述

**项目名称**: V5.3 调试平台功能增强
**项目目标**: 解决 AI 调试平台的三大核心问题：模块依赖、复制功能缺失、日志系统不完整
**预计工期**: 12 个工作日
**当前状态**: 📝 PLAN 模式，准备进入 ⚙️ EXECUTE 模式

## 🎯 技术实施路线图

### 🔄 阶段一：模块独立性增强 (6 天)

#### 任务 1: ModuleInputEditor 通用组件开发

**预估时间**: 2 天
**优先级**: 🔥 高
**技术规范**:

```typescript
// components/debug-platform/ModuleInputEditor.tsx
interface ModuleInputEditorProps<T> {
  label: string;
  value: T | null;
  onValueChange: (value: T) => void;
  inputMode: InputMode;
  onInputModeChange: (mode: InputMode) => void;
  defaultTemplate: T;
  validation?: (value: T) => ValidationResult;
  schema?: JSONSchema;
  upstreamValue?: T;
  placeholder?: string;
  className?: string;
}

type InputMode = "upstream" | "manual" | "hybrid";

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export function ModuleInputEditor<T>({
  label,
  value,
  onValueChange,
  inputMode,
  onInputModeChange,
  defaultTemplate,
  validation,
  schema,
  upstreamValue,
  placeholder,
  className,
}: ModuleInputEditorProps<T>) {
  // 实现三种输入模式的切换逻辑
  // 集成Monaco编辑器
  // 提供数据验证和格式化
  // 支持模板填充和重置
}
```

**关键特性**:

- 三种输入模式无缝切换
- 实时数据验证和错误提示
- 模板数据一键填充
- 完整的 TypeScript 类型支持

#### 任务 2: 输入模式类型系统定义

**预估时间**: 0.5 天
**依赖**: ModuleInputEditor 组件
**技术规范**:

```typescript
// types/debug-input-modes.ts
export type InputMode = "upstream" | "manual" | "hybrid";

export interface InputModeConfig {
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  enabled: boolean;
  tooltip: string;
}

export const INPUT_MODE_CONFIGS: Record<InputMode, InputModeConfig> = {
  upstream: {
    label: "上游数据",
    description: "使用上游模块的输出结果",
    icon: ArrowDown,
    enabled: true,
    tooltip: "直接使用上游模块的输出数据，无法编辑",
  },
  manual: {
    label: "手动输入",
    description: "完全手动输入数据",
    icon: Edit,
    enabled: true,
    tooltip: "完全手动输入数据，支持自定义任意内容",
  },
  hybrid: {
    label: "混合模式",
    description: "基于上游数据进行二次编辑",
    icon: Layers,
    enabled: true,
    tooltip: "基于上游数据进行二次编辑和调整",
  },
};
```

#### 任务 3: 默认数据模板创建

**预估时间**: 0.5 天
**依赖**: 输入模式类型系统
**技术规范**:

```typescript
// constants/debug-templates.ts
export const DEFAULT_ANALYZER_OUTPUT: AnalyzerOutput = {
  status: "success",
  content: {
    metrics: [
      {
        metric: "commits",
        value: 1500,
        valence: "positive",
        analysis: "高频次提交显示出持续的开发活动",
        confidence: 0.9,
      },
      {
        metric: "stars",
        value: 120,
        valence: "positive",
        analysis: "项目获得了良好的社区认可",
        confidence: 0.8,
      },
    ],
    overallPattern: "predominantly_positive_influence_focused",
    insights: ["该开发者展现出持续的贡献活动", "项目具有一定的影响力和认可度"],
    processingTime: 1200,
  },
  confidence: 0.85,
  processing_time: 1200,
};

export const DEFAULT_STRATEGIST_OUTPUT: StrategistOutput = {
  status: "success",
  content: {
    selectedStrategyId: "humble_achiever",
    targetEmotion: "humble",
    perspective: "first_person",
    keyTalkingPoints: ["持续学习和改进", "团队协作的重要性", "技术分享的价值"],
    justification: "基于用户的贡献模式，选择谦逊但自信的表达方式",
    confidence: 0.88,
  },
  confidence: 0.88,
  processing_time: 800,
};

// 继续定义其他模块的默认模板...
```

#### 任务 4-6: Tab 组件重构 (StrategistTab → WriterTab → CriticTab)

**预估时间**: 每个 1 天，共 3 天
**技术规范**:

```typescript
// components/debug-platform/tabs/StrategistTab.tsx (重构示例)
export function StrategistTab({
  githubData,
  analyzerOutput,
  onResultChange,
  className,
}: StrategistTabProps) {
  const [preferences, setPreferences] =
    useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [inputMode, setInputMode] = useState<InputMode>("upstream");
  const [manualAnalyzerOutput, setManualAnalyzerOutput] =
    useState<AnalyzerOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<StrategistOutput | null>(null);

  // 获取有效的输入数据
  const getEffectiveAnalyzerOutput = (): AnalyzerOutput | null => {
    switch (inputMode) {
      case "upstream":
        return analyzerOutput || null;
      case "manual":
        return manualAnalyzerOutput;
      case "hybrid":
        return analyzerOutput && manualAnalyzerOutput
          ? { ...analyzerOutput, ...manualAnalyzerOutput }
          : analyzerOutput || manualAnalyzerOutput || null;
      default:
        return null;
    }
  };

  const canExecute = getEffectiveAnalyzerOutput() !== null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 模块头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-lg">Strategist 模块</h3>
          <p className="text-sm text-muted-foreground">
            基于分析结果选择最适合的表达策略和情感风格
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPreferences(DEFAULT_PREFERENCES)}
            disabled={isLoading}
          >
            <Settings className="h-4 mr-2 w-4" />
            重置配置
          </Button>
          <Button
            onClick={runStrategist}
            disabled={isLoading || !canExecute}
            size="sm"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 mr-2 animate-spin w-4" />
                策略选择中...
              </>
            ) : (
              <>
                <Play className="h-4 mr-2 w-4" />
                运行策略
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
        {/* 输入数据区域 */}
        <div className="space-y-4">
          <h4 className="font-medium">输入数据</h4>
          <ModuleInputEditor<AnalyzerOutput>
            label="Analyzer 输出"
            value={getEffectiveAnalyzerOutput()}
            onValueChange={setManualAnalyzerOutput}
            inputMode={inputMode}
            onInputModeChange={setInputMode}
            defaultTemplate={DEFAULT_ANALYZER_OUTPUT}
            upstreamValue={analyzerOutput}
            validation={(value) => validateAnalyzerOutput(value)}
          />
        </div>

        {/* 参数配置区域 */}
        <div className="space-y-4">
          <h4 className="font-medium">参数配置</h4>
          <MonacoJsonEditor
            value={JSON.stringify(preferences, null, 2)}
            onRawChange={(value) => {
              try {
                const parsed = JSON.parse(value);
                setPreferences(parsed);
              } catch (e) {
                // 忽略无效JSON
              }
            }}
            height={400}
          />
        </div>

        {/* 执行结果区域 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">执行结果</h4>
            {result && (
              <CopyButton
                data={result}
                format="json"
                label="复制结果"
                onCopySuccess={() => toast.success("结果已复制")}
              />
            )}
          </div>
          <MonacoJsonEditor
            value={JSON.stringify(result || {}, null, 2)}
            readOnly
            height={400}
          />
        </div>
      </div>
    </div>
  );
}
```

### 🔄 阶段二：一键复制功能 (3.5 天)

#### 任务 7: CopyButton 组件开发

**预估时间**: 1.5 天
**技术规范**:

```typescript
// components/debug-platform/CopyButton.tsx
interface CopyButtonProps {
  data: any;
  format: CopyFormat;
  label?: string;
  onCopySuccess?: () => void;
  onCopyError?: (error: Error) => void;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "outline" | "ghost";
}

type CopyFormat = "json" | "compact" | "formatted" | "raw";

export function CopyButton({
  data,
  format = "json",
  label = "复制",
  onCopySuccess,
  onCopyError,
  className,
  size = "sm",
  variant = "outline",
}: CopyButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [justCopied, setJustCopied] = useState(false);

  const formatData = (data: any, format: CopyFormat): string => {
    switch (format) {
      case "json":
        return JSON.stringify(data, null, 2);
      case "compact":
        return JSON.stringify(data);
      case "formatted":
        return formatForHuman(data);
      case "raw":
        return String(data);
      default:
        return JSON.stringify(data, null, 2);
    }
  };

  const handleCopy = async () => {
    if (!data) return;

    setIsLoading(true);
    try {
      const formatted = formatData(data, format);
      await navigator.clipboard.writeText(formatted);

      setJustCopied(true);
      onCopySuccess?.();

      // 重置状态
      setTimeout(() => setJustCopied(false), 2000);
    } catch (error) {
      const err = error as Error;
      onCopyError?.(err);
      toast.error(`复制失败: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleCopy}
      disabled={isLoading || !data}
      className={className}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : justCopied ? (
        <Check className="h-4 w-4 mr-2 text-green-600" />
      ) : (
        <Copy className="h-4 w-4 mr-2" />
      )}
      {justCopied ? "已复制" : label}
    </Button>
  );
}
```

#### 任务 8: 复制功能集成

**预估时间**: 1 天
**技术规范**: 为所有 Tab 组件的输出结果区域添加 CopyButton

#### 任务 9: 格式化选项实现

**预估时间**: 1 天
**技术规范**: 实现多种复制格式的支持

### 🔄 阶段三：统一日志系统 (6 天)

#### 任务 10: LogManager 日志管理器

**预估时间**: 1.5 天
**技术规范**:

```typescript
// lib/debug/LogManager.ts
interface ModuleExecutionLog {
  id: string;
  timestamp: number;
  sessionId: string;
  module: ModuleType;
  phase: LogPhase;
  message: string;
  data?: {
    inputParams?: any;
    outputResult?: any;
    processingTime?: number;
    tokensUsed?: number;
    errorDetails?: string;
  };
  metadata: {
    userId?: string;
    userAgent?: string;
    version?: string;
  };
}

type LogPhase = "start" | "processing" | "success" | "error" | "timeout";

export class LogManager {
  private static instance: LogManager;
  private logs: ModuleExecutionLog[] = [];
  private subscribers: ((logs: ModuleExecutionLog[]) => void)[] = [];
  private maxLogs: number = 1000;

  static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  addLog(log: Omit<ModuleExecutionLog, "id" | "timestamp">): void {
    const fullLog: ModuleExecutionLog = {
      ...log,
      id: `${log.module}-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`,
      timestamp: Date.now(),
    };

    this.logs.push(fullLog);

    // 保持日志数量在限制内
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    this.notifySubscribers();
  }

  getLogs(filter?: LogFilter): ModuleExecutionLog[] {
    let filteredLogs = this.logs;

    if (filter) {
      if (filter.module) {
        filteredLogs = filteredLogs.filter(
          (log) => log.module === filter.module
        );
      }
      if (filter.phase) {
        filteredLogs = filteredLogs.filter((log) => log.phase === filter.phase);
      }
      if (filter.timeRange) {
        filteredLogs = filteredLogs.filter(
          (log) =>
            log.timestamp >= filter.timeRange!.start &&
            log.timestamp <= filter.timeRange!.end
        );
      }
      if (filter.sessionId) {
        filteredLogs = filteredLogs.filter(
          (log) => log.sessionId === filter.sessionId
        );
      }
    }

    return filteredLogs.sort((a, b) => b.timestamp - a.timestamp);
  }

  subscribe(callback: (logs: ModuleExecutionLog[]) => void): () => void {
    this.subscribers.push(callback);
    return () => {
      this.subscribers = this.subscribers.filter((sub) => sub !== callback);
    };
  }

  clearLogs(): void {
    this.logs = [];
    this.notifySubscribers();
  }

  exportLogs(format: "json" | "csv" = "json"): string {
    if (format === "json") {
      return JSON.stringify(this.logs, null, 2);
    }

    // CSV export implementation
    const headers = ["id", "timestamp", "module", "phase", "message"];
    const csvRows = [headers.join(",")];

    this.logs.forEach((log) => {
      const row = [
        log.id,
        new Date(log.timestamp).toISOString(),
        log.module,
        log.phase,
        `"${log.message.replace(/"/g, '""')}"`, // Escape quotes
      ];
      csvRows.push(row.join(","));
    });

    return csvRows.join("\n");
  }

  private notifySubscribers(): void {
    this.subscribers.forEach((callback) => callback(this.logs));
  }
}

interface LogFilter {
  module?: ModuleType;
  phase?: LogPhase;
  timeRange?: { start: number; end: number };
  sessionId?: string;
}
```

#### 任务 11: 日志类型定义

**预估时间**: 0.5 天
**依赖**: LogManager

#### 任务 12: BaseModule 集成更新

**预估时间**: 1 天
**技术规范**: 更新 BaseModule 的日志机制，集成 LogManager

#### 任务 13: RealtimeFeedbackSystem 增强

**预估时间**: 2 天
**技术规范**: 增强现有的实时反馈系统，支持详细的日志展示

#### 任务 14: 日志过滤和导出

**预估时间**: 1 天
**技术规范**: 实现日志过滤、搜索和导出功能

### 🔄 阶段四：集成测试和验证 (1.5 天)

#### 任务 15: 集成测试

**预估时间**: 1.5 天
**技术规范**: 确保所有新功能正常工作，不影响现有功能

## 📊 里程碑时间表

### Week 1: 模块独立性增强

- **Day 1**: ModuleInputEditor 组件开发 (任务 1)
- **Day 2**: 完成 ModuleInputEditor + 类型定义 (任务 1-2)
- **Day 3**: 模板创建 + StrategistTab 重构 (任务 3-4)
- **Day 4**: WriterTab 重构 (任务 5)
- **Day 5**: CriticTab 重构 (任务 6)
- **Day 6**: 阶段一测试和优化

### Week 2: 复制功能 + 日志系统

- **Day 7**: CopyButton 组件开发 (任务 7)
- **Day 8**: 复制功能集成 (任务 8-9)
- **Day 9**: LogManager 开发 (任务 10-11)
- **Day 10**: BaseModule 集成 (任务 12)
- **Day 11**: RealtimeFeedbackSystem 增强 (任务 13)
- **Day 12**: 日志过滤导出 + 集成测试 (任务 14-15)

## 🎯 质量保证措施

### 开发标准

1. **代码审查**: 每个组件完成后进行代码审查
2. **类型安全**: 100% TypeScript 覆盖，无 any 类型
3. **错误处理**: 完整的异常处理和用户友好的错误提示
4. **性能优化**: 避免不必要的重新渲染，使用 React.memo 优化

### 测试策略

1. **单元测试**: 每个组件的核心功能测试
2. **集成测试**: 模块间数据流测试
3. **端到端测试**: 完整的用户操作流程测试
4. **性能测试**: 大量数据情况下的性能表现

### 文档要求

1. **API 文档**: 所有组件的完整 API 文档
2. **使用指南**: 新功能的使用说明
3. **升级指南**: 现有用户的升级指导
4. **技术文档**: 架构决策和实现细节

## 📈 成功标准

### 功能完成度

- [x] 模块独立性增强: 100%
- [x] 一键复制功能: 100%
- [x] 统一日志系统: 100%

### 性能指标

- 调试效率提升 ≥ 70%
- 数据传输效率提升 ≥ 50%
- 问题排查时间减少 ≥ 80%

### 质量指标

- TypeScript 编译零错误
- 单元测试覆盖率 ≥ 90%
- 代码可维护性评分 ≥ 8.5/10

## 🚀 项目交付

### 交付物清单

1. 完整的功能代码
2. 完整的测试套件
3. 详细的技术文档
4. 用户使用指南
5. 升级和部署指南

### 部署计划

1. **开发环境验证**: 完整功能测试
2. **预发布环境**: 性能和稳定性测试
3. **生产环境**: 灰度发布和监控
4. **用户培训**: 新功能使用培训

## 📝 风险管理

### 已识别风险

1. **技术风险**: 组件间集成复杂度
2. **时间风险**: 开发进度延期
3. **质量风险**: 新功能影响现有功能
4. **用户风险**: 学习成本和适应性

### 风险缓解措施

1. **技术风险**: 分阶段开发，及时集成测试
2. **时间风险**: 任务分解详细，进度监控
3. **质量风险**: 完整的测试策略，代码审查
4. **用户风险**: 详细文档，渐进式部署

---

**项目状态**: 📝 PLAN 完成，等待 ⚙️ EXECUTE 开始
**下一步**: 开始实施任务 1 - ModuleInputEditor 组件开发
