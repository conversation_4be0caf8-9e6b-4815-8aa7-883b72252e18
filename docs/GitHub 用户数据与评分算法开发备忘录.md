# 📊 GitHub 用户数据与评分算法开发备忘录

_Version: V4.3 | 创建日期: 2024-12-19 | 更新日期: 2024-12-19 | 数据版本: 2_

## 🎯 文档概述

本文档记录了项目中 GitHub 用户数据字段、V4 多维度评分算法和数据获取机制的核心信息。

---

## 📡 GitHub API 数据获取

### 🔌 API 端点总览

| API 端点                                          | 数据类型     | 获取内容               | 缓存时间 |
| ------------------------------------------------- | ------------ | ---------------------- | -------- |
| `/users/{username}`                               | 用户基础信息 | 个人资料、社交数据     | 24 小时  |
| `/users/{username}/repos?per_page=100`            | 仓库列表     | 项目信息、Stars、Forks | 24 小时  |
| `/search/commits?q=author:{username}`             | 提交搜索     | 最近 10 年提交总数     | 24 小时  |
| `/search/issues?q=author:{username}+type:pr`      | PR 搜索      | Pull Request 总数      | 24 小时  |
| `/search/issues?q=author:{username}+type:issue`   | Issue 搜索   | Issue 创建总数         | 24 小时  |
| `/search/issues?q=reviewed-by:{username}+type:pr` | 审查搜索     | 代码审查总数           | 24 小时  |
| `GraphQL: contributionsCollection`                | 贡献统计     | 过去 1 年贡献仓库数    | 24 小时  |
| `/repos/{owner}/{repo}/languages`                 | 语言分析     | 编程语言分布           | 3 天     |

### 🔧 GraphQL 贡献仓库数查询

- **时间范围**：过去 10 年（2014 年至今）
- **数据准确性**：真正的贡献仓库数，包括 PR、Issue、Commit 等所有贡献类型
- **容错机制**：GraphQL 失败时返回 0，确保系统稳定性

---

## 📊 数据字段定义

### 🧑 用户基础信息 (`GitHubUserData`)

```typescript
interface GitHubUserData {
  login: string; // GitHub 用户名
  name: string; // 显示名称
  avatar_url: string; // 头像 URL
  bio: string; // 个人简介
  blog: string; // 个人网站
  location: string; // 地理位置
  twitter_username: string; // Twitter 用户名
  public_repos: number; // 公开仓库数
  followers: number; // 关注者数
  following: number; // 关注数
  created_at: number; // 账号创建时间（毫秒时间戳）
}
```

### 📈 贡献统计数据 (`GitHubContributionsData`)

```typescript
interface GitHubContributionsData {
  total_stars: number; // 总 Stars 数
  contribution_score: number; // 综合贡献分数
  commits: number; // 提交总数（最近10年）
  pull_requests: number; // Pull Request 总数
  public_repos: number; // 公开仓库数
  followers: number; // 关注者数
  following: number; // 关注数
  issues: number; // Issue 创建总数
  reviews: number; // 代码审查总数
  // V4 字段
  total_forks: number; // 总 Forks 数
  contributed_repos: number; // 贡献仓库数（过去1年）
  // V4.1 字段 - 语言统计扩展
  language_stats?: LanguageStatsSummary; // 详细语言统计数据
}
```

### 🌈 V4.1 语言统计数据结构

#### 📊 语言统计摘要 (`LanguageStatsSummary`)

```typescript
interface LanguageStatsSummary {
  totalLanguages: number; // 语言总数
  totalBytes: number; // 代码总字节数
  analyzedRepos: number; // 已分析仓库数
  languages: LanguageStatEntry[]; // 语言详细列表
  primaryLanguage?: LanguageStatEntry; // 主要语言
  metadata: {
    analysisVersion: string; // 分析版本 "v4.1"
    lastUpdated: number; // 最后更新时间戳
    cacheExpiry: number; // 缓存过期时间戳
  };
}
```

#### 🔍 语言条目详情 (`LanguageStatEntry`)

```typescript
interface LanguageStatEntry {
  language: string; // 语言名称
  totalBytes: number; // 该语言总字节数
  repoCount: number; // 使用该语言的仓库数
  primaryRepoCount: number; // 以该语言为主的仓库数
  percentageByBytes: number; // 按字节数计算的百分比
  percentageByRepos: number; // 按仓库数计算的百分比
  languageRank: number; // 语言排名
  repos: Array<{
    name: string; // 仓库名称
    bytes: number; // 该语言在此仓库的字节数
    isPrimary: boolean; // 是否为该仓库的主要语言
    createdAt: number; // 仓库创建时间戳
    updatedAt: number; // 仓库更新时间戳
  }>;
}
```

### 💾 数据库存储结构 (`ContributeData`)

包含用户基础数据、贡献统计数据、V4 字段和元数据，详见 `lib/db/schema.ts`。

**V4.1 重要变更**：

- ❌ 移除字段：`language_diversity: integer`
- ✅ 新增字段：`language_stats: text` (JSON 格式存储)

---

## 🧮 V4 多维度评分系统

### 📐 四维度评分框架

| 维度       | 英文名称            | 权重 | 主要指标                          | 评分范围 |
| ---------- | ------------------- | ---- | --------------------------------- | -------- |
| 代码提交型 | Code Architect      | 25%  | commits, contributedRepos         | 0-100    |
| 协作交流型 | Community Builder   | 25%  | pullRequests, reviews, issues     | 0-100    |
| 开源影响型 | Open Source Pioneer | 25%  | totalStars, totalForks, followers | 0-100    |
| 学习探索型 | Innovation Explorer | 25%  | languageDiversity, publicRepos    | 0-100    |

### 🎯 V4.1 智能评分算法架构

#### 🏗️ 代码提交型评分 (Code Architect)

- **35%** 代码产出质量评分 - 基于 commit 数量的非线性增长模型
- **30%** 贡献一致性评分 - 基于贡献仓库数评估持续贡献能力
- **25%** 技术影响力评分 - 基于仓库贡献广度评估技术影响力
- **10%** 效率与质量平衡评分 - 评估平均每个仓库的 commit 效率

#### 🤝 协作交流型评分 (Community Builder)

- **40%** 主动协作能力评分 - Pull Request 主动协作评分
- **30%** 代码审查专业度评分 - 基于 reviews 与 PR 比例的质量评估
- **20%** 社区参与广度评分 - Issue 参与度和社区活跃度
- **10%** 协作效率与质量评分 - 协作多样性和规模评估

#### 🌟 开源影响型评分 (Open Source Pioneer)

- **40%** 技术作品影响力评分 - Stars 技术认可度的非线性增长
- **30%** 社区领导力评分 - 基于 followers 的社区影响力评估
- **20%** 影响力增长潜力评分 - Stars 和 followers 的增长效率
- **10%** 影响力质量与深度评分 - 影响力集中度、真实性和可持续性

#### 🎓 学习探索型评分 (Innovation Explorer)

- **25%** 开发生涯深度评分 - 基于仓库数量推断的经验深度
- **30%** 技术多样性评分 - **基于 language_stats 的语言多样性和项目多样性**
- **25%** 学习活跃度评分 - 基于仓库创建活跃度的学习评估
- **20%** 成长潜力评分 - 技术广度和项目创造力的增长潜力

### 🧮 核心算法特性

#### 智能分层评分模型

- **非线性增长**：新手阶段每个贡献价值更高，专家阶段趋于平稳
- **时间因素考量**：考虑账号年龄、经验年限、持续性等时间维度
- **质量指标融合**：不仅看数量，更重视效率、平衡性、真实性
- **防刷数据机制**：避免通过大量低质量贡献获得高分

#### 评分精度优化

- **精确到小数点后 2 位**：使用 `Math.round(score * 100) / 100` 确保精度
- **统一配置管理**：从 `@/constants` 导入维度配置，确保一致性
- **动态标签映射**：使用 `getAllDimensionConfigs()` 获取最新的维度标签

#### 综合等级计算

基于领域突出度的智能判定算法：

| 等级  | 判定条件                  | 开发者类型   | 特征描述                         |
| ----- | ------------------------- | ------------ | -------------------------------- |
| **S** | 3-4 个领域突出 (≥80 分)   | 卓越贡献者   | 在多个领域都有出色表现，全面发展 |
| **A** | 2 个领域突出 (≥80 分)     | 双领域专家   | 在两个核心维度上达到专业水平     |
| **B** | 1 个领域突出 (≥80 分)     | 专精型贡献者 | 在特定维度上展现出专业实力       |
| **C** | 最高分未达 80 分但 ≥60 分 | 潜力型贡献者 | 各维度均衡发展，具备成长潜力     |
| **D** | 最高分<60 分              | 成长型贡献者 | 正在建立自己的贡献轨迹           |

---

## 🔧 系统配置

### 🗃️ 缓存策略

| 数据类型     | 缓存层级    | 缓存时间         | 失效策略 |
| ------------ | ----------- | ---------------- | -------- |
| 用户基础信息 | KV + 数据库 | 1 小时 + 24 小时 | 手动刷新 |
| 贡献统计     | KV + 数据库 | 1 小时 + 24 小时 | 自动更新 |
| 语言分析     | API 缓存    | **3 天**         | 懒加载   |
| 多维度评分   | 数据库      | 24 小时          | 计算触发 |

### 🌈 V4.1 语言多样性分析配置

#### 🔧 环境变量配置

| 环境变量                            | 默认值    | 生产值 | 说明                                  |
| ----------------------------------- | --------- | ------ | ------------------------------------- |
| `ENABLE_DETAILED_LANGUAGE_ANALYSIS` | `"false"` | `true` | 启用详细语言分析（消耗更多 API 调用） |
| `MAX_LANGUAGE_ANALYSIS_REPOS`       | `"50"`    | `"50"` | 最大分析仓库数                        |

#### 📋 分析流程配置

##### 第一阶段：基础语言分析（所有用户）

- **数据源**：仓库元数据中的 `language` 字段
- **API 消耗**：无额外 API 调用
- **分析内容**：
  - 主要语言识别
  - 语言数量统计
  - 基础仓库映射

##### 第二阶段：详细语言分析（条件触发）

- **触发条件**：
  - `ENABLE_DETAILED_LANGUAGE_ANALYSIS=true`
  - 且用户仓库数 ≤ 20 个
- **API 消耗**：每个仓库 1 次 `/repos/{owner}/{repo}/languages` 调用
- **限制策略**：
  - 最多分析前 15 个仓库
  - 请求间隔 60ms 防止限流
  - 缓存时间 3 天减少重复调用
- **分析内容**：
  - 精确字节数统计
  - 语言排名计算
  - 仓库级语言分布
  - 百分比精确计算

#### ⚡ 性能优化策略

- **智能缓存**：语言分析结果缓存 3 天
- **分层分析**：基础分析 + 可选详细分析
- **限流保护**：请求间隔、最大仓库数限制
- **容错机制**：详细分析失败时降级到基础分析

### 📊 数据版本管理

- **当前版本**: `dataVersion: 2`
- **特性**: 多维度评分 + GraphQL 贡献仓库数 + V4.3 完善评分算法 + **语言统计扩展**
- **兼容性**: 向下兼容 V3 数据

### 🎯 V4.3 calculateContributionScore 权重配置

#### 基础权重配置

| 指标          | 基准值 | 权重 | 说明                                             |
| ------------- | ------ | ---- | ------------------------------------------------ |
| STARS         | 1      | 0.28 | 项目技术认可度（略微降低为 public_repos 让权重） |
| COMMITS       | 50     | 0.22 | 代码提交活跃度（略微降低）                       |
| PULL_REQUESTS | 5      | 0.14 | 协作贡献能力                                     |
| FOLLOWERS     | 5      | 0.13 | 社区影响力                                       |
| ISSUES        | 5      | 0.05 | 问题发现与参与度                                 |
| REVIEWS       | 5      | 0.05 | 代码审查专业度                                   |

#### V4 新增权重配置

| 指标              | 基准值 | 权重  | 说明                                 |
| ----------------- | ------ | ----- | ------------------------------------ |
| TOTAL_FORKS       | 1      | 3     | 项目被复用程度                       |
| CONTRIBUTED_REPOS | 5      | 7     | 贡献仓库数（略微降低）               |
| LANGUAGE_STATS    | 3      | 4     | 技术栈广度（略微降低）               |
| **PUBLIC_REPOS**  | **8**  | **6** | **项目创造力和经验深度指标（新增）** |

#### 保留权重配置

| 指标        | 基准值 | 权重 | 说明                                   |
| ----------- | ------ | ---- | -------------------------------------- |
| FOLLOWING   | 25     | 4    | 学习态度指标                           |
| ACCOUNT_AGE | 2      | 7    | 账号年龄(年)，体现经验积累（略微降低） |

#### 评分维度分配

- **代码创造力评分**: COMMITS + CONTRIBUTED_REPOS + LANGUAGE_STATS + **PUBLIC_REPOS**
- **协作交流评分**: PULL_REQUESTS + REVIEWS + ISSUES
- **影响力评分**: STARS + TOTAL_FORKS + FOLLOWERS
- **经验成长评分**: ACCOUNT_AGE
- **学习探索评分**: FOLLOWING

### 🔄 算法变更记录

#### V4.3 (2024-12-19) - calculateContributionScore 算法完善

- ✅ **修复 public_repos 参数未使用问题**: 将 public_repos 正式纳入评分计算
- ✅ **新增项目创造力指标**: PUBLIC_REPOS (基准值: 8, 权重: 6) 评估开发者项目创造力和经验深度
- ✅ **权重配置重新平衡**: 调整各指标权重确保算法平衡性
  - STARS: 0.3 → 0.28, COMMITS: 0.25 → 0.22
  - CONTRIBUTED_REPOS: 8 → 7, LANGUAGE_STATS: 5 → 4, ACCOUNT_AGE: 8 → 7
- ✅ **算法一致性提升**: calculateContributionScore 现在使用所有传入参数，与 V4 多维度算法保持一致
- ✅ **评分维度增强**: 代码创造力维度新增 publicRepos 考量，提供更全面的开发者画像

#### V4.2 (2024-12-19) - 智能评分算法升级

- ✅ **算法架构重构**: 从简单对数增长模型升级为多层次智能评分算法
- ✅ **维度标签优化**: 更新为更具荣誉感的称号 (Code Architect, Community Builder 等)
- ✅ **评分精度提升**: 精确到小数点后 2 位，提供更准确的评分
- ✅ **配置统一管理**: 使用 `@/constants` 统一管理维度配置
- ✅ **时间因素考量**: 算法考虑经验年限、持续性等时间维度
- ✅ **质量指标融合**: 不仅看数量，更重视效率、平衡性、真实性
- ✅ **防刷数据机制**: 避免通过大量低质量贡献获得高分

## 📚 参考资源

### 📁 核心代码文件

- `lib/github/fetch.ts` - 数据获取实现（**V4.1 重构**）
- `lib/github/score.ts` - 评分算法
- `lib/github/token-manager.ts` - Token 管理
- `lib/db/schema.ts` - 数据库结构（**V4.1 升级**）
- `types/github.ts` - 类型定义（**V4.1 扩展**）
- `lib/adapters/multidimension-adapter.ts` - 数据适配器（**V4.1 更新**）

### 🗄️ 数据库迁移文件

- `drizzle/migrations/0011_last_midnight.sql` - V4.1 语言统计升级迁移
- `drizzle/migrations/meta/0011_snapshot.json` - 最新数据库结构快照

### ⚙️ 配置文件

- `example.env` - 环境变量模板（包含语言分析配置）
- `wrangler.jsonc` - Cloudflare Workers 配置（生产环境变量）

---

_📝 本文档记录项目核心数据结构和算法概述，V4.1 版本增加了语言统计系统的全面升级_
