# V4 产品需求

调研利用 GitHub 接口可以获得的公开数据，如何尽可能有效呈现一个 GitHub 用户的开发贡献度，从而针对工程师中的高自我认同感群体开发一款“码力”测试工具，测试方案可以针对不同类型群体分别设计，尽量让更多的人得出漂亮的分数。最终将测试结果升成美观的卡片或链接，用于社交分享，并形成裂变效应。帮我做必要的调研，并生成完整的设计方案。目标用户群体包括 Github 个人用户和组织账号；针对多种维度分别设计得分算法，便于用户在自己擅长的领域得到高分。

# API 数据调研结果

- **用户信息**：通过 REST API 的 `GET /users/{username}` 可获取用户公开资料字段，包括公开仓库数 (`public_repos`)、粉丝数 (`followers`)、关注数 (`following`) 等。例如示例响应中 `"public_repos": 2, "followers": 20, "following": 0`。这些字段可直接反映用户的项目规模和社交影响。
- **仓库数据**：使用 `GET /users/{username}/repos` 或组织仓库接口可列出用户所有仓库。每个仓库返回详细信息，包括主语言 (`language`)、星标数 (`stargazers_count`)、被 watchers 数 (`watchers_count`)、Fork 数 (`forks_count`)、开放 Issue 数 (`open_issues_count`) 等。例如：`"forks_count": 9, "stargazers_count": 80, "watchers_count": 80, "open_issues_count": 0`。这些数据可统计用户仓库总星标、Fork、语言分布等。
- **贡献统计 (GraphQL)**：GitHub REST API 不直接返回贡献统计（如总提交数、总 Issue 数等），需要使用 GraphQL。`user { contributionsCollection(from:…, to:…) { totalCommitContributions, totalIssueContributions, totalPullRequestContributions, totalPullRequestReviewContributions } }` 查询可获得用户在指定时间段内的提交、Issue、PR 和 PR 评审总数。注意时间范围最长一年，若需更长统计需多次请求。另可通过 `contributionCalendar` 获取贡献日历详情。此方式是获取年度贡献的主要途径。
- **参与仓库数**：GraphQL 的 `user { contributionsCollection { totalRepositoriesWithContributedCommits } }` 等字段可统计用户参与过提交的仓库总数（包括自身仓库）。但社区讨论指出，`repositoriesContributedTo` 连接仅返回**过去一年**内有贡献的仓库；若需全部时间的贡献仓库数，目前只能自行跨年查询或筛选排除自身仓库。
- **语言使用**：GitHub 没有直接的“用户所用语言” API，但可以遍历用户仓库并调用 `/repos/{owner}/{repo}/languages` 接口获取每个仓库的语言及代码字节数。例如：`GET /repos/dotnet/corefx/languages` 返回 `{ "C#": 131055040, "C": 1078381, … }`。汇总所有仓库的语言统计可了解用户的技术栈多样性。
- **社交和影响**：通过 API 可获取用户的粉丝数，以及用户仓库的星标总和。REST 无法直接返回用户所有仓库的星标总数，需要遍历仓库列表累加 `stargazers_count`。类似地，可统计仓库被 Fork 数、订阅者（watchers）数等指标。

综上，可利用 GitHub 公共 API 获得多维度数据：提交、Issue、PR、评论评审数、粉丝/关注数、仓库星标/叉数、所用语言、贡献仓库数等。合理组合这些数据，可全面评估开发者的不同类型贡献。

## 维度与评分算法设计

根据开发者类型划分维度，分别设计评分：

- **代码提交型（Commit-Oriented）**：指标包括提交次数和贡献仓库数（可反映活跃度和广度）。算法示例：令 `C = log10(总提交数 + 1)`，`R = log10(贡献仓库数 + 1)`，得分 = `W1·C + W2·R`（W1、W2 为权重）。采用对数处理避免极端值过度拉分，保证大部分用户有较好分数。比如预设满分 100，选取使得中位用户达到 70 分左右，使得多用户可获“漂亮”分数。
- **协作交流型（Collaboration-Oriented）**：指标包括发起和合并的 PR 数、打开/关闭的 Issue 数、PR 评审数（代码审查贡献）。算法示例：每个 PR 计 2 分，每个 Issue 计 1.5 分，每次评审计 1 分，总分累加后再进行归一化（如除以最大值乘 100）。通过组合统计，让积极参与项目交流和协作的用户得分更高。
- **开源影响型（Open-Source-Influence）**：指标包括用户仓库获得的星标总数、被 Fork 数，以及粉丝数。算法示例：可以加权和计算：`stars_score = log(总星标+1)`；`forks_score = log(总Fork+1)`；`followers_score = log(粉丝数+1)`，然后线性组合（如各占总分的 33%）得到影响分。这样能衡量个人项目受欢迎程度和社交影响力。为了提高用户分享欲望，可将综合影响分映射为级别（如钻石/铂金/黄金）而非原始数值，看起来更“酷”。
- **学习探索型（Learning/Exploration）**：指标包括使用不同编程语言的数量、所参与的创新项目或主题标签多样性、参与文档/讨论数等。算法示例：`language_diversity = 统计不同语言个数`，`topics_count = 统计独特仓库标签数`，将其归一化（如满分 100）。例如使用语言越多、参与项目越广泛者分数更高，奖励多领域学习。

每个维度得分可归一化到固定区间（如 0–100）或等级（A/B/C），并确保分布合理：比如使用百分位策略或指数曲线，使得 60–80% 的用户可获中高档评价（A/B），极少数达到最高级（S 级）。这样既鼓励分享，也防止大部分用户得分过低而失去动力。最后将各维度加权求和（或打分卡统计）生成综合得分，并映射为称号/等级。例如依据总分设定 S/A/B/C 级别，或参考日本学制 A+、A、B 等分级模式。

## 可视化设计建议

- **卡片布局**：设计用于社交媒体分享的图片卡片（如宽度约 1200px，高 630px），包括用户头像、用户名、总等级/称号和各维度得分。可采用柱状图、雷达图或进度条等形式展示各维度分数，并用图标标注（如提交用代码图标、Issue 用信息图标、星标用星星图标等）。整体风格应简洁明亮，突出用户头像和高分项，易于在各种设备上阅读。
- **色彩和主题**：使用清晰的配色区分不同维度（如每个维度配一种颜色），并在用户达到高分等级时添加金色/粉色等高亮元素增加成就感。可以提供多种主题（深色、浅色或品牌色）以适应不同背景。背景可用渐变或图形元素增强科技感，但避免信息过载。
- **动态元素**：虽然输出为静态图片，可考虑在在线预览页面使用动效（如分数增长动画、触发效果），提高趣味性。但社交分享时以静图为主。
- **示例布局**：可参考竞品，如 Readme Stats 的卡片样式，或 Profile Trophy 的徽章布局。例如在卡片上方显示用户头像和总等级，下方分列展示四个维度得分，每项旁标注进度条与数值。卡片底部可小字标注生成时间和分享链接。

## 分享机制设计

- **图片卡片**：为每个用户生成独立的可分享图片卡片，支持 PNG/SVG 格式，可嵌入社交平台帖子或个人博客。如 GitHub README Stats 的用法，用户通过一段 Markdown 即可插入卡片。提供**公开 URL** 访问图片，方便复制粘贴或链接分享。
- **分享链接**：生成包含卡片的分享页面，并配置 Open Graph meta 标签，以便分享到微信、微博、Twitter 等社交平台时自动显示卡片预览（图像、标题、描述）。链接可类似 `https://example.com/{username}`。页面上可显示全数据详情，增强互动性。
- **社交媒体集成**：提供一键分享按钮（如微信扫一扫、微博分享、Twitter 分享），鼓励用户直接发布结果。可考虑预设分享文案模板（例如“我的 GitHub 码力达到了 X 级！查看我的图谱…”），增强传播效果。
- **鼓励机制**：增加游戏化元素，例如完成分享后获得额外“徽章”或解锁更多主题样式。同时可以展示用户排行，让用户乐于与朋友比拼。

## 竞品对比

- **GitHub Readme Stats**：该工具生成 GitHub 统计卡片，显示星标、提交、PR、Issue、贡献数等。它采用日本学制分级(A+/A/B+等)和统计百分位数来给出综合等级。优点是简单直接，易嵌入 README；缺点是设计较为朴素、交互性低，且仅公开数据，个性化不足。
- **Profile Trophy**：生成一系列“奖杯”徽章显示用户的各项成就（如星标、Follower、Language 等），并用 SSS、SS、S 等等级划分。视觉上有趣，带有游戏化徽章元素，但缺乏多维评分体系，仅提供单一维度的奖杯，定制化和反馈有限。
- **超越建议**：我们可以结合以上优点并增强创新：在视觉上提供更加现代和信息丰富的卡片（例如多维统计图表、进度环），而非单纯图标徽章；在玩法上引入多类型工程师维度及等级系统，让用户看到自己的强项与弱项；在反馈机制上提供改进建议、成长提示（如“再贡献 10 次 PR 可升级为 S 级”）。同时可设计丰富的主题模板和动态效果，提高与竞品的差异化吸引力。

\*\*图 1：\*\*示例——GitHub Profile Trophy 的徽章设计。Profile Trophy 用奖杯图标和等级标签（如 SSS/SS/S）来展示各项统计，具有较强的游戏化风格，但对不同开发者类型的多维度展现较为有限。

## API 限制与技术实现建议

- **调用频率限制**：GitHub REST API 未认证时每小时限 60 次，认证用户限 5,000 次；GraphQL API 默认为每小时 5,000 积分（查询复杂度越高耗积分越多）。需要使用 GitHub Access Token 提高限额，并合理控制请求频率。建议服务端使用缓存（如 Redis）保存用户统计结果，定期（如每小时或每天）更新，以避免频繁调用 API 导致限流。竞品也采用了类似缓存策略来防限流。
- **分页策略**：REST 列表接口每页最多 100 条，应根据响应头的 `Link` 字段分页获取所有数据（循环请求下一页直至完成）；GraphQL 采用游标分页，需在连接类型（如仓库列表、贡献仓库列表等）中使用 `first: 100` 并检查 `pageInfo.hasNextPage`，再使用 `after` 继续查询。例如获取所有仓库时可在 `user.repositories(first:100, after:CURSOR)` 中循环请求。
- **缓存机制**：建议前端调用时先查询后端缓存或数据库，如过期则异步刷新。可对用户数据建立最近更新时间戳，若用户近期未更新则直接用缓存展示，减轻实时计算压力。针对热门用户或批量请求，可定期批处理更新数据。
- **并发与错误处理**：对大量用户同时请求，应限制并发量，避免触发 API 的二级限速（secondary rate limits）或超时。可以对个别查询超时后重试，并在后台排队重算。
- **安全性**：对于需要获取私有数据的功能，应要求用户授权（提供 Token）；公开分享则仅展示公共统计。
- **技术框架**：推荐使用支持并发 HTTP 请求和图像生成的技术栈（如 Node.js + Canvas、Deno、或后端框架），并部署在可扩展的环境（如云函数或 Docker 容器）以应对流量。使用 GitHub Actions 自托管实例可以避免公共实例限流。
- **可扩展性**：设计时考虑后续支持扩展指标，如贡献地图、社区讨论等。利用 GitHub GraphQL 的灵活性，可随时增加字段查询而不破坏现有接口。

通过以上设计，结合丰富的 API 数据和对开发者心理的理解，可构建一个既科学公正又具有游戏化和分享属性的“码力”测试工具，激发用户的参与和传播热情。
