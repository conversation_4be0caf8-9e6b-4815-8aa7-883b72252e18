# Browser Rendering API 技术实现备忘

## 项目背景

为 GitHub Card 项目的 Pro 用户提供卡片图片 API 服务，基于 Cloudflare Browser Rendering API 实现服务端截图生成，支持嵌入到 Markdown、博客等在线文档中。

**核心理念**: 保留前端生成逻辑，新增 Pro API 功能作为差异化服务。

## 实现流程

```
API请求 → Pro用户验证 → /generate页面(隐藏UI) → Browser截图 → R2存储 → 重定向图片URL
```

## 核心配置

### 1. wrangler.jsonc 配置更新

```json
{
  // 新增 Browser Rendering 配置
  "browser": {
    "binding": "BROWSER"
  },
  
  // 新增 Durable Objects 配置
  "durable_objects": {
    "bindings": [
      {
        "name": "BROWSER_SESSION",
        "class_name": "BrowserSession"
      }
    ]
  },
  
  // 数据库迁移配置
  "migrations": [
    {
      "tag": "v2", 
      "new_sqlite_classes": ["BrowserSession"]
    }
  ]
}
```

### 2. package.json 依赖

```json
{
  "dependencies": {
    "@cloudflare/puppeteer": "^0.0.8"
  }
}
```

## Pro API 实现要点

### 1. API 端点设计

```
GET /api/card/{username}?token={pro_token}&style={style}&theme={theme}&refresh={boolean}
```

**参数说明**:
- `username`: GitHub用户名（必需）
- `token`: Pro用户API Token（必需）
- `style`: 卡片样式（可选）
- `theme`: 主题色彩（可选）
- `refresh`: 强制刷新缓存（可选）

### 2. 核心实现逻辑

#### 步骤1: Pro用户验证
```typescript
// 验证API Token有效性和Pro用户身份
const isValidProUser = await validateProToken(token);
if (!isValidProUser) {
  return new Response('Unauthorized', { status: 401 });
}
```

#### 步骤2: 生成页面准备
```typescript
// 构造内部generate页面URL，添加api=true标识
const generateUrl = `/generate?api=true&username=${username}&style=${style}&theme=${theme}`;
```

#### 步骤3: /generate页面UI隐藏逻辑
```typescript
// 在generate页面检测api参数
const searchParams = new URLSearchParams(window.location.search);
const isApiMode = searchParams.get('api') === 'true';

if (isApiMode) {
  // 隐藏所有操作按钮和菜单
  document.querySelectorAll('.action-buttons, .menu, .share-controls').forEach(el => {
    el.style.display = 'none';
  });
  
  // 优化布局用于截图
  document.body.classList.add('api-screenshot-mode');
}
```

#### 步骤4: Browser截图生成
```typescript
// 使用Durable Object生成截图
const browserSession = env.BROWSER_SESSION.get(id);
const screenshot = await browserSession.generateScreenshot(generateUrl, options);
```

#### 步骤5: R2存储和缓存
```typescript
// 生成缓存键
const cacheKey = `api_card:${username}:${style}:${theme}:${dataHash}`;

// 检查缓存
if (!refresh) {
  const cached = await getCachedImage(cacheKey);
  if (cached) return Response.redirect(cached.url);
}

// 存储到R2
const r2Key = `api-cards/${cacheKey}.png`;
await env.GITHUB_CARD_R2.put(r2Key, screenshot);

// 返回重定向
const imageUrl = `${env.R2_PUBLIC_URL}/${r2Key}`;
return Response.redirect(imageUrl, 302);
```

### 3. 缓存策略

- **缓存键格式**: `api_card:{username}:{style}:{theme}:{data_hash}`
- **缓存时间**: 1小时默认，最短5分钟
- **强制刷新**: `refresh=true`参数忽略缓存
- **存储位置**: KV存储缓存元数据，R2存储图片文件

## 关键技术实现

### 1. Durable Object 实现

```typescript
// lib/browser-session.ts
export class BrowserSession {
  private browser: Browser | null = null;
  private keepAliveSeconds = 0;
  private readonly KEEP_ALIVE_TIMEOUT = 60;
  
  constructor(private state: DurableObjectState, private env: Env) {}

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    
    if (url.pathname === '/screenshot' && request.method === 'POST') {
      return this.handleScreenshot(request);
    }
    
    return new Response('Not Found', { status: 404 });
  }

  private async handleScreenshot(request: Request): Promise<Response> {
    const { pageUrl, options = {} } = await request.json();
    
    // 确保浏览器实例可用
    await this.ensureBrowser();
    
    const page = await this.browser!.newPage();
    
    // 设置视口
    await page.setViewport({
      width: options.width || 1200,
      height: options.height || 800,
      deviceScaleFactor: 2
    });
    
    // 访问页面
    await page.goto(pageUrl, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });
    
    // 等待页面完全加载
    await page.waitForTimeout(2000);
    
    // 生成截图
    const screenshot = await page.screenshot({
      type: 'png',
      fullPage: true,
      omitBackground: true
    });
    
    await page.close();
    this.resetKeepAlive();
    
    return new Response(screenshot, {
      headers: { 'Content-Type': 'image/png' }
    });
  }

  private async ensureBrowser(): Promise<void> {
    if (!this.browser || !this.browser.isConnected()) {
      this.browser = await puppeteer.launch(this.env.BROWSER);
    }
  }

  private resetKeepAlive(): void {
    this.keepAliveSeconds = 0;
    this.scheduleAlarm();
  }

  private async scheduleAlarm(): Promise<void> {
    const currentAlarm = await this.state.storage.getAlarm();
    if (!currentAlarm) {
      await this.state.storage.setAlarm(Date.now() + 10000);
    }
  }

  async alarm(): Promise<void> {
    this.keepAliveSeconds += 10;
    
    if (this.keepAliveSeconds < this.KEEP_ALIVE_TIMEOUT) {
      await this.state.storage.setAlarm(Date.now() + 10000);
    } else {
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    }
  }
}
```

### 2. API 路由实现

```typescript
// app/api/card/[username]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCloudflareContext } from '@opennextjs/cloudflare';

export async function GET(
  request: NextRequest,
  { params }: { params: { username: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const style = searchParams.get('style') || 'default';
    const theme = searchParams.get('theme') || 'light';
    const refresh = searchParams.get('refresh') === 'true';
    
    // 验证Pro用户
    if (!token) {
      return NextResponse.json({ error: 'Token required' }, { status: 401 });
    }
    
    const { env } = await getCloudflareContext();
    const isValidPro = await validateProToken(token, env);
    
    if (!isValidPro) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
    
    // 检查频率限制
    const rateLimitOk = await checkRateLimit(token, env);
    if (!rateLimitOk) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }
    
    // 生成缓存键
    const dataHash = await generateDataHash(params.username);
    const cacheKey = `api_card:${params.username}:${style}:${theme}:${dataHash}`;
    
    // 检查缓存
    if (!refresh) {
      const cachedUrl = await getCachedImageUrl(cacheKey, env);
      if (cachedUrl) {
        return NextResponse.redirect(cachedUrl);
      }
    }
    
    // 生成截图
    const generateUrl = `${request.nextUrl.origin}/generate?api=true&username=${params.username}&style=${style}&theme=${theme}`;
    
    const browserSessionId = env.BROWSER_SESSION.idFromName('github-card-browser');
    const browserSession = env.BROWSER_SESSION.get(browserSessionId);
    
    const screenshotResponse = await browserSession.fetch(
      new Request('https://dummy.com/screenshot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pageUrl: generateUrl,
          options: { width: 1200, height: 800 }
        })
      })
    );
    
    if (!screenshotResponse.ok) {
      throw new Error('Screenshot generation failed');
    }
    
    const screenshot = await screenshotResponse.arrayBuffer();
    
    // 存储到R2
    const r2Key = `api-cards/${cacheKey}.png`;
    await env.GITHUB_CARD_R2.put(r2Key, screenshot, {
      httpMetadata: {
        contentType: 'image/png',
        cacheControl: 'public, max-age=3600'
      }
    });
    
    const imageUrl = `${env.R2_PUBLIC_URL}/${r2Key}`;
    
    // 缓存URL
    await setCachedImageUrl(cacheKey, imageUrl, env);
    
    // 记录使用统计
    await recordApiUsage(token, env);
    
    return NextResponse.redirect(imageUrl);
    
  } catch (error) {
    console.error('API card generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 3. 辅助函数

```typescript
// lib/pro-api-utils.ts

export async function validateProToken(token: string, env: Env): Promise<boolean> {
  // 验证token格式
  if (!token.startsWith('ghc_') || token.length !== 36) {
    return false;
  }
  
  // 从数据库验证token和用户Pro状态
  const result = await env.DB.prepare(
    'SELECT users.subscription_status FROM api_tokens JOIN users ON api_tokens.user_id = users.id WHERE api_tokens.token = ? AND api_tokens.active = 1'
  ).bind(token).first();
  
  return result?.subscription_status === 'pro';
}

export async function checkRateLimit(token: string, env: Env): Promise<boolean> {
  const key = `rate_limit:${token}`;
  const now = Date.now();
  const windowStart = now - 60000; // 1分钟窗口
  
  const existing = await env.GITHUB_CARD_KV.get(key);
  let requests: number[] = existing ? JSON.parse(existing) : [];
  
  // 清理过期请求
  requests = requests.filter(timestamp => timestamp > windowStart);
  
  // 检查限制（每分钟60次）
  if (requests.length >= 60) {
    return false;
  }
  
  // 记录新请求
  requests.push(now);
  await env.GITHUB_CARD_KV.put(key, JSON.stringify(requests), {
    expirationTtl: 60
  });
  
  return true;
}

export async function generateDataHash(username: string): Promise<string> {
  // 基于用户最新数据生成hash，用于缓存失效
  // 这里可以包含用户的最后更新时间、贡献数据等
  const data = `${username}:${Date.now()}`;
  const encoder = new TextEncoder();
  const hash = await crypto.subtle.digest('SHA-256', encoder.encode(data));
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
    .substring(0, 16);
}

export async function getCachedImageUrl(cacheKey: string, env: Env): Promise<string | null> {
  try {
    const cached = await env.GITHUB_CARD_KV.get(`img_cache:${cacheKey}`);
    if (cached) {
      const { url, timestamp } = JSON.parse(cached);
      // 检查是否过期（1小时）
      if (Date.now() - timestamp < 3600000) {
        return url;
      }
    }
  } catch (error) {
    console.warn('Cache retrieval failed:', error);
  }
  return null;
}

export async function setCachedImageUrl(cacheKey: string, url: string, env: Env): Promise<void> {
  try {
    const cacheData = { url, timestamp: Date.now() };
    await env.GITHUB_CARD_KV.put(
      `img_cache:${cacheKey}`,
      JSON.stringify(cacheData),
      { expirationTtl: 3600 }
    );
  } catch (error) {
    console.warn('Cache storage failed:', error);
  }
}

export async function recordApiUsage(token: string, env: Env): Promise<void> {
  // 记录API使用统计，用于Dashboard显示
  try {
    await env.DB.prepare(
      'INSERT INTO api_usage_logs (token, timestamp, endpoint) VALUES (?, ?, ?)'
    ).bind(token, Date.now(), 'card_generation').run();
  } catch (error) {
    console.warn('Usage recording failed:', error);
  }
}
```

## 性能和限制

### 服务限制
- **并发浏览器**: 10个（Paid计划）
- **请求频率**: 每用户每分钟60次
- **浏览器超时**: 60秒自动关闭
- **截图超时**: 30秒页面加载超时

### 性能目标
- **首次生成**: < 5秒
- **缓存命中**: < 500ms
- **图片大小**: < 500KB
- **可用性**: 99.9%

## 错误处理

### 常见错误场景
1. **Token无效**: 返回401状态码
2. **频率超限**: 返回429状态码  
3. **用户不存在**: 返回404或默认占位图
4. **服务异常**: 返回500状态码
5. **浏览器超时**: 重试机制

### 降级策略
- 保留现有前端生成功能作为备选
- 错误时返回静态占位图片
- 监控告警和自动恢复

---

*文档版本：v1.0*  
*创建时间：2025-01-24*  
*最后更新：2025-01-24*
