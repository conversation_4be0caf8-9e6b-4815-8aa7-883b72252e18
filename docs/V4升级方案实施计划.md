[MODE: PLAN]

基于前期调研和创新方案，我将制定一个详细的 V4 升级实施计划。

## 项目升级总体架构

### 核心目标

1. 实现四维度开发者评分系统
2. 建立稳定的 API 限流和缓存机制
3. 保证数据一致性和系统性能
4. 渐进式升级，最小化风险

### 技术栈扩展

- 保持现有 Next.js + Cloudflare Workers 架构
- 扩展数据库 schema 支持多维度数据
- 增强 GitHub API 集成能力
- 优化缓存和性能管理

## 详细实施计划

### 阶段一：基础设施升级（第 1-2 周）

#### 1.1 数据库 Schema 扩展

**文件路径**: `lib/db/schema.ts`
**修改内容**:

- 扩展`contributeDatas`表，添加新字段：
  - `totalForks: integer` - 总 Fork 数
  - `contributedRepos: integer` - 贡献仓库数
  - `languageDiversity: integer` - 编程语言多样性
  - `dataVersion: integer` - 数据版本号
  - `lastFullUpdate: integer` - 最后完整更新时间
  - `updateStatus: text` - 更新状态（pending/updating/completed/failed）

#### 1.2 API 限流控制器实现

**新建文件**: `lib/github/rate-limiter.ts`
**功能实现**:

- 令牌桶算法实现
- 多 Token 管理和轮换
- API 调用统计和监控
- 降级策略处理

**新建文件**: `lib/github/token-manager.ts`
**功能实现**:

- GitHub Token 池管理
- Token 健康检查
- 自动故障转移

#### 1.3 数据版本控制系统

**新建文件**: `lib/data/version-manager.ts`
**功能实现**:

- 数据版本控制
- 向后兼容性处理
- 数据迁移工具

### 阶段二：多维度评分系统（第 3-4 周）

#### 2.1 评分算法重构

**修改文件**: `lib/github/score.ts`
**新增功能**:

- 四维度评分算法：
  - `calculateCommitScore()` - 代码提交型评分
  - `calculateCollaborationScore()` - 协作交流型评分
  - `calculateInfluenceScore()` - 开源影响型评分
  - `calculateExplorationScore()` - 学习探索型评分
- 综合评分计算和等级映射

#### 2.2 增强数据获取逻辑

**修改文件**: `lib/github/fetch.ts`
**新增功能**:

- Fork 数统计
- 贡献仓库数计算
- 编程语言多样性统计（可选，基于 API 配额）
- 智能数据获取策略

#### 2.3 数据类型扩展

**修改文件**: `lib/github/types.ts`
**新增类型**:

```typescript
export interface MultiDimensionScore {
  commitScore: number;
  collaborationScore: number;
  influenceScore: number;
  explorationScore: number;
  overallGrade: string;
}

export interface ExtendedGitHubData extends GitHubData {
  totalForks: number;
  contributedRepos: number;
  languageDiversity: number;
  multiDimensionScore: MultiDimensionScore;
}
```

### 阶段三：异步更新系统（第 5-6 周）

#### 3.1 后台数据更新服务

**新建文件**: `app/api/cron/update-user-data/route.ts`
**功能实现**:

- 批量用户数据更新
- 优先级队列管理
- 错误处理和重试机制

#### 3.2 用户主动刷新功能

**新建文件**: `app/api/user/refresh-data/route.ts`
**功能实现**:

- 用户主动触发数据更新
- 冷却期控制
- 实时进度反馈

#### 3.3 数据一致性管理

**新建文件**: `lib/data/consistency-manager.ts`
**功能实现**:

- 数据完整性验证
- 自动修复机制
- 增量更新逻辑

### 阶段四：前端界面升级（第 7-8 周）

#### 4.1 多维度卡片组件

**修改文件**: `components/cards/`
**新增组件**:

- `MultiDimensionCard.tsx` - 四维度展示卡片
- `ScoreRadarChart.tsx` - 雷达图展示组件
- `DimensionProgressBar.tsx` - 维度进度条

#### 4.2 用户控制面板

**新建文件**: `components/user/DataControlPanel.tsx`
**功能实现**:

- 数据刷新控制
- 更新状态显示
- 个性化设置

#### 4.3 分享功能增强

**修改文件**: `app/api/share-links/`
**功能扩展**:

- 支持多维度卡片分享
- 新增分享模板
- 优化分享链接生成

### 阶段五：性能优化和监控（第 9-10 周）

#### 5.1 缓存策略优化

**修改文件**: `lib/cloudflare/kv-cache-manager.ts`
**优化内容**:

- 多层缓存实现
- 智能缓存失效
- 预热机制

#### 5.2 监控和分析系统

**新建文件**: `lib/monitoring/`
**功能实现**:

- API 使用情况监控
- 数据质量分析
- 性能指标收集

#### 5.3 错误处理和日志

**新建文件**: `lib/logging/error-handler.ts`
**功能实现**:

- 统一错误处理
- 详细日志记录
- 告警机制

## 数据库迁移计划

### 迁移脚本设计

**新建文件**: `drizzle/migrations/v4-upgrade.sql`
**迁移内容**:

1. 添加新字段到`contribute_datas`表
2. 为现有数据设置默认值
3. 创建索引优化查询性能
4. 数据完整性约束

### 数据重新计算策略

1. 分批处理现有用户数据
2. 优先处理活跃用户
3. 后台异步重新计算评分
4. 保留原有数据作为备份

## 测试策略

### 单元测试

**新建目录**: `__tests__/`
**测试覆盖**:

- API 限流逻辑测试
- 评分算法准确性测试
- 数据一致性验证测试

### 集成测试

- GitHub API 集成测试
- 数据库操作测试
- 缓存机制测试

### 性能测试

- API 调用性能测试
- 并发用户处理测试
- 缓存效率测试

## 实施清单

1. 设计和实现数据库 schema 扩展
2. 开发 API 限流控制器和 Token 管理器
3. 实现数据版本控制系统
4. 重构多维度评分算法
5. 扩展 GitHub 数据获取逻辑
6. 开发异步数据更新系统
7. 实现用户主动刷新功能
8. 建立数据一致性管理机制
9. 开发多维度前端展示组件
10. 优化缓存策略和性能
11. 实现监控和日志系统
12. 执行数据库迁移
13. 进行全面测试
14. 灰度发布和监控
15. 全量发布和后续优化

此计划确保了系统的稳定性、可扩展性和用户体验，同时最小化了升级风险。每个阶段都有明确的交付物和验收标准，便于项目管理和质量控制。
