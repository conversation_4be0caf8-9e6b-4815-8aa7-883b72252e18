// wrangler.jsonc - Cloudflare Pages 配置文件
// wrangler.jsonc (wrangler v3.88.0^)
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "github-card",
  "compatibility_date": "2025-04-02",
  "main": ".open-next/worker.js",
  "compatibility_flags": [
    "nodejs_compat",
    "global_fetch_strictly_public"
  ],
  "observability": {
    "enabled": true
  },
  // 静态资源路由配置
  "assets": {
    "directory": ".open-next/assets",
    "binding": "ASSETS"
  },
  // 添加 OpenNext 所需的服务引用
  "services": [
    {
      "binding": "WORKER_SELF_REFERENCE",
      "service": "github-card"
    }
  ],
  // KV 命名空间配置
  "kv_namespaces": [
    {
      "binding": "GITHUB_CARD_KV",
      "id": "ee963ddf7c4d40c5bf12bb090b0c46fb"
    }
  ],
  // D1 数据库配置
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "github-card-db",
      "database_id": "03bbbfb8-d68a-4528-978e-74bd50742dea",
      "migrations_dir": "drizzle/migrations"
    }
  ],
  // R2 对象存储配置
  "r2_buckets": [
    {
      "binding": "GITHUB_CARD_R2",
      "bucket_name": "github-card-images"
    }
  ],
  "vars": {
    "NEXTAUTH_URL": "https://github-card.refined-x.workers.dev",
    "NEXTAUTH_SECRET": "2wRxxrhdtW4tvZLZM9VEc36bh+rfwTf4uRxgFeEiF/I=",
    "GITHUB_ID": "********************",
    "GITHUB_SECRET": "****************************************",
    "GITHUB_TOKENS": "*********************************************************************************************,*********************************************************************************************",
    "STRIPE_SECRET_KEY": "sk_test_51RLyT2HFsKiAaOeKjMuEnYHGQKrfBtVMv9HUtwbf6omOto7ZzomeUtRDk1roMNHxkw2cvktB3yOc6AzjW5fGmkS400T3eQzHbY",
    "STRIPE_WEBHOOK_SECRET": "whsec_9HYszJrgrYGhepM0m2YAA4Drx4kkLgKH",
    "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "pk_test_51RLyT2HFsKiAaOeKxuQLQlEiINfi6qxP7DD71a19RfoUOTdvf2TDOp54lSoh7SyAc99Vz0Xwcmuk3DnFwl7P496v001Jcvz36H",
    "ENABLE_DETAILED_LANGUAGE_ANALYSIS": "true",
    "MAX_LANGUAGE_ANALYSIS_REPOS": "50",
    "PEXELS_API_KEY": "sdCKHrhpHihjmJodJGB0ZDdreTEeR4RvGIJoRWtiq27SEsf3w4cJdTG8",
    "PIXABAY_API_KEY": "**********************************",
    "UNSPLASH_ACCESS_KEY": "lqn-C5Aar2v1lr0sitPWqO8BhxtO0ewDfnqFz28oPYw",
    "REFRESH_CACHE_TOKEN": "123",
    "R2_PUBLIC_URL": "https://pub-7fc4aeafd81144928c2b7700b70025da.r2.dev",
    "DOUBAO_API_KEY": "2842976b-8294-4e0d-bf4e-36f271099168",
    "OPENROUTER_API_KEY": "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",
    "AI_GATEWAY_API_KEY": "IRstAQECtInKRlCculN87K0B",
  },
  "triggers": {
    "crons": [
      "0 0 * * *"
    ]
  },
  "env": {
    "dev": {
      "services": [
        {
          "binding": "WORKER_SELF_REFERENCE",
          "service": "github-card"
        }
      ],
      "kv_namespaces": [
        {
          "binding": "GITHUB_CARD_KV",
          "id": "ee963ddf7c4d40c5bf12bb090b0c46fb"
        }
      ],
      "d1_databases": [
        {
          "binding": "DB",
          "database_name": "github-card-db",
          "database_id": "03bbbfb8-d68a-4528-978e-74bd50742dea",
          "migrations_dir": "drizzle/migrations"
        }
      ],
      "r2_buckets": [
        {
          "binding": "GITHUB_CARD_R2",
          "bucket_name": "github-card-images"
        }
      ],
      "vars": {
        // 本地开发环境变量
        "NEXTAUTH_URL": "http://localhost:3000",
        "NEXTAUTH_SECRET": "2wRxxrhdtW4tvZLZM9VEc36bh+rfwTf4uRxgFeEiF/I=",
        "GITHUB_ID": "********************",
        "GITHUB_SECRET": "****************************************",
        "GITHUB_TOKENS": "*********************************************************************************************,*********************************************************************************************",
        "STRIPE_SECRET_KEY": "sk_test_51RLyT2HFsKiAaOeKjMuEnYHGQKrfBtVMv9HUtwbf6omOto7ZzomeUtRDk1roMNHxkw2cvktB3yOc6AzjW5fGmkS400T3eQzHbY",
        "STRIPE_WEBHOOK_SECRET": "whsec_9HYszJrgrYGhepM0m2YAA4Drx4kkLgKH",
        "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "pk_test_51RLyT2HFsKiAaOeKxuQLQlEiINfi6qxP7DD71a19RfoUOTdvf2TDOp54lSoh7SyAc99Vz0Xwcmuk3DnFwl7P496v001Jcvz36H",
        "ENABLE_DETAILED_LANGUAGE_ANALYSIS": "true",
        "MAX_LANGUAGE_ANALYSIS_REPOS": "50",
        "PEXELS_API_KEY": "sdCKHrhpHihjmJodJGB0ZDdreTEeR4RvGIJoRWtiq27SEsf3w4cJdTG8",
        "PIXABAY_API_KEY": "**********************************",
        "UNSPLASH_ACCESS_KEY": "lqn-C5Aar2v1lr0sitPWqO8BhxtO0ewDfnqFz28oPYw",
        "REFRESH_CACHE_TOKEN": "123",
        "R2_PUBLIC_URL": "https://pub-7fc4aeafd81144928c2b7700b70025da.r2.dev",
        "DOUBAO_API_KEY": "2842976b-8294-4e0d-bf4e-36f271099168",
        "OPENROUTER_API_KEY": "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",
        "AI_GATEWAY_API_KEY": "IRstAQECtInKRlCculN87K0B",
      }
    }
  }
}