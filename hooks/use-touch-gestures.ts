"use client";

import { useEffect, useRef, useState, useCallback } from "react";

interface TouchGestureOptions {
  onPullToRefresh?: () => void;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  pullToRefreshThreshold?: number;
  swipeThreshold?: number;
  enabled?: boolean;
}

interface TouchState {
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  startTime: number;
  isScrolling: boolean;
  isPulling: boolean;
}

export function useTouchGestures({
  onPullToRefresh,
  onSwipeLeft,
  onSwipeRight,
  pullToRefreshThreshold = 80,
  swipeThreshold = 50,
  enabled = true,
}: TouchGestureOptions = {}) {
  const containerRef = useRef<HTMLElement>(null);
  const [touchState, setTouchState] = useState<TouchState | null>(null);
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);

  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      if (!enabled || !containerRef.current) return;

      const touch = e.touches[0];
      const scrollTop = containerRef.current.scrollTop;

      setTouchState({
        startX: touch.clientX,
        startY: touch.clientY,
        currentX: touch.clientX,
        currentY: touch.clientY,
        startTime: Date.now(),
        isScrolling: false,
        isPulling: scrollTop === 0,
      });
    },
    [enabled]
  );

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!enabled || !touchState || !containerRef.current) return;

      const touch = e.touches[0];
      const deltaX = touch.clientX - touchState.startX;
      const deltaY = touch.clientY - touchState.startY;

      setTouchState((prev) =>
        prev
          ? {
              ...prev,
              currentX: touch.clientX,
              currentY: touch.clientY,
            }
          : null
      );

      if (!touchState.isScrolling && Math.abs(deltaY) > 10) {
        setTouchState((prev) => (prev ? { ...prev, isScrolling: true } : null));
      }

      if (touchState.isPulling && deltaY > 0 && !touchState.isScrolling) {
        e.preventDefault();
        const distance = Math.min(deltaY * 0.5, pullToRefreshThreshold * 1.5);
        setPullDistance(distance);
        setIsPulling(distance > pullToRefreshThreshold * 0.6);
      }
    },
    [enabled, touchState, pullToRefreshThreshold]
  );

  const handleTouchEnd = useCallback(() => {
    if (!enabled || !touchState) return;

    const deltaX = touchState.currentX - touchState.startX;
    const deltaTime = Date.now() - touchState.startTime;

    if (touchState.isPulling && pullDistance >= pullToRefreshThreshold) {
      onPullToRefresh?.();
    }

    if (Math.abs(deltaX) > swipeThreshold && deltaTime < 300) {
      if (deltaX > 0) {
        onSwipeRight?.();
      } else {
        onSwipeLeft?.();
      }
    }

    setTouchState(null);
    setIsPulling(false);
    setPullDistance(0);
  }, [
    enabled,
    touchState,
    pullDistance,
    pullToRefreshThreshold,
    swipeThreshold,
    onPullToRefresh,
    onSwipeLeft,
    onSwipeRight,
  ]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled) return;

    container.addEventListener("touchstart", handleTouchStart, {
      passive: false,
    });
    container.addEventListener("touchmove", handleTouchMove, {
      passive: false,
    });
    container.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [enabled, handleTouchStart, handleTouchMove, handleTouchEnd]);

  return {
    containerRef,
    isPulling,
    pullDistance,
    gestures: {
      isPulling,
      pullProgress: Math.min(pullDistance / pullToRefreshThreshold, 1),
    },
  };
}
