"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";

export interface ExtendedDataStatus {
  exists: boolean;
  expired: boolean;
  needsUpdate: boolean;
  updateStatus?: "idle" | "updating" | "completed" | "failed";
  lastFetchedAt?: number;
  expiresAt?: number;
  errorMessage?: string;
}

// API响应类型定义
interface ExtendedDataGetResponse {
  success: boolean;
  data?: ExtendedDataStatus;
  error?: string;
}

interface ExtendedDataPostResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// 类型守卫函数
function isExtendedDataGetResponse(
  data: unknown
): data is ExtendedDataGetResponse {
  return (
    typeof data === "object" &&
    data !== null &&
    typeof (data as any).success === "boolean"
  );
}

function isExtendedDataPostResponse(
  data: unknown
): data is ExtendedDataPostResponse {
  return (
    typeof data === "object" &&
    data !== null &&
    typeof (data as any).success === "boolean"
  );
}

export interface UseExtendedDataReturn {
  status: ExtendedDataStatus | null;
  isLoading: boolean;
  error: string | null;
  triggerUpdate: () => Promise<void>;
  checkStatus: () => Promise<void>;
}

/**
 * 扩展数据管理 Hook
 *
 * 功能：
 * - 自动检查扩展数据状态
 * - 手动触发扩展数据更新
 * - 提供加载状态和错误处理
 */
export function useExtendedData(): UseExtendedDataReturn {
  const { data: session, status: sessionStatus } = useSession();
  const [status, setStatus] = useState<ExtendedDataStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 检查扩展数据状态
  const checkStatus = useCallback(async () => {
    if (sessionStatus !== "authenticated" || !session?.user?.id) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/github-extended", {
        method: "GET",
      });

      const rawData = await response.json();

      if (!isExtendedDataGetResponse(rawData)) {
        throw new Error("Invalid response format");
      }

      if (rawData.success && rawData.data) {
        setStatus(rawData.data);
      } else {
        // 如果没有数据，设置默认状态
        setStatus({
          exists: false,
          expired: true,
          needsUpdate: true,
          updateStatus: "idle",
        });
      }
    } catch (err) {
      console.error("Failed to check extended data status:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setIsLoading(false);
    }
  }, [session, sessionStatus]);

  // 触发扩展数据更新
  const triggerUpdate = useCallback(async () => {
    if (sessionStatus !== "authenticated" || !session?.user?.id) {
      throw new Error("User not authenticated");
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log(
        `🚀 [useExtendedData] Triggering update for user ${session.user.id}`
      );

      const response = await fetch("/api/github-extended", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const rawData = await response.json();

      if (!isExtendedDataPostResponse(rawData)) {
        throw new Error("Invalid response format");
      }

      if (rawData.success) {
        console.log(`✅ [useExtendedData] Update triggered successfully`);
        // 更新状态为正在更新
        setStatus((prev) =>
          prev ? { ...prev, updateStatus: "updating" } : null
        );

        // 稍后重新检查状态
        setTimeout(() => {
          checkStatus();
        }, 2000);
      } else {
        throw new Error(rawData.error || "Failed to trigger update");
      }
    } catch (err) {
      console.error("Failed to trigger extended data update:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [session, sessionStatus, checkStatus]);

  // 初始化时检查状态
  useEffect(() => {
    if (sessionStatus === "authenticated" && session?.user?.id) {
      checkStatus();
    }
  }, [session, sessionStatus, checkStatus]);

  return {
    status,
    isLoading,
    error,
    triggerUpdate,
    checkStatus,
  };
}

/**
 * 简化版本的 Hook，只负责自动触发
 * 用于在 AuthProvider 中使用
 */
export function useAutoTriggerExtendedData() {
  const { data: session, status } = useSession();
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    if (status === "authenticated" && session?.user?.id && !hasTriggered) {
      console.log(
        `🚀 [AutoTrigger] Starting extended data fetch for user ${session.user.id}`
      );

      setHasTriggered(true);

      fetch("/api/github-extended", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then((response) => response.json())
        .then((rawData) => {
          if (isExtendedDataPostResponse(rawData)) {
            if (rawData.success) {
              console.log(
                `✅ [AutoTrigger] Extended data fetch triggered successfully`
              );
            } else {
              console.warn(
                `⚠️ [AutoTrigger] Extended data fetch failed:`,
                rawData.error
              );
            }
          } else {
            console.warn(`⚠️ [AutoTrigger] Invalid response format:`, rawData);
          }
        })
        .catch((error) => {
          console.error(
            `❌ [AutoTrigger] Error triggering extended data fetch:`,
            error
          );
        });
    }
  }, [session, status, hasTriggered]);

  return { hasTriggered };
}
