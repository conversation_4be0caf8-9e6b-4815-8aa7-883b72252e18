// Dashboard Feature - Data Fetching Hook
// Created: 2025-01-30
// Purpose: 提供Dashboard数据获取的统一Hook接口

import useSWR from "swr";
import { useSession } from "next-auth/react";
import { useCallback, useMemo } from "react";

// 数据获取器函数
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const data = (await response.json()) as {
    success: boolean;
    message?: string;
    data: any;
  };
  if (!data.success) {
    throw new Error(data.message || "API request failed");
  }
  return data.data;
};

// SWR 配置选项
const swrOptions = {
  revalidateOnFocus: true, // 允许页面切换时重新验证
  revalidateOnMount: true, // 组件挂载时重新验证
  revalidateOnReconnect: true,
  dedupingInterval: 30000, // 30秒去重，避免重复请求
  errorRetryCount: 3,
  errorRetryInterval: 1000,
  onError: (error: Error) => {
    console.error("Dashboard data fetch error:", error);
  },
};

// 独立数据获取Hooks - 替代overview接口
export function useGitHubData() {
  const { data: session } = useSession();

  const { data, error, isLoading, mutate } = useSWR(
    session?.user?.id ? "/api/github-data" : null,
    fetcher,
    {
      ...swrOptions,
      refreshInterval: 10 * 60 * 1000, // 10分钟自动刷新
    }
  );

  return {
    data,
    error,
    isLoading,
    refresh: mutate,
    isAuthenticated: !!session?.user?.id,
  };
}

export function useAIStats() {
  const { data: session } = useSession();

  const { data, error, isLoading, mutate } = useSWR(
    session?.user?.id ? "/api/ai/stats" : null,
    fetcher,
    {
      ...swrOptions,
      refreshInterval: 5 * 60 * 1000, // 5分钟自动刷新
    }
  );

  return {
    data: data?.data || null,
    error,
    isLoading,
    refresh: mutate,
    isAuthenticated: !!session?.user?.id,
  };
}

export function useShareStats() {
  const { data: session } = useSession();

  const { data, error, isLoading, mutate } = useSWR(
    session?.user?.id ? "/api/share-links/stats" : null,
    fetcher,
    {
      ...swrOptions,
      refreshInterval: 5 * 60 * 1000, // 5分钟自动刷新
    }
  );

  return {
    data,
    error,
    isLoading,
    refresh: mutate,
    isAuthenticated: !!session?.user?.id,
  };
}

export function useSubscriptionData() {
  const { data: session } = useSession();

  const { data, error, isLoading, mutate } = useSWR(
    session?.user?.id ? "/api/subscription" : null,
    fetcher,
    {
      ...swrOptions,
      refreshInterval: 5 * 60 * 1000, // 5分钟自动刷新
    }
  );

  return {
    data,
    error,
    isLoading,
    refresh: mutate,
    isAuthenticated: !!session?.user?.id,
  };
}

// Dashboard分析数据Hook
export function useDashboardAnalytics() {
  const { data: session } = useSession();

  const { data, error, isLoading, mutate } = useSWR(
    session?.user?.id ? "/api/dashboard/analytics" : null,
    fetcher,
    {
      ...swrOptions,
      refreshInterval: 30 * 60 * 1000, // 30分钟自动刷新
    }
  );

  return {
    data,
    error,
    isLoading,
    refresh: mutate,
    isAuthenticated: !!session?.user?.id,
  };
}

// 注意：useDashboardStats 已被移除
// 各个模块现在直接使用独立的 API：
// - Share 页面：直接调用 /api/share-links/stats
// - AI 页面：直接调用 /api/ai/stats
// - 其他需要统计数据的页面：按需调用相应的独立 API

// 注意：generateRecommendations 函数已被移除
// 如果需要推荐功能，可以在具体的组件中实现

// 综合Dashboard数据Hook - 使用独立API替代overview
export function useDashboardData() {
  const { data: session } = useSession();
  const githubData = useGitHubData();
  const aiStats = useAIStats();
  const shareStats = useShareStats();
  const subscriptionData = useSubscriptionData();
  const analytics = useDashboardAnalytics();

  // 构建overview格式的数据以保持兼容性
  const overview = useMemo(() => {
    if (!session?.user || !githubData.data) return null;

    return {
      user: {
        id: session.user.id,
        username: session.user.username,
        name: session.user.name,
        email: session.user.email,
        image: session.user.image,
        displayName: session.user.name,
        avatarUrl: session.user.avatarUrl,
        createdAt: undefined, // 用户创建时间不可用
      },
      github: githubData.data?.success
        ? {
            login: githubData.data.data.login,
            totalStars: githubData.data.data.totalStars,
            totalRepos: githubData.data.data.publicRepos,
            totalCommits: githubData.data.data.commits,
            totalFollowers: githubData.data.data.followers,
            lastUpdated: githubData.data.data.lastUpdated,
            contributionScore: githubData.data.data.contributionScore,
            hasData: true,
          }
        : {
            hasData: false,
            message: "GitHub data not available",
          },
      ai: {
        hasDescription: aiStats.data?.current?.hasDescription || false,
        lastGenerated: aiStats.data?.current?.generatedAt,
        isCustomized: aiStats.data?.current?.isCustomApplied || false,
        style: aiStats.data?.current?.style,
        expiresAt: aiStats.data?.current?.expiresAt,
        isExpired: aiStats.data?.current?.isExpired,
      },
      sharing: {
        totalLinks: shareStats.data?.totalLinks || 0,
        activeLinks: shareStats.data?.activeLinks || 0,
        recentLinks: [], // 可以从shareStats.data获取
        hasActiveLinks: (shareStats.data?.activeLinks || 0) > 0,
      },
      subscription: {
        isPro: !!subscriptionData.data,
        status: subscriptionData.data?.status || "free",
        currentPeriodEnd: subscriptionData.data?.currentPeriodEnd,
        planId: subscriptionData.data?.planId,
        cancelAtPeriodEnd: subscriptionData.data?.cancelAtPeriodEnd,
      },
      // 为了兼容性，添加profile字段（指向github数据）
      profile: githubData.data?.success ? githubData.data.data : null,
    };
  }, [
    session,
    githubData.data,
    aiStats.data,
    shareStats.data,
    subscriptionData.data,
  ]);

  // 刷新所有数据
  const refreshAll = useCallback(async () => {
    await Promise.all([
      githubData.refresh(),
      aiStats.refresh(),
      shareStats.refresh(),
      subscriptionData.refresh(),
      analytics.refresh(),
    ]);
  }, [
    githubData.refresh,
    aiStats.refresh,
    shareStats.refresh,
    subscriptionData.refresh,
    analytics.refresh,
  ]);

  return {
    // 概览数据（兼容格式）
    overview,
    isOverviewLoading:
      githubData.isLoading ||
      aiStats.isLoading ||
      shareStats.isLoading ||
      subscriptionData.isLoading,
    overviewError:
      githubData.error ||
      aiStats.error ||
      shareStats.error ||
      subscriptionData.error,

    // 分析数据
    analytics: analytics.data,
    isAnalyticsLoading: analytics.isLoading,
    analyticsError: analytics.error,

    // 独立数据访问
    githubData: githubData.data,
    aiStats: aiStats.data,
    shareStats: shareStats.data,
    subscriptionData: subscriptionData.data,

    // 综合状态
    isLoading: githubData.isLoading || analytics.isLoading,
    error: githubData.error || analytics.error,
    criticalError: githubData.error && analytics.error,
    isAuthenticated: githubData.isAuthenticated,

    // 刷新方法
    refreshAll,
    refreshGitHub: githubData.refresh,
    refreshAI: aiStats.refresh,
    refreshShare: shareStats.refresh,
    refreshSubscription: subscriptionData.refresh,
    refreshAnalytics: analytics.refresh,
  };
}

// 类型定义 - 现在通过独立API构建，保持兼容性
export interface DashboardOverviewData {
  user: {
    id: string;
    username: string;
    name?: string;
    email?: string;
    image?: string;
    displayName?: string;
    avatarUrl?: string;
    createdAt?: number;
  };
  github:
    | {
        login: string;
        totalStars: number;
        totalRepos: number;
        totalCommits: number;
        totalFollowers: number;
        lastUpdated: number;
        contributionScore: number;
        hasData: boolean;
      }
    | {
        hasData: false;
        message: string;
      };
  ai: {
    hasDescription: boolean;
    lastGenerated?: number;
    isCustomized: boolean;
    style?: string;
    expiresAt?: number;
    isExpired?: boolean;
  };
  sharing: {
    totalLinks: number;
    activeLinks: number;
    recentLinks: Array<{
      id: string;
      templateType: string;
      createdAt: number;
      expiresAt: number;
      isActive: boolean;
    }>;
    hasActiveLinks: boolean;
  };
  subscription: {
    isPro: boolean;
    status: string;
    currentPeriodEnd?: number;
    planId?: string;
    cancelAtPeriodEnd?: boolean;
  };
  lastUpdated: number;
  cacheExpiry: number;
}

export interface DashboardStatsData {
  profile: {
    accountAge: number;
    isVerified: boolean;
    hasGitHubData: boolean;
    joinedDate: string | null;
  };
  github: {
    totalStars: number;
    totalForks: number;
    totalCommits: number;
    totalRepos: number;
    followers: number;
    following: number;
    contributionScore: number;
    lastUpdated: number;
    dataFreshness: number;
    accountAge: number;
  } | null;
  ai: {
    hasDescription: boolean;
    generatedAt?: number;
    isCustomized: boolean;
    style?: string;
    isExpired?: boolean;
    daysSinceGenerated?: number;
  };
  sharing: {
    totalLinks: number;
    activeLinks: number;
    expiredLinks: number;
    mostUsedTemplate: string | null;
    oldestLink: number | null;
    newestLink: number | null;
  };
  subscription: {
    isPro: boolean;
    status: string;
    daysRemaining: number | null;
    planId?: string;
    subscribedSince: number | null;
  };

  overall: {
    completionScore: number;
    engagementLevel: string;
    nextRecommendations: string[];
  };
}

export interface DashboardAnalyticsData {
  activityMetrics: {
    commits: number;
    pullRequests: number;
    issues: number;
    reviews: number;
    totalContributions: number;
    averagePerMonth: number;
  };
  influenceMetrics: {
    totalStars: number;
    totalForks: number;
    followers: number;
    publicRepos: number;
    starPerRepo: number;
    forkPerRepo: number;
    influenceScore: number;
  };
  diversityMetrics: {
    languageCount: number;
    contributedRepos: number;
    following: number;
    explorationScore: number;
    languageDistribution: {
      totalLanguages: number;
      primaryLanguage: string;
      topLanguages: Array<{ name: string; percentage: number }>;
    } | null;
  };
  timeMetrics: {
    accountAge: number;
    lastUpdate: number;
    avgCommitsPerDay: number;
    avgContributionsPerDay: number;
    activityLevel: string;
  };
  overallMetrics: {
    contributionScore: number;
    totalScore: number; // 添加totalScore字段
    rank: number | null; // 添加rank字段
    percentileRank: number;
    growthTrend: {
      // 添加growthTrend字段
      percentage: number;
      trend: "up" | "down" | "stable";
    };
    strengthAreas: string[];
    improvementAreas: string[];
    nextMilestones: string[];
  };
  comparisonMetrics: {
    vsAverageUser: {
      commits: { user: number; average: number; ratio: number };
      stars: { user: number; average: number; ratio: number };
      followers: { user: number; average: number; ratio: number };
    };
    growthPotential: string;
    benchmarkStatus: string;
  };
}
