"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import type { UserData } from "@/types/user-data";

// 流式事件类型定义
export interface StreamEvent {
  type:
    | "stage_start"
    | "stage_progress"
    | "stage_complete"
    | "content_chunk"
    | "complete"
    | "error";
  stage: string; // 🎯 支持任意自定义stage名称
  data: {
    content?: string;
    progress?: number;
    metadata?: any;
    error?: string;
  };
  timestamp: number;
  sessionId: string;
  // 🎯 新增：事件序列号，确保严格顺序（可选，在客户端分配）
  sequenceId?: number;
}

// 流式状态
export interface StreamState {
  isStreaming: boolean;
  currentStage: StreamEvent["stage"] | null;
  progress: number;
  accumulatedContent: string;
  finalResult: string | null;
  error: string | null;
  events: StreamEvent[];
}

// Hook配置
interface UseAIStreamOptions {
  onEvent?: (event: StreamEvent) => void;
  onComplete?: (result: string) => void;
  onError?: (error: string) => void;
  maxRetries?: number;
  retryDelay?: number;
}

// 请求参数
interface StreamRequest {
  sessionId: string;
  githubData: UserData;
  userPreferences?: {
    perspective?: "first_person" | "third_person";
    targetEmotion?: "witty" | "philosophical" | "confident" | "humble";
    style?: string;
    includeWords?: string[];
    excludeWords?: string[];
  };
  options?: {
    optimizationEnabled?: boolean;
    enableStreaming?: boolean;
  };
}

export const useAIStream = (options: UseAIStreamOptions = {}) => {
  const {
    onEvent,
    onComplete,
    onError,
    maxRetries = 3,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<StreamState>({
    isStreaming: false,
    currentStage: null,
    progress: 0,
    accumulatedContent: "",
    finalResult: null,
    error: null,
    events: [],
  });

  const eventSourceRef = useRef<EventSource | null>(null);
  const retryCountRef = useRef(0);
  const abortControllerRef = useRef<AbortController | null>(null);
  // 🎯 新增：事件序列号计数器，确保严格顺序
  const eventSequenceRef = useRef(0);

  // 重置状态
  const resetState = useCallback(() => {
    setState({
      isStreaming: false,
      currentStage: null,
      progress: 0,
      accumulatedContent: "",
      finalResult: null,
      error: null,
      events: [],
    });
    retryCountRef.current = 0;
    // 🎯 重置序列号计数器
    eventSequenceRef.current = 0;
  }, []);

  // 处理流式事件 - 优化版本
  const handleStreamEvent = useCallback(
    (event: StreamEvent) => {
      // 🎯 为事件分配序列号，确保严格顺序（如果事件还没有序列号）
      const eventWithSequence = {
        ...event,
        sequenceId: event.sequenceId ?? eventSequenceRef.current++,
      };

      // 使用函数式更新来避免依赖问题
      setState((prev) => {
        const updatedState = {
          ...prev,
          currentStage: eventWithSequence.stage,
          progress: eventWithSequence.data.progress || prev.progress,
          events: [...prev.events, eventWithSequence],
        };

        // 处理不同类型的事件
        switch (eventWithSequence.type) {
          case "content_chunk":
            updatedState.accumulatedContent =
              prev.accumulatedContent + (eventWithSequence.data.content || "");
            break;

          case "complete":
            updatedState.isStreaming = false;
            updatedState.finalResult =
              eventWithSequence.data.content || prev.accumulatedContent;
            updatedState.progress = 100;
            // 异步调用回调以避免渲染阻塞
            setTimeout(() => {
              onComplete?.(
                eventWithSequence.data.content || prev.accumulatedContent
              );
            }, 0);
            break;

          case "error":
            updatedState.isStreaming = false;
            updatedState.error =
              eventWithSequence.data.error || "Unknown error";
            // 异步调用错误回调
            setTimeout(() => {
              onError?.(eventWithSequence.data.error || "Unknown error");
            }, 0);
            break;

          case "stage_start":
          case "stage_progress":
          case "stage_complete":
            // 这些事件类型只更新进度和阶段信息
            // 新的统一日志系统会自动处理这些事件的详细信息
            break;
        }

        return updatedState;
      });

      // 异步调用事件回调以避免渲染性能问题
      setTimeout(() => {
        onEvent?.(eventWithSequence);
      }, 0);
    },
    [onEvent, onComplete, onError]
  );

  // 启动流式请求 - 增强版本
  const startStream = useCallback(
    async (request: StreamRequest) => {
      // 清理之前的连接
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      resetState();
      setState((prev) => ({ ...prev, isStreaming: true }));

      try {
        // 创建AbortController用于取消请求
        abortControllerRef.current = new AbortController();

        // 发送POST请求启动流式生成
        const response = await fetch("/api/ai/stream", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...request,
            options: {
              ...request.options,
              enableStreaming: true,
              // 添加优化参数以配合新的统一日志系统
              unifiedLogging: true,
            },
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            `HTTP ${response.status}: ${errorText || response.statusText}`
          );
        }

        // 检查响应是否为SSE流
        const contentType = response.headers.get("content-type");
        if (!contentType?.includes("text/event-stream")) {
          throw new Error("Response is not a valid SSE stream");
        }

        // 创建流读取器处理SSE流
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error("Failed to get response stream reader");
        }

        const decoder = new TextDecoder();
        let buffer = "";
        let eventBatch: StreamEvent[] = [];
        let lastBatchTime = Date.now();

        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split("\n");
            buffer = lines.pop() || ""; // 保留最后一行（可能不完整）

            for (const line of lines) {
              if (line.trim() === "") continue;

              if (line.startsWith("data: ")) {
                const data = line.slice(6).trim();

                if (data === "[DONE]") {
                  // 处理剩余的批量事件
                  if (eventBatch.length > 0) {
                    eventBatch.forEach(handleStreamEvent);
                    eventBatch = [];
                  }
                  setState((prev) => ({ ...prev, isStreaming: false }));
                  return;
                }

                try {
                  const event: StreamEvent = JSON.parse(data);

                  // 批量处理非关键事件以提升性能
                  if (event.type === "stage_progress") {
                    eventBatch.push(event);

                    // 每100ms或批量达到5个事件时处理一次
                    const now = Date.now();
                    if (eventBatch.length >= 5 || now - lastBatchTime >= 100) {
                      eventBatch.forEach(handleStreamEvent);
                      eventBatch = [];
                      lastBatchTime = now;
                    }
                  } else {
                    // 立即处理关键事件
                    handleStreamEvent(event);
                  }
                } catch (parseError) {
                  console.warn("Failed to parse SSE event:", data, parseError);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
          // 处理剩余的批量事件
          if (eventBatch.length > 0) {
            eventBatch.forEach(handleStreamEvent);
          }
        }
      } catch (error) {
        console.error("Stream error:", error);

        // 如果是取消操作，不进行重试
        if (error instanceof Error && error.name === "AbortError") {
          setState((prev) => ({ ...prev, isStreaming: false }));
          return;
        }

        // 智能重试逻辑
        if (retryCountRef.current < maxRetries) {
          retryCountRef.current++;
          console.log(
            `🔄 智能重试 (${retryCountRef.current}/${maxRetries})...`
          );

          // 指数退避重试策略
          const retryTimeout =
            retryDelay * Math.pow(2, retryCountRef.current - 1);

          setTimeout(() => {
            startStream(request);
          }, retryTimeout);
        } else {
          const errorMessage =
            error instanceof Error ? error.message : "Unknown stream error";
          setState((prev) => ({
            ...prev,
            isStreaming: false,
            error: `Stream failed after ${maxRetries} retries: ${errorMessage}`,
          }));
          onError?.(errorMessage);
        }
      }
    },
    [handleStreamEvent, maxRetries, retryDelay, onError, resetState]
  );

  // 停止流式请求
  const stopStream = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setState((prev) => ({ ...prev, isStreaming: false }));
  }, []);

  // 清理资源
  useEffect(() => {
    return () => {
      stopStream();
    };
  }, [stopStream]);

  return {
    ...state,
    startStream,
    stopStream,
    resetState,
  };
};
