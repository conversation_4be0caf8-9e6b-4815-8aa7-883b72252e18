import useSWR from "swr";
import { useSession } from "next-auth/react";

// 订阅数据类型定义
type SubscriptionPlan = {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  features: string[];
};

type Subscription = {
  id: string;
  userId: string;
  planId: string;
  status: string;
  priceId: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAt?: number;
  cancelAtPeriodEnd: boolean;
  createdAt: number;
  updatedAt: number;
  plan: SubscriptionPlan;
};

// 数据获取器函数
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return response.json() as Promise<Subscription>;
};

export function useSubscription() {
  const { data: session, status } = useSession();

  // 使用SWR进行数据缓存和去重
  const {
    data: subscription,
    error,
    isLoading,
    mutate,
  } = useSWR(session?.user?.id ? "/api/subscription" : null, fetcher, {
    revalidateOnFocus: true, // 允许页面切换时重新验证
    revalidateOnMount: true, // 组件挂载时重新验证
    revalidateOnReconnect: true,
    dedupingInterval: 30000, // 30秒去重，避免重复请求
    errorRetryCount: 3,
    errorRetryInterval: 1000,
    onError: (error: Error) => {
      console.error("Subscription fetch error:", error);
    },
  });

  // 统一的 Pro 用户判断逻辑
  const isSubscribed = !!subscription && subscription.status === "active";
  const isPro =
    isSubscribed && subscription.plan?.name?.toLowerCase().includes("pro");
  const isPremium =
    isSubscribed && subscription.plan?.name?.toLowerCase().includes("premium");

  const canUseFeature = (feature: string) => {
    if (!subscription || subscription.status !== "active") return false;

    // 根据不同的功能需求进行权限检查
    switch (feature) {
      case "advanced_analytics":
        return isPro || isPremium;
      case "premium_support":
        return isPremium;
      case "ai_description":
        return isPro || isPremium;
      case "unlimited_share_links":
        return isPro || isPremium;
      default:
        return true;
    }
  };

  return {
    subscription,
    isSubscribed,
    isPro,
    isPremium,
    isLoading: isLoading || status === "loading",
    error,
    canUseFeature,
    refreshSubscription: mutate, // 使用SWR的mutate函数来刷新数据
  };
}
