"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import {
  DASHBOARD_NAV_ITEMS,
  getCurrentNavItem,
} from "@/constants/dashboard-navigation";

export function useDashboardNavigation() {
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const currentNavItem = getCurrentNavItem(pathname);

  const toggleSidebar = () => setSidebarCollapsed(!sidebarCollapsed);
  const toggleMobileMenu = () => setMobileMenuOpen(!mobileMenuOpen);
  const closeMobileMenu = () => setMobileMenuOpen(false);

  return {
    // 当前状态
    pathname,
    currentNavItem,
    sidebarCollapsed,
    mobileMenuOpen,

    // 操作方法
    setSidebarCollapsed,
    setMobileMenuOpen,
    toggleSidebar,
    toggleMobileMenu,
    closeMobileMenu,

    // 导航数据
    navItems: DASHBOARD_NAV_ITEMS,
  };
}
