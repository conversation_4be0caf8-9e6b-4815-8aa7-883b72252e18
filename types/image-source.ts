export interface ImageSource {
  id: string;
  name: string;
  type: "direct" | "pexels" | "pixabay" | "unsplash";
  url: string;
  weight: number; // 选择权重 1-10
  config?: {
    apiKey?: string;
    orientation?: "landscape" | "portrait" | "square";
    size?: "large" | "medium" | "small";
    per_page?: number;
    keywords?: string[];
    // Pixabay 专用配置
    image_type?: "photo" | "illustration" | "vector";
    category?: string;
    min_width?: number;
    min_height?: number;
    safesearch?: boolean;
    order?: "popular" | "latest" | "oldest" | "relevant";
    // 内容过滤配置
    exclude_keywords?: string[];
    exclude_categories?: string[];
  };
  enabled: boolean;
}

export interface FetchedImage {
  imageUrl: string;
  imageData: string;
  contentType: string;
  sourceId?: string;
  metadata?: {
    photographer?: string;
    photographerUrl?: string;
    pexelsId?: number;
    unsplashId?: string;
    sourceType: "direct" | "pexels" | "pixabay" | "unsplash";
  };
}
