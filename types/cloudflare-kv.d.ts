// 定义Cloudflare KV客户端接口
export interface CloudflareKVClient {
  get<T = unknown>(key: string): Promise<T | null>;
  getAll<T = unknown>(): Promise<Record<string, T>>;
  has(key: string): Promise<boolean>;
  digest?(): Promise<string>;
  set<T>(key: string, value: T): Promise<void>;
  delete(key: string): Promise<void>;
}

// R2 原生 API 类型定义
export interface R2Bucket {
  put(
    key: string,
    value: A<PERSON>yBuffer | ReadableStream | string,
    options?: {
      httpMetadata?: {
        contentType?: string;
        cacheControl?: string;
        contentDisposition?: string;
        contentEncoding?: string;
        contentLanguage?: string;
        expires?: Date;
      };
      customMetadata?: Record<string, string>;
    }
  ): Promise<R2Object | null>;

  get(
    key: string,
    options?: {
      onlyIf?: R2Conditional;
      range?: R2Range;
    }
  ): Promise<R2ObjectBody | null>;

  head(
    key: string,
    options?: {
      onlyIf?: R2Conditional;
    }
  ): Promise<R2Object | null>;

  delete(keys: string | string[]): Promise<void>;

  list(options?: {
    limit?: number;
    prefix?: string;
    cursor?: string;
    delimiter?: string;
    include?: ("httpMetadata" | "customMetadata")[];
  }): Promise<R2Objects>;
}

export interface R2Object {
  key: string;
  version: string;
  size: number;
  etag: string;
  uploaded: Date;
  httpMetadata?: R2HTTPMetadata;
  customMetadata?: Record<string, string>;
  range?: R2Range;
}

export interface R2ObjectBody extends R2Object {
  body: ReadableStream;
  bodyUsed: boolean;
  arrayBuffer(): Promise<ArrayBuffer>;
  text(): Promise<string>;
  json<T>(): Promise<T>;
  blob(): Promise<Blob>;
}

export interface R2HTTPMetadata {
  contentType?: string;
  cacheControl?: string;
  contentDisposition?: string;
  contentEncoding?: string;
  contentLanguage?: string;
  expires?: Date;
}

export interface R2Objects {
  objects: R2Object[];
  truncated: boolean;
  cursor?: string;
  delimitedPrefixes: string[];
}

export interface R2Conditional {
  etagMatches?: string;
  etagDoesNotMatch?: string;
  uploadedBefore?: Date;
  uploadedAfter?: Date;
}

export interface R2Range {
  offset?: number;
  length?: number;
  suffix?: number;
}

// Cloudflare Workers 环境变量类型
declare global {
  const GITHUB_CARD_R2: R2Bucket;
}

// 简化的KV值类型
export interface KVStoredValue<T> {
  value: T;
  expiry: number; // 过期时间戳
}

// 声明模块以防止类型错误
declare module "../lib/cloudflare/kv-service" {
  const createKVClient: () => CloudflareKVClient;
  export { CloudflareKVClient, KVStoredValue };
  export default createKVClient();
}
