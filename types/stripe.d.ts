import type { Stripe } from 'stripe';

declare global {
  namespace Stripe {
    interface Subscription {
      current_period_start: number;
      current_period_end: number;
      cancel_at: number | null;
      canceled_at: number | null;
      cancel_at_period_end: boolean;
      items: {
        data: Array<{
          price: Stripe.Price;
        }>;
      };
    }

    interface Invoice {
      subscription?: string | Stripe.Subscription;
      payment_intent?: string | Stripe.PaymentIntent;
      metadata: Record<string, string> | null;
      paid: boolean;
      amount_paid: number;
      amount_due: number;
      currency: string;
      invoice_pdf: string | null;
      customer?: string | Stripe.Customer;
    }
  }
}

export {};
