// R2存储方案的数据结构定义 - Phase 2.2

export interface R2ImageMapping {
  id: string; // 图片ID (MD5 hash)
  r2Key: string; // R2存储键 (images/imageId)
  r2Url: string; // R2公共访问URL
  originalUrl: string; // 原始图片URL
  contentType: string; // 图片MIME类型
  fileSize?: number; // 文件大小 (字节)
  uploadedAt: number; // 上传时间戳
  lastAccessed: number; // 最后访问时间
  accessCount: number; // 访问次数
  source: ImageSource; // 图片来源
}

export type ImageSource =
  | "pexels"
  | "unsplash"
  | "pixabay"
  | "direct"
  | "backup"
  | "smart_cache"
  | "traditional_cache";

export interface R2ImageMetadata {
  images: R2ImageMapping[];
  totalCount: number;
  totalSize: string; // 格式化的总大小 (如 "1.2 MB")
  lastUpdated: number;
  version: string; // 数据结构版本，用于兼容性处理
}

// 兼容性类型 - 用于从旧的base64缓存迁移
export interface LegacyImageCache {
  id: string;
  imageUrl: string;
  imageData: string; // base64数据
  contentType: string;
  timestamp: number;
  expiry: number;
}

export interface MigrationStatus {
  totalLegacyImages: number;
  migratedImages: number;
  failedMigrations: string[]; // 失败的图片ID列表
  migrationStarted: number;
  migrationCompleted?: number;
  inProgress: boolean;
}

// R2存储配置
export interface R2StorageConfig {
  enabled: boolean; // 是否启用R2存储
  fallbackToKV: boolean; // R2失败时是否回退到KV
  maxImageSize: number; // 最大图片大小 (字节)
  allowedContentTypes: string[]; // 允许的内容类型
  cacheTTL: number; // 缓存TTL (秒)
  migrationBatchSize: number; // 迁移批次大小
}

// 存储操作结果
export interface StorageOperationResult {
  success: boolean;
  imageId: string;
  r2Url?: string;
  error?: string;
  fallbackUsed?: boolean; // 是否使用了降级方案
  operationTime?: number; // 操作耗时 (毫秒)
}

// 图片查询结果
export interface ImageQueryResult {
  found: boolean;
  imageId: string;
  r2Url?: string;
  mapping?: R2ImageMapping;
  source: "r2" | "kv" | "not_found";
  cached: boolean; // 是否来自缓存
}

// 统计信息
export interface R2StorageStats {
  totalImages: number;
  totalSize: number; // 字节
  avgImageSize: number; // 平均图片大小
  mostAccessedImages: Array<{
    id: string;
    url: string;
    accessCount: number;
  }>;
  storageUsagePercent: number; // R2免费额度使用百分比
  monthlyOperations: {
    uploads: number;
    downloads: number;
  };
}

// 常量定义
export const R2_CONSTANTS = {
  // KV键名
  IMAGE_MAPPING_PREFIX: "r2_img_",
  METADATA_KEY: "r2_image_metadata",
  MIGRATION_STATUS_KEY: "r2_migration_status",
  STORAGE_CONFIG_KEY: "r2_storage_config",

  // R2路径
  IMAGE_PATH_PREFIX: "images/",

  // 默认配置
  DEFAULT_CONFIG: {
    enabled: true,
    fallbackToKV: true,
    maxImageSize: 10 * 1024 * 1024, // 10MB
    allowedContentTypes: [
      "image/jpeg",
      "image/png",
      "image/webp",
      "image/gif",
      "image/svg+xml",
    ],
    cacheTTL: 86400, // 24小时
    migrationBatchSize: 10,
  } as R2StorageConfig,

  // R2免费额度限制
  FREE_TIER_LIMITS: {
    storage: 10 * 1024 * 1024 * 1024, // 10GB
    classAOperations: 1000000, // 1M operations
    classBOperations: 10000000, // 10M operations
  },
} as const;
