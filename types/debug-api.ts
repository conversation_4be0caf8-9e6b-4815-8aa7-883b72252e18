import type { UserData } from "./user-data";
import type { GitHubExtendedData } from "./github-extended";
import type {
  AnalyzerConfig,
  UserPreferences,
  ModuleType,
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
} from "@/lib/ai/types";
import type {
  DebugAnalyzerOutput,
  DebugStrategistOutput,
  DebugWriterOutput,
  DebugCriticOutput,
} from "@/lib/ai/types/debug-adapters";

// 支持的模块类型
export type SupportedModuleType =
  | "analyzer"
  | "strategist"
  | "writer"
  | "critic";

// Writer和Critic模块的AI参数类型 - V6.0更新
export interface WriterAIParams {
  temperature?: number; // 温度参数(0-1)：控制创意性，默认0.8
  max_tokens?: number; // 最大Token数：控制生成长度，默认800
  retry_count?: number; // 重试次数：失败后重试次数，默认1
  // V6.0移除：few_shot_examples (改用模板化生成)
}

export interface CriticAIParams {
  temperature?: number; // 温度参数(0-1)：控制评估一致性，默认0.2
  max_tokens?: number; // 最大Token数：控制评估详细程度，默认600
  retry_count?: number; // 重试次数：失败后重试次数，默认3
  scoring_strictness?: number; // 评分严格度(0-1)：控制评分标准，默认0.5
}

// =================================================================
// 🚀 NEW: 统一请求结构 (V2.0) - 解决API一致性问题
// =================================================================

/**
 * 统一执行选项
 */
export interface UnifiedExecutionOptions {
  enableStreaming?: boolean;
  timeout?: number;
  retryCount?: number;
}

/**
 * 统一执行上下文 - 包含所有模块执行需要的基础环境信息
 */
export interface UnifiedExecutionContext {
  githubData: UserData;
  extendedData?: GitHubExtendedData; // 仅Analyzer模块必需
}

/**
 * 模块输入数据类型映射
 */
export interface ModuleInputMap {
  analyzer: never; // Analyzer无前置输入
  strategist: AnalyzerOutput;
  writer: {
    analyzerOutput: AnalyzerOutput;
    strategistOutput: StrategistOutput;
  };
  critic: {
    writerOutput: WriterOutput;
    strategistOutput: StrategistOutput;
  };
}

/**
 * 模块配置类型映射
 */
export interface ModuleConfigMap {
  analyzer: AnalyzerConfig;
  strategist: UserPreferences;
  writer: WriterAIParams;
  critic: CriticAIParams;
}

/**
 * 统一调试请求基础接口
 */
export interface UnifiedDebugRequest<
  T extends SupportedModuleType = SupportedModuleType
> {
  sessionId: string;
  context: UnifiedExecutionContext;
  input?: ModuleInputMap[T];
  config?: ModuleConfigMap[T];
  options?: UnifiedExecutionOptions;
}

/**
 * 各模块的具体统一请求类型
 */
export interface UnifiedAnalyzerRequest
  extends UnifiedDebugRequest<"analyzer"> {
  context: {
    githubData: UserData;
    extendedData: GitHubExtendedData; // 必需
  };
  input?: never;
  config?: AnalyzerConfig;
}

export interface UnifiedStrategistRequest
  extends UnifiedDebugRequest<"strategist"> {
  context: {
    githubData: UserData;
    extendedData?: never; // 不需要
  };
  input: AnalyzerOutput;
  config?: UserPreferences;
}

export interface UnifiedWriterRequest extends UnifiedDebugRequest<"writer"> {
  context: {
    githubData: UserData;
    extendedData?: never;
  };
  input: {
    analyzerOutput: AnalyzerOutput;
    strategistOutput: StrategistOutput;
  };
  config?: WriterAIParams;
}

export interface UnifiedCriticRequest extends UnifiedDebugRequest<"critic"> {
  context: {
    githubData: UserData;
    extendedData?: never;
  };
  input: {
    writerOutput: WriterOutput;
    strategistOutput: StrategistOutput;
  };
  config?: CriticAIParams;
}

/**
 * 统一请求类型联合
 */
export type UnifiedDebugRequestUnion =
  | UnifiedAnalyzerRequest
  | UnifiedStrategistRequest
  | UnifiedWriterRequest
  | UnifiedCriticRequest;

/**
 * 统一响应接口
 */
export interface UnifiedDebugResponse<T = any> {
  success: boolean;
  sessionId: string;
  module: SupportedModuleType;
  processingTime: number;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    timestamp: number;
    version: string;
    traceId?: string;
    framework: "UnifiedDebugAPI_v2.0";
  };
}

// =================================================================
// 📚 LEGACY: 保持向后兼容的旧接口定义
// =================================================================

// 模块配置类型联合
export type ModuleConfig =
  | AnalyzerConfig
  | UserPreferences
  | WriterAIParams
  | CriticAIParams;

// 基础调试请求接口
export interface BaseDebugRequest {
  sessionId: string;
  githubData: UserData;
  moduleConfig: ModuleConfig;
  inputData?: any; // 前置模块输出数据
}

// 不同模块的具体请求类型
export interface AnalyzerDebugRequest extends BaseDebugRequest {
  moduleConfig: AnalyzerConfig;
  inputData?: never; // Analyzer不需要输入数据
}

export interface StrategistDebugRequest extends BaseDebugRequest {
  moduleConfig: UserPreferences;
  inputData: DebugAnalyzerOutput; // 需要Analyzer的输出
}

export interface WriterDebugRequest extends BaseDebugRequest {
  moduleConfig: WriterAIParams;
  inputData: {
    analyzerOutput: DebugAnalyzerOutput;
    strategistOutput: DebugStrategistOutput;
  };
}

export interface CriticDebugRequest extends BaseDebugRequest {
  moduleConfig: CriticAIParams;
  inputData: {
    writerOutput: DebugWriterOutput;
    strategistOutput: DebugStrategistOutput;
  };
}

// 调试请求类型联合
export type DebugRequest =
  | AnalyzerDebugRequest
  | StrategistDebugRequest
  | WriterDebugRequest
  | CriticDebugRequest;

// 调试响应基础接口
export interface BaseDebugResponse {
  success: boolean;
  processingTime: number;
  confidence?: number;
  error?: string;
}

// 成功响应
export interface SuccessDebugResponse extends BaseDebugResponse {
  success: true;
  result:
    | DebugAnalyzerOutput
    | DebugStrategistOutput
    | DebugWriterOutput
    | DebugCriticOutput;
  confidence: number;
  metadata: {
    module: SupportedModuleType;
    sessionId: string;
    timestamp: number;
    userId: string;
    [key: string]: any;
  };
}

// 失败响应
export interface ErrorDebugResponse extends BaseDebugResponse {
  success: false;
  error: string;
  metadata?: {
    module?: SupportedModuleType;
    sessionId?: string;
    timestamp?: number;
    userId?: string;
    validationErrors?: any[];
    [key: string]: any;
  };
}

// 调试响应类型联合
export type DebugResponse = SuccessDebugResponse | ErrorDebugResponse;

// 特定模块的响应类型
export interface AnalyzerDebugResponse
  extends Omit<SuccessDebugResponse, "result"> {
  result: DebugAnalyzerOutput;
}

export interface StrategistDebugResponse
  extends Omit<SuccessDebugResponse, "result"> {
  result: DebugStrategistOutput;
}

export interface WriterDebugResponse
  extends Omit<SuccessDebugResponse, "result"> {
  result: DebugWriterOutput;
}

export interface CriticDebugResponse
  extends Omit<SuccessDebugResponse, "result"> {
  result: DebugCriticOutput;
}

// 流水线调试相关类型
export interface PipelineDebugRequest {
  sessionId: string;
  githubData: UserData;
  configs: {
    analyzer: AnalyzerConfig;
    strategist: UserPreferences;
    writer: WriterAIParams;
    critic: CriticAIParams;
  };
  options?: {
    stopAt?: SupportedModuleType; // 在指定模块停止
    skipModules?: SupportedModuleType[]; // 跳过指定模块
  };
}

export interface PipelineDebugResponse {
  success: boolean;
  sessionId: string;
  totalProcessingTime: number;
  results: {
    analyzer?: AnalyzerDebugResponse;
    strategist?: StrategistDebugResponse;
    writer?: WriterDebugResponse;
    critic?: CriticDebugResponse;
  };
  pipeline_metadata: {
    completed_modules: SupportedModuleType[];
    failed_modules: SupportedModuleType[];
    total_modules: number;
    success_rate: number;
  };
  error?: string;
}

// 健康检查响应类型
export interface HealthCheckResponse {
  status: "healthy" | "degraded" | "unhealthy";
  modules: SupportedModuleType[];
  timestamp: number;
  checks?: {
    [module in SupportedModuleType]?: {
      status: "healthy" | "unhealthy";
      response_time?: number;
      error?: string;
    };
  };
}

// 模块信息响应类型
export interface ModulesInfoResponse {
  supported_modules: SupportedModuleType[];
  description: string;
  module_details: {
    [module in SupportedModuleType]: {
      name: string;
      description: string;
      required_inputs: string[];
      config_schema: string;
      output_type: string;
    };
  };
}

// 调试会话状态
export interface DebugSession {
  id: string;
  status: "idle" | "running" | "completed" | "failed";
  created_at: number;
  started_at?: number;
  completed_at?: number;
  current_module?: SupportedModuleType;
  input_data: UserData;
  module_configurations: {
    analyzer?: AnalyzerConfig;
    strategist?: UserPreferences;
    writer?: WriterAIParams;
    critic?: CriticAIParams;
  };
  execution_results: {
    [module in SupportedModuleType]?: {
      success: boolean;
      data?: any;
      processingTime: number;
      timestamp: number;
      error?: string;
      metadata?: Record<string, any>;
    };
  };
  retry_count: number;
  max_retries?: number;
}

// 调试会话管理相关类型
export interface CreateSessionRequest {
  githubData: UserData;
  initialConfigs?: Partial<DebugSession["module_configurations"]>;
}

export interface UpdateSessionRequest {
  sessionId: string;
  updates: {
    status?: DebugSession["status"];
    current_module?: SupportedModuleType;
    module_configurations?: Partial<DebugSession["module_configurations"]>;
    execution_results?: Partial<DebugSession["execution_results"]>;
  };
}

export interface SessionResponse {
  success: boolean;
  session?: DebugSession;
  error?: string;
}

// 错误类型定义
export interface DebugApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

// 常见错误代码
export enum DebugErrorCodes {
  UNAUTHORIZED = "UNAUTHORIZED",
  INVALID_MODULE = "INVALID_MODULE",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  MODULE_EXECUTION_ERROR = "MODULE_EXECUTION_ERROR",
  MISSING_INPUT_DATA = "MISSING_INPUT_DATA",
  SESSION_NOT_FOUND = "SESSION_NOT_FOUND",
  RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
  INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
}

// 调试统计信息
export interface DebugStats {
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_processing_time: number;
  module_stats: {
    [module in SupportedModuleType]: {
      total_executions: number;
      successful_executions: number;
      average_processing_time: number;
      error_rate: number;
    };
  };
  last_24h_stats: {
    total_requests: number;
    peak_hour: number;
    average_concurrent_sessions: number;
  };
}

// 默认配置常量 - V6.0更新
export const DEFAULT_WRITER_AI_PARAMS: WriterAIParams = {
  temperature: 0.8,
  max_tokens: 800,
  retry_count: 1,
};

export const DEFAULT_CRITIC_AI_PARAMS: CriticAIParams = {
  temperature: 0.2,
  max_tokens: 600,
  retry_count: 3,
  scoring_strictness: 0.5,
};

export const DEFAULT_ANALYZER_CONFIG_TEMPLATE: AnalyzerConfig = {
  includeItems: [],
  excludeItems: [],
  enableLLM: true,
  llmTimeout: 30000,
};

// 类型守卫函数
export function isSuccessResponse(
  response: DebugResponse
): response is SuccessDebugResponse {
  return response.success === true;
}

export function isErrorResponse(
  response: DebugResponse
): response is ErrorDebugResponse {
  return response.success === false;
}

export function isAnalyzerRequest(
  request: DebugRequest,
  moduleType: string
): request is AnalyzerDebugRequest {
  return moduleType === "analyzer";
}

export function isStrategistRequest(
  request: DebugRequest,
  moduleType: string
): request is StrategistDebugRequest {
  return moduleType === "strategist";
}

export function isWriterRequest(
  request: DebugRequest,
  moduleType: string
): request is WriterDebugRequest {
  return moduleType === "writer";
}

export function isCriticRequest(
  request: DebugRequest,
  moduleType: string
): request is CriticDebugRequest {
  return moduleType === "critic";
}
