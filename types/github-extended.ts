/**
 * GitHub扩展数据类型定义
 * 统一的类型系统 - 确保数据库、API、业务逻辑的类型一致性
 *
 * 设计原则：
 * - 单一数据源：每个数据结构只有一个权威定义
 * - 类型安全：严格的TypeScript类型，避免any和unknown
 * - 数据库同步：类型定义与数据库schema保持同步
 * - 向前兼容：支持数据版本管理和迁移
 */

// ==================== 核心数据类型 ====================

/**
 * 仓库详细信息
 * 基于真实GitHub API响应结构定义
 */
export interface RepositoryDetailData {
  /** 仓库名称 */
  name: string;
  /** 完整名称 (owner/repo) */
  fullName: string;
  /** 仓库描述 */
  description: string;
  /** 是否私有仓库 */
  private: boolean;
  /** 主要编程语言 */
  language: string;
  /** Stars数量 */
  stargazersCount: number;
  /** Forks数量 */
  forksCount: number;
  /** 仓库创建时间 (ISO 8601) */
  createdAt: string;
  /** 最后更新时间 (ISO 8601) */
  updatedAt: string;
  /** 最后推送时间 (ISO 8601) */
  pushedAt: string;
  /** 仓库大小 (KB) */
  size: number;
  /** 默认分支名 */
  defaultBranch: string;
  /** 主题标签列表 */
  topics: string[];
  /** 是否包含README文件 */
  hasReadme: boolean;
  /** README内容 (如果获取) */
  readmeContent?: string;
  /** README文件大小 (字节) */
  readmeSize?: number;
}

/**
 * 提交详细信息
 * 基于GitHub Commits API响应结构定义
 */
export interface CommitDetailData {
  /** 提交SHA值 */
  sha: string;
  /** 提交消息 */
  message: string;
  /** 作者信息 */
  author: {
    /** 作者姓名 */
    name: string;
    /** 作者邮箱 */
    email: string;
    /** 提交时间 (ISO 8601) */
    date: string;
  };
  /** 提交者信息 */
  committer: {
    /** 提交者姓名 */
    name: string;
    /** 提交者邮箱 */
    email: string;
    /** 提交时间 (ISO 8601) */
    date: string;
  };
  /** 所属仓库名称 */
  repository: string;
  /** 新增代码行数 */
  additions?: number;
  /** 删除代码行数 */
  deletions?: number;
  /** 修改文件数量 */
  changedFiles?: number;
}

// ==================== README详细信息 ====================

export interface ReadmeDetailData {
  /** 仓库名称 */
  repository: string;
  /** README文件名 */
  name: string;
  /** 文件路径 */
  path: string;
  /** 内容（Base64编码） */
  content: string;
  /** 文件大小 */
  size: number;
  /** 编码方式 */
  encoding: string;
  /** SHA值 */
  sha: string;
  /** 下载URL */
  downloadUrl: string;
}

// ==================== 技术栈文件详细信息 ====================

export interface TechStackFileData {
  /** 仓库名称 */
  repository: string;
  /** 文件名 */
  name: string;
  /** 文件类型 */
  fileType: TechStackFileType;
  /** 文件路径 */
  path: string;
  /** 内容（Base64编码） */
  content: string;
  /** 文件大小 */
  size: number;
  /** 编码方式 */
  encoding: string;
  /** SHA值 */
  sha: string;
  /** 下载URL */
  downloadUrl: string;
  /** 备注：解析功能暂未实现，仅存储原始文件内容 */
}

export type TechStackFileType =
  | "package.json" // Node.js
  | "pyproject.toml" // Python (Poetry)
  | "requirements.txt" // Python (pip)
  | "Pipfile" // Python (pipenv)
  | "Cargo.toml" // Rust
  | "go.mod" // Go
  | "composer.json" // PHP
  | "pom.xml" // Java (Maven)
  | "build.gradle" // Java/Kotlin (Gradle)
  | "Gemfile" // Ruby
  | "pubspec.yaml" // Dart/Flutter
  | "mix.exs" // Elixir
  | "project.clj" // Clojure
  | "stack.yaml" // Haskell
  | "deno.json" // Deno
  | "bun.lockb"; // Bun

// 解析器类型定义暂时移除，仅存储原始文件内容
// 后续可根据需要添加解析功能

// ==================== 时间分布统计数据 ====================

export interface TimeStatsData {
  /** 仓库名称 */
  repository: string;
  /** 每小时提交统计 [day, hour, commits] */
  punchCard: Array<[number, number, number]>;
  /** 每周提交活动 */
  commitActivity: Array<{
    /** 周开始时间戳 */
    week: number;
    /** 每天提交数 [周日-周六] */
    days: number[];
    /** 本周总提交数 */
    total: number;
  }>;
  /** 贡献者统计 */
  contributors: Array<{
    /** 作者信息 */
    author: {
      login: string;
      id: number;
      avatarUrl: string;
    };
    /** 总提交数 */
    total: number;
    /** 每周统计 */
    weeks: Array<{
      /** 周开始时间戳 */
      w: number;
      /** 添加行数 */
      a: number;
      /** 删除行数 */
      d: number;
      /** 提交数 */
      c: number;
    }>;
  }>;
}

// ==================== 扩展数据存储结构 ====================

export interface GitHubExtendedData {
  /** 数据版本 */
  version: string;
  /** 获取时间戳 */
  fetchedAt: number;
  /** 用户名 */
  username: string;

  /** 仓库详细信息列表 */
  repositories: RepositoryDetailData[];
  /** Commit详细信息列表 */
  commits: CommitDetailData[];
  /** README详细信息列表 */
  readmes: ReadmeDetailData[];
  /** 技术栈文件详细信息列表 */
  techStackFiles: TechStackFileData[];
  /** 时间分布统计数据 */
  timeStats: TimeStatsData[];

  /** 获取配置 */
  fetchConfig: {
    /** 最大仓库数 */
    maxRepositories: number;
    /** 每个仓库最大commit数 */
    maxCommitsPerRepo: number;
    /** 是否获取README */
    includeReadme: boolean;
    /** 是否获取技术栈文件 */
    includeTechStackFiles: boolean;
    /** 是否获取时间统计 */
    includeTimeStats: boolean;
  };
}

// ==================== API响应类型 ====================

/**
 * 扩展数据获取响应
 */
export interface ExtendedDataFetchResponse {
  /** 请求是否成功 */
  success: boolean;
  /** 扩展数据 (成功时) */
  data?: GitHubExtendedData;
  /** 错误信息 (失败时) */
  error?: string;
  /** 响应元数据 */
  metadata: {
    /** 处理时间 (毫秒) */
    processingTime: number;
    /** API调用次数 */
    apiCalls: number;
    /** 获取的数据量统计 */
    dataSize: {
      /** 仓库数量 */
      repositories: number;
      /** 提交数量 */
      commits: number;
      /** README数量 */
      readmes: number;
      /** 技术栈文件数量 */
      techStackFiles: number;
      /** 时间统计数量 */
      timeStats: number;
    };
  };
}

// ==================== 获取配置 ====================

export interface ExtendedDataFetchConfig {
  /** 最大分析仓库数（按star排序） */
  maxRepositories: number;
  /** 每个仓库最大commit数 */
  maxCommitsPerRepo: number;
  /** 是否获取README内容 */
  includeReadme: boolean;
  /** 是否获取技术栈文件 */
  includeTechStackFiles: boolean;
  /** 是否获取时间分布统计 */
  includeTimeStats: boolean;
  /** 请求间隔（毫秒） */
  requestDelay: number;
  /** 缓存有效期（小时） */
  cacheValidityHours: number;
}

export const DEFAULT_EXTENDED_FETCH_CONFIG: ExtendedDataFetchConfig = {
  maxRepositories: 10,
  maxCommitsPerRepo: 100,
  includeReadme: true,
  includeTechStackFiles: true,
  includeTimeStats: true,
  requestDelay: 100,
  cacheValidityHours: 24,
};

// ==================== 数据库存储类型 ====================

/**
 * 扩展数据更新状态
 */
export type ExtendedDataStatus =
  | "pending"
  | "updating"
  | "completed"
  | "failed";

/**
 * GitHub扩展数据数据库记录类型
 * 与 lib/db/schema.ts 中的 githubExtendedDatas 表结构保持同步
 */
export interface GitHubExtendedDataRecord {
  /** 记录ID */
  id: string;
  /** 用户ID */
  userId: string;

  // JSON存储的扩展数据
  /** 仓库数据 (JSON字符串) */
  repositoriesData: string | null;
  /** 提交数据 (JSON字符串) */
  commitsData: string | null;
  /** README数据 (JSON字符串) */
  readmeData: string | null;
  /** 技术栈文件数据 (JSON字符串) */
  techStackFilesData: string | null;
  /** 时间统计数据 (JSON字符串) */
  timeStatsData: string | null;
  /** 获取配置 (JSON字符串) */
  fetchConfig: string | null;

  // 元数据
  /** 数据版本 */
  dataVersion: string;
  /** 数据获取时间戳 */
  fetchedAt: number;
  /** 数据过期时间戳 */
  expiresAt: number;
  /** 更新状态 */
  updateStatus: ExtendedDataStatus;
  /** 错误信息 */
  errorMessage: string | null;

  // 统计信息
  /** 仓库数量 */
  repositoriesCount: number;
  /** 提交数量 */
  commitsCount: number;
  /** README数量 */
  readmesCount: number;
  /** 技术栈文件数量 */
  techStackFilesCount: number;
  /** 时间统计数量 */
  timeStatsCount: number;

  // 时间戳
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
}

/**
 * 扩展数据状态检查结果
 */
export interface ExtendedDataStatusInfo {
  /** 数据是否存在 */
  exists: boolean;
  /** 数据是否已过期 */
  expired: boolean;
  /** 是否需要更新 */
  needsUpdate: boolean;
  /** 最后获取时间戳 */
  lastFetchedAt?: number;
  /** 过期时间戳 */
  expiresAt?: number;
  /** 当前更新状态 */
  updateStatus?: ExtendedDataStatus;
}

// ==================== 数据转换工具 ====================

/**
 * 数据库记录转换为业务数据
 */
export function parseExtendedDataRecord(
  record: GitHubExtendedDataRecord
): GitHubExtendedData | null {
  try {
    return {
      version: record.dataVersion,
      fetchedAt: record.fetchedAt,
      username: "", // 需要从用户表获取
      repositories: JSON.parse(record.repositoriesData || "[]"),
      commits: JSON.parse(record.commitsData || "[]"),
      readmes: JSON.parse(record.readmeData || "[]"),
      techStackFiles: JSON.parse(record.techStackFilesData || "[]"),
      timeStats: JSON.parse(record.timeStatsData || "[]"),
      fetchConfig: JSON.parse(record.fetchConfig || "{}"),
    };
  } catch (error) {
    console.error("Failed to parse extended data record:", error);
    return null;
  }
}

/**
 * 业务数据转换为数据库存储格式
 */
export function serializeExtendedData(
  userId: string,
  data: GitHubExtendedData,
  expiresAt: number
): Omit<GitHubExtendedDataRecord, "id" | "createdAt" | "updatedAt"> {
  return {
    userId,
    repositoriesData: JSON.stringify(data.repositories),
    commitsData: JSON.stringify(data.commits),
    readmeData: JSON.stringify(data.readmes),
    techStackFilesData: JSON.stringify(data.techStackFiles),
    timeStatsData: JSON.stringify(data.timeStats),
    fetchConfig: JSON.stringify(data.fetchConfig),
    dataVersion: data.version,
    fetchedAt: data.fetchedAt,
    expiresAt,
    updateStatus: "completed",
    errorMessage: null,
    repositoriesCount: data.repositories.length,
    commitsCount: data.commits.length,
    readmesCount: data.readmes.length,
    techStackFilesCount: data.techStackFiles.length,
    timeStatsCount: data.timeStats.length,
  };
}
