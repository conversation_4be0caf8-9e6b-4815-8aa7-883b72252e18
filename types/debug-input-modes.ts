/**
 * 调试平台输入模式类型定义
 *
 * 为 ModuleInputEditor 组件提供完整的类型支持
 * 包含三种输入模式的配置和验证逻辑
 */

import { ArrowDown, Edit, Layers } from "lucide-react";
import type { LucideIcon } from "lucide-react";

// 输入模式类型
export type InputMode = "upstream" | "manual" | "hybrid";

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 输入模式配置接口
export interface InputModeConfig {
  label: string;
  description: string;
  icon: LucideIcon;
  enabled: boolean;
  tooltip: string;
  shortcut?: string;
}

// 输入模式配置映射
export const INPUT_MODE_CONFIGS: Record<InputMode, InputModeConfig> = {
  upstream: {
    label: "上游数据",
    description: "使用上游模块的输出结果",
    icon: ArrowDown,
    enabled: true,
    tooltip: "直接使用上游模块的输出数据，无法编辑",
    shortcut: "Ctrl+U",
  },
  manual: {
    label: "手动输入",
    description: "完全手动输入数据",
    icon: Edit,
    enabled: true,
    tooltip: "完全手动输入数据，支持自定义任意内容",
    shortcut: "Ctrl+M",
  },
  hybrid: {
    label: "混合模式",
    description: "基于上游数据进行二次编辑",
    icon: Layers,
    enabled: true,
    tooltip: "基于上游数据进行二次编辑和调整",
    shortcut: "Ctrl+H",
  },
};

// 输入模式状态接口
export interface InputModeState {
  mode: InputMode;
  hasUpstreamData: boolean;
  hasManualData: boolean;
  canSwitch: boolean;
}

// 数据源信息接口
export interface DataSourceInfo {
  source: "upstream" | "manual" | "template" | "hybrid";
  timestamp: number;
  isModified: boolean;
  originalValue?: any;
}

// 模块输入编辑器的完整属性接口
export interface ModuleInputEditorProps<T> {
  // 基础属性
  label: string;
  value: T | null;
  onValueChange: (value: T | null) => void;

  // 输入模式相关
  inputMode: InputMode;
  onInputModeChange: (mode: InputMode) => void;

  // 数据源
  defaultTemplate: T;
  upstreamValue?: T | null;

  // 验证和配置
  validation?: (value: T) => ValidationResult;
  schema?: any; // JSON Schema for validation

  // UI 配置
  placeholder?: string;
  className?: string;
  height?: number;
  readOnly?: boolean;

  // 回调函数
  onValidationChange?: (result: ValidationResult) => void;
  onModeSwitch?: (oldMode: InputMode, newMode: InputMode) => void;
  onDataSourceChange?: (info: DataSourceInfo) => void;
}

// 输入模式切换的验证函数
export function canSwitchToMode(
  targetMode: InputMode,
  state: InputModeState
): { canSwitch: boolean; reason?: string } {
  switch (targetMode) {
    case "upstream":
      if (!state.hasUpstreamData) {
        return {
          canSwitch: false,
          reason: "没有可用的上游数据",
        };
      }
      return { canSwitch: true };

    case "manual":
      return { canSwitch: true };

    case "hybrid":
      if (!state.hasUpstreamData) {
        return {
          canSwitch: false,
          reason: "混合模式需要上游数据作为基础",
        };
      }
      return { canSwitch: true };

    default:
      return {
        canSwitch: false,
        reason: "未知的输入模式",
      };
  }
}

// 根据输入模式获取有效值的工具函数
export function getEffectiveValue<T>(
  mode: InputMode,
  upstreamValue: T | null,
  manualValue: T | null,
  defaultTemplate: T
): T | null {
  switch (mode) {
    case "upstream":
      return upstreamValue || null;

    case "manual":
      return manualValue || defaultTemplate;

    case "hybrid":
      if (upstreamValue && manualValue) {
        // 合并上游数据和手动数据
        if (
          typeof upstreamValue === "object" &&
          typeof manualValue === "object"
        ) {
          return { ...upstreamValue, ...manualValue };
        }
        return manualValue; // 如果不是对象，优先使用手动数据
      }
      return upstreamValue || manualValue || defaultTemplate;

    default:
      return null;
  }
}

// 验证JSON数据的工具函数
export function validateJSON(value: string): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  if (!value.trim()) {
    result.isValid = false;
    result.errors.push("数据不能为空");
    return result;
  }

  try {
    const parsed = JSON.parse(value);

    // 检查是否为有效的对象或数组
    if (typeof parsed !== "object" || parsed === null) {
      result.warnings.push("数据应该是一个有效的对象或数组");
    }

    // 检查数据大小
    if (JSON.stringify(parsed).length > 100000) {
      result.warnings.push("数据量较大，可能影响性能");
    }
  } catch (error) {
    result.isValid = false;
    result.errors.push(
      `JSON格式错误: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }

  return result;
}

// 格式化JSON数据的工具函数
export function formatJSON(value: any, indent: number = 2): string {
  try {
    return JSON.stringify(value, null, indent);
  } catch (error) {
    return typeof value === "string" ? value : String(value);
  }
}

// 深度比较两个值是否相等
export function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;

  if (a == null || b == null) return a === b;

  if (typeof a !== typeof b) return false;

  if (typeof a !== "object") return false;

  const keysA = Object.keys(a);
  const keysB = Object.keys(b);

  if (keysA.length !== keysB.length) return false;

  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }

  return true;
}
