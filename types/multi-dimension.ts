/**
 * 多维度评分系统类型定义
 *
 * 定义V4版本新增的四维度评分相关类型，确保前后端接口一致性
 */

// ==================== 基础评分类型 ====================

/**
 * 四维度评分基础接口
 */
export interface DimensionScores {
  /** 代码提交型评分 (0-100) */
  commitScore: number;
  /** 协作交流型评分 (0-100) */
  collaborationScore: number;
  /** 开源影响型评分 (0-100) */
  influenceScore: number;
  /** 学习探索型评分 (0-100) */
  explorationScore: number;
}

/**
 * 维度权重配置
 */
export interface DimensionWeights {
  commit: number;
  collaboration: number;
  influence: number;
  exploration: number;
}

/**
 * 完整的多维度评分数据
 */
export interface MultiDimensionScore extends DimensionScores {
  /** 综合得分 */
  totalScore: number;
  /** 综合等级 (S, A, B, C, D) */
  overallGrade: string;
  /** 最佳维度 */
  bestDimension: string;
  /** 最佳维度得分 */
  bestDimensionScore: number;
  /** 数据版本标识 */
  dataVersion: string;
  /** 评分计算时间 */
  calculatedAt: number;
}

// ==================== 维度类型枚举 ====================

import type { DimensionType } from "@/constants";

/**
 * 支持的维度类型
 */
export type { DimensionType } from "@/constants";

/**
 * 维度信息配置
 */
export interface DimensionConfig {
  key: DimensionType;
  label: string;
  description: string;
  icon: string;
  color: string;
}

// ==================== 用户档案类型 ====================

import type { UserData } from "./user-data";

// ==================== API 响应类型 ====================

/**
 * 多维度评分 API 响应
 */
export interface MultiDimensionScoreResponse {
  success: boolean;
  data: MultiDimensionScore;
  message?: string;
  timestamp: string;
}

// ==================== 组件 Props 类型 ====================

/**
 * 雷达图组件 Props
 */
export interface RadarChartProps {
  scores: DimensionScores;
  size?: "sm" | "md" | "lg";
  showLegend?: boolean;
  showTooltip?: boolean;
  animated?: boolean;
  className?: string;
}

/**
 * 进度条组件 Props
 */
export interface ProgressBarProps {
  dimension: DimensionType;
  score: number;
  label: string;
  description?: string;
  showValue?: boolean;
  animated?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
}

/**
 * 多维度卡片组件 Props
 */
export interface MultiDimensionCardProps {
  userProfile: UserData;
  multiDimensionScore: MultiDimensionScore;
  size?: "sm" | "md" | "lg";
  defaultView?: "radar" | "progress";
  showViewToggle?: boolean;
  showDetails?: boolean;
  className?: string;
  onClick?: () => void;
}

// ==================== 图表数据类型 ====================

/**
 * 雷达图数据点
 */
export interface RadarDataPoint {
  dimension: DimensionType;
  score: number;
  label: string;
  color: string;
  maxValue: number;
}

/**
 * 进度条数据
 */
export interface ProgressData {
  dimension: DimensionType;
  score: number;
  label: string;
  description: string;
  grade: string;
  color: string;
}

// ==================== 配置常量类型 ====================

/**
 * 颜色主题配置
 */
export interface ColorTheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  border: string;
}

/**
 * 维度颜色映射
 */
export type DimensionColorMap = Record<DimensionType, ColorTheme>;

/**
 * 等级颜色映射
 */
export type GradeColorMap = Record<string, string>;

// ==================== 工具函数类型 ====================

/**
 * 评分计算函数类型
 */
export type ScoreCalculator = (data: any) => number;

/**
 * 等级确定函数类型
 */
export type GradeCalculator = (score: number) => string;

/**
 * 数据格式化函数类型
 */
export type DataFormatter<T, R> = (data: T) => R;

// ==================== 导出便捷类型 ====================

/**
 * 所有维度评分的联合类型
 */
export type AllDimensionScores = DimensionScores & {
  [K in DimensionType as `${K}Score`]: number;
};

/**
 * 维度配置映射类型
 */
export type DimensionConfigMap = Record<DimensionType, DimensionConfig>;

// 注意：TypeScript类型不能作为运行时值导出
// 如需要，请使用 import type { TypeName } from './multi-dimension' 语法
