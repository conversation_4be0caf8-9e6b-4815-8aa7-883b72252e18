// R2图片队列数据结构 - Phase 4.1
// 简化架构：零CPU消耗的图片服务

export interface QueuedImage {
  id: string; // 图片ID (MD5 hash)
  r2Url: string; // R2公共访问URL
  contentType: string; // 图片MIME类型
  addedAt: number; // 添加时间戳
  expiry: number; // 过期时间戳
  sourceUrl: string; // 原始图片URL（用于调试和重新获取）
  accessCount: number; // 访问次数（用于统计）
}

export interface ImageCacheQueue {
  images: QueuedImage[];
  lastUpdated: number;
  version: string; // 数据结构版本
  totalServed: number; // 总服务次数
}

// 队列统计信息
export interface QueueStats {
  totalImages: number;
  activeImages: number;
  expiredImages: number;
  avgAccessCount: number;
  lastCleanup: number;
  r2StorageUsed: number; // 字节
}

// 垃圾清理结果
export interface CleanupResult {
  removedFromQueue: number;
  removedFromR2: number;
  freedSpace: number; // 字节
  errors: string[];
}

// 图片获取来源类型
export type ImageSource =
  | "pexels"
  | "unsplash"
  | "pixabay"
  | "direct"
  | "backup";

// 队列操作结果
export interface QueueOperationResult {
  success: boolean;
  imageId?: string;
  r2Url?: string;
  error?: string;
  queueSize?: number;
}
