import type { Stripe } from 'stripe';

export type StripeWebhookEvent = Stripe.Event;
export type StripeCheckoutSession = Stripe.Checkout.Session;
export type StripeSubscription = Stripe.Subscription & {
  current_period_start: number;
  current_period_end: number;
};
export type StripeInvoice = Stripe.Invoice & {
  subscription?: string | { id: string };
  payment_intent?: string | { id: string };
};

export interface WebhookHandlers {
  'checkout.session.completed': (session: StripeCheckoutSession) => Promise<void>;
  'customer.subscription.updated': (subscription: StripeSubscription) => Promise<void>;
  'customer.subscription.deleted': (subscription: StripeSubscription) => Promise<void>;
  'invoice.payment_succeeded': (invoice: StripeInvoice) => Promise<void>;
  'invoice.payment_failed': (invoice: StripeInvoice) => Promise<void>;
}
