/**
 * V5.3 调试平台 - 统一日志系统类型定义
 *
 * 提供模块执行、性能监控、错误跟踪的完整日志架构
 */

import type { ModuleType, ModuleOutput } from "@/lib/ai/types";

// =================================================================
// 核心日志数据结构
// =================================================================

export interface ModuleExecutionLog {
  /** 日志唯一标识符 */
  id: string;
  /** 执行会话ID，用于关联同一次调试会话的所有日志 */
  sessionId: string;
  /** 模块类型 */
  moduleType: ModuleType;
  /** 模块名称 */
  moduleName: string;
  /** 日志级别 */
  level: LogLevel;
  /** 执行状态 */
  status: ExecutionStatus;
  /** 日志消息 */
  message: string;
  /** 创建时间戳（毫秒） */
  timestamp: number;
  /** 执行开始时间戳 */
  startTime: number;
  /** 执行结束时间戳 */
  endTime?: number;
  /** 处理时间（毫秒） */
  processingTime?: number;
  /** 执行上下文信息 */
  context?: Record<string, any>;
  /** 性能指标 */
  metrics?: PerformanceMetrics;
  /** 错误详情（如果有） */
  error?: ErrorDetails;
  /** 输入数据快照（可选，用于调试） */
  inputSnapshot?: Record<string, any>;
  /** 输出数据快照（可选，用于调试） */
  outputSnapshot?: ModuleOutput;
  /** 元数据 */
  metadata?: Record<string, any>;
}

export type LogLevel =
  | "debug" // 详细调试信息
  | "info" // 一般信息
  | "warn" // 警告信息
  | "error" // 错误信息
  | "fatal" // 致命错误
  | "trace"; // 追踪信息

export type ExecutionStatus =
  | "pending" // 等待执行
  | "running" // 执行中
  | "completed" // 成功完成
  | "failed" // 执行失败
  | "timeout" // 超时
  | "cancelled" // 被取消
  | "retrying"; // 重试中

// =================================================================
// 执行上下文和性能指标
// =================================================================

export interface ExecutionContext {
  /** 重试次数 */
  retryCount: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 超时设置（毫秒） */
  timeout: number;
  /** 配置摘要 */
  configSummary?: string;
  /** 用户代理/环境信息 */
  userAgent?: string;
  /** 请求ID（用于追踪） */
  requestId?: string;
  /** 上游模块信息 */
  upstreamModule?: string;
  /** 自定义上下文数据 */
  customData?: Record<string, any>;
}

export interface PerformanceMetrics {
  /** 处理时间（毫秒） */
  processingTime?: number;
  /** 使用的Token数量 */
  tokensUsed?: number;
  /** API调用次数 */
  apiCalls?: number;
  /** 缓存命中率 */
  cacheHitRate?: number;
  /** 内存使用（MB） */
  memoryUsage?: number;
  /** CPU使用率（%） */
  cpuUsage?: number;
  /** 置信度分数 */
  confidence?: number;
  /** 质量分数 */
  qualityScore?: number;
  /** 网络延迟（毫秒） */
  networkLatency?: number;
  /** 队列等待时间 */
  queueTime?: number;
}

export interface ErrorDetails {
  /** 错误类型 */
  type: ErrorType;
  /** 错误代码 */
  code?: string;
  /** 错误消息 */
  message: string;
  /** 详细错误信息 */
  details?: string;
  /** 堆栈跟踪 */
  stackTrace?: string;
  /** 是否已尝试恢复 */
  recoveryAttempted: boolean;
  /** 恢复策略 */
  recoveryStrategy?: string;
  /** 相关请求信息 */
  requestInfo?: Record<string, any>;
}

export type ErrorType =
  | "validation" // 输入验证错误
  | "execution" // 执行逻辑错误
  | "timeout" // 超时错误
  | "api_error" // API调用错误
  | "network" // 网络错误
  | "parsing" // 数据解析错误
  | "configuration" // 配置错误
  | "resource" // 资源不足
  | "permission" // 权限错误
  | "unknown"; // 未知错误

// =================================================================
// 日志管理和查询
// =================================================================

export interface LogFilter {
  /** 会话ID过滤 */
  sessionIds?: string[];
  /** 模块类型过滤 */
  moduleTypes?: ModuleType[];
  /** 日志级别过滤 */
  levels?: LogLevel[];
  /** 执行状态过滤 */
  statuses?: ExecutionStatus[];
  /** 时间范围过滤 */
  timeRange?: {
    start: number;
    end: number;
  };
  /** 错误类型过滤 */
  errorTypes?: ErrorType[];
  /** 关键词搜索 */
  keywords?: string[];
  /** 最大结果数 */
  limit?: number;
  /** 排序方式 */
  sort?: LogSortOptions;
}

export interface LogSortOptions {
  /** 排序字段 */
  field: "timestamp" | "processingTime" | "level" | "status";
  /** 排序方向 */
  direction: "asc" | "desc";
}

export interface LogQueryResult {
  /** 日志条目 */
  logs: ModuleExecutionLog[];
  /** 总数量 */
  total: number;
  /** 是否有更多数据 */
  hasMore: boolean;
  /** 查询执行时间 */
  queryTime: number;
  /** 聚合统计 */
  aggregations?: LogAggregations;
}

export interface LogAggregations {
  /** 按模块类型分组的统计 */
  byModuleType: Record<ModuleType, LogStatistics>;
  /** 按状态分组的统计 */
  byStatus: Record<ExecutionStatus, number>;
  /** 按错误类型分组的统计 */
  byErrorType: Record<ErrorType, number>;
  /** 性能统计 */
  performance: {
    averageProcessingTime: number;
    medianProcessingTime: number;
    totalTokensUsed: number;
    averageConfidence: number;
  };
}

export interface LogStatistics {
  /** 总执行次数 */
  totalExecutions: number;
  /** 成功次数 */
  successCount: number;
  /** 失败次数 */
  failureCount: number;
  /** 成功率 */
  successRate: number;
  /** 平均处理时间 */
  averageProcessingTime: number;
  /** 总处理时间 */
  totalProcessingTime: number;
}

// =================================================================
// 日志管理器配置
// =================================================================

export interface LogManagerConfig {
  /** 最大存储的日志数量 */
  maxLogs: number;
  /** 日志保留时间（毫秒） */
  retentionTime: number;
  /** 是否启用持久化存储 */
  enablePersistence: boolean;
  /** 存储引擎类型 */
  storageEngine: "memory" | "localStorage" | "indexedDB";
  /** 批量写入大小 */
  batchSize: number;
  /** 自动清理间隔（毫秒） */
  cleanupInterval: number;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring: boolean;
  /** 是否启用错误追踪 */
  enableErrorTracking: boolean;
  /** 压缩级别（0-9） */
  compressionLevel: number;
}

// =================================================================
// 事件和回调
// =================================================================

export interface LogEvent {
  /** 事件类型 */
  type: LogEventType;
  /** 相关日志 */
  log: ModuleExecutionLog;
  /** 事件时间戳 */
  timestamp: number;
}

export interface StreamEvent {
  /** 事件类型 */
  type: "stream_event";
  /** 日志ID */
  logId: string;
  /** 会话ID */
  sessionId: string;
  /** 事件类型 */
  eventType: string;
  /** 阶段 */
  stage: string;
  /** 数据 */
  data: any;
  /** 时间戳 */
  timestamp: number;
}

export interface StreamContentUpdateEvent {
  /** 事件类型 */
  type: "stream_content_update";
  /** 日志ID */
  logId: string;
  /** 会话ID */
  sessionId: string;
  /** 模块类型 */
  moduleType: string;
  /** 阶段 */
  stage: string;
  /** 内容 */
  content: string;
  /** 累积内容 */
  accumulatedContent: string;
  /** 块数量 */
  chunkCount: number;
  /** 时间戳 */
  timestamp: number;
}

export type LogEventType =
  | "log_created" // 新日志创建
  | "log_updated" // 日志更新
  | "log_deleted" // 日志删除
  | "session_started" // 会话开始
  | "session_ended" // 会话结束
  | "stream_event" // 流式事件
  | "stream_content_update" // 流式内容更新（用于打字机效果）
  | "error_occurred" // 错误发生
  | "cleanup_started" // 清理开始
  | "cleanup_ended"; // 清理结束

export type AnyLogEvent = LogEvent | StreamEvent | StreamContentUpdateEvent;

export type LogEventCallback = (event: AnyLogEvent) => void;

// =================================================================
// 日志导出和报告
// =================================================================

export interface LogExportOptions {
  /** 导出格式 */
  format: "json" | "csv" | "xlsx" | "txt";
  /** 过滤条件 */
  filter?: LogFilter;
  /** 是否包含元数据 */
  includeMetadata: boolean;
  /** 是否压缩 */
  compress: boolean;
  /** 文件名前缀 */
  filenamePrefix?: string;
}

export interface SessionReport {
  /** 会话ID */
  sessionId: string;
  /** 会话开始时间 */
  startTime: number;
  /** 会话结束时间 */
  endTime?: number;
  /** 会话持续时间 */
  duration?: number;
  /** 执行的模块列表 */
  executedModules: ModuleType[];
  /** 总体状态 */
  overallStatus: "success" | "partial" | "failed";
  /** 性能概述 */
  performanceSummary: {
    totalProcessingTime: number;
    totalTokensUsed: number;
    averageConfidence: number;
    cacheHitRate: number;
  };
  /** 错误概述 */
  errorSummary: {
    totalErrors: number;
    errorsByType: Record<ErrorType, number>;
    criticalErrors: number;
  };
  /** 详细日志 */
  logs: ModuleExecutionLog[];
  /** 建议和优化提示 */
  recommendations?: string[];
}

// =================================================================
// 调试平台集成接口
// =================================================================

export interface DebugLoggerInterface {
  /** 记录模块执行开始 */
  logExecutionStart(
    sessionId: string,
    moduleType: ModuleType,
    moduleName: string,
    context?: Partial<ExecutionContext>
  ): string; // 返回日志ID

  /** 记录模块执行完成 */
  logExecutionComplete(
    logId: string,
    output: ModuleOutput,
    metrics?: PerformanceMetrics
  ): void;

  /** 记录模块执行失败 */
  logExecutionFailure(
    logId: string,
    error: Error,
    errorDetails?: Partial<ErrorDetails>
  ): void;

  /** 记录一般信息 */
  logInfo(
    sessionId: string,
    moduleType: ModuleType,
    message: string,
    context?: Record<string, any>
  ): void;

  /** 记录警告 */
  logWarning(
    sessionId: string,
    moduleType: ModuleType,
    message: string,
    context?: Record<string, any>
  ): void;

  /** 记录错误 */
  logError(
    sessionId: string,
    moduleType: ModuleType,
    error: Error,
    context?: Record<string, any>
  ): void;

  /** 查询日志 */
  queryLogs(filter: LogFilter): Promise<LogQueryResult>;

  /** 生成会话报告 */
  generateSessionReport(sessionId: string): Promise<SessionReport>;

  /** 导出日志 */
  exportLogs(options: LogExportOptions): Promise<Blob>;

  /** 清理过期日志 */
  cleanup(): Promise<number>; // 返回清理的日志数量
}
