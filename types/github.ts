export interface GitHubRepoResponse {
  stargazers_count: number;
  forks_count: number;
  name: string;
  full_name: string;
  language: string | null;
  created_at: string; // GitHub API 返回的创建时间字符串
  updated_at: string; // GitHub API 返回的更新时间字符串
}

export interface GitHubContributionsResponse {
  total_count: number;
}

// 基础的 GitHub 用户信息（直接来自 GitHub API）
export interface GitHubUserData {
  github_id: number;
  login: string;
  name: string;
  avatar_url: string;
  bio: string;
  blog: string;
  location: string;
  twitter_username: string;
  public_repos: number;
  followers: number;
  following: number;
  created_at: number;
}


export type GitHubContributionsData = {
  total_stars: number;
  contribution_score: number; // 保留：用于排行榜性能优化
  commits: number;
  pull_requests: number;
  public_repos: number;
  followers: number;
  following: number;
  issues: number;
  reviews: number;
  // V4 新增字段
  total_forks: number;
  contributed_repos: number;
  // V4.1 新增字段 - 语言统计（移除 language_diversity，通过 language_stats 计算）
  language_stats: LanguageStatsSummary;
};

// V4.1 简化：语言统计接口（去除分类和评分）
export interface LanguageStatEntry {
  language: string;
  totalBytes: number;
  repoCount: number;
  primaryRepoCount: number;
  percentageByBytes: number;
  percentageByRepos: number;
  languageRank: number;
  repos: Array<{
    name: string;
    bytes: number;
    isPrimary: boolean;
    createdAt: number;
    updatedAt: number;
  }>;
}

export interface LanguageStatsSummary {
  totalLanguages: number;
  totalBytes: number;
  analyzedRepos: number;
  languages: LanguageStatEntry[];
  primaryLanguage?: LanguageStatEntry;
  metadata: {
    analysisVersion: string;
    lastUpdated: number;
    cacheExpiry: number;
  };
}

// API限流相关类型
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  used: number;
}

export interface TokenInfo {
  token: string;
  isActive: boolean;
  rateLimitInfo?: RateLimitInfo;
  lastUsed: number;
  errorCount: number;
}
