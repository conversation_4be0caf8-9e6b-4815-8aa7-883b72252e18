/**
 * 类型安全的API响应定义
 * 替换所有使用 any 的地方
 */

import type { CompleteUserResponse } from "./user-data";
import type {
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
} from "@/lib/ai/types";

// ==================== GitHub Data API 响应类型 ====================

/**
 * GitHub数据API的成功响应
 */
export interface GitHubDataSuccessResponse {
  success: true;
  data: CompleteUserResponse;
  message?: string;
}

/**
 * GitHub数据API的错误响应
 */
export interface GitHubDataErrorResponse {
  success: false;
  error: string;
  message?: string;
}

/**
 * GitHub数据API响应联合类型
 */
export type GitHubDataResponse =
  | GitHubDataSuccessResponse
  | GitHubDataErrorResponse;

// ==================== AI模块调试API响应类型 ====================

/**
 * Analyzer模块调试响应
 */
export interface AnalyzerDebugResponse {
  success: true;
  result: AnalyzerOutput;
  processingTime: number;
  confidence: number;
  metadata: {
    module: "analyzer";
    sessionId: string;
    timestamp: number;
    userId: string;
    [key: string]: unknown;
  };
}

/**
 * Strategist模块调试响应
 */
export interface StrategistDebugResponse {
  success: true;
  result: StrategistOutput;
  processingTime: number;
  confidence: number;
  metadata: {
    module: "strategist";
    sessionId: string;
    timestamp: number;
    userId: string;
    [key: string]: unknown;
  };
}

/**
 * Writer模块调试响应
 */
export interface WriterDebugResponse {
  success: true;
  result: WriterOutput;
  processingTime: number;
  confidence: number;
  metadata: {
    module: "writer";
    sessionId: string;
    timestamp: number;
    userId: string;
    [key: string]: unknown;
  };
}

/**
 * Critic模块调试响应
 */
export interface CriticDebugResponse {
  success: true;
  result: CriticOutput;
  processingTime: number;
  confidence: number;
  metadata: {
    module: "critic";
    sessionId: string;
    timestamp: number;
    userId: string;
    [key: string]: unknown;
  };
}

/**
 * AI模块调试错误响应
 */
export interface ModuleDebugErrorResponse {
  success: false;
  processingTime: number;
  error: string;
  metadata?: {
    module?: string;
    sessionId?: string;
    timestamp?: number;
    userId?: string;
    validationErrors?: unknown[];
    code?: string;
    details?: unknown;
    [key: string]: unknown;
  };
}

// ==================== 响应类型联合 ====================

/**
 * 所有AI模块调试成功响应的联合类型
 */
export type ModuleDebugSuccessResponse =
  | AnalyzerDebugResponse
  | StrategistDebugResponse
  | WriterDebugResponse
  | CriticDebugResponse;

/**
 * AI模块调试响应联合类型
 */
export type ModuleDebugResponse =
  | ModuleDebugSuccessResponse
  | ModuleDebugErrorResponse;

// ==================== 类型守卫函数 ====================

/**
 * 检查是否为GitHub数据成功响应
 */
export function isGitHubDataSuccessResponse(
  response: GitHubDataResponse
): response is GitHubDataSuccessResponse {
  return response.success === true;
}

/**
 * 检查是否为GitHub数据错误响应
 */
export function isGitHubDataErrorResponse(
  response: GitHubDataResponse
): response is GitHubDataErrorResponse {
  return response.success === false;
}

/**
 * 检查是否为模块调试成功响应
 */
export function isModuleDebugSuccessResponse(
  response: ModuleDebugResponse
): response is ModuleDebugSuccessResponse {
  return response.success === true;
}

/**
 * 检查是否为模块调试错误响应
 */
export function isModuleDebugErrorResponse(
  response: ModuleDebugResponse
): response is ModuleDebugErrorResponse {
  return response.success === false;
}

/**
 * 检查是否为Analyzer响应
 */
export function isAnalyzerResponse(
  response: ModuleDebugSuccessResponse
): response is AnalyzerDebugResponse {
  return response.metadata.module === "analyzer";
}

/**
 * 检查是否为Strategist响应
 */
export function isStrategistResponse(
  response: ModuleDebugSuccessResponse
): response is StrategistDebugResponse {
  return response.metadata.module === "strategist";
}

/**
 * 检查是否为Writer响应
 */
export function isWriterResponse(
  response: ModuleDebugSuccessResponse
): response is WriterDebugResponse {
  return response.metadata.module === "writer";
}

/**
 * 检查是否为Critic响应
 */
export function isCriticResponse(
  response: ModuleDebugSuccessResponse
): response is CriticDebugResponse {
  return response.metadata.module === "critic";
}
