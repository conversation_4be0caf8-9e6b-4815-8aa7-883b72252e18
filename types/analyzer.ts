/**
 * Analyzer 类型定义
 * 基于 analyzer-map.json 的分析结果类型定义
 *
 * 设计原则：
 * - 类型安全优先：严格的 TypeScript 类型定义
 * - 约定大于配置：提供合理的默认值和结构
 * - 尽早报错：明确的必需字段和验证
 */

import type { UserData } from "./user-data";
import type { GitHubExtendedData } from "./github-extended";

// =================================================================
// 核心分析结果类型
// =================================================================

/**
 * 单个分析项的结果
 */
export interface AnalysisItem {
  /** 分析项名称，对应 analyzer-map.json 中的 key */
  name: string;
  /** 分析结果值，来自 analyzer-map.json 中的 values 数组 */
  value: string;
  /** 数据来源描述 */
  source: string;
  /** 是否使用了 LLM 分析 */
  usedLLM: boolean;
  /** 对标签的创造性解读，为策略模块提供情感色彩素材池 */
  enrichedNarrative: string;
  /** 原始数据（用于调试和验证） */
  rawData?: any;
  /** 分析过程的元数据 */
  metadata?: {
    /** 处理时间（毫秒） */
    processingTime: number;
    /** 数据完整性评分 (0-1) */
    dataCompleteness: number;
    /** 错误信息（如果有） */
    error?: string;
  };
}

/**
 * 分析器的完整输出结果
 */
export interface AnalyzerOutput {
  /** 执行状态 */
  status: "success" | "partial" | "error";
  /** 分析结果列表 */
  results: AnalysisItem[];
  /** 整体分析摘要 */
  summary: {
    /** 成功分析的项目数 */
    successCount: number;
    /** 总分析项目数 */
    totalCount: number;
    /** 数据质量评估 */
    dataQuality: "excellent" | "good" | "fair" | "poor";
  };
  /** 执行元数据 */
  metadata: {
    /** 总处理时间（毫秒） */
    totalProcessingTime: number;
    /** 使用的数据版本 */
    dataVersion: string;
    /** LLM 调用次数 */
    llmCallCount: number;
    /** 分析时间戳 */
    timestamp: number;
    /** 错误详情（如果有） */
    errors?: string[];
  };
}

// =================================================================
// 输入数据类型
// =================================================================

/**
 * 分析器输入数据
 */
export interface AnalyzerInput {
  /** 基础用户数据 */
  userData: UserData;
  /** GitHub 扩展数据 */
  extendedData: GitHubExtendedData;
  /** 分析配置 */
  config?: AnalyzerConfig;
}

/**
 * 分析器配置
 */
export interface AnalyzerConfig {
  /** 要包含的分析项（空数组表示全部） */
  includeItems?: AnalysisItemName[];
  /** 要排除的分析项 */
  excludeItems?: AnalysisItemName[];
  /** 是否启用 LLM 分析 */
  enableLLM?: boolean;
  /** LLM 调用超时时间（毫秒） */
  llmTimeout?: number;
}

// =================================================================
// 分析项名称类型（基于 analyzer-map.json）
// =================================================================

/**
 * 所有支持的分析项名称
 */
export type AnalysisItemName =
  | "commit_density"
  | "commit_consistency"
  | "commit_length"
  | "commit_sentiment"
  | "repo_naming_style"
  | "active_hours"
  | "weekend_dev"
  | "long_pause_gaps"
  | "language_diversity"
  | "framework_focus"
  | "experimental_ratio"
  | "star_to_follower_ratio";

// =================================================================
// 分析项值类型（基于 analyzer-map.json 的 values）
// =================================================================

export type CommitDensityValue = "low" | "medium" | "high";
export type CommitConsistencyValue = "random" | "streak: x days" | "seasonal";
export type CommitLengthValue = "terse" | "verbose" | "poetic";
export type CommitSentimentValue = "neutral" | "joking" | "dramatic";
export type RepoNamingStyleValue = "thematic" | "random" | "poetic";
export type ActiveHoursValue = "earlybird" | "night_owl" | "insomniac";
export type WeekendDevValue = "yes" | "no" | "only_weekends";
export type LongPauseGapsValue = "no_gap" | "occasional" | "huge_gap";
export type LanguageDiversityValue = "mono" | "multi:3" | "multi:5" | "chaotic";
export type FrameworkFocusValue = "react_purist" | "rustacean" | "scatter";
export type ExperimentalRatioValue = "low" | "moderate" | "high";
export type StarToFollowerRatioValue = "stealthy" | "popular" | "asymmetrical";

/**
 * 所有分析项值的联合类型
 */
export type AnalysisItemValue =
  | CommitDensityValue
  | CommitConsistencyValue
  | CommitLengthValue
  | CommitSentimentValue
  | RepoNamingStyleValue
  | ActiveHoursValue
  | WeekendDevValue
  | LongPauseGapsValue
  | LanguageDiversityValue
  | FrameworkFocusValue
  | ExperimentalRatioValue
  | StarToFollowerRatioValue;

// =================================================================
// 分析器映射配置类型
// =================================================================

/**
 * 单个分析项的配置（对应 analyzer-map.json 中的每一项）
 */
export interface AnalysisItemConfig {
  /** 数据源描述 */
  source: string;
  /** 提取器描述 */
  extractor: string;
  /** 是否需要 LLM */
  requiresLLM: boolean;
  /** 可能的值列表 */
  values: string[];
  /** 分析器名称（如果有特定的分析器） */
  analyzer?: string;
}

/**
 * 完整的分析器映射配置
 */
export type AnalyzerMapConfig = Record<AnalysisItemName, AnalysisItemConfig>;

// =================================================================
// 默认配置
// =================================================================

/**
 * 默认分析器配置
 */
export const DEFAULT_ANALYZER_CONFIG: Required<AnalyzerConfig> = {
  includeItems: [], // 空数组表示包含所有项
  excludeItems: [],
  enableLLM: true,
  llmTimeout: 30000, // 30秒
};

/**
 * 需要 LLM 的分析项列表
 */
export const LLM_REQUIRED_ITEMS: AnalysisItemName[] = [
  "commit_sentiment",
  "repo_naming_style",
];

/**
 * 算法分析项列表
 */
export const ALGORITHM_ITEMS: AnalysisItemName[] = [
  "commit_density",
  "commit_consistency",
  "commit_length",
  "active_hours",
  "weekend_dev",
  "long_pause_gaps",
  "language_diversity",
  "framework_focus",
  "experimental_ratio",
  "star_to_follower_ratio",
];

// =================================================================
// 工具函数类型
// =================================================================

/**
 * 分析项验证函数类型
 */
export type AnalysisItemValidator = (
  value: string,
  config: AnalysisItemConfig
) => boolean;

/**
 * 分析器函数类型
 */
export type AnalyzerFunction = (
  input: AnalyzerInput,
  itemName: AnalysisItemName
) => Promise<AnalysisItem>;
