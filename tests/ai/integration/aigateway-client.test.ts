/**
 * AIGatewayClient Integration Tests
 * 测试 Vercel AI Gateway 客户端的完整功能
 */

import {
  AIGatewayClient,
  AIGatewayClientError,
} from "../../../lib/ai/core/clients/AIGatewayClient";
import { AI_GATEWAY_MODELS } from "../../../lib/ai/core/clients/AIGatewayClient";

// Polyfill for Node.js environment
if (typeof ReadableStream === "undefined") {
  global.ReadableStream = require("stream/web").ReadableStream;
}
if (typeof TextEncoder === "undefined") {
  global.TextEncoder = require("util").TextEncoder;
}
if (typeof TextDecoder === "undefined") {
  global.TextDecoder = require("util").TextDecoder;
}

// Mock fetch for testing
global.fetch = jest.fn();
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("AIGatewayClient", () => {
  let client: AIGatewayClient;
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset environment variables
    process.env = { ...originalEnv };
    process.env.AI_GATEWAY_API_KEY = "test-api-key";

    // Reset fetch mock
    mockFetch.mockReset();

    // Create new client instance
    client = new AIGatewayClient();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe("Constructor", () => {
    it("should initialize with valid API key", () => {
      expect(client).toBeInstanceOf(AIGatewayClient);
    });

    it("should throw error when API key is missing", () => {
      delete process.env.AI_GATEWAY_API_KEY;

      expect(() => new AIGatewayClient()).toThrow(AIGatewayClientError);
      expect(() => new AIGatewayClient()).toThrow(
        "AI_GATEWAY_API_KEY environment variable is required"
      );
    });

    it("should use custom configuration from environment", () => {
      process.env.AI_GATEWAY_APP_NAME = "Custom-App";
      process.env.AI_GATEWAY_DEFAULT_MODEL = "anthropic/claude-sonnet-4";

      const customClient = new AIGatewayClient();
      expect(customClient).toBeInstanceOf(AIGatewayClient);
    });
  });

  describe("Basic Chat", () => {
    it("should make successful chat request", async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              role: "assistant",
              content: "Hello! How can I help you today?",
            },
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 8,
          total_tokens: 18,
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const response = await client.chat({
        messages: [{ role: "user", content: "Hello" }],
        temperature: 0.7,
        max_tokens: 100,
      });

      expect(response).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        "https://ai-gateway.vercel.sh/v1/chat/completions",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            Authorization: "Bearer test-api-key",
            "Content-Type": "application/json",
          }),
          body: expect.stringContaining("openai/gpt-4o"), // Default model
        })
      );
    });

    it("should use custom model when specified", async () => {
      const mockResponse = {
        choices: [{ message: { role: "assistant", content: "Response" } }],
        usage: { prompt_tokens: 5, completion_tokens: 5, total_tokens: 10 },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await client.chat({
        model: AI_GATEWAY_MODELS.CLAUDE_SONNET_4,
        messages: [{ role: "user", content: "Test" }],
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining("anthropic/claude-sonnet-4"),
        })
      );
    });

    it("should handle API errors properly", async () => {
      const errorResponse = {
        error: {
          message: "Invalid API key",
          type: "authentication_error",
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: "Unauthorized",
        json: async () => errorResponse,
      } as Response);

      await expect(
        client.chat({
          messages: [{ role: "user", content: "Test" }],
        })
      ).rejects.toThrow(AIGatewayClientError);
    });
  });

  describe("Streaming Chat", () => {
    it("should handle streaming responses", async () => {
      const mockStreamData = [
        'data: {"choices":[{"delta":{"content":"Hello"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":" world"}}]}\n\n',
        "data: [DONE]\n\n",
      ];

      const mockStream = new ReadableStream({
        start(controller) {
          mockStreamData.forEach((chunk) => {
            controller.enqueue(new TextEncoder().encode(chunk));
          });
          controller.close();
        },
      });

      mockFetch.mockResolvedValueOnce({
        ok: true,
        body: mockStream,
      } as Response);

      const chunks: any[] = [];
      for await (const chunk of client.chatStream({
        messages: [{ role: "user", content: "Hello" }],
        stream: true,
      })) {
        chunks.push(chunk);
      }

      expect(chunks).toHaveLength(2);
      expect(chunks[0].choices[0].delta.content).toBe("Hello");
      expect(chunks[1].choices[0].delta.content).toBe(" world");
    });

    it("should handle streaming errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
        json: async () => ({ error: { message: "Server error" } }),
      } as Response);

      const streamGenerator = client.chatStream({
        messages: [{ role: "user", content: "Test" }],
      });

      await expect(streamGenerator.next()).rejects.toThrow(
        AIGatewayClientError
      );
    });
  });

  describe("Multimodal Support", () => {
    it("should handle image content", async () => {
      const mockResponse = {
        choices: [
          { message: { role: "assistant", content: "I can see an image" } },
        ],
        usage: { prompt_tokens: 20, completion_tokens: 10, total_tokens: 30 },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await client.chat({
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: "What's in this image?" },
              {
                type: "image",
                image_url: {
                  url: "data:image/jpeg;base64,test-image-data",
                },
              },
            ],
          },
        ],
      });

      const requestBody = JSON.parse(
        mockFetch.mock.calls[0][1]?.body as string
      );
      expect(requestBody.messages[0].content).toHaveLength(2);
      expect(requestBody.messages[0].content[1].type).toBe("image_url");
    });
  });

  describe("Tool Calling", () => {
    it("should support function calling", async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              role: "assistant",
              tool_calls: [
                {
                  id: "call_123",
                  type: "function",
                  function: {
                    name: "get_weather",
                    arguments: '{"location": "Beijing"}',
                  },
                },
              ],
            },
          },
        ],
        usage: { prompt_tokens: 15, completion_tokens: 12, total_tokens: 27 },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await client.chat({
        messages: [{ role: "user", content: "What's the weather in Beijing?" }],
        tools: [
          {
            type: "function",
            function: {
              name: "get_weather",
              description: "Get weather for a location",
              parameters: {
                type: "object",
                properties: {
                  location: { type: "string" },
                },
                required: ["location"],
              },
            },
          },
        ],
        tool_choice: "auto",
      });

      const requestBody = JSON.parse(
        mockFetch.mock.calls[0][1]?.body as string
      );
      expect(requestBody.tools).toBeDefined();
      expect(requestBody.tool_choice).toBe("auto");
    });
  });

  describe("Error Handling and Retry", () => {
    it("should retry on retryable errors", async () => {
      // First call fails with 500 (retryable)
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: "Internal Server Error",
          json: async () => ({ error: { message: "Server error" } }),
        } as Response)
        // Second call succeeds
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            choices: [{ message: { role: "assistant", content: "Success" } }],
            usage: { prompt_tokens: 5, completion_tokens: 5, total_tokens: 10 },
          }),
        } as Response);

      const response = await client.chat({
        messages: [{ role: "user", content: "Test" }],
      });

      expect(response.choices[0].message.content).toBe("Success");
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it("should not retry on non-retryable errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: "Bad Request",
        json: async () => ({ error: { message: "Invalid request" } }),
      } as Response);

      await expect(
        client.chat({
          messages: [{ role: "user", content: "Test" }],
        })
      ).rejects.toThrow(AIGatewayClientError);

      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });

  describe("Performance Monitoring", () => {
    it("should track performance statistics", async () => {
      const mockResponse = {
        choices: [{ message: { role: "assistant", content: "Response" } }],
        usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await client.chat({
        messages: [{ role: "user", content: "Test" }],
      });

      const stats = client.getPerformanceStats();
      expect(stats.totalRequests).toBe(1);
      expect(stats.totalTokens).toBe(15);
      expect(stats.avgTokensPerRequest).toBe(15);
    });

    it("should track request metrics", async () => {
      const mockResponse = {
        choices: [{ message: { role: "assistant", content: "Response" } }],
        usage: { prompt_tokens: 8, completion_tokens: 7, total_tokens: 15 },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await client.chat({
        messages: [{ role: "user", content: "Test" }],
      });

      const metrics = client.getRequestMetrics() as any[];
      expect(metrics).toHaveLength(1);
      expect(metrics[0].requestId).toMatch(/^aigateway_/);
      expect(metrics[0].startTime).toBeDefined();
      expect(metrics[0].endTime).toBeDefined();
    });
  });

  describe("Health Check", () => {
    it("should return true for healthy service", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [] }),
      } as Response);

      const isHealthy = await client.healthCheck();
      expect(isHealthy).toBe(true);
    });

    it("should return false for unhealthy service", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      const isHealthy = await client.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });
});
