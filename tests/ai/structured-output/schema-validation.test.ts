/**
 * Schema Validation Tests
 * 测试新的结构化输出 Schema 定义和验证功能
 */

// Jest 测试框架
import {
  schemaManager,
  AIModuleType,
  StrategistOutputSchema,
  CriticOutputSchema,
  SimplifiedEnrichedNarrativeSchema,
  SimplifiedEnrichedNarrativeResponseFormat,
} from "@/lib/ai/schemas";

describe("Schema Manager", () => {
  it("should return correct response format for supported modules", () => {
    const strategistFormat = schemaManager.getResponseFormat(
      AIModuleType.STRATEGIST
    );
    expect(strategistFormat).toBeDefined();
    expect(strategistFormat?.type).toBe("json_object");
    expect(strategistFormat?.json_schema?.name).toBe("strategist_output");

    const criticFormat = schemaManager.getResponseFormat(AIModuleType.CRITIC);
    expect(criticFormat).toBeDefined();
    expect(criticFormat?.type).toBe("json_object");
    expect(criticFormat?.json_schema?.name).toBe("critic_output");
  });

  it("should return null for unsupported modules", () => {
    const writerFormat = schemaManager.getResponseFormat(AIModuleType.WRITER);
    expect(writerFormat).toBeNull();
  });

  it("should correctly identify supported modules", () => {
    expect(
      schemaManager.supportsStructuredOutput(AIModuleType.STRATEGIST)
    ).toBe(true);
    expect(schemaManager.supportsStructuredOutput(AIModuleType.CRITIC)).toBe(
      true
    );
    expect(schemaManager.supportsStructuredOutput(AIModuleType.WRITER)).toBe(
      false
    );
  });
});

describe("Schema Validation", () => {
  it("should validate StrategistOutput data correctly", () => {
    const validData = {
      keyTalkingPoints: ["point1", "point2"],
      creativeFocus: "test focus",
      humorAngle: "test humor",
      confidence: 0.8,
    };

    const isValid = schemaManager.validateData(
      AIModuleType.STRATEGIST,
      validData
    );
    expect(isValid).toBe(true);
  });

  it("should reject invalid StrategistOutput data", () => {
    const invalidData = {
      keyTalkingPoints: ["point1"],
      // missing creativeFocus and humorAngle
      confidence: 0.8,
    };

    const isValid = schemaManager.validateData(
      AIModuleType.STRATEGIST,
      invalidData
    );
    expect(isValid).toBe(false);
  });

  it("should validate CriticOutput data correctly", () => {
    const validData = {
      dimensions: {
        humor: 85,
        compliance: 90,
        originality: 80,
        naturalness: 88,
        relevance: 92,
      },
      detailedAssessment: "Test assessment",
      confidence: 0.9,
    };

    const isValid = schemaManager.validateData(AIModuleType.CRITIC, validData);
    expect(isValid).toBe(true);
  });

  it("should reject invalid CriticOutput data", () => {
    const invalidData = {
      dimensions: {
        humor: 85,
        // missing other required dimensions
      },
      confidence: 0.9,
      // missing detailedAssessment
    };

    const isValid = schemaManager.validateData(
      AIModuleType.CRITIC,
      invalidData
    );
    expect(isValid).toBe(false);
  });
});

describe("Schema Definitions", () => {
  it("should have correct StrategistOutput schema structure", () => {
    expect(StrategistOutputSchema.type).toBe("object");
    expect(StrategistOutputSchema.required).toContain("keyTalkingPoints");
    expect(StrategistOutputSchema.required).toContain("creativeFocus");
    expect(StrategistOutputSchema.required).toContain("humorAngle");
    expect(StrategistOutputSchema.required).toContain("confidence");
  });

  it("should have correct CriticOutput schema structure", () => {
    expect(CriticOutputSchema.type).toBe("object");
    expect(CriticOutputSchema.required).toContain("dimensions");
    expect(CriticOutputSchema.required).toContain("detailedAssessment");
    expect(CriticOutputSchema.required).toContain("confidence");
  });
});

describe("Simplified EnrichedNarrative Schema", () => {
  it("should have correct structure for token optimization", () => {
    expect(SimplifiedEnrichedNarrativeSchema.type).toBe("object");
    expect(SimplifiedEnrichedNarrativeSchema.required).toContain(
      "enrichedNarratives"
    );

    const narrativesSchema =
      SimplifiedEnrichedNarrativeSchema.properties?.enrichedNarratives;
    expect(narrativesSchema?.type).toBe("array");

    const itemSchema = (narrativesSchema as any)?.items;
    expect(itemSchema?.required).toEqual(["name", "enrichedNarrative"]);
    expect(itemSchema?.required).toHaveLength(2); // 确保只有2个必需字段
  });

  it("should validate simplified enriched narrative data correctly", () => {
    const validData = {
      enrichedNarratives: [
        {
          name: "commit_frequency",
          enrichedNarrative: "提交频率低得像笔友回信，可能他家网速不太好",
        },
        {
          name: "code_quality",
          enrichedNarrative:
            "用写遗嘱的精神在写 commit 日志，生怕有哪一笔精神遗产没安排好",
        },
      ],
    };

    // 简单的结构验证 - 检查必需字段是否存在
    expect(validData.enrichedNarratives).toBeDefined();
    expect(Array.isArray(validData.enrichedNarratives)).toBe(true);
    expect(validData.enrichedNarratives.length).toBeGreaterThan(0);

    validData.enrichedNarratives.forEach((item) => {
      expect(item.name).toBeDefined();
      expect(item.enrichedNarrative).toBeDefined();
      expect(typeof item.name).toBe("string");
      expect(typeof item.enrichedNarrative).toBe("string");
    });
  });

  it("should have correct response format configuration", () => {
    expect(SimplifiedEnrichedNarrativeResponseFormat.type).toBe("json_object");
    expect(SimplifiedEnrichedNarrativeResponseFormat.json_schema.name).toBe(
      "simplified_enriched_narrative_completion"
    );
    expect(SimplifiedEnrichedNarrativeResponseFormat.json_schema.strict).toBe(
      true
    );
  });
});
