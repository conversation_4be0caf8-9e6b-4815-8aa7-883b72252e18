{"patterns": {"api_keys": [], "credentials": ["password\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?", "passwd\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?", "secret\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?", "private[_-]?key\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?", "access[_-]?key\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?", "auth[_-]?token\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?", "api[_-]?key\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?", "client[_-]?secret\\s*[:=]\\s*[\"']?[^\\s\"']+[\"']?"], "sensitive_files": [".env", ".env.local", ".env.production", ".env.development", ".env.staging", "credentials.json", "google-credentials.json", "service-account.json", "private.key", "id_rsa", "id_ed25519", "id_dsa", "id_ecdsa", ".pem", ".key", ".p12", ".pfx", "keystore", ".jks"], "regex_patterns": ["sk-[a-zA-Z0-9]{32,}", "sk-proj-[a-zA-Z0-9]{32,}", "AIza[0-9A-Za-z\\-_]{35}", "gsk_[a-zA-Z0-9]{32,}", "AKIA[0-9A-Z]{16}", "aws_secret_access_key\\s*=\\s*[a-zA-Z0-9/+=]{40}", "(postgres|postgresql|mysql|mongodb|redis)://[^:]+:[^@]+@[^/]+", "-----BEGIN.*(RSA|DSA|EC|OPENSSH|PGP).*PRIVATE KEY-----", "Bearer\\s+[a-zA-Z0-9\\-_]+\\.[a-zA-Z0-9\\-_]+\\.[a-zA-Z0-9\\-_]+", "[A-Z_]+(KEY|TOKEN|SECRET|PASSWORD|PASSWD)\\s*=\\s*[\"']?[^\\s\"']+[\"']?", "(api[_-]?key|access[_-]?token|auth[_-]?token)\\s*[:=]\\s*[\"']?[a-zA-Z0-9_\\-]{20,}[\"']?", "(password|secret|key)\\s*[:=]\\s*[\"']?[A-Za-z0-9+/]{40,}={0,2}[\"']?", "ghp_[a-zA-Z0-9]{36}", "gho_[a-zA-Z0-9]{36}", "github_pat_[a-zA-Z0-9]{22}_[a-zA-Z0-9]{59}", "glpat-[a-zA-Z0-9\\-_]{20}", "xoxb-[0-9]{10,13}-[0-9]{10,13}-[a-zA-Z0-9]{24}", "sq0atp-[0-9A-Za-z\\-_]{22}", "sq0csp-[0-9A-Za-z\\-_]{43}", "SK[a-z0-9]{32}"]}, "whitelist": {"allowed_mentions": ["GOOGLE_API_KEY=your_google_api_key_here", "GROQ_API_KEY=your_groq_api_key_here", "OPENAI_API_KEY=your_openai_api_key_here", "GEMINI_API_KEY=your_gemini_api_key_here", "AWS_ACCESS_KEY_ID=your_access_key_here", "api_key=settings.api_key", "api_key=process.env.API_KEY", "api_key=YOUR_API_KEY", "password=your_password_here", "password=<your_password>", "password=${PASSWORD}", "token=your_token_here", "token=<auth_token>", "secret=your_secret_here", "Bearer YOUR_TOKEN_HERE", "Bearer <token>", "postgres://user:password@localhost", "mysql://root:password@localhost", "*********************************", "redis://user:password@localhost", "connection_string = \"your_connection_string_here\"", "API_KEY=<your_api_key>", "SECRET_KEY=<your_secret_key>", "DATABASE_URL=<your_database_url>"]}}