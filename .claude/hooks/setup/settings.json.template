{"hooks": {"PreToolUse": [{"matcher": "mcp__gemini__consult_gemini", "hooks": [{"type": "command", "command": "${WORKSPACE}/.claude/hooks/gemini-context-injector.sh", "description": "Automatically adds project structure to new Gemini sessions"}]}, {"matcher": "mcp__.*", "hooks": [{"type": "command", "command": "${WORKSPACE}/.claude/hooks/mcp-security-scan.sh", "description": "Scans for sensitive data before sending to external services"}]}, {"matcher": "Task", "hooks": [{"type": "command", "command": "${WORKSPACE}/.claude/hooks/subagent-context-injector.sh", "description": "Automatically adds project context to sub-agent prompts"}]}], "Notification": [{"matcher": ".*", "hooks": [{"type": "command", "command": "${WORKSPACE}/.claude/hooks/notify.sh input", "description": "Plays sound when <PERSON> needs user input"}]}], "Stop": [{"matcher": ".*", "hooks": [{"type": "command", "command": "${WORKSPACE}/.claude/hooks/notify.sh complete", "description": "Plays sound when <PERSON> completes tasks"}]}]}, "environment": {"WORKSPACE": "/path/to/your/project"}}