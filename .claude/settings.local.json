{"environment": {"WORKSPACE": "/Users/<USER>/workspace/tower1229/github-card"}, "experimentalTools": {"notify": {"commandAfterRun": "bash /Users/<USER>/workspace/tower1229/github-card/.claude/hooks/notify.sh", "commandAfterUserInput": "bash /Users/<USER>/workspace/tower1229/github-card/.claude/hooks/notify.sh input"}}, "experimentalHooks": {"preToolUse": "bash /Users/<USER>/workspace/tower1229/github-card/.claude/hooks/mcp-security-scan.sh", "preToolUse_gemini": "bash /Users/<USER>/workspace/tower1229/github-card/.claude/hooks/gemini-context-injector.sh", "preToolUse_task": "bash /Users/<USER>/workspace/tower1229/github-card/.claude/hooks/subagent-context-injector.sh"}}