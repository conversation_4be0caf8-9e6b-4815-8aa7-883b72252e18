"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Copy,
  Check,
  ChevronDown,
  FileText,
  Minimize2,
  Code,
  Hash,
} from "lucide-react";
import toast from "react-hot-toast";

export type CopyFormat = "json" | "compact" | "formatted" | "raw";

// 可复制的数据类型
type CopyableData =
  | Record<string, unknown>
  | Record<PropertyKey, unknown>
  | unknown[]
  | string
  | number
  | boolean
  | null
  | object;

interface CopyFormatConfig {
  id: CopyFormat;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  transform: (data: CopyableData) => string;
}

const COPY_FORMATS: CopyFormatConfig[] = [
  {
    id: "formatted",
    label: "格式化 JSON",
    description: "美观的缩进格式，适合阅读",
    icon: Code,
    transform: (data: CopyableData) => JSON.stringify(data, null, 2),
  },
  {
    id: "compact",
    label: "紧凑 JSON",
    description: "单行格式，节省空间",
    icon: Minimize2,
    transform: (data: CopyableData) => JSON.stringify(data),
  },
  {
    id: "raw",
    label: "原始字符串",
    description: "纯文本内容，不含JSON结构",
    icon: FileText,
    transform: (data: CopyableData) => {
      if (typeof data === "string") return data;
      if (data && typeof data === "object" && !Array.isArray(data)) {
        // 尝试提取文本内容
        const obj = data as Record<string, unknown>;
        if (obj.content && typeof obj.content === "string") return obj.content;
        if (obj.generatedText && typeof obj.generatedText === "string")
          return obj.generatedText;
        if (obj.text && typeof obj.text === "string") return obj.text;
        if (obj.message && typeof obj.message === "string") return obj.message;
      }
      return String(data);
    },
  },
  {
    id: "json",
    label: "JSON 结构",
    description: "标准JSON格式，包含完整结构",
    icon: Hash,
    transform: (data: CopyableData) => JSON.stringify(data, null, 2),
  },
];

interface CopyButtonProps {
  data: CopyableData;
  label?: string;
  formats?: CopyFormat[];
  defaultFormat?: CopyFormat;
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost";
  size?: "sm" | "default" | "lg";
  showFormatInLabel?: boolean;
}

export function CopyButton({
  data,
  label = "复制",
  formats = ["formatted", "compact", "raw"],
  defaultFormat = "formatted",
  className,
  variant = "outline",
  size = "sm",
  showFormatInLabel = false,
}: CopyButtonProps) {
  const [selectedFormat, setSelectedFormat] =
    useState<CopyFormat>(defaultFormat);
  const [isJustCopied, setIsJustCopied] = useState(false);

  const availableFormats = COPY_FORMATS.filter((format) =>
    formats.includes(format.id)
  );
  const currentFormat =
    availableFormats.find((f) => f.id === selectedFormat) ||
    availableFormats[0];

  const handleCopy = async (format?: CopyFormat) => {
    const formatToCopy = format || selectedFormat;
    const formatConfig = COPY_FORMATS.find((f) => f.id === formatToCopy);

    if (!formatConfig) {
      toast.error("不支持的复制格式");
      return;
    }

    try {
      const textToCopy = formatConfig.transform(data);

      if (!textToCopy || textToCopy.trim() === "") {
        toast.error("没有可复制的内容");
        return;
      }

      await navigator.clipboard.writeText(textToCopy);

      // 更新按钮状态
      setIsJustCopied(true);
      setTimeout(() => setIsJustCopied(false), 2000);

      // 显示成功提示
      const formatLabel = formatConfig.label;
      toast.success(`已复制 ${formatLabel} 格式内容`);

      // 如果是快速复制，更新当前选择的格式
      if (format && format !== selectedFormat) {
        setSelectedFormat(format);
      }
    } catch (error) {
      console.error("Copy failed:", error);
      toast.error("复制失败，请重试");
    }
  };

  // 单一格式模式（只有一种格式可选）
  if (availableFormats.length === 1) {
    const format = availableFormats[0];
    const IconComponent = format.icon;

    return (
      <Button
        variant={variant}
        size={size}
        onClick={() => handleCopy(format.id)}
        className={className}
        disabled={!data}
      >
        {isJustCopied ? (
          <Check className="h-4 w-4 mr-2 text-green-600" />
        ) : (
          <IconComponent className="h-4 w-4 mr-2" />
        )}
        {isJustCopied
          ? "已复制"
          : `${label}${showFormatInLabel ? ` (${format.label})` : ""}`}
      </Button>
    );
  }

  // 多格式模式（下拉菜单）
  const IconComponent = currentFormat.icon;

  return (
    <div className={`flex ${className}`}>
      {/* 主复制按钮 */}
      <Button
        variant={variant}
        size={size}
        onClick={() => handleCopy()}
        className="rounded-r-none border-r-0"
        disabled={!data}
      >
        {isJustCopied ? (
          <Check className="h-4 w-4 mr-2 text-green-600" />
        ) : (
          <IconComponent className="h-4 w-4 mr-2" />
        )}
        {isJustCopied
          ? "已复制"
          : `${label}${showFormatInLabel ? ` (${currentFormat.label})` : ""}`}
      </Button>

      {/* 格式选择下拉菜单 */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className="rounded-l-none border-l-0 px-2"
            disabled={!data}
          >
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <div className="px-2 py-1.5 text-sm font-medium text-muted-foreground">
            选择复制格式
          </div>
          <DropdownMenuSeparator />
          {availableFormats.map((format) => {
            const FormatIcon = format.icon;
            const isSelected = selectedFormat === format.id;

            return (
              <DropdownMenuItem
                key={format.id}
                onClick={() => handleCopy(format.id)}
                className="flex flex-col items-start gap-1 p-3"
              >
                <div className="flex items-center gap-2 w-full">
                  <FormatIcon
                    className={`h-4 w-4 ${isSelected ? "text-primary" : ""}`}
                  />
                  <span
                    className={`font-medium ${
                      isSelected ? "text-primary" : ""
                    }`}
                  >
                    {format.label}
                  </span>
                  {isSelected && (
                    <Check className="h-3 w-3 text-primary ml-auto" />
                  )}
                </div>
                <span className="text-xs text-muted-foreground pl-6">
                  {format.description}
                </span>
              </DropdownMenuItem>
            );
          })}
          <DropdownMenuSeparator />
          <div className="px-3 py-2 text-xs text-muted-foreground">
            当前格式: {currentFormat.label}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

// 预设配置的便捷组件
export function CopyJsonButton(props: Omit<CopyButtonProps, "formats">) {
  return (
    <CopyButton
      {...props}
      formats={["formatted", "compact"]}
      defaultFormat="formatted"
    />
  );
}

export function CopyTextButton(props: Omit<CopyButtonProps, "formats">) {
  return <CopyButton {...props} formats={["raw"]} defaultFormat="raw" />;
}

export function CopyAllFormatsButton(props: Omit<CopyButtonProps, "formats">) {
  return (
    <CopyButton
      {...props}
      formats={["formatted", "compact", "raw", "json"]}
      defaultFormat="formatted"
    />
  );
}
