"use client";

import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from "react";
import { MonacoJsonEditor } from "../MonacoJsonEditor";
import { CopyButton } from "../CopyButton";
import toast from "react-hot-toast";
import type { UserData } from "@/types/user-data";
import type { AnalyzerOutput } from "@/lib/ai/types";
import type { GitHubExtendedData } from "@/types/github-extended";
import { getLogManager } from "@/lib/ai/core/utils/LogManager";

interface AnalyzerTabProps {
  sessionId?: string;
  githubData?: UserData; // 上游数据
  extendedData?: GitHubExtendedData | null; // 上游扩展数据
  isLoadingExtended?: boolean; // 扩展数据加载状态
  onResultChange?: (result: AnalyzerOutput | undefined) => void;
  onLoadingChange?: (loading: boolean) => void;
  className?: string;
}

// 暴露给父组件的方法
export interface AnalyzerTabRef {
  runModule: () => void;
  resetConfig: () => void;
}

// 创建空白提示数据
const createEmptyGithubDataPlaceholder = () => ({
  message: "请先获取GitHub数据或手动输入数据",
  tip: "系统将自动获取当前登录用户的GitHub数据",
});

export const AnalyzerTab = forwardRef<AnalyzerTabRef, AnalyzerTabProps>(
  (
    {
      sessionId = `analyzer-session-${Date.now()}`,
      githubData: upstreamGithubData,
      extendedData: upstreamExtendedData,
      isLoadingExtended: upstreamIsLoadingExtended = false,
      onResultChange,
      onLoadingChange,
      className,
    },
    ref
  ) => {
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<AnalyzerOutput | null>(null);

    // 避免TypeScript未使用变量警告
    void isLoading;

    // GitHub数据输入状态管理 - 优先使用上游真实数据，备用空白数据
    const [manualGithubData, setManualGithubData] = useState<UserData | null>(
      upstreamGithubData || null
    );

    // GitHub扩展数据状态管理 - 使用上游数据，支持手动修改
    const [manualExtendedData, setManualExtendedData] =
      useState<GitHubExtendedData | null>(upstreamExtendedData || null);

    // 监听上游GitHub数据变化，自动更新编辑器显示
    useEffect(() => {
      if (upstreamGithubData) {
        console.log(
          "🔄 [AnalyzerTab] 检测到上游GitHub数据更新，更新编辑器显示"
        );
        setManualGithubData(upstreamGithubData);
      }
    }, [upstreamGithubData]);

    // 监听上游扩展数据变化，自动更新编辑器显示
    useEffect(() => {
      if (upstreamExtendedData) {
        console.log("🔄 [AnalyzerTab] 检测到上游扩展数据更新，更新编辑器显示");
        setManualExtendedData(upstreamExtendedData);
      }
    }, [upstreamExtendedData]);

    // 获取当前有效的GitHub数据 - 优先使用真实数据
    const getEffectiveGithubData = (): UserData | null => {
      return manualGithubData || upstreamGithubData || null;
    };

    // 获取当前有效的扩展数据 - 优先使用手动数据
    const getEffectiveExtendedData = (): GitHubExtendedData | null => {
      return manualExtendedData || upstreamExtendedData || null;
    };

    const runAnalyzer = async () => {
      const effectiveGithubData = getEffectiveGithubData();
      const effectiveExtendedData = getEffectiveExtendedData();

      if (!effectiveGithubData) {
        toast.error("请提供有效的GitHub数据作为输入");
        return;
      }

      if (!effectiveExtendedData) {
        toast.error("请先输入扩展数据");
        return;
      }

      setIsLoading(true);
      onLoadingChange?.(true);
      const logManager = getLogManager();

      // 开始记录执行日志
      const logId = logManager.logExecutionStart(
        sessionId,
        "analyzer",
        "AnalyzerModule",
        {
          username: effectiveGithubData.username,
          hasExtendedData: !!effectiveExtendedData,
        }
      );

      try {
        const response = await fetch(`/api/debug/ai-modules?module=analyzer`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            sessionId,
            context: {
              githubData: effectiveGithubData,
              extendedData: effectiveExtendedData,
            },
            config: undefined, // Analyzer配置可选
            options: {
              enableStreaming: true,
            },
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 处理流式响应
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error("Failed to get response stream reader");
        }

        const decoder = new TextDecoder();
        let buffer = "";
        let streamResult: AnalyzerOutput | null = null;

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const eventData = JSON.parse(line.slice(6));

                switch (eventData.type) {
                  case "debug_start":
                    break;

                  case "content_chunk":
                    if (eventData.content) {
                      // 🎯 修复：使用与其他模块一致的事件处理逻辑
                      let isComplete = false;

                      // 1. 优先使用API明确返回的isComplete参数
                      if (typeof eventData.isComplete === "boolean") {
                        isComplete = eventData.isComplete;
                      } else {
                        // 2. 兜底：启发式判断（仅当API未提供isComplete时）
                        isComplete =
                          eventData.stage?.endsWith("_complete") ||
                          eventData.stage?.endsWith("_selected") ||
                          eventData.stage?.endsWith("_end") ||
                          eventData.stage === "complete";
                      }

                      // 🎯 修复：正确传递自描述数据结构
                      logManager.logStreamEvent(
                        sessionId,
                        "analyzer",
                        "content_chunk", // 🎯 使用content_chunk保持一致性
                        eventData.stage || "analysis",
                        {
                          content: eventData.content, // 🎯 保持原始自描述数据结构
                          message: eventData.message, // 🎯 保留消息信息
                          isComplete, // 🎯 传递完成状态
                          metadata: eventData.metadata, // 🎯 保留元数据
                        }
                      );
                    }
                    break;

                  case "debug_complete":
                    streamResult = eventData.result;
                    break;

                  case "debug_error":
                    throw new Error(eventData.error || "分析过程中发生错误");
                }
              } catch (parseError) {
                console.error("解析流式数据失败:", parseError);
              }
            }
          }
        }

        if (streamResult) {
          setResult(streamResult);
          onResultChange?.(streamResult);

          // 记录执行成功
          logManager.logExecutionComplete(logId, streamResult);

          toast.success("分析完成");
        } else {
          throw new Error("未收到有效的分析结果");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "分析失败";

        // 记录执行失败
        logManager.logExecutionFailure(logId, new Error(errorMessage));

        toast.error(errorMessage);
        console.error("Analyzer执行失败:", error);
      } finally {
        setIsLoading(false);
        onLoadingChange?.(false);
      }
    };

    // 重置数据 - 重置为上游真实数据
    const resetConfig = () => {
      setManualGithubData(upstreamGithubData || null);
      setManualExtendedData(upstreamExtendedData || null); // 重置扩展数据为上游数据
      setResult(null);
      onResultChange?.(undefined);
      toast.success("数据已重置");
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      runModule: runAnalyzer,
      resetConfig,
    }));

    return (
      <div className={`space-y-6 ${className}`}>
        {/* 输入数据区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* GitHub基础数据 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                GitHub基础数据
              </h3>
              <div className="text-sm text-gray-500">
                {upstreamGithubData && manualGithubData
                  ? manualGithubData.login === upstreamGithubData.login
                    ? "使用真实数据"
                    : "手动修改"
                  : upstreamGithubData
                  ? "使用真实数据"
                  : "使用模拟数据"}
              </div>
            </div>

            <MonacoJsonEditor
              value={
                (getEffectiveGithubData() ||
                  createEmptyGithubDataPlaceholder()) as any
              }
              onChange={(value) =>
                setManualGithubData(value as unknown as UserData)
              }
              height="300px"
            />
          </div>

          {/* GitHub扩展数据 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                GitHub扩展数据
              </h3>
              <div className="text-sm text-gray-500">
                {upstreamIsLoadingExtended
                  ? "获取中..."
                  : getEffectiveExtendedData()
                  ? "使用真实数据"
                  : "等待数据"}
              </div>
            </div>

            <MonacoJsonEditor
              value={
                (getEffectiveExtendedData()
                  ? JSON.parse(JSON.stringify(getEffectiveExtendedData()))
                  : null) ||
                (upstreamIsLoadingExtended
                  ? { message: "正在获取GitHub扩展数据..." }
                  : { message: "暂无扩展数据，请检查登录状态或网络连接" })
              }
              onChange={(value) =>
                setManualExtendedData(value as unknown as GitHubExtendedData)
              }
              height="300px"
            />
          </div>
        </div>

        {/* 分析结果区域 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              分析结果
            </h3>
            {result && <CopyButton data={result} />}
          </div>

          {/* 语义标签预览 */}
          {result?.content &&
            Array.isArray(result.content) &&
            result.content.length > 0 && (
              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-purple-800 dark:text-purple-200 flex items-center gap-2">
                    🏷️ 语义标签 ({result.content.length}个)
                  </h4>
                  <CopyButton
                    data={result.content}
                    label="复制标签"
                    variant="ghost"
                    size="sm"
                  />
                </div>
                <div className="flex flex-wrap gap-2">
                  {result.content.map((tag: string, index: number) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100 border border-purple-200 dark:border-purple-700"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

          {/* 分析统计信息 */}
          {result && (
            <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
              <p className="font-medium mb-2 text-green-800 dark:text-green-200">
                分析完成 ✅
              </p>
              <div className="space-y-1 text-green-700 dark:text-green-300 text-sm">
                <p>
                  • 分析项目:{" "}
                  {(result.metadata?.analysisResults as any[])?.length || 0} 项
                </p>
                <p>
                  • 语义标签:{" "}
                  {Array.isArray(result.content) ? result.content.length : 0} 个
                </p>
                <p>
                  • 总体置信度:{" "}
                  {(
                    ((result.metadata?.summary as any)?.overallConfidence ||
                      0) * 100
                  ).toFixed(1)}
                  %
                </p>
                <p>• 处理时间: {result.processing_time || 0}ms</p>
                <p>
                  • 数据质量:{" "}
                  {(result.metadata?.summary as any)?.dataQuality || "未知"}
                </p>
              </div>
            </div>
          )}

          <MonacoJsonEditor
            value={(result as any) || { message: "点击执行按钮开始分析..." }}
            height="400px"
            readOnly
          />
        </div>
      </div>
    );
  }
);
