"use client";

import React, {
  useState,
  useImperative<PERSON><PERSON>le,
  forwardRef,
  useEffect,
} from "react";
import { MonacoJsonEditor } from "../MonacoJsonEditor";
import { CopyButton } from "../CopyButton";
import { AlertCircle } from "lucide-react";
import toast from "react-hot-toast";
import type { UserData } from "@/types/user-data";
import type {
  WriterAIParams,
  WriterOutput,
  AnalyzerOutput,
  StrategistOutput,
} from "@/lib/ai/types";
import {
  DEFAULT_ANALYZER_OUTPUT_TEMPLATE,
  DEFAULT_STRATEGIST_OUTPUT_TEMPLATE,
} from "../DefaultTemplates";
import { getLogManager } from "@/lib/ai/core/utils/LogManager";

interface WriterTabProps {
  sessionId?: string;
  githubData: UserData;
  analyzerOutput?: AnalyzerOutput; // 上游数据
  strategistOutput?: StrategistOutput; // 上游数据
  onResultChange?: (result: WriterOutput | undefined) => void;
  onLoadingChange?: (loading: boolean) => void;
  className?: string;
}

// 暴露给父组件的方法
export interface WriterTabRef {
  runModule: () => void;
  resetConfig: () => void;
}

const DEFAULT_CONFIG: WriterAIParams = {
  temperature: 0.8,
  max_tokens: 800, // V6.0调整：模板化生成更简洁
  retry_count: 1, // V6.0调整：模板化生成更稳定
  // V6.0移除：few_shot_examples (改用模板化生成)
};

export const WriterTab = forwardRef<WriterTabRef, WriterTabProps>(
  (
    {
      sessionId = `writer-session-${Date.now()}`,
      githubData,
      analyzerOutput,
      strategistOutput,
      onResultChange,
      onLoadingChange,
      className,
    },
    ref
  ) => {
    const [config, setConfig] = useState<WriterAIParams>(DEFAULT_CONFIG);
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<WriterOutput | null>(null);
    const [enableStreaming, setEnableStreaming] = useState(true);

    // 输入状态管理 - 混合模式：接收上游数据，支持手动修改
    const [manualAnalyzerOutput, setManualAnalyzerOutput] =
      useState<AnalyzerOutput | null>(null);
    const [manualStrategistOutput, setManualStrategistOutput] =
      useState<StrategistOutput | null>(null);

    // 监听上游数据变化，自动更新编辑器显示
    useEffect(() => {
      if (analyzerOutput) {
        console.log(
          "🔄 [WriterTab] 检测到上游Analyzer数据更新，更新编辑器显示"
        );
        setManualAnalyzerOutput(analyzerOutput);
      }
    }, [analyzerOutput]);

    useEffect(() => {
      if (strategistOutput) {
        console.log(
          "🔄 [WriterTab] 检测到上游Strategist数据更新，更新编辑器显示"
        );
        setManualStrategistOutput(strategistOutput);
      }
    }, [strategistOutput]);

    // 获取当前有效的输入数据 - 混合模式：手动输入优先，回退到上游数据
    const getEffectiveInputs = () => {
      const effectiveAnalyzer = manualAnalyzerOutput || analyzerOutput || null;
      const effectiveStrategist =
        manualStrategistOutput || strategistOutput || null;
      return { effectiveAnalyzer, effectiveStrategist };
    };

    const runWriterWithStreaming = async () => {
      const { effectiveAnalyzer, effectiveStrategist } = getEffectiveInputs();

      if (!effectiveAnalyzer || !effectiveStrategist) {
        toast.error("请提供有效的 Analyzer 和 Strategist 输出作为输入");
        return;
      }

      setIsLoading(true);
      onLoadingChange?.(true);

      const logManager = getLogManager();

      try {
        // 开始记录流式执行日志
        const logId = logManager.logExecutionStart(
          sessionId,
          "writer",
          "WriterModule (Streaming)",
          {
            temperature: config.temperature,
            max_tokens: config.max_tokens,
          }
        );

        // 创建流式请求
        const response = await fetch(`/api/debug/ai-modules?module=writer`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            sessionId,
            context: {
              githubData,
              extendedData: undefined, // Writer不需要扩展数据
            },
            input: {
              analyzerOutput: effectiveAnalyzer,
              strategistOutput: effectiveStrategist,
            },
            config: config, // Writer AI参数配置
            options: {
              enableStreaming: true,
            },
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          throw new Error("无法获取响应流");
        }

        let buffer = "";
        let finalResult: WriterOutput | null = null;

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const eventData = JSON.parse(line.slice(6));

                switch (eventData.type) {
                  case "debug_start":
                    break;

                  case "content_chunk":
                    if (eventData.content) {
                      // 🎯 修复：优先使用API返回的isComplete参数，避免启发式逻辑覆盖
                      let isComplete = false;

                      // 1. 优先使用API明确返回的isComplete参数
                      if (typeof eventData.isComplete === "boolean") {
                        isComplete = eventData.isComplete;
                      } else {
                        // 2. 兜底：启发式判断（仅当API未提供isComplete时）
                        isComplete =
                          eventData.stage?.endsWith("_complete") ||
                          eventData.stage?.endsWith("_selected") ||
                          eventData.stage?.endsWith("_end") || // 添加_end匹配
                          eventData.stage === "complete";
                      }

                      logManager.logStreamEvent(
                        sessionId,
                        "writer",
                        "content_chunk",
                        eventData.stage,
                        {
                          content: eventData.content,
                          message: eventData.message, // ✅ 添加message参数
                          isComplete, // 🎯 使用修正后的isComplete
                        }
                      );
                    }
                    break;

                  case "debug_complete":
                    finalResult = eventData.result;
                    // 完成流式日志聚合 - 使用正确的stage参数
                    logManager.completeStreamingLog(
                      sessionId,
                      "writer",
                      "text_generation",
                      {
                        generatedTextLength:
                          finalResult?.content?.generatedText?.length || 0,
                        confidence: finalResult?.confidence,
                      }
                    );

                    break;

                  case "debug_error":
                    throw new Error(eventData.error);
                }
              } catch (parseError) {
                console.warn("解析SSE事件失败:", parseError);
              }
            }
          }
        }

        if (finalResult) {
          setResult(finalResult);
          onResultChange?.(finalResult);

          logManager.logExecutionComplete(logId, finalResult, {
            tokensUsed: finalResult.content?.generatedText?.length || 0,
            confidence: finalResult.confidence || 0,
            processingTime: finalResult.processing_time || 0,
          });

          toast.success("流式文本生成完成！");
        }
      } catch (error) {
        console.error("Streaming Writer error:", error);
        logManager.logError(
          sessionId,
          "writer",
          error instanceof Error ? error : new Error("💥 流式文本生成失败")
        );
        toast.error(
          `流式文本生成失败: ${
            error instanceof Error ? error.message : "未知错误"
          }`
        );
      } finally {
        setIsLoading(false);
        onLoadingChange?.(false);
      }
    };

    const resetConfig = () => {
      setConfig(DEFAULT_CONFIG);
      toast.success("已重置为默认配置");
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      runModule: runWriterWithStreaming,
      resetConfig,
    }));

    // 获取输入状态信息
    const getInputStatusInfo = () => {
      const { effectiveAnalyzer, effectiveStrategist } = getEffectiveInputs();
      const hasAnalyzer = !!effectiveAnalyzer;
      const hasStrategist = !!effectiveStrategist;

      return {
        canExecute: hasAnalyzer && hasStrategist,
        description:
          hasAnalyzer && hasStrategist
            ? "输入数据完整"
            : hasAnalyzer
            ? "缺少 Strategist 输出"
            : hasStrategist
            ? "缺少 Analyzer 输出"
            : "缺少 Analyzer 和 Strategist 输出",
      };
    };

    const inputStatus = getInputStatusInfo();

    return (
      <div className={`space-y-6 ${className}`}>
        {/* 双输入编辑器 */}
        <div className="space-y-6">
          {/* Analyzer输出输入编辑器 */}
          <div className="space-y-4">
            <MonacoJsonEditor
              title="Analyzer Output (混合模式)"
              value={JSON.stringify(
                manualAnalyzerOutput ||
                  analyzerOutput ||
                  DEFAULT_ANALYZER_OUTPUT_TEMPLATE,
                null,
                2
              )}
              onRawChange={(value) => {
                try {
                  const parsed = JSON.parse(value);
                  setManualAnalyzerOutput(parsed);
                } catch (e) {
                  // 忽略无效JSON
                }
              }}
              height={200}
            />
            <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded">
              💡 混合模式：显示Analyzer上游输出，支持手动修改。
            </div>
          </div>

          {/* Strategist输出输入编辑器 */}
          <div className="space-y-4">
            <MonacoJsonEditor
              title="Strategist Output (混合模式)"
              value={JSON.stringify(
                manualStrategistOutput ||
                  strategistOutput ||
                  DEFAULT_STRATEGIST_OUTPUT_TEMPLATE,
                null,
                2
              )}
              onRawChange={(value) => {
                try {
                  const parsed = JSON.parse(value);
                  setManualStrategistOutput(parsed);
                } catch (e) {
                  // 忽略无效JSON
                }
              }}
              height={200}
            />
            <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded">
              💡 混合模式：显示Strategist上游输出，支持手动修改。
            </div>
          </div>

          {/* 输入状态指示器 */}
          <div
            className={`rounded-lg p-3 flex gap-2 items-center ${
              inputStatus.canExecute
                ? "bg-green-50 dark:bg-green-900/20"
                : "bg-yellow-50 dark:bg-yellow-900/20"
            }`}
          >
            <AlertCircle
              className={`h-4 w-4 ${
                inputStatus.canExecute
                  ? "text-green-600 dark:text-green-400"
                  : "text-yellow-600 dark:text-yellow-400"
              }`}
            />
            <p
              className={`text-sm ${
                inputStatus.canExecute
                  ? "text-green-800 dark:text-green-200"
                  : "text-yellow-800 dark:text-yellow-200"
              }`}
            >
              {inputStatus.description}
            </p>
          </div>
        </div>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* 参数配置 */}
          <div className="space-y-4">
            <div>
              <MonacoJsonEditor
                title="模块参数配置"
                value={JSON.stringify(config, null, 2)}
                onRawChange={(value) => {
                  try {
                    const parsed = JSON.parse(value);
                    setConfig(parsed);
                  } catch (e) {
                    // 忽略无效JSON
                  }
                }}
                height={300}
              />
            </div>

            <div className="rounded-lg bg-muted/50 text-sm text-muted-foreground p-3">
              <p className="font-medium mb-2">参数说明：</p>
              <ul className="space-y-1 text-xs">
                <li>
                  • <strong>temperature</strong>: 创意程度
                  (0.0-1.0)，值越高越有创意
                </li>
                <li>
                  • <strong>max_tokens</strong>: 最大生成长度
                </li>
                <li>
                  • <strong>retry_count</strong>: 失败重试次数
                </li>
                {/* V6.0移除：few_shot_examples (改用模板化生成，无需配置示例数量) */}
              </ul>
            </div>
          </div>

          <div className="space-y-4">
            <MonacoJsonEditor
              title="执行结果"
              value={JSON.stringify(result || {}, null, 2)}
              readOnly
              height={300}
            />

            {result && (
              <div className="rounded-lg bg-green-50 text-sm p-3 dark:bg-green-900/20">
                <p className="font-medium mb-2 text-green-800 dark:text-green-200">
                  执行状态: {result.status}
                  {enableStreaming && (
                    <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      流式模式
                    </span>
                  )}
                </p>
                <div className="space-y-1 text-green-700 dark:text-green-300">
                  <p>
                    • 生成文本长度: {result.content?.generatedText?.length || 0}{" "}
                    字符
                  </p>
                  <p>• 处理时间: {result.processing_time || 0}ms</p>
                  <p>• 置信度: {result.content?.confidence || 0}</p>
                  {result.content?.usedTemplate && (
                    <p>• 使用模板: {result.content.usedTemplate}</p>
                  )}
                  {result.content?.iterations && (
                    <p>• 迭代次数: {result.content.iterations}</p>
                  )}
                  {result.content?.qualityScore && (
                    <p>• 质量分数: {result.content.qualityScore}</p>
                  )}
                </div>
              </div>
            )}

            {/* 生成文本预览 */}
            {result?.content?.generatedText && (
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200">
                    生成文本预览
                  </h5>
                  <CopyButton
                    data={result.content.generatedText}
                    label="复制文本"
                    variant="ghost"
                    size="sm"
                    formats={["raw", "formatted"]}
                    defaultFormat="raw"
                  />
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300 bg-white/50 dark:bg-black/20 p-3 rounded border-l-4 border-blue-400">
                  <p className="whitespace-pre-wrap">
                    {result.content.generatedText}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);
