"use client";

import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from "react";
import { MonacoJsonEditor } from "../MonacoJsonEditor";
import { AlertCircle } from "lucide-react";
import toast from "react-hot-toast";
import type { UserData } from "@/types/user-data";
import type { StrategistOutput, AnalyzerOutput } from "@/lib/ai/types";

import { DEFAULT_ANALYZER_OUTPUT_TEMPLATE } from "../DefaultTemplates";
import { getLogManager } from "@/lib/ai/core/utils/LogManager";

interface StrategistTabProps {
  sessionId?: string;
  githubData: UserData;
  analyzerOutput?: AnalyzerOutput; // 上游数据
  onResultChange?: (result: StrategistOutput | undefined) => void;
  onLoadingChange?: (loading: boolean) => void;
  className?: string;
}

// 暴露给父组件的方法
export interface StrategistTabRef {
  runModule: () => void;
  resetConfig: () => void;
}

export const StrategistTab = forwardRef<StrategistTabRef, StrategistTabProps>(
  (
    {
      sessionId = `strategist-session-${Date.now()}`,
      githubData,
      analyzerOutput,
      onResultChange,
      onLoadingChange,
      className,
    },
    ref
  ) => {
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<StrategistOutput | null>(null);
    const [enableStreaming, setEnableStreaming] = useState(true);

    // AnalyzerOutput输入状态管理 - 混合模式：接收上游数据，支持手动修改
    const [manualAnalyzerOutput, setManualAnalyzerOutput] =
      useState<AnalyzerOutput | null>(null);

    // 监听上游AnalyzerOutput变化，自动更新编辑器显示
    useEffect(() => {
      if (analyzerOutput) {
        setManualAnalyzerOutput(analyzerOutput);
      }
    }, [analyzerOutput]);

    // 获取当前有效的AnalyzerOutput - 混合模式：手动输入优先，回退到上游数据
    const getEffectiveAnalyzerInput = (): AnalyzerOutput | null => {
      return manualAnalyzerOutput || analyzerOutput || null;
    };

    const runStrategistWithStreaming = async () => {
      const effectiveAnalyzerOutput = getEffectiveAnalyzerInput();

      if (!effectiveAnalyzerOutput) {
        toast.error("请提供有效的 Analyzer 输出作为输入");
        return;
      }

      setIsLoading(true);
      onLoadingChange?.(true);

      const logManager = getLogManager();

      try {
        // 开始记录流式执行日志
        const logId = logManager.logExecutionStart(
          sessionId,
          "strategist",
          "StrategistModule (Creative Brief)",
          {
            semanticTagsCount: effectiveAnalyzerOutput.content?.length || 0,
          }
        );

        // 创建EventSource连接 - 简化的API调用，不再需要复杂配置
        const response = await fetch(
          `/api/debug/ai-modules?module=strategist`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              sessionId,
              context: {
                githubData,
                extendedData: undefined, // Strategist不需要扩展数据
              },
              input: effectiveAnalyzerOutput, // 直接传入AnalyzerOutput
              config: undefined, // 用户偏好配置可选
              options: {
                enableStreaming: true,
              },
            }),
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          throw new Error("无法获取响应流");
        }

        let buffer = "";
        let finalResult: StrategistOutput | null = null;

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const eventData = JSON.parse(line.slice(6));

                switch (eventData.type) {
                  case "debug_start":
                    break;

                  case "content_chunk":
                    if (eventData.content) {
                      // 🎯 优先使用API返回的isComplete参数
                      let isComplete = false;

                      if (typeof eventData.isComplete === "boolean") {
                        isComplete = eventData.isComplete;
                      } else {
                        // 兜底：启发式判断
                        isComplete =
                          eventData.stage?.endsWith("_complete") ||
                          eventData.stage?.endsWith("_end") ||
                          eventData.stage === "complete";
                      }

                      logManager.logStreamEvent(
                        sessionId,
                        "strategist",
                        "content_chunk",
                        eventData.stage,
                        {
                          content: eventData.content,
                          message: eventData.message,
                          isComplete,
                        }
                      );
                    }
                    break;

                  case "debug_complete":
                    finalResult = eventData.result;
                    // 完成流式日志聚合
                    logManager.completeStreamingLog(
                      sessionId,
                      "strategist",
                      "creative_brief",
                      {
                        narrative_scaffold:
                          finalResult?.content?.narrative_scaffold,
                        confidence: finalResult?.confidence,
                      }
                    );

                    break;

                  case "debug_error":
                    throw new Error(eventData.error);
                }
              } catch (parseError) {
                console.warn("解析SSE事件失败:", parseError);
              }
            }
          }
        }

        if (finalResult) {
          setResult(finalResult);
          onResultChange?.(finalResult);

          logManager.logExecutionComplete(logId, finalResult, {
            tokensUsed:
              finalResult.content?.insight_pool?.analogy_pool?.length || 0,
            confidence: finalResult.confidence || 0,
            processingTime: finalResult.processing_time || 0,
          });

          toast.success("创意简报生成完成！");
        }
      } catch (error) {
        console.error("Streaming Strategist error:", error);
        logManager.logError(
          sessionId,
          "strategist",
          error instanceof Error ? error : new Error("💥 创意简报生成失败")
        );
        toast.error(
          `创意简报生成失败: ${
            error instanceof Error ? error.message : "未知错误"
          }`
        );
      } finally {
        setIsLoading(false);
        onLoadingChange?.(false);
      }
    };

    const resetConfig = () => {
      // 新版本不再需要复杂配置，重置为清空结果
      setResult(null);
      setManualAnalyzerOutput(null);
      toast.success("已重置状态");
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      runModule: runStrategistWithStreaming,
      resetConfig,
    }));

    // 获取输入状态信息
    const getInputStatusInfo = () => {
      const effectiveAnalyzer = getEffectiveAnalyzerInput();
      const hasAnalyzer = !!effectiveAnalyzer;
      const hasSemanticTags =
        hasAnalyzer && effectiveAnalyzer.content?.length > 0;

      return {
        canExecute: hasSemanticTags,
        description: hasSemanticTags
          ? `语义标签完整 (${effectiveAnalyzer.content.length}个)`
          : hasAnalyzer
          ? "Analyzer输出缺少语义标签"
          : "缺少 Analyzer 输出",
      };
    };

    const inputStatus = getInputStatusInfo();

    return (
      <div className={`space-y-6 ${className}`}>
        {/* Analyzer输出输入编辑器 */}
        <div className="space-y-4">
          <MonacoJsonEditor
            title="Analyzer Output (语义标签输入)"
            value={JSON.stringify(
              getEffectiveAnalyzerInput() || DEFAULT_ANALYZER_OUTPUT_TEMPLATE,
              null,
              2
            )}
            onRawChange={(value) => {
              try {
                const parsed = JSON.parse(value);
                setManualAnalyzerOutput(parsed);
              } catch (e) {
                // 忽略无效JSON
              }
            }}
            height={250}
          />

          {/* 数据源说明 - 更新为新架构说明 */}
          <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded">
            💡
            新架构：策略模块专注于将语义标签转换为创意简报，无需复杂配置。输入为Analyzer的语义标签数组(content字段)。
          </div>

          {/* 输入状态指示器 */}
          <div
            className={`rounded-lg p-3 flex gap-2 items-center ${
              inputStatus.canExecute
                ? "bg-green-50 dark:bg-green-900/20"
                : "bg-yellow-50 dark:bg-yellow-900/20"
            }`}
          >
            <AlertCircle
              className={`h-4 w-4 ${
                inputStatus.canExecute
                  ? "text-green-600 dark:text-green-400"
                  : "text-yellow-600 dark:text-yellow-400"
              }`}
            />
            <p
              className={`text-sm ${
                inputStatus.canExecute
                  ? "text-green-800 dark:text-green-200"
                  : "text-yellow-800 dark:text-yellow-200"
              }`}
            >
              {inputStatus.description}
            </p>
          </div>
        </div>

        <div className="grid gap-6 grid-cols-1">
          {/* 执行结果 - 适配新的创意简报格式 */}
          <div className="space-y-4">
            <MonacoJsonEditor
              title="创意简报输出"
              value={JSON.stringify(result || {}, null, 2)}
              readOnly
              height={400}
            />

            {result && (
              <div className="rounded-lg bg-green-50 text-sm p-3 dark:bg-green-900/20">
                <p className="font-medium mb-2 text-green-800 dark:text-green-200">
                  执行状态: {result.status}
                  {enableStreaming && (
                    <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      流式模式
                    </span>
                  )}
                </p>
                <div className="space-y-1 text-green-700 dark:text-green-300">
                  <p>• 处理时间: {result.processing_time || 0}ms</p>
                  <p>• 置信度: {result.confidence || 0}</p>
                  {result.content?.insight_pool?.persona && (
                    <p>• 人物设定: {result.content.insight_pool.persona}</p>
                  )}
                  {result.content?.insight_pool?.humor_angle && (
                    <p>• 幽默角度: {result.content.insight_pool.humor_angle}</p>
                  )}
                  {result.content?.punchline_focus?.tone && (
                    <p>• 语言气质: {result.content.punchline_focus.tone}</p>
                  )}
                  {result.content?.insight_pool?.analogy_pool && (
                    <p>
                      • 类比句数量:{" "}
                      {result.content.insight_pool.analogy_pool.length} 个
                    </p>
                  )}
                  {result.content?.punchline_focus?.punchline_candidates && (
                    <p>
                      • 收尾候选句:{" "}
                      {
                        result.content.punchline_focus.punchline_candidates
                          .length
                      }{" "}
                      个
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);
