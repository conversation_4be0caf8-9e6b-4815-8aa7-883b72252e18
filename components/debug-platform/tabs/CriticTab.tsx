"use client";

import React, {
  useState,
  useImperative<PERSON><PERSON><PERSON>,
  forwardRef,
  useEffect,
} from "react";
import { MonacoJsonEditor } from "../MonacoJsonEditor";
import { ModuleInputEditor } from "../ModuleInputEditor";
import { Copy<PERSON><PERSON>Button, CopyButton } from "../CopyButton";
import { AlertCircle } from "lucide-react";
import toast from "react-hot-toast";
import type { UserData } from "@/types/user-data";
import type {
  CriticAIParams,
  CriticOutput,
  WriterOutput,
  StrategistOutput,
  DEFAULT_CRITIC_AI_PARAMS,
} from "@/lib/ai/types";
import type {
  CriticDebugResponse,
  ModuleDebugErrorResponse,
} from "@/types/api-responses";
import { isModuleDebugSuccessResponse } from "@/types/api-responses";
import type { InputMode } from "@/types/debug-input-modes";
import {
  DEFAULT_STRATEGIST_OUTPUT_TEMPLATE,
  DEFAULT_WRITER_OUTPUT_TEMPLATE,
} from "../DefaultTemplates";
import { getLogManager } from "@/lib/ai/core/utils/LogManager";

interface CriticTabProps {
  sessionId?: string;
  githubData: UserData;
  writerOutput?: WriterOutput;
  strategistOutput?: StrategistOutput;
  onResultChange?: (result: CriticOutput | undefined) => void;
  onLoadingChange?: (loading: boolean) => void;
  className?: string;
}

// 暴露给父组件的方法
export interface CriticTabRef {
  runModule: () => void;
  resetConfig: () => void;
}

const DEFAULT_CONFIG: CriticAIParams = {
  temperature: 0.2,
  max_tokens: 600,
  retry_count: 3,
  scoring_strictness: 0.5,
};

export const CriticTab = forwardRef<CriticTabRef, CriticTabProps>(
  (
    {
      sessionId = `critic-session-${Date.now()}`,
      githubData,
      writerOutput,
      strategistOutput,
      onResultChange,
      onLoadingChange,
      className,
    },
    ref
  ) => {
    const [config, setConfig] = useState<CriticAIParams>(DEFAULT_CONFIG);
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<CriticOutput | null>(null);
    const [enableStreaming, setEnableStreaming] = useState(true);

    // WriterOutput输入的ModuleInputEditor状态管理
    const [writerInputMode, setWriterInputMode] =
      useState<InputMode>("upstream");
    const [manualWriterOutput, setManualWriterOutput] =
      useState<WriterOutput | null>(null);

    // StrategistOutput输入的ModuleInputEditor状态管理
    const [strategistInputMode, setStrategistInputMode] =
      useState<InputMode>("upstream");
    const [manualStrategistOutput, setManualStrategistOutput] =
      useState<StrategistOutput | null>(null);

    // 监听上游数据变化，自动更新编辑器显示
    useEffect(() => {
      if (writerOutput && writerInputMode === "upstream") {
        console.log("🔄 [CriticTab] 检测到上游Writer数据更新，更新编辑器显示");
        setManualWriterOutput(writerOutput);
      }
    }, [writerOutput, writerInputMode]);

    useEffect(() => {
      if (strategistOutput && strategistInputMode === "upstream") {
        console.log(
          "🔄 [CriticTab] 检测到上游Strategist数据更新，更新编辑器显示"
        );
        setManualStrategistOutput(strategistOutput);
      }
    }, [strategistOutput, strategistInputMode]);

    // 获取当前有效的输入数据
    const getEffectiveInputs = () => {
      const effectiveWriter = (() => {
        switch (writerInputMode) {
          case "upstream":
            return writerOutput || null;
          case "manual":
            return manualWriterOutput;
          case "hybrid":
            return manualWriterOutput || writerOutput || null;
          default:
            return writerOutput || null;
        }
      })();

      const effectiveStrategist = (() => {
        switch (strategistInputMode) {
          case "upstream":
            return strategistOutput || null;
          case "manual":
            return manualStrategistOutput;
          case "hybrid":
            return manualStrategistOutput || strategistOutput || null;
          default:
            return strategistOutput || null;
        }
      })();

      return { effectiveWriter, effectiveStrategist };
    };

    const runCriticWithStreaming = async () => {
      const { effectiveWriter, effectiveStrategist } = getEffectiveInputs();

      if (!effectiveWriter || !effectiveStrategist) {
        toast.error("请提供有效的 Writer 和 Strategist 输出作为输入");
        return;
      }

      setIsLoading(true);
      onLoadingChange?.(true);

      const logManager = getLogManager();

      try {
        // 开始记录流式执行日志
        const logId = logManager.logExecutionStart(
          sessionId,
          "critic",
          "CriticModule (Streaming)",
          {
            temperature: config.temperature,
            scoring_strictness: config.scoring_strictness,
          }
        );

        // 创建流式请求
        const response = await fetch(`/api/debug/ai-modules?module=critic`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            sessionId,
            context: {
              githubData,
              extendedData: undefined, // Critic不需要扩展数据
            },
            input: {
              writerOutput: effectiveWriter,
              strategistOutput: effectiveStrategist,
            },
            config: config, // Critic AI参数配置
            options: {
              enableStreaming: true,
            },
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          throw new Error("无法获取响应流");
        }

        let buffer = "";
        let finalResult: CriticOutput | null = null;

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const eventData = JSON.parse(line.slice(6));

                switch (eventData.type) {
                  case "debug_start":
                    break;

                  case "content_chunk":
                    if (eventData.content) {
                      // 🎯 修复：优先使用API返回的isComplete参数，避免启发式逻辑覆盖
                      let isComplete = false;

                      // 1. 优先使用API明确返回的isComplete参数
                      if (typeof eventData.isComplete === "boolean") {
                        isComplete = eventData.isComplete;
                      } else {
                        // 2. 兜底：启发式判断（仅当API未提供isComplete时）
                        isComplete =
                          eventData.stage?.endsWith("_complete") ||
                          eventData.stage?.endsWith("_selected") ||
                          eventData.stage?.endsWith("_end") || // 添加_end匹配
                          eventData.stage === "complete";
                      }

                      logManager.logStreamEvent(
                        sessionId,
                        "critic",
                        "content_chunk",
                        eventData.stage,
                        {
                          content: eventData.content,
                          message: eventData.message, // ✅ 添加message参数
                          isComplete, // 🎯 使用修正后的isComplete
                        }
                      );
                    }
                    break;

                  case "debug_complete":
                    finalResult = eventData.result;
                    // 完成流式日志聚合 - 使用正确的stage参数
                    logManager.completeStreamingLog(
                      sessionId,
                      "critic",
                      "ai_evaluation_content",
                      {
                        overallScore: finalResult?.content?.overallScore || 0,
                        confidence: finalResult?.confidence,
                      }
                    );

                    break;

                  case "debug_error":
                    throw new Error(eventData.error);
                }
              } catch (parseError) {
                console.warn("解析SSE事件失败:", parseError);
              }
            }
          }
        }

        if (finalResult) {
          setResult(finalResult);
          onResultChange?.(finalResult);

          logManager.logExecutionComplete(logId, finalResult, {
            qualityScore: finalResult.content?.overallScore || 0,
            confidence: finalResult.confidence || 0,
            processingTime: finalResult.processing_time || 0,
          });

          toast.success("流式质量评估完成！");
        }
      } catch (error) {
        console.error("Streaming Critic error:", error);
        logManager.logError(
          sessionId,
          "critic",
          error instanceof Error ? error : new Error("💥 流式质量评估失败")
        );
        toast.error(
          `流式质量评估失败: ${
            error instanceof Error ? error.message : "未知错误"
          }`
        );
      } finally {
        setIsLoading(false);
        onLoadingChange?.(false);
      }
    };

    const resetConfig = () => {
      setConfig(DEFAULT_CONFIG);
      toast.success("已重置为默认配置");
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      runModule: runCriticWithStreaming,
      resetConfig,
    }));

    // 获取输入状态信息
    const getInputStatusInfo = () => {
      const { effectiveWriter, effectiveStrategist } = getEffectiveInputs();
      const hasWriter = !!effectiveWriter;
      const hasStrategist = !!effectiveStrategist;

      const writerSource =
        writerInputMode === "upstream"
          ? "上游输出"
          : writerInputMode === "manual"
          ? "手动输入"
          : "混合模式";

      const strategistSource =
        strategistInputMode === "upstream"
          ? "上游输出"
          : strategistInputMode === "manual"
          ? "手动输入"
          : "混合模式";

      return {
        canExecute: hasWriter && hasStrategist,
        writerSource,
        strategistSource,
        description:
          hasWriter && hasStrategist
            ? `输入数据完整 (Writer: ${writerSource}, Strategist: ${strategistSource})`
            : hasWriter
            ? "缺少 Strategist 输出"
            : hasStrategist
            ? "缺少 Writer 输出"
            : "缺少 Writer 和 Strategist 输出",
      };
    };

    const inputStatus = getInputStatusInfo();

    return (
      <div className={`space-y-6 ${className}`}>
        {/* 双输入编辑器 */}
        <div className="space-y-6">
          {/* Writer输出输入编辑器 */}
          <div className="space-y-4">
            <h4 className="font-medium">Writer 输出输入</h4>
            <ModuleInputEditor<WriterOutput>
              label="Writer Output"
              value={manualWriterOutput}
              onValueChange={setManualWriterOutput}
              inputMode={writerInputMode}
              onInputModeChange={setWriterInputMode}
              defaultTemplate={DEFAULT_WRITER_OUTPUT_TEMPLATE}
              upstreamValue={writerOutput || null}
              height={200}
            />
          </div>

          {/* Strategist输出输入编辑器 */}
          <div className="space-y-4">
            <h4 className="font-medium">Strategist 输出输入</h4>
            <ModuleInputEditor<StrategistOutput>
              label="Strategist Output"
              value={manualStrategistOutput}
              onValueChange={setManualStrategistOutput}
              inputMode={strategistInputMode}
              onInputModeChange={setStrategistInputMode}
              defaultTemplate={DEFAULT_STRATEGIST_OUTPUT_TEMPLATE}
              upstreamValue={strategistOutput || null}
              height={200}
            />
          </div>

          {/* 输入状态指示器 */}
          <div
            className={`rounded-lg p-3 flex gap-2 items-center ${
              inputStatus.canExecute
                ? "bg-green-50 dark:bg-green-900/20"
                : "bg-yellow-50 dark:bg-yellow-900/20"
            }`}
          >
            <AlertCircle
              className={`h-4 w-4 ${
                inputStatus.canExecute
                  ? "text-green-600 dark:text-green-400"
                  : "text-yellow-600 dark:text-yellow-400"
              }`}
            />
            <p
              className={`text-sm ${
                inputStatus.canExecute
                  ? "text-green-800 dark:text-green-200"
                  : "text-yellow-800 dark:text-yellow-200"
              }`}
            >
              {inputStatus.description}
            </p>
          </div>
        </div>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* 参数配置 */}
          <div className="space-y-4">
            <div>
              <MonacoJsonEditor
                title="模块参数配置"
                value={JSON.stringify(config, null, 2)}
                onRawChange={(value) => {
                  try {
                    const parsed = JSON.parse(value);
                    setConfig(parsed);
                  } catch (e) {
                    // 忽略无效JSON
                  }
                }}
                height={300}
              />
            </div>

            <div className="rounded-lg bg-muted/50 text-sm text-muted-foreground p-3">
              <p className="font-medium mb-2">参数说明：</p>
              <ul className="space-y-1 text-xs">
                <li>
                  • <strong>temperature</strong>: 评估一致性
                  (0.0-1.0)，值越低越一致
                </li>
                <li>
                  • <strong>max_tokens</strong>: 评估详细程度，值越大越详细
                </li>
                <li>
                  • <strong>retry_count</strong>: 失败重试次数
                </li>
                <li>
                  • <strong>scoring_strictness</strong>: 评分严格程度 (0.0-1.0)
                </li>
              </ul>
            </div>
          </div>

          <div className="space-y-4">
            <MonacoJsonEditor
              title="执行结果"
              value={JSON.stringify(result || {}, null, 2)}
              readOnly
              height={300}
            />

            {result && (
              <div className="rounded-lg bg-green-50 text-sm p-3 dark:bg-green-900/20">
                <p className="font-medium mb-2 text-green-800 dark:text-green-200">
                  执行状态: {result.status}
                  {enableStreaming && (
                    <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      流式模式
                    </span>
                  )}
                </p>
                <div className="space-y-1 text-green-700 dark:text-green-300">
                  <p>• 总体评分: {result.content?.overallScore || 0}/100</p>
                  <p>• 处理时间: {result.processing_time || 0}ms</p>
                  <p>• 置信度: {result.content?.confidence || 0}</p>
                  {result.content?.standardizedGrade && (
                    <p>• 标准化等级: {result.content.standardizedGrade}</p>
                  )}
                  {result.content?.suggestions &&
                    result.content.suggestions.length > 0 && (
                      <p>• 改进建议: {result.content.suggestions.length} 条</p>
                    )}
                </div>
              </div>
            )}

            {/* 质量评估详情 */}
            {result?.content?.dimensions && (
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200">
                    质量维度评分
                  </h5>
                  <CopyJsonButton
                    data={result.content.dimensions}
                    label="复制评分"
                    variant="ghost"
                    size="sm"
                  />
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm text-blue-700 dark:text-blue-300">
                  {Object.entries(result.content.dimensions).map(
                    ([dimension, score]) => (
                      <div key={dimension} className="flex justify-between">
                        <span>{dimension}:</span>
                        <span className="font-medium">{score}</span>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

            {/* 改进建议 */}
            {result?.content?.suggestions &&
              result.content.suggestions.length > 0 && (
                <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-amber-800 dark:text-amber-200">
                      改进建议
                    </h5>
                    <CopyButton
                      data={result.content.suggestions}
                      label="复制建议"
                      variant="ghost"
                      size="sm"
                      formats={["raw", "formatted"]}
                      defaultFormat="raw"
                    />
                  </div>
                  <div className="space-y-1 text-sm text-amber-700 dark:text-amber-300">
                    {result.content.suggestions
                      .slice(0, 3)
                      .map((suggestion, index) => (
                        <p key={index}>• {suggestion}</p>
                      ))}
                    {result.content.suggestions.length > 3 && (
                      <p className="text-xs text-amber-600 dark:text-amber-400">
                        ... 还有 {result.content.suggestions.length - 3} 条建议
                      </p>
                    )}
                  </div>
                </div>
              )}

            {/* 整体评估 */}
            {result?.content?.assessment && (
              <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium text-purple-800 dark:text-purple-200">
                    整体评估
                  </h5>
                  <CopyButton
                    data={result.content.assessment}
                    label="复制评估"
                    variant="ghost"
                    size="sm"
                    formats={["raw"]}
                    defaultFormat="raw"
                  />
                </div>
                <div className="text-sm text-purple-700 dark:text-purple-300 bg-white/50 dark:bg-black/20 p-3 rounded border-l-4 border-purple-400">
                  <p className="whitespace-pre-wrap">
                    {result.content.assessment}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);
