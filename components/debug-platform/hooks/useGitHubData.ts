/**
 * GitHub数据获取相关的Hook
 */

import { useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import toast from "react-hot-toast";
import type { UserData } from "@/types/user-data";
import type {
  GitHubDataResponse,
  GitHubDataErrorResponse,
} from "@/types/api-responses";
import { isGitHubDataSuccessResponse } from "@/types/api-responses";

export const useGitHubData = () => {
  const { data: session } = useSession();
  const [githubData, setGithubData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchGitHubData = useCallback(async (): Promise<UserData | null> => {
    if (!session) {
      toast.error("请先登录");
      return null;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/github-data", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      const result: GitHubDataResponse | GitHubDataErrorResponse =
        await response.json();

      if (isGitHubDataSuccessResponse(result)) {
        const newData = result.data.userData;
        setGithubData(newData);
        toast.success("GitHub数据获取成功");
        return newData; // 🔧 返回新获取的数据
      } else {
        toast.error(`获取GitHub数据失败: ${result.error}`);
        return null;
      }
    } catch (error) {
      console.error("GitHub data fetch error:", error);
      toast.error("网络错误，请稍后重试");
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  return {
    githubData,
    setGithubData,
    isLoading,
    fetchGitHubData,
  };
};
