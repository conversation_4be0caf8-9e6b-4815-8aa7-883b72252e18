/**
 * 流式内容管理Hook - 处理实时流式内容更新和打字机效果
 */

import { useState, useEffect, useCallback, useRef } from "react";
import { getLogManager } from "@/lib/ai/core/utils/LogManager";
import type {
  ModuleExecutionLog,
  AnyLogEvent,
  StreamContentUpdateEvent,
  LogEvent,
} from "@/types/debug-logging";
import type { ModuleType } from "@/lib/ai/types";

interface StreamingContentState {
  [logId: string]: {
    content: string;
    isStreaming: boolean;
    chunkCount: number;
    lastUpdateTime: number;
  };
}

interface UseStreamingContentProps {
  sessionId: string;
  onContentUpdate?: (
    logId: string,
    content: string,
    isComplete: boolean
  ) => void;
}

export const useStreamingContent = ({
  sessionId,
  onContentUpdate,
}: UseStreamingContentProps) => {
  const [streamingContents, setStreamingContents] =
    useState<StreamingContentState>({});
  const logManager = getLogManager();
  const callbackRef = useRef(onContentUpdate);

  // 更新回调引用
  useEffect(() => {
    callbackRef.current = onContentUpdate;
  }, [onContentUpdate]);

  // 处理流式内容更新事件
  const handleStreamContentUpdate = useCallback(
    (event: StreamContentUpdateEvent) => {
      if (event.sessionId !== sessionId) return;

      const { logId, content, accumulatedContent, chunkCount, timestamp } =
        event;

      // 🚀 添加调试日志
      if (chunkCount <= 5) {
        console.log(
          `📡 [Frontend Streaming] 收到第 ${chunkCount} 个内容更新，累积长度: ${
            accumulatedContent.length
          }, 新内容: "${content.substring(0, 30)}..."`
        );
      }

      setStreamingContents((prev) => ({
        ...prev,
        [logId]: {
          content: accumulatedContent,
          isStreaming: true,
          chunkCount,
          lastUpdateTime: timestamp,
        },
      }));

      // 触发外部回调
      callbackRef.current?.(logId, accumulatedContent, false);
    },
    [sessionId]
  );

  // 处理日志更新事件（用于检测流式完成）
  const handleLogUpdate = useCallback(
    (event: LogEvent) => {
      if (event.log.sessionId !== sessionId) return;

      const log: ModuleExecutionLog = event.log;
      const streamingInfo = log.context?.streamingContent;

      if (streamingInfo && !streamingInfo.isStreaming) {
        // 流式内容已完成
        const logId = log.id;
        setStreamingContents((prev) => ({
          ...prev,
          [logId]: {
            content: streamingInfo.accumulatedContent,
            isStreaming: false,
            chunkCount: streamingInfo.chunkCount,
            lastUpdateTime: streamingInfo.lastUpdateTime || Date.now(),
          },
        }));

        // 触发完成回调
        callbackRef.current?.(logId, streamingInfo.accumulatedContent, true);
      }
    },
    [sessionId]
  );

  // 设置事件监听器
  useEffect(() => {
    // 监听流式内容更新事件
    const streamUpdateCallback = (event: AnyLogEvent) => {
      if (event.type === "stream_content_update") {
        handleStreamContentUpdate(event as StreamContentUpdateEvent);
      }
    };

    // 监听日志更新事件
    const logUpdateCallback = (event: AnyLogEvent) => {
      if (event.type === "log_updated") {
        handleLogUpdate(event as LogEvent);
      }
    };

    logManager.addEventCallback(streamUpdateCallback);
    logManager.addEventCallback(logUpdateCallback);

    return () => {
      logManager.removeEventCallback(streamUpdateCallback);
      logManager.removeEventCallback(logUpdateCallback);
    };
  }, [logManager, handleStreamContentUpdate, handleLogUpdate]);

  // 获取指定日志的流式内容
  const getStreamingContent = useCallback(
    (logId: string) => {
      return streamingContents[logId];
    },
    [streamingContents]
  );

  // 检查日志是否正在流式传输
  const isLogStreaming = useCallback(
    (logId: string) => {
      return streamingContents[logId]?.isStreaming || false;
    },
    [streamingContents]
  );

  // 获取日志的累积内容
  const getAccumulatedContent = useCallback(
    (logId: string) => {
      return streamingContents[logId]?.content || "";
    },
    [streamingContents]
  );

  // 清理指定会话的流式内容
  const clearStreamingContent = useCallback(() => {
    setStreamingContents({});
  }, []);

  // 手动完成流式日志（用于调试）
  const completeStreamingLog = useCallback(
    (moduleType: ModuleType, stage: string, finalMetadata?: any) => {
      return logManager.completeStreamingLog(
        sessionId,
        moduleType,
        stage,
        finalMetadata
      );
    },
    [logManager, sessionId]
  );

  return {
    streamingContents,
    getStreamingContent,
    isLogStreaming,
    getAccumulatedContent,
    clearStreamingContent,
    completeStreamingLog,
  };
};
