/**
 * 日志管理相关的Hook
 */

import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { getLogManager } from "@/lib/ai/core/utils/LogManager";
import type {
  ModuleExecutionLog,
  LogLevel,
  ExecutionStatus,
  LogEvent,
  AnyLogEvent,
  StreamEvent,
  StreamContentUpdateEvent,
} from "@/types/debug-logging";
import type { ModuleType } from "@/lib/ai/types";

/**
 * 从日志ID中提取序列号 - 与LogManager保持一致
 */
const extractSequenceFromLogId = (logId: string): number => {
  // 日志ID格式：log-{timestamp}-{sequence}-{randomString}
  const match = logId.match(/^log-\d+-(\d+)-/);
  if (match && match[1]) {
    return parseInt(match[1], 10);
  }
  return 0; // 默认序列号
};

interface UseLogManagerProps {
  sessionId: string;
  maxLogs?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onEventReceived?: (event: AnyLogEvent) => void;
}

export const useLogManager = ({
  sessionId,
  maxLogs = 1000,
  autoRefresh = true,
  refreshInterval = 5000,
  onEventReceived,
}: UseLogManagerProps) => {
  const [logs, setLogs] = useState<ModuleExecutionLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filteredLogs, setFilteredLogs] = useState<ModuleExecutionLog[]>([]);

  // 过滤器状态
  const [searchTerm, setSearchTerm] = useState("");
  const [levelFilter, setLevelFilter] = useState<LogLevel | "all">("all");
  const [statusFilter, setStatusFilter] = useState<ExecutionStatus | "all">(
    "all"
  );
  const [moduleFilter, setModuleFilter] = useState<ModuleType | "all">("all");

  const refreshTimerRef = useRef<NodeJS.Timeout>();
  const logManager = getLogManager();

  // 加载日志
  const loadLogs = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await logManager.queryLogs({
        sessionIds: [sessionId],
        limit: maxLogs,
        sort: { field: "timestamp", direction: "desc" },
      });
      setLogs(result.logs);
    } catch (error) {
      console.error("Failed to load logs:", error);
    } finally {
      setIsLoading(false);
    }
  }, [sessionId, maxLogs, logManager]);

  // 应用过滤器
  const applyFilters = useCallback(() => {
    let filtered = [...logs];

    // 搜索过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(term) ||
          log.moduleName.toLowerCase().includes(term) ||
          log.moduleType.toLowerCase().includes(term)
      );
    }

    // 日志级别过滤
    if (levelFilter !== "all") {
      filtered = filtered.filter((log) => log.level === levelFilter);
    }

    // 状态过滤
    if (statusFilter !== "all") {
      filtered = filtered.filter((log) => log.status === statusFilter);
    }

    // 模块过滤
    if (moduleFilter !== "all") {
      filtered = filtered.filter((log) => log.moduleType === moduleFilter);
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, levelFilter, statusFilter, moduleFilter]);

  // 导出日志
  const exportLogs = useCallback(
    async (format: "json" | "csv" | "txt") => {
      try {
        const blob = await logManager.exportLogs({
          format,
          filter: { sessionIds: [sessionId] },
          includeMetadata: true,
          compress: false,
          filenamePrefix: `debug-session-${sessionId}`,
        });

        // 下载文件
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `debug-session-${sessionId}.${format}`;
        a.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Failed to export logs:", error);
      }
    },
    [sessionId, logManager]
  );

  // 清理日志
  const clearLogs = useCallback(async () => {
    if (confirm("确定要清理当前会话的所有日志吗？此操作不可撤销。")) {
      try {
        await logManager.cleanup();
        await loadLogs();
      } catch (error) {
        console.error("Failed to clear logs:", error);
      }
    }
  }, [logManager, loadLogs]);

  // 重置过滤器
  const resetFilters = useCallback(() => {
    setSearchTerm("");
    setLevelFilter("all");
    setStatusFilter("all");
    setModuleFilter("all");
  }, []);

  // 初始化和事件监听
  useEffect(() => {
    loadLogs();

    // 设置事件监听器
    const handleLogEvent = (event: AnyLogEvent) => {
      // 获取事件的sessionId，根据事件类型不同位置不同
      let eventSessionId: string;
      if (
        event.type === "stream_event" ||
        event.type === "stream_content_update"
      ) {
        eventSessionId = (event as StreamEvent | StreamContentUpdateEvent)
          .sessionId;
      } else {
        eventSessionId = (event as LogEvent).log.sessionId;
      }

      if (eventSessionId === sessionId) {
        // 只对特定事件类型更新日志列表
        if (event.type === "log_created" || event.type === "log_updated") {
          const logEvent = event as LogEvent;
          setLogs((prev) => {
            // 检查是否已存在相同ID的日志，避免重复
            const existingIndex = prev.findIndex(
              (log) => log.id === logEvent.log.id
            );
            if (existingIndex >= 0) {
              // 如果已存在，更新该日志
              const updated = [...prev];
              updated[existingIndex] = logEvent.log;
              return updated;
            } else {
              // 如果不存在，插入到正确的排序位置
              const newLogs = [...prev, logEvent.log];

              // 应用与LogManager相同的排序逻辑
              newLogs.sort((a, b) => {
                // 首先按时间戳降序排序
                const timestampDiff = b.timestamp - a.timestamp;
                if (timestampDiff !== 0) {
                  return timestampDiff;
                }

                // 时间戳相同时，按日志ID序列号排序（与时间戳降序保持一致）
                const aSequence = extractSequenceFromLogId(a.id);
                const bSequence = extractSequenceFromLogId(b.id);

                return bSequence - aSequence; // 🎯 修复：序列号大的在前，保持与时间戳降序一致
              });

              return newLogs.slice(0, maxLogs);
            }
          });
        }
        onEventReceived?.(event);
      }
    };

    logManager.addEventCallback(handleLogEvent);

    // 设置自动刷新
    if (autoRefresh) {
      refreshTimerRef.current = setInterval(loadLogs, refreshInterval);
    }

    return () => {
      logManager.removeEventCallback(handleLogEvent);
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, [
    sessionId,
    maxLogs,
    autoRefresh,
    refreshInterval,
    loadLogs,
    logManager,
    onEventReceived,
  ]);

  // 应用过滤器
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // 统计数据
  const statistics = useMemo(() => {
    const total = filteredLogs.length;
    const completed = filteredLogs.filter(
      (l) => l.status === "completed"
    ).length;
    const failed = filteredLogs.filter((l) => l.status === "failed").length;
    const avgProcessingTime =
      filteredLogs
        .filter((l) => l.processingTime)
        .reduce((sum, l) => sum + (l.processingTime || 0), 0) /
      Math.max(1, filteredLogs.filter((l) => l.processingTime).length);

    return {
      total,
      completed,
      failed,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      avgProcessingTime,
    };
  }, [filteredLogs]);

  return {
    logs,
    filteredLogs,
    isLoading,
    statistics,

    // 过滤器状态和控制
    searchTerm,
    setSearchTerm,
    levelFilter,
    setLevelFilter,
    statusFilter,
    setStatusFilter,
    moduleFilter,
    setModuleFilter,
    resetFilters,

    // 操作方法
    loadLogs,
    exportLogs,
    clearLogs,
  };
};
