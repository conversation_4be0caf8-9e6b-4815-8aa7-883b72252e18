/**
 * 默认数据模板 - Doubao 1.6 迁移版本
 *
 * 为每个AI模块提供独立测试用的示例数据模板
 * 这些模板使我们能够在没有上游模块输出时进行模块测试
 *
 * 更新历史：
 * - 2025-01-15: 更新所有模板以适配 Doubao 1.6 迁移后的新输出格式
 */

import type {
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
} from "@/lib/ai/types";

/**
 * 默认分析器输出模板 - 新版本
 * 用于测试Strategist模块
 *
 * 更新：适配新的 AnalyzerOutput 格式
 */
export const DEFAULT_ANALYZER_OUTPUT_TEMPLATE: AnalyzerOutput = {
  status: "success",
  content: [
    "深夜编程侠",
    "多语言探索者",
    "幽默代码诗人",
    "技术栈收集家",
    "佛系提交者",
  ], // 语义标签数组
  processing_time: 1160,
  confidence: 0.8,
  metadata: {
    module: "analyzer",
    timestamp: Date.now(),
    summary: {
      successCount: 4,
      totalCount: 4,
      overallConfidence: 0.8,
      dataQuality: "good" as const,
    },
    llmCallCount: 1,
    dataVersion: "1.0.0",
  },
};

/**
 * 默认策略师输出模板 - V6.0 创意简报版本
 * 用于测试Writer模块
 *
 * 更新：适配 StrategistModule 的简化架构输出格式
 */
export const DEFAULT_STRATEGIST_OUTPUT_TEMPLATE: StrategistOutput = {
  status: "success",
  content: {
    narrative_scaffold: {
      A: "这是一位沉浸在代码世界中的程序员，以2850次提交书写着自己的技术成长轨迹",
      B: "但1250个粉丝的关注度似乎与技术实力形成了某种微妙的反差",
      C_hint: "以自嘲式收尾，暗示技术宅的社交困境与代码世界的反差萌",
    },
    insight_pool: {
      behavior_summary:
        "高产出技术贡献者，拥有显著社区影响力，在代码质量和协作方面表现突出",
      persona: "低调的技术实力派，用代码说话的务实主义者",
      humor_angle: "认知错位：技术实力与社交表现的反差",
      analogy_pool: [
        "像是深藏功与名的扫地僧，在代码的江湖里默默耕耘",
        "如同夜里的猫头鹰，在键盘声中找到属于自己的节奏",
        "仿佛一位收藏家，每个commit都是精心雕琢的艺术品",
      ],
    },
    punchline_focus: {
      punchline_candidates: [
        "毕竟，能让代码说话的人，往往在现实中更喜欢让代码替自己说话",
        "在技术的世界里，他们是国王；在社交的场合，他们可能只是那个默默点赞的人",
        "代码千行易得，知音一个难求——这或许就是程序员的浪漫",
      ],
      tone: "温暖幽默 + 技术文艺",
      stylistic_suggestion: "以哲思式暗喻收尾，体现程序员群体的内心独白",
    },
    writing_instruction: {
      focus_on: "重点打磨C段的情感共鸣，让技术人员能够产生'被理解'的感受",
      overall_tone: "轻松诙谐中带有一丝技术人的自嘲和温暖，避免刻板印象的堆砌",
    },
  },
  processing_time: 150,
  confidence: 0.89,
  metadata: {
    module: "strategist",
    timestamp: Date.now(),
    semanticTagsCount: 6,
    llmModel: "doubao-seed-1.6",
    templateVersion: "tags-to-strategist-v1.0",
  },
};

/**
 * 默认写作者输出模板 - V6.0 模板化版本
 * 用于测试和调试WriterModule
 *
 * 更新：适配基于strategist-to-text.md模板的简化架构
 */
export const DEFAULT_WRITER_OUTPUT_TEMPLATE: WriterOutput = {
  status: "success",
  content: {
    generatedText:
      "最近有点忙得不可开交，写了 2850 次 commit，感觉键盘都快被我敲坏了。说实话，有时候我都怀疑自己是不是有代码强迫症，一天不写点什么就浑身不舒服。\n\n虽然累是累，但看到 1250 个开发者关注我的项目，还是挺有成就感的。代码审查也做了 156 次，帮团队小伙伴们看看代码，顺便学习一下别人的思路。有时候审查别人的代码比写自己的还有意思，能发现很多有趣的解决方案。\n\n我的项目总共收获了 890+ stars，虽然不算特别多，但每一个 star 都是对我努力的认可。希望能继续保持这种节奏，用代码创造更多有价值的东西，同时也希望能帮助更多的开发者成长。",
    usedTemplate: "docs/v6/strategist-to-text.md", // V6.0新增：模板路径
    confidence: 0.85, // 基于模板的高置信度
    iterations: 1, // V6.0简化：单次生成
    qualityScore: 0.85, // 简化：使用confidence作为质量分数
    // V6.0移除的复杂字段：
    // - usedExamples (FewShot示例，已简化移除)
    // - reasoningEnhanced (推理增强模式，已简化移除)
    // - creativityMetrics (复杂的创意指标，已简化移除)
  },
  processing_time: 2800,
  metadata: {
    writerModuleVersion: "V6.0-Template",
    templateBased: true,
    templatePath: "docs/v6/strategist-to-text.md",
  },
};
