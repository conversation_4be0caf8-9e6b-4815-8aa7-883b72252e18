"use client";

import { useState, useRef, useEffect } from "react";
import Editor from "@monaco-editor/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Code, Check, X, AlertCircle } from "lucide-react";
import type { editor } from "monaco-editor";

// JSON 值类型定义
type JsonValue = string | number | boolean | null | JsonObject | JsonArray;
type JsonObject = { [key: string]: JsonValue };
type JsonArray = JsonValue[];

interface MonacoJsonEditorProps {
  title?: string;
  value: JsonValue | string;
  onChange?: (value: JsonValue) => void;
  onRawChange?: (value: string) => void;
  height?: number | string;
  readOnly?: boolean;
}

export function MonacoJsonEditor({
  title = "JSON Editor",
  value,
  onChange,
  onRawChange,
  height = 300,
  readOnly = false,
}: MonacoJsonEditorProps) {
  const [isValid, setIsValid] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);

  const [editorValue, setEditorValue] = useState(() => {
    try {
      if (typeof value === "string") {
        // 如果是字符串，先尝试解析再格式化
        try {
          return JSON.stringify(JSON.parse(value), null, 2);
        } catch {
          // 如果解析失败，直接返回字符串
          return value;
        }
      }
      return JSON.stringify(value, null, 2);
    } catch {
      return String(value);
    }
  });

  // Update editor value when prop changes
  useEffect(() => {
    try {
      if (typeof value === "string") {
        // 如果是字符串，先尝试解析再格式化
        try {
          const parsed = JSON.parse(value);
          setEditorValue(JSON.stringify(parsed, null, 2));
        } catch {
          // 如果解析失败，直接使用字符串
          setEditorValue(value);
        }
      } else {
        const newJsonString = JSON.stringify(value, null, 2);
        setEditorValue(newJsonString);
      }
    } catch {
      // Keep current value if parsing fails
    }
  }, [value]);

  // Handle editor change
  const handleEditorChange = (newValue: string | undefined) => {
    if (newValue === undefined || readOnly) return;

    setEditorValue(newValue);

    // Call raw change handler if provided
    onRawChange?.(newValue);

    try {
      const parsed = JSON.parse(newValue);
      setIsValid(true);
      setError(null);
      onChange?.(parsed);
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : "Invalid JSON";
      setIsValid(false);
      setError(errorMessage);
    }
  };

  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor) => {
    editorRef.current = editor;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex gap-2 items-center">
            <Code className="h-5 w-5" />
            {title}
            <Badge variant={isValid ? "secondary" : "destructive"}>
              {isValid ? (
                <>
                  <Check className="h-3 mr-1 w-3" /> Valid
                </>
              ) : (
                <>
                  <X className="h-3 mr-1 w-3" /> Invalid
                </>
              )}
            </Badge>
          </CardTitle>
        </div>
        {error && (
          <div className="flex text-sm text-red-600 gap-2 items-center">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div className="border rounded-md overflow-hidden">
          <Editor
            height={height}
            defaultLanguage="json"
            value={editorValue}
            onChange={handleEditorChange}
            onMount={handleEditorDidMount}
            options={{
              selectOnLineNumbers: true,
              automaticLayout: true,
              fontSize: 14,
              minimap: { enabled: false },
              readOnly,
              wordWrap: "on",
              theme: "vs-dark",
            }}
            theme="vs-dark"
          />
        </div>
      </CardContent>
    </Card>
  );
}
