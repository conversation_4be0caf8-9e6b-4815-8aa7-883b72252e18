/**
 * 日志统计信息组件
 */

import { Badge } from "@/components/ui/badge";

interface LogStatistics {
  total: number;
  completed: number;
  failed: number;
  successRate: number;
  avgProcessingTime: number;
}

interface LogStatsProps {
  statistics: LogStatistics;
}

export const LogStats = ({ statistics }: LogStatsProps) => {
  return (
    <div className="flex items-center gap-2 text-xs">
      <Badge variant="secondary" className="bg-blue-600/20 text-blue-300">
        总计: {statistics.total}
      </Badge>
      <Badge variant="secondary" className="bg-green-600/20 text-green-300">
        成功: {statistics.completed}
      </Badge>
      <Badge variant="secondary" className="bg-red-600/20 text-red-300">
        失败: {statistics.failed}
      </Badge>
      <Badge variant="secondary" className="bg-purple-600/20 text-purple-300">
        成功率: {statistics.successRate.toFixed(1)}%
      </Badge>
      <Badge variant="secondary" className="bg-yellow-600/20 text-yellow-300">
        平均耗时: {statistics.avgProcessingTime.toFixed(0)}ms
      </Badge>
    </div>
  );
};