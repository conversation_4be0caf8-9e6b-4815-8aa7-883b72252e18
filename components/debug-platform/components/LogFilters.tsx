/**
 * 日志过滤器组件
 */

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, Search } from "lucide-react";
import type { LogLevel, ExecutionStatus } from "@/types/debug-logging";
import type { ModuleType } from "@/lib/ai/types";

interface LogFiltersProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  levelFilter: LogLevel | "all";
  onLevelChange: (level: LogLevel | "all") => void;
  statusFilter: ExecutionStatus | "all";
  onStatusChange: (status: ExecutionStatus | "all") => void;
  moduleFilter: ModuleType | "all";
  onModuleChange: (module: ModuleType | "all") => void;
  onReset: () => void;
}

export const LogFilters = ({
  searchTerm,
  onSearchChange,
  levelFilter,
  onLevelChange,
  statusFilter,
  onStatusChange,
  moduleFilter,
  onModuleChange,
  onReset,
}: LogFiltersProps) => {
  return (
    <div className="flex items-center gap-2 p-2 border-b border-gray-700 bg-gray-800">
      <div className="relative flex-1 max-w-xs">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
        <Input
          placeholder="搜索..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-7 h-7 text-xs bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400"
        />
      </div>

      <Select value={levelFilter} onValueChange={onLevelChange}>
        <SelectTrigger className="w-20 h-7 text-xs bg-gray-700 border-gray-600 text-gray-100">
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="bg-gray-800 border-gray-600">
          <SelectItem value="all">全部</SelectItem>
          <SelectItem value="debug">Debug</SelectItem>
          <SelectItem value="info">Info</SelectItem>
          <SelectItem value="warn">Warn</SelectItem>
          <SelectItem value="error">Error</SelectItem>
        </SelectContent>
      </Select>

      <Select value={statusFilter} onValueChange={onStatusChange}>
        <SelectTrigger className="w-24 h-7 text-xs bg-gray-700 border-gray-600 text-gray-100">
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="bg-gray-800 border-gray-600">
          <SelectItem value="all">全部状态</SelectItem>
          <SelectItem value="completed">已完成</SelectItem>
          <SelectItem value="running">运行中</SelectItem>
          <SelectItem value="failed">失败</SelectItem>
          <SelectItem value="pending">待处理</SelectItem>
        </SelectContent>
      </Select>

      <Select value={moduleFilter} onValueChange={onModuleChange}>
        <SelectTrigger className="w-24 h-7 text-xs bg-gray-700 border-gray-600 text-gray-100">
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="bg-gray-800 border-gray-600">
          <SelectItem value="all">全部模块</SelectItem>
          <SelectItem value="analyzer">分析器</SelectItem>
          <SelectItem value="strategist">策略师</SelectItem>
          <SelectItem value="writer">写作者</SelectItem>
          <SelectItem value="critic">评论家</SelectItem>
        </SelectContent>
      </Select>

      <Button
        variant="ghost"
        size="sm"
        className="h-7 px-2 text-gray-400 hover:text-gray-100"
        onClick={onReset}
      >
        <Filter className="h-3 w-3" />
      </Button>
    </div>
  );
};