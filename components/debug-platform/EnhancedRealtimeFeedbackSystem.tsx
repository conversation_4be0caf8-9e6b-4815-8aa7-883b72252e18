"use client";

import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Download,
  Trash2,
  ChevronUp,
  ChevronDown,
  Bug,
  Loader2,
  User,
  Play,
  RotateCcw,
  Brain,
  Target,
  PenTool,
  MessageSquare,
} from "lucide-react";
import type { AnyLogEvent } from "@/types/debug-logging";
import type { UserData } from "@/types/user-data";
import type {
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
} from "@/lib/ai/types";
import type { AnalyzerTabRef } from "./tabs/AnalyzerTab";
import type { StrategistTabRef } from "./tabs/StrategistTab";
import type { WriterTabRef } from "./tabs/WriterTab";
import type { CriticTabRef } from "./tabs/CriticTab";
import { useGitHubData, useLogManager, useStreamingContent } from "./hooks";
import {
  LogEntry,
  StreamingLogEntry,
  LogFilters,
  LogStats,
} from "./components";

/**
 * V5.3 增强版实时反馈系统 - Dock模式
 *
 * 功能：
 * - 底部固定Dock面板设计
 * - 实时日志流显示
 * - 智能过滤和搜索
 * - 多格式日志导出
 * - GitHub数据集成
 * - 会话状态统计
 * - 自动滚动和刷新
 * - 可折叠面板控制
 */

interface EnhancedRealtimeFeedbackSystemProps {
  sessionId: string;
  maxLogs?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onEventReceived?: (event: AnyLogEvent) => void;
  githubData?: UserData;
  onGithubDataChange?: (data: UserData | null) => void;
  // 模块控制相关
  activeTab?: string;
  onActiveTabChange?: (tab: string) => void;
  moduleRefs?: React.MutableRefObject<{
    analyzer?: AnalyzerTabRef;
    strategist?: StrategistTabRef;
    writer?: WriterTabRef;
    critic?: CriticTabRef;
  }>;
  loadingStates?: { [key: string]: boolean };
  // 模块状态指示
  analyzerOutput?: AnalyzerOutput;
  strategistOutput?: StrategistOutput;
  writerOutput?: WriterOutput;
  criticOutput?: CriticOutput;
  streamingResult?: string | null;
}

export const EnhancedRealtimeFeedbackSystem = ({
  sessionId,
  maxLogs = 1000,
  autoRefresh = true,
  refreshInterval = 5000,
  onEventReceived,
  githubData: externalGithubData,
  onGithubDataChange,
  activeTab = "analyzer",
  onActiveTabChange,
  moduleRefs,
  loadingStates = {},
  analyzerOutput,
  strategistOutput,
  writerOutput,
  criticOutput,
  streamingResult,
}: EnhancedRealtimeFeedbackSystemProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // GitHub数据管理
  const {
    githubData: internalGithubData,
    isLoading: isLoadingGithub,
    fetchGitHubData,
  } = useGitHubData();

  // 使用外部传入的数据或内部状态
  const effectiveGithubData = externalGithubData || internalGithubData;

  // 获取GitHub数据
  const handleGetGithubData = async () => {
    console.log("🔄 [Debug] 开始获取GitHub数据...");
    // 🔧 直接使用返回的新数据，而不是依赖异步状态更新
    const newData = await fetchGitHubData();
    console.log(
      "📦 [Debug] fetchGitHubData返回的数据:",
      newData ? `用户 ${newData.login}` : "null"
    );
    // 如果有外部回调且获取到了新数据，则传递给父组件
    if (onGithubDataChange && newData) {
      console.log("📤 [Debug] 正在通过回调传递新数据给父组件...");
      onGithubDataChange(newData);
      console.log("✅ [Debug] 数据传递完成，父组件应该立即更新");
    } else {
      console.log("⚠️ [Debug] 没有回调或没有获取到数据");
    }
  };

  // 模块操作处理
  const handleModuleAction = (action: "run" | "reset") => {
    if (!moduleRefs?.current) return;

    const moduleRef =
      moduleRefs.current[activeTab as keyof typeof moduleRefs.current];
    if (!moduleRef) return;

    if (action === "run") {
      moduleRef.runModule?.();
    } else if (action === "reset") {
      moduleRef.resetConfig?.();
    }
  };

  // 标签页选项配置
  const tabOptions = [
    {
      value: "analyzer",
      label: "Analyzer",
      icon: Brain,
      hasOutput: !!analyzerOutput,
    },
    {
      value: "strategist",
      label: "Strategist",
      icon: Target,
      hasOutput: !!strategistOutput,
    },
    {
      value: "writer",
      label: "Writer",
      icon: PenTool,
      hasOutput: !!writerOutput,
    },
    {
      value: "critic",
      label: "Critic",
      icon: MessageSquare,
      hasOutput: !!criticOutput,
    },
  ];

  // 检查当前模块是否可以显示操作按钮
  const showModuleActions = activeTab !== "input" && activeTab !== "all";
  const isModuleLoading = loadingStates[activeTab] || false;

  // 日志管理
  const {
    filteredLogs,
    isLoading,
    statistics,
    searchTerm,
    setSearchTerm,
    levelFilter,
    setLevelFilter,
    statusFilter,
    setStatusFilter,
    moduleFilter,
    setModuleFilter,
    resetFilters,
    exportLogs,
    clearLogs,
  } = useLogManager({
    sessionId,
    maxLogs,
    autoRefresh,
    refreshInterval,
    onEventReceived,
  });

  // 流式内容管理
  const { getStreamingContent, isLogStreaming, getAccumulatedContent } =
    useStreamingContent({
      sessionId,
      onContentUpdate: (logId, content, isComplete) => {
        console.log(
          `📝 [Streaming Content] Log ${logId}: ${content.length} chars, complete: ${isComplete}`
        );
      },
    });

  return (
    <>
      {/* 固定底部Dock栏 */}
      <div className="fixed bottom-0 left-0 right-0 z-40 bg-gray-900 border-t border-gray-700 shadow-lg">
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Bug className="h-4 w-4 text-blue-400" />
              <span className="text-sm font-medium text-gray-300">
                调试控制台
              </span>
            </div>

            <LogStats statistics={statistics} />
          </div>

          <div className="flex items-center gap-2">
            {/* 标签页选择器 */}
            <div className="flex items-center gap-2">
              <Select value={activeTab} onValueChange={onActiveTabChange}>
                <SelectTrigger className="w-[140px] h-8 bg-gray-800 border-gray-600 text-gray-200 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {tabOptions.map((option) => {
                    const IconComponent = option.icon;
                    return (
                      <SelectItem
                        key={option.value}
                        value={option.value}
                        className="text-gray-200 focus:bg-gray-700 focus:text-gray-100"
                      >
                        <div className="flex items-center gap-2">
                          <IconComponent className="h-3 w-3" />
                          <span>{option.label}</span>
                          {option.hasOutput && (
                            <span className="text-green-400 text-xs">✓</span>
                          )}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            {/* 模块控制按钮 */}
            {showModuleActions && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 text-gray-400 hover:text-gray-100"
                  onClick={() => handleModuleAction("reset")}
                  disabled={isModuleLoading}
                >
                  <RotateCcw className="h-3 w-3" />
                  <span className="ml-1 text-xs">重置配置</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 text-gray-400 hover:text-gray-100"
                  onClick={() => handleModuleAction("run")}
                  disabled={isModuleLoading}
                >
                  {isModuleLoading ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Play className="h-3 w-3" />
                  )}
                  <span className="ml-1 text-xs">运行模块</span>
                </Button>
              </>
            )}

            {/* GitHub数据获取按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-gray-400 hover:text-gray-100"
              onClick={handleGetGithubData}
              disabled={isLoadingGithub}
            >
              {isLoadingGithub ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <User className="h-3 w-3" />
              )}
              <span className="ml-1 text-xs">
                {effectiveGithubData ? "更新数据" : "获取数据"}
              </span>
            </Button>

            {/* 导出按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-gray-400 hover:text-gray-100"
              onClick={() => exportLogs("json")}
            >
              <Download className="h-3 w-3" />
              <span className="ml-1 text-xs">导出</span>
            </Button>

            {/* 清理按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-gray-400 hover:text-gray-100"
              onClick={clearLogs}
            >
              <Trash2 className="h-3 w-3" />
              <span className="ml-1 text-xs">清理</span>
            </Button>

            {/* 展开/折叠按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-gray-400 hover:text-gray-100"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronUp className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* 展开的日志面板 */}
      <div
        className={`fixed bottom-[57px] left-0 right-0 z-30 bg-gray-900 text-gray-100 font-mono border-t border-gray-700 shadow-lg transition-transform duration-300 ${
          isExpanded ? "translate-y-0" : "translate-y-full"
        }`}
        style={{ height: isExpanded ? "60vh" : "0" }}
      >
        <div className="h-full flex flex-col">
          {/* 过滤器 */}
          <LogFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            levelFilter={levelFilter}
            onLevelChange={setLevelFilter}
            statusFilter={statusFilter}
            onStatusChange={setStatusFilter}
            moduleFilter={moduleFilter}
            onModuleChange={setModuleFilter}
            onReset={resetFilters}
          />

          {/* 日志内容 - 命令行风格 */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full" ref={scrollAreaRef}>
              {filteredLogs.length === 0 ? (
                <div className="text-center text-gray-500 py-8 text-sm">
                  {isLoading ? "加载中..." : "暂无日志数据"}
                </div>
              ) : (
                <div className="p-4 space-y-2">
                  {filteredLogs.map((log) => {
                    // 检查是否是流式日志
                    const streamingContent = getStreamingContent(log.id);
                    const isStreaming = isLogStreaming(log.id);

                    // 🎯 修复：更严格的流式内容检查
                    const effectiveStreamingContent =
                      getAccumulatedContent(log.id) ||
                      log.context?.streamingContent?.accumulatedContent ||
                      "";

                    const hasStreamingContent =
                      (streamingContent && effectiveStreamingContent) ||
                      (log.context?.streamingContent &&
                        effectiveStreamingContent);

                    if (hasStreamingContent) {
                      return (
                        <StreamingLogEntry
                          key={log.id}
                          log={log}
                          streamingContent={effectiveStreamingContent}
                          isStreaming={isStreaming}
                        />
                      );
                    } else {
                      return <LogEntry key={log.id} log={log} />;
                    }
                  })}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>
      </div>
    </>
  );
};

export default EnhancedRealtimeFeedbackSystem;
