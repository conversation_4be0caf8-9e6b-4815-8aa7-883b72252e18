"use client";

import React, { useState, useMemo, useRef, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { RefreshCw } from "lucide-react";
import toast from "react-hot-toast";

import { AnalyzerTab, StrategistTab, WriterTab, CriticTab } from "./tabs";
import type { AnalyzerTabRef } from "./tabs/AnalyzerTab";
import type { StrategistTabRef } from "./tabs/StrategistTab";
import type { WriterTabRef } from "./tabs/WriterTab";
import type { CriticTabRef } from "./tabs/CriticTab";
import { EnhancedRealtimeFeedbackSystem } from "./EnhancedRealtimeFeedbackSystem";

import type { UserData } from "@/types/user-data";
import type {
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  CriticOutput,
} from "@/lib/ai/types";
import type { GitHubExtendedData } from "@/types/github-extended";
import { useGitHubData } from "./hooks/useGitHubData";

interface DebugControlCenterProps {
  className?: string;
}

export function DebugControlCenter({}: DebugControlCenterProps) {
  // GitHub数据获取Hook
  const {
    githubData: realGithubData,
    isLoading: isLoadingGithub,
    fetchGitHubData,
  } = useGitHubData();

  // 生成唯一的调试会话ID
  const sessionId = useMemo(
    () =>
      `debug-session-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`,
    []
  );

  // 公共状态：GitHub数据
  const [githubData, setGithubData] = useState<UserData | null>(
    realGithubData || null
  );

  // 扩展数据状态管理
  const [extendedData, setExtendedData] = useState<GitHubExtendedData | null>(
    null
  );
  const [isLoadingExtended, setIsLoadingExtended] = useState(false);

  const [activeTab, setActiveTab] = useState("analyzer");

  // 获取扩展数据
  const fetchExtendedData = async () => {
    if (isLoadingExtended) return; // 防止重复请求

    setIsLoadingExtended(true);
    try {
      console.log("🔄 [DebugControlCenter] 获取GitHub扩展数据...");
      const response = await fetch("/api/github-extended");
      if (response.ok) {
        const result = (await response.json()) as {
          success: boolean;
          data?: GitHubExtendedData;
        };
        if (result.success && result.data) {
          console.log("✅ [DebugControlCenter] 获取到真实扩展数据");
          setExtendedData(result.data);
        } else {
          console.error(
            "❌ [DebugControlCenter] 扩展数据API返回成功但没有数据"
          );
          toast.error("扩展数据获取失败：API返回成功但没有数据");
        }
      } else {
        console.error(
          "❌ [DebugControlCenter] 扩展数据API请求失败:",
          response.status
        );
        toast.error(`扩展数据获取失败：API请求失败 (${response.status})`);
      }
    } catch (error) {
      const errorMsg = `获取扩展数据失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`;
      console.error("❌ [DebugControlCenter]", errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsLoadingExtended(false);
    }
  };

  // 自动获取当前登录用户的GitHub数据和扩展数据
  useEffect(() => {
    const initializeData = async () => {
      // 首先获取基础GitHub数据
      if (!realGithubData && !isLoadingGithub) {
        console.log(
          "🔄 [DebugControlCenter] 自动获取当前登录用户的GitHub数据..."
        );
        try {
          const data = await fetchGitHubData();
          if (data) {
            console.log(
              "✅ [DebugControlCenter] 已获取真实GitHub数据，更新状态"
            );
            setGithubData(data);

            // GitHub数据获取成功后，自动获取扩展数据
            fetchExtendedData();
          } else {
            const errorMsg = "无法获取GitHub数据，请检查登录状态";
            console.error("❌ [DebugControlCenter]", errorMsg);
            toast.error(errorMsg);
          }
        } catch (error) {
          const errorMsg = `获取GitHub数据失败: ${
            error instanceof Error ? error.message : "未知错误"
          }`;
          console.error("❌ [DebugControlCenter]", errorMsg);
          toast.error(errorMsg);
        }
      } else if (realGithubData) {
        console.log("✅ [DebugControlCenter] 使用已缓存的GitHub数据");
        setGithubData(realGithubData);

        // 有GitHub数据时，也获取扩展数据
        fetchExtendedData();
      }
    };

    initializeData();
  }, [realGithubData, isLoadingGithub, fetchGitHubData]);

  // 各模块的执行结果状态
  const [analyzerOutput, setAnalyzerOutput] = useState<
    AnalyzerOutput | undefined
  >();
  const [strategistOutput, setStrategistOutput] = useState<
    StrategistOutput | undefined
  >();
  const [writerOutput, setWriterOutput] = useState<WriterOutput | undefined>();
  const [criticOutput, setCriticOutput] = useState<CriticOutput | undefined>();

  // 流式生成结果
  const [streamingResult, setStreamingResult] = useState<string | null>(null);

  // 各模块的loading状态
  const [loadingStates, setLoadingStates] = useState<{
    [key: string]: boolean;
  }>({});

  // 各模块的引用，用于调用其方法
  const moduleRefs = useRef<{
    analyzer?: AnalyzerTabRef;
    strategist?: StrategistTabRef;
    writer?: WriterTabRef;
    critic?: CriticTabRef;
  }>({});

  // 状态重置处理
  const handleGithubDataChange = (newData: UserData | null) => {
    console.log(
      "📨 [Debug] DebugControlCenter收到新的GitHub数据:",
      newData ? `用户 ${newData.login}` : "null"
    );
    if (newData) {
      console.log("🔄 [Debug] 正在更新githubData状态...");
      setGithubData(newData);
      console.log("✅ [Debug] githubData状态已更新，将传递给Tab组件");
      // 当GitHub数据改变时，清除所有模块结果
      setAnalyzerOutput(undefined);
      setStrategistOutput(undefined);
      setWriterOutput(undefined);
      setCriticOutput(undefined);
      console.log("🧹 [Debug] 已清除所有模块结果状态");
    }
  };

  // 设置模块loading状态
  const setModuleLoading = (module: string, loading: boolean) => {
    setLoadingStates((prev) => ({ ...prev, [module]: loading }));
  };

  // 渲染当前活动的标签页内容
  // 注意：这个函数只在 githubData 不为 null 时被调用，所以可以安全地进行类型断言
  const renderActiveTabContent = () => {
    const validGithubData = githubData!; // 类型断言：此时 githubData 已确保不为 null

    switch (activeTab) {
      case "analyzer":
        return (
          <AnalyzerTab
            ref={(ref) => {
              if (ref) moduleRefs.current.analyzer = ref;
            }}
            sessionId={sessionId}
            githubData={validGithubData}
            extendedData={extendedData}
            isLoadingExtended={isLoadingExtended}
            onResultChange={setAnalyzerOutput}
            onLoadingChange={(loading) => setModuleLoading("analyzer", loading)}
          />
        );
      case "strategist":
        return (
          <StrategistTab
            ref={(ref) => {
              if (ref) moduleRefs.current.strategist = ref;
            }}
            sessionId={sessionId}
            githubData={validGithubData}
            analyzerOutput={analyzerOutput}
            onResultChange={setStrategistOutput}
            onLoadingChange={(loading) =>
              setModuleLoading("strategist", loading)
            }
          />
        );
      case "writer":
        return (
          <WriterTab
            ref={(ref) => {
              if (ref) moduleRefs.current.writer = ref;
            }}
            sessionId={sessionId}
            githubData={validGithubData}
            analyzerOutput={analyzerOutput}
            strategistOutput={strategistOutput}
            onResultChange={setWriterOutput}
            onLoadingChange={(loading) => setModuleLoading("writer", loading)}
          />
        );
      case "critic":
        return (
          <CriticTab
            ref={(ref) => {
              if (ref) moduleRefs.current.critic = ref;
            }}
            sessionId={sessionId}
            githubData={validGithubData}
            writerOutput={writerOutput}
            strategistOutput={strategistOutput}
            onResultChange={setCriticOutput}
            onLoadingChange={(loading) => setModuleLoading("critic", loading)}
          />
        );

      default:
        return null;
    }
  };

  // 加载状态显示
  if (isLoadingGithub || !githubData) {
    return (
      <div className={`w-full max-w-7xl mx-auto pb-16`}>
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
              <RefreshCw className="h-12 w-12 animate-spin text-blue-500" />
              <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300">
                正在获取GitHub数据...
              </h2>
              <p className="text-gray-600 dark:text-gray-400 text-center">
                请稍候，系统正在获取您的GitHub信息
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`w-full max-w-7xl mx-auto pb-16`}>
      <Card>
        <CardContent className="pt-6">{renderActiveTabContent()}</CardContent>
      </Card>

      {/* 底部常驻调试日志系统 */}
      <EnhancedRealtimeFeedbackSystem
        sessionId={sessionId}
        maxLogs={200}
        autoRefresh={true}
        refreshInterval={3000}
        activeTab={activeTab}
        onActiveTabChange={setActiveTab}
        moduleRefs={moduleRefs}
        loadingStates={loadingStates}
        githubData={githubData!} // 类型断言：此时 githubData 已确保不为 null
        onGithubDataChange={handleGithubDataChange}
        analyzerOutput={analyzerOutput}
        strategistOutput={strategistOutput}
        writerOutput={writerOutput}
        criticOutput={criticOutput}
        streamingResult={streamingResult}
        onEventReceived={(event) => {
          console.log("📊 [Debug Platform] Log event received:", event);
        }}
      />
    </div>
  );
}
