"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MonacoJsonEditor } from "./MonacoJsonEditor";
import {
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Info,
  FileText,
  Zap,
} from "lucide-react";
import toast from "react-hot-toast";

import type {
  ModuleInputEditorProps,
  ValidationResult,
  InputMode,
  InputModeState,
  DataSourceInfo,
} from "@/types/debug-input-modes";
import {
  INPUT_MODE_CONFIGS,
  canSwitchToMode,
  getEffectiveValue,
  validateJSON,
  formatJSON,
  deepEqual,
} from "@/types/debug-input-modes";

/**
 * 通用模块输入编辑器组件
 *
 * 支持三种输入模式：upstream、manual、hybrid
 * 提供完整的数据验证、格式化和模板支持
 */
export function ModuleInputEditor<T>({
  label,
  value,
  onValueChange,
  inputMode,
  onInputModeChange,
  defaultTemplate,
  upstreamValue,
  validation,
  placeholder = "输入JSON数据...",
  className = "",
  height = 300,
  readOnly = false,
  onValidationChange,
  onModeSwitch,
  onDataSourceChange,
}: ModuleInputEditorProps<T>) {
  // 内部状态管理
  const [manualValue, setManualValue] = useState<T | null>(null);
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: [],
  });
  const [isValidating, setIsValidating] = useState(false);
  const [lastModified, setLastModified] = useState<number>(Date.now());

  // Refs for tracking
  const initialValueRef = useRef<T | null>(null);
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 计算输入模式状态
  const inputModeState: InputModeState = {
    mode: inputMode,
    hasUpstreamData: upstreamValue != null,
    hasManualData: manualValue != null,
    canSwitch: true,
  };

  // 获取当前有效值
  const effectiveValue = getEffectiveValue(
    inputMode,
    upstreamValue ?? null,
    manualValue,
    defaultTemplate
  );

  // 获取显示用的JSON字符串
  const getDisplayValue = (): string => {
    const displayValue = effectiveValue || defaultTemplate;
    return formatJSON(displayValue);
  };

  // 数据验证函数
  const validateValue = useCallback(
    (newValue: T | null) => {
      setIsValidating(true);

      // 清除之前的验证超时
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }

      // 延迟验证，避免频繁验证
      validationTimeoutRef.current = setTimeout(() => {
        let result: ValidationResult = {
          isValid: true,
          errors: [],
          warnings: [],
        };

        try {
          // JSON格式验证
          const jsonStr = formatJSON(newValue);
          const jsonValidation = validateJSON(jsonStr);
          result = { ...jsonValidation };

          // 自定义业务验证
          if (validation && newValue) {
            const customValidation = validation(newValue);
            result.errors.push(...customValidation.errors);
            result.warnings.push(...customValidation.warnings);
            result.isValid = result.isValid && customValidation.isValid;
          }

          // 数据一致性检查
          if (inputMode === "hybrid" && upstreamValue && newValue) {
            if (deepEqual(newValue, upstreamValue)) {
              result.warnings.push(
                "混合模式下的数据与上游数据完全相同，建议使用上游模式"
              );
            }
          }
        } catch (error) {
          result.isValid = false;
          result.errors.push(
            `验证过程出错: ${
              error instanceof Error ? error.message : "未知错误"
            }`
          );
        }

        setValidationResult(result);
        setIsValidating(false);

        // 通知父组件验证结果变化
        onValidationChange?.(result);
      }, 300);
    },
    [validation, inputMode, upstreamValue, onValidationChange]
  );

  // 处理输入模式切换
  const handleModeSwitch = (newMode: InputMode) => {
    const switchValidation = canSwitchToMode(newMode, inputModeState);

    if (!switchValidation.canSwitch) {
      toast.error(switchValidation.reason || "无法切换到该模式");
      return;
    }

    const oldMode = inputMode;

    // 特殊处理：从上游模式切换到混合模式时，复制上游数据到手动编辑区
    if (oldMode === "upstream" && newMode === "hybrid" && upstreamValue) {
      setManualValue(upstreamValue);
    }

    // 通知父组件模式变化
    onModeSwitch?.(oldMode, newMode);
    onInputModeChange(newMode);

    toast.success(`已切换到${INPUT_MODE_CONFIGS[newMode].label}模式`);
  };

  // 处理手动数据变化
  const handleManualValueChange = (newJsonStr: string) => {
    try {
      const parsed = JSON.parse(newJsonStr) as T;
      setManualValue(parsed);
      setLastModified(Date.now());

      // 通知数据源变化
      onDataSourceChange?.({
        source: inputMode === "hybrid" ? "hybrid" : "manual",
        timestamp: Date.now(),
        isModified: !deepEqual(parsed, initialValueRef.current),
        originalValue: initialValueRef.current,
      });
    } catch (error) {
      // JSON解析错误时，不更新值，但触发验证显示错误
      validateValue(null);
    }
  };

  // 重置到模板数据
  const handleResetToTemplate = () => {
    setManualValue(defaultTemplate);
    setLastModified(Date.now());
    toast.success("已重置为默认模板");
  };

  // 从上游数据填充
  const handleFillFromUpstream = () => {
    if (upstreamValue) {
      setManualValue(upstreamValue);
      setLastModified(Date.now());
      toast.success("已从上游数据填充");
    } else {
      toast.error("没有可用的上游数据");
    }
  };

  // 监听有效值变化，通知父组件
  useEffect(() => {
    onValueChange(effectiveValue);
  }, [effectiveValue, onValueChange]);

  // 监听有效值变化，进行验证
  useEffect(() => {
    validateValue(effectiveValue);
  }, [effectiveValue, validateValue]);

  // 初始化时记录初始值
  useEffect(() => {
    if (!initialValueRef.current) {
      initialValueRef.current = value;
    }
  }, [value]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, []);

  // 渲染验证状态指示器
  const renderValidationIndicator = () => {
    if (isValidating) {
      return (
        <div className="flex items-center gap-2 text-blue-600">
          <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <span className="text-xs">验证中...</span>
        </div>
      );
    }

    if (!validationResult.isValid) {
      return (
        <div className="flex items-center gap-2 text-red-600">
          <AlertTriangle className="w-4 h-4" />
          <span className="text-xs">
            {validationResult.errors.length} 个错误
          </span>
        </div>
      );
    }

    if (validationResult.warnings.length > 0) {
      return (
        <div className="flex items-center gap-2 text-yellow-600">
          <Info className="w-4 h-4" />
          <span className="text-xs">
            {validationResult.warnings.length} 个警告
          </span>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-2 text-green-600">
        <CheckCircle className="w-4 h-4" />
        <span className="text-xs">验证通过</span>
      </div>
    );
  };

  // 渲染错误和警告列表
  const renderValidationMessages = () => {
    const hasMessages =
      validationResult.errors.length > 0 ||
      validationResult.warnings.length > 0;

    if (!hasMessages) return null;

    return (
      <div className="space-y-2 mt-3">
        {validationResult.errors.map((error, index) => (
          <div
            key={`error-${index}`}
            className="flex items-start gap-2 text-red-600 text-xs"
          >
            <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        ))}
        {validationResult.warnings.map((warning, index) => (
          <div
            key={`warning-${index}`}
            className="flex items-start gap-2 text-yellow-600 text-xs"
          >
            <Info className="w-3 h-3 mt-0.5 flex-shrink-0" />
            <span>{warning}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h4 className="font-medium text-sm">{label}</h4>
            <Badge variant="outline" className="text-xs">
              {INPUT_MODE_CONFIGS[inputMode].label}
            </Badge>
          </div>
          {renderValidationIndicator()}
        </div>

        {/* 输入模式切换 */}
        <Tabs
          value={inputMode}
          onValueChange={(value) => handleModeSwitch(value as InputMode)}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3">
            {(
              Object.entries(INPUT_MODE_CONFIGS) as [
                InputMode,
                (typeof INPUT_MODE_CONFIGS)[InputMode]
              ][]
            ).map(([mode, config]) => {
              const switchCheck = canSwitchToMode(mode, inputModeState);
              const Icon = config.icon;

              return (
                <TabsTrigger
                  key={mode}
                  value={mode}
                  disabled={!switchCheck.canSwitch}
                  className="text-xs"
                  title={
                    switchCheck.canSwitch ? config.tooltip : switchCheck.reason
                  }
                >
                  <Icon className="w-3 h-3 mr-1" />
                  {config.label}
                </TabsTrigger>
              );
            })}
          </TabsList>

          <TabsContent value="upstream" className="mt-4">
            <div className="space-y-3">
              <div className="text-xs text-muted-foreground">
                使用上游模块的输出数据，数据为只读状态
              </div>
              {upstreamValue ? (
                <MonacoJsonEditor
                  value={formatJSON(upstreamValue)}
                  readOnly
                  height={height}
                />
              ) : (
                <div className="flex items-center justify-center h-32 border-2 border-dashed border-gray-300 rounded-lg text-muted-foreground">
                  <div className="text-center">
                    <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">没有可用的上游数据</p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="manual" className="mt-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-xs text-muted-foreground">
                  手动输入数据，支持完全自定义
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetToTemplate}
                    className="text-xs h-7"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    重置模板
                  </Button>
                  {upstreamValue && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleFillFromUpstream}
                      className="text-xs h-7"
                    >
                      <Zap className="w-3 h-3 mr-1" />
                      填充上游
                    </Button>
                  )}
                </div>
              </div>
              <MonacoJsonEditor
                value={getDisplayValue()}
                onRawChange={handleManualValueChange}
                readOnly={readOnly}
                height={height}
              />
            </div>
          </TabsContent>

          <TabsContent value="hybrid" className="mt-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-xs text-muted-foreground">
                  基于上游数据进行二次编辑和调整
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetToTemplate}
                    className="text-xs h-7"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    重置模板
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleFillFromUpstream}
                    className="text-xs h-7"
                    disabled={!upstreamValue}
                  >
                    <Zap className="w-3 h-3 mr-1" />
                    重新填充
                  </Button>
                </div>
              </div>
              <MonacoJsonEditor
                value={getDisplayValue()}
                onRawChange={handleManualValueChange}
                readOnly={readOnly}
                height={height}
              />
              {upstreamValue && (
                <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded border">
                  💡 提示：当前数据基于上游数据，您可以直接编辑进行调整
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardHeader>

      <CardContent className="pt-0">
        {renderValidationMessages()}

        {/* 数据源信息 */}
        <div className="mt-4 pt-3 border-t text-xs text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>数据源: {INPUT_MODE_CONFIGS[inputMode].label}</span>
            {lastModified && (
              <span>
                最后修改: {new Date(lastModified).toLocaleTimeString()}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
