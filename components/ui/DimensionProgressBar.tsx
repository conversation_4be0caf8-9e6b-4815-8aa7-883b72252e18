"use client";

import React from "react";
import { cn } from "@/utils";
import {
  DIMENSION_CONFIGS,
  getAllDimensionConfigs,
  getDimensionConfig,
  type DimensionType as ImportedDimensionType,
} from "@/constants";

// 维度类型
export type DimensionType = ImportedDimensionType;

// 维度进度条 Props
export interface DimensionProgressBarProps {
  /** 维度类型 */
  dimension: DimensionType;
  /** 当前评分 (0-100) */
  score: number;
  /** 维度标签 */
  label: string;
  /** 维度描述 */
  description?: string;
  /** 是否显示数值 */
  showValue?: boolean;
  /** 是否启用动画 */
  animated?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 进度条大小 */
  size?: "sm" | "md" | "lg";
}

// 尺寸配置
const SIZE_CONFIGS = {
  sm: {
    height: "h-2",
    padding: "px-3 py-2",
    text: "text-sm",
    valueText: "text-xs",
  },
  md: {
    height: "h-3",
    padding: "px-4 py-3",
    text: "text-base",
    valueText: "text-sm",
  },
  lg: {
    height: "h-4",
    padding: "px-5 py-4",
    text: "text-lg",
    valueText: "text-base",
  },
} as const;

/**
 * 获取评分等级
 */
function getScoreGrade(score: number): string {
  if (score >= 90) return "S";
  if (score >= 80) return "A";
  if (score >= 70) return "B";
  if (score >= 60) return "C";
  return "D";
}

/**
 * 获取评分等级颜色
 */
function getGradeColor(grade: string): string {
  switch (grade) {
    case "S":
      return "text-yellow-500";
    case "A":
      return "text-green-500";
    case "B":
      return "text-blue-500";
    case "C":
      return "text-orange-500";
    default:
      return "text-gray-500";
  }
}

/**
 * 维度进度条组件
 *
 * 用于展示单个维度的详细评分，包括：
 * - 可视化进度条
 * - 评分数值和等级
 * - 维度描述说明
 */
export function DimensionProgressBar({
  dimension,
  score,
  label,
  description,
  showValue = true,
  animated = true,
  className,
  size = "md",
}: DimensionProgressBarProps) {
  const config = getDimensionConfig(dimension);
  const sizeConfig = SIZE_CONFIGS[size];
  const grade = getScoreGrade(score);
  const gradeColor = getGradeColor(grade);

  // 确保评分在有效范围内
  const clampedScore = Math.max(0, Math.min(100, score));

  return (
    <div className={cn("w-full", className)}>
      <div
        className={cn(
          "flex items-center justify-between mb-2",
          sizeConfig.padding
        )}
      >
        {/* 维度标签 */}
        <div className="flex items-center space-x-2">
          <span
            className={cn("font-medium", config.colors.text, sizeConfig.text)}
          >
            {label}
          </span>
          {showValue && (
            <span className={cn("font-bold", gradeColor, sizeConfig.valueText)}>
              {grade}
            </span>
          )}
        </div>

        {/* 评分数值 */}
        {showValue && (
          <span
            className={cn(
              "font-semibold text-gray-700 dark:text-gray-300",
              sizeConfig.valueText
            )}
          >
            {clampedScore.toFixed(1)}
          </span>
        )}
      </div>

      {/* 进度条容器 */}
      <div
        className={cn("w-full rounded-full border", sizeConfig.height)}
        style={{
          backgroundColor: config.colors.bg,
          borderColor: config.colors.border,
        }}
      >
        {/* 进度条填充 */}
        <div
          className={cn(
            "h-full rounded-full transition-all duration-1000 ease-out",
            animated ? "transform" : ""
          )}
          style={{
            backgroundColor: config.colors.light,
            width: `${clampedScore}%`,
            transitionDelay: animated ? "200ms" : "0ms",
          }}
        />
      </div>

      {/* 维度描述 */}
      {description && (
        <p
          className={cn(
            "mt-2 text-gray-600 dark:text-gray-400",
            size === "sm" ? "text-xs" : "text-sm"
          )}
        >
          {description}
        </p>
      )}
    </div>
  );
}

/**
 * 多维度进度条组合组件
 */
export interface MultiDimensionProgressBarsProps {
  /** 四维度评分数据 */
  scores: {
    commitScore: number;
    collaborationScore: number;
    influenceScore: number;
    explorationScore: number;
  };
  /** 是否显示描述 */
  showDescriptions?: boolean;
  /** 进度条大小 */
  size?: "sm" | "md" | "lg";
  /** 自定义样式类名 */
  className?: string;
}

export function MultiDimensionProgressBars({
  scores,
  showDescriptions = true,
  size = "md",
  className,
}: MultiDimensionProgressBarsProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {getAllDimensionConfigs().map((config) => (
        <DimensionProgressBar
          key={config.key}
          dimension={config.key}
          score={scores[config.scoreKey]}
          label={config.label}
          description={showDescriptions ? config.description : undefined}
          size={size}
        />
      ))}
    </div>
  );
}

export default DimensionProgressBar;
