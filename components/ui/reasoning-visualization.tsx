"use client";

import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/utils";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import {
  Brain,
  MessageSquare,
  Clock,
  TrendingUp,
  Copy,
  ChevronDown,
  ChevronUp,
} from "lucide-react";

interface ReasoningStep {
  step: number;
  content: string;
  timestamp: number;
  type: "reasoning" | "content";
}

interface ReasoningVisualizationProps {
  steps: ReasoningStep[];
  isStreaming: boolean;
  fullReasoningContent?: string;
  onCopyContent?: (content: string) => void;
}

export function ReasoningVisualization({
  steps,
  isStreaming,
  fullReasoningContent,
  onCopyContent,
}: ReasoningVisualizationProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const [expandedSteps, setExpandedSteps] = useState<Set<number>>(new Set());

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [steps, autoScroll]);

  const toggleStepExpansion = (stepIndex: number) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(stepIndex)) {
      newExpanded.delete(stepIndex);
    } else {
      newExpanded.add(stepIndex);
    }
    setExpandedSteps(newExpanded);
  };

  const reasoningSteps = steps.filter((step) => step.type === "reasoning");
  const contentSteps = steps.filter((step) => step.type === "content");

  return (
    <div className="space-y-4">
      {/* 统计信息 */}
      <ReasoningStats steps={steps} isStreaming={isStreaming} />

      {/* 推理步骤 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex gap-2 items-center">
              <Brain className="h-5 text-blue-600 w-5" />
              推理过程
            </CardTitle>
            <div className="flex gap-2 items-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoScroll(!autoScroll)}
              >
                {autoScroll ? "关闭" : "开启"}自动滚动
              </Button>
              {fullReasoningContent && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onCopyContent?.(fullReasoningContent)}
                >
                  <Copy className="h-4 mr-1 w-4" />
                  复制全部
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]" ref={scrollAreaRef}>
            <div className="space-y-3 pr-4">
              {steps.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <Brain className="mx-auto h-12 mb-4 opacity-50 w-12" />
                  <p>暂无推理过程数据</p>
                  <p className="text-sm">点击生成开始观察AI的思考过程</p>
                </div>
              ) : (
                <>
                  {steps.map((step, index) => (
                    <ReasoningStepCard
                      key={`${step.step}-${index}`}
                      step={step}
                      index={index}
                      isExpanded={expandedSteps.has(index)}
                      onToggleExpansion={() => toggleStepExpansion(index)}
                      onCopy={onCopyContent}
                    />
                  ))}

                  {isStreaming && (
                    <div className="rounded-lg bg-gray-50 border-l-4 border-l-gray-400 p-3 dark:bg-gray-950/20">
                      <div className="flex gap-2 items-center">
                        <div className="border-t-transparent rounded-full border-2 border-gray-400 h-4 animate-spin w-4" />
                        <TypewriterText text="正在深度思考中..." />
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}

interface ReasoningStepCardProps {
  step: ReasoningStep;
  index: number;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onCopy?: (content: string) => void;
}

function ReasoningStepCard({
  step,
  index,
  isExpanded,
  onToggleExpansion,
  onCopy,
}: ReasoningStepCardProps) {
  const isReasoning = step.type === "reasoning";
  const shouldTruncate = step.content.length > 200;
  const displayContent =
    shouldTruncate && !isExpanded
      ? step.content.slice(0, 200) + "..."
      : step.content;

  return (
    <div
      className={`p-3 rounded-lg border-l-4 transition-all duration-200 ${
        isReasoning
          ? "bg-blue-50 border-l-blue-500 dark:bg-blue-950/20"
          : "bg-green-50 border-l-green-500 dark:bg-green-950/20"
      }`}
    >
      <div className="flex mb-2 items-center justify-between">
        <div className="flex gap-2 items-center">
          <Badge
            variant={isReasoning ? "default" : "secondary"}
            className="text-xs"
          >
            {isReasoning ? (
              <>
                <Brain className="h-3 mr-1 w-3" />
                思考
              </>
            ) : (
              <>
                <MessageSquare className="h-3 mr-1 w-3" />
                输出
              </>
            )}
          </Badge>
          <span className="text-xs text-muted-foreground">
            步骤 {index + 1}
          </span>
        </div>
        <div className="flex gap-2 items-center">
          <span className="text-xs text-muted-foreground">
            {new Date(step.timestamp).toLocaleTimeString()}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCopy?.(step.content)}
            className="h-6 p-0 w-6"
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      </div>

      <div className="space-y-2">
        <p className="text-sm leading-relaxed whitespace-pre-wrap">
          <TypewriterText text={displayContent} speed={isReasoning ? 30 : 50} />
        </p>

        {shouldTruncate && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleExpansion}
            className="h-6 text-xs"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-3 mr-1 w-3" />
                收起
              </>
            ) : (
              <>
                <ChevronDown className="h-3 mr-1 w-3" />
                展开全部
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}

interface TypewriterTextProps {
  text: string;
  speed?: number; // 毫秒
}

function TypewriterText({ text, speed = 50 }: TypewriterTextProps) {
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, speed);

      return () => clearTimeout(timer);
    }
  }, [text, currentIndex, speed]);

  useEffect(() => {
    setDisplayText("");
    setCurrentIndex(0);
  }, [text]);

  return (
    <span>
      {displayText}
      {currentIndex < text.length && (
        <span className="animate-pulse text-blue-500">|</span>
      )}
    </span>
  );
}

interface ReasoningStatsProps {
  steps: ReasoningStep[];
  isStreaming: boolean;
}

function ReasoningStats({ steps, isStreaming }: ReasoningStatsProps) {
  const reasoningSteps = steps.filter((step) => step.type === "reasoning");
  const contentSteps = steps.filter((step) => step.type === "content");

  const startTime = steps.length > 0 ? steps[0].timestamp : Date.now();
  const endTime =
    steps.length > 0 ? steps[steps.length - 1].timestamp : Date.now();
  const duration = Math.round((endTime - startTime) / 1000);

  return (
    <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-bold text-2xl text-blue-600">
                {reasoningSteps.length}
              </p>
              <p className="text-sm text-muted-foreground">推理步骤</p>
            </div>
            <Brain className="h-8 opacity-60 text-blue-600 w-8" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-bold text-2xl text-green-600">
                {contentSteps.length}
              </p>
              <p className="text-sm text-muted-foreground">输出步骤</p>
            </div>
            <MessageSquare className="h-8 opacity-60 text-green-600 w-8" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-bold text-2xl text-purple-600">
                {steps.length}
              </p>
              <p className="text-sm text-muted-foreground">总步骤</p>
            </div>
            <TrendingUp className="h-8 opacity-60 text-purple-600 w-8" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-bold text-2xl text-orange-600">
                {isStreaming ? "..." : `${duration}s`}
              </p>
              <p className="text-sm text-muted-foreground">
                {isStreaming ? "进行中" : "总耗时"}
              </p>
            </div>
            <Clock className="h-8 opacity-60 text-orange-600 w-8" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
