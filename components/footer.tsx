"use client";
import { Gith<PERSON> } from "lucide-react";
import Qrcode from "qrcode";
import { cn } from "@/utils";
import AnimatedGradientText from "@/components/ui/animated-gradient-text";
import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import Logo from "@/public/logo.png";
import { BlurFade } from "./blur-fade";
import { useShareLinks } from "@/contexts/sharelinks-context";

export interface FooterProps {
  showQrcode?: boolean;
  showStyle?: 1 | 2;
}

export function Footer({ showQrcode = false, showStyle = 1 }: FooterProps) {
  const [qrCodeGenerated, setQrCodeGenerated] = useState(false);
  const [footerStaticLink, setFooterStaticLink] = useState("");
  const { shareLink } = useShareLinks();

  useEffect(() => {
    // This code runs only on the client side
    setFooterStaticLink(
      window.location.pathname.indexOf("/shared/") === 0
        ? window.location.href
        : ""
    );
  }, []);

  useEffect(() => {
    console.log("Footer中的shareLink:", shareLink);
  }, [shareLink]);

  const footerLinks = [
    { name: "Templates", href: "#templates" },
    { name: "About", href: "/about" },
    { name: "GitHub", href: "https://github.com/tower1229/github-card" },
    { name: "Support", href: "https://github.com/tower1229/GitHub-Card-Community/issues" },
  ];

  useEffect(() => {
    // 仅在需要显示二维码且Canvas元素存在时生成
    if (showQrcode && !qrCodeGenerated) {
      const canvasElement = document.getElementById("footer-qr-canvas");
      if (!canvasElement) {
        console.error("No canvas element found");
        return;
      }

      if (shareLink?.shareLink || footerStaticLink) {
        try {
          Qrcode.toCanvas(
            canvasElement,
            shareLink?.shareLink || footerStaticLink,
            {
              margin: 2,
              width: 100,
            }
          );
          setQrCodeGenerated(true);
        } catch (error) {
          console.error("Failed to generate QR code:", error);
        }
      }
    }
  }, [showQrcode, shareLink, qrCodeGenerated, footerStaticLink]);

  if (showQrcode) {
    // showStyle
    const showStyles = {
      1: "z-10 flex min-h-40 flex-col items-center justify-center gap-4 p-4 mt-10 relative",
      2: "z-10 flex items-center gap-4 p-4 flex-row-reverse relative min-h-[100px]",
    };

    return (
      <div className={showStyles[showStyle]}>
        <canvas id="footer-qr-canvas" className="w-[100px]"></canvas>

        <Link href="/" className={showStyle === 2 ? "flex-1" : ""}>
          <AnimatedGradientText className="bg-black/40 mx-0">
            <Github size={20} />
            <hr className="bg-gray-300 h-4 mx-2 w-px shrink-0" />{" "}
            <span
              className={cn(
                `inline animate-gradient bg-linear-to-r from-[#fa7b19] via-[#f0883e] to-[#fa7b19] bg-[length:var(--bg-size)_100%] bg-clip-text text-transparent`
              )}
            >
              Github Card
            </span>
          </AnimatedGradientText>
          {showStyle === 2 && (
            <div className="mt-2 text-sm">
              Let passion guide your code, and purpose drive your impact
            </div>
          )}
        </Link>
      </div>
    );
  }

  return (
    <footer className="border-t bg-[#161b22] border-[#30363d]">
      <div className="container mx-auto py-8 px-4">
        {/* Main Footer Content */}
        <BlurFade delay={100}>
          <div className="flex flex-col items-center text-center space-y-6">
            {/* Logo and Brand */}
            <Link href="/" className="flex gap-2 items-center">
              <Image
                src={Logo}
                alt="Logo"
                width={32}
                height={32}
                className="rounded-full object-cover"
              />
              <span className="font-bold text-xl">GitHub Card</span>
            </Link>

            {/* Description */}
            <p className="text-[#8b949e] max-w-md">
              Create Beautiful GitHub Profile Cards
            </p>

            {/* Navigation Links */}
            <div className="flex flex-wrap justify-center gap-6 text-sm">
              {footerLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  target={link.href.startsWith("http") ? "_blank" : "_self"}
                  className="text-[#8b949e] hover:text-white transition-colors"
                >
                  {link.name}
                </a>
              ))}
            </div>

          </div>
        </BlurFade>

        {/* Bottom Section */}
        <BlurFade delay={300}>
          <div className="mt-8">
            {/* Copyright - subtle */}
            <div className="flex justify-center">
              <p className="text-xs text-[#6e7681]">
                &copy; {new Date().getFullYear()} GitHub Card. All rights reserved.
              </p>
            </div>
            {/* Made with love message - prominent */}
            <div className="flex justify-center mt-4">
              <AnimatedGradientText className="bg-black/40">
                <span
                  className={cn(
                    `inline animate-gradient bg-linear-to-r from-[#fa7b19] via-[#f0883e] to-[#fa7b19] bg-[length:var(--bg-size)_100%] bg-clip-text text-transparent`
                  )}
                >
                  Made with ❤️ for the GitHub community
                </span>
              </AnimatedGradientText>
            </div>


          </div>
        </BlurFade>
      </div>
    </footer>
  );
}
