"use client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Github } from "lucide-react";
import { signIn, useSession, signOut } from "next-auth/react";
import Image from "next/image";
import { Crown, LayoutDashboard } from "lucide-react";
import { useSubscription } from "@/hooks/use-subscription";
import Link from "next/link";

interface GithubButtonProps {
  className?: string;
}

export function GithubButton({ className = "" }: GithubButtonProps) {
  const { data: session, status } = useSession();
  const isLoading = status === "loading";

  const handleSignIn = () => {
    try {
      signIn("github", { callbackUrl: `${window.location.origin}` });
    } catch (error) {
      console.error("Error during sign in:", error);
    }
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" });
  };

  // 确定是否已登录
  const isSignedIn = !!session?.user;

  const { isPro } = useSubscription();

  return (
    <div className={className}>
      {isSignedIn ? (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex items-center gap-2 px-3 py-2 h-auto bg-[#21262d] hover:bg-[#30363d] transition-all"
            >
              <div className="flex items-center gap-2">
                {session.user?.image ? (
                  <div className="rounded-full h-8 w-8 relative overflow-hidden border border-gray-600">
                    <Image
                      src={session.user.image}
                      alt={session.user?.name || "User"}
                      fill
                      className="object-cover"
                      sizes="32px"
                    />
                  </div>
                ) : (
                  <div className="rounded-full h-8 w-8 bg-gray-700 flex items-center justify-center">
                    <Github className="h-4 w-4 text-gray-300" />
                  </div>
                )}
                <span className="text-sm font-medium text-gray-200">
                  {session.user?.name || session.user?.email?.split("@")[0]}
                </span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 bg-[#161b22] border-gray-700 text-gray-200">
            <DropdownMenuLabel className="text-xs text-gray-400">
              Account
            </DropdownMenuLabel>

            <DropdownMenuItem className="flex items-center gap-2 cursor-pointer hover:bg-[#1f6feb] hover:text-white">
              {isPro ? <Crown className="h-4 w-4 text-yellow-400" /> : null}
              <span>{isPro ? "Pro Plan" : "Free Plan"}</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-700" />
            <DropdownMenuItem asChild>
              <Link
                href="/dashboard"
                className="flex items-center gap-2 cursor-pointer hover:bg-[#1f6feb] hover:text-white"
              >
                <LayoutDashboard className="h-4 w-4 text-blue-400" />
                <span>Dashboard</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-700" />
            <DropdownMenuItem
              onClick={handleSignOut}
              className="cursor-pointer text-red-400 hover:bg-red-900/50 hover:text-red-400 focus:text-red-400"
            >
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <Button
          onClick={handleSignIn}
          disabled={isLoading}
          className={`flex items-center px-4 py-2 bg-[#fa7b19] hover:bg-[#e76b0a] transition transform hover:scale-105`}
        >
          <Github className="h-4 w-4 mr-2" />
          {isLoading ? "Loading..." : "Sign in"}
        </Button>
      )}
    </div>
  );
}
