"use client";

import { SessionProvider, signOut, useSession } from "next-auth/react";
import { ReactNode, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import { useAutoTriggerExtendedData } from "@/hooks/use-extended-data";

interface AuthProviderProps {
  children: ReactNode;
}

// 扩展数据自动获取组件
function ExtendedDataAutoFetcher() {
  useAutoTriggerExtendedData();
  return null;
}

// 自定义会话监测组件
function SessionMonitor() {
  const { data: session, status } = useSession();

  useEffect(() => {

    // 当会话加载完成后，检查用户数据完整性
    if (status === "authenticated" && session) {

      // 检查用户数据是否完整
      const user = session.user;
      if (!user || !user.id || user.id === "") {
        console.warn("用户数据不完整，尝试修复...");

        // 尝试刷新会话
        const refreshSession = async () => {
          try {
            // 使用数据库策略下的会话刷新方式
            const sessionResponse = await fetch("/api/auth/session");
            const contentType = sessionResponse.headers.get("content-type");

            let sessionData: any = null;
            if (contentType && contentType.includes("application/json")) {
              sessionData = await sessionResponse.json();
            } else {
              // 如果是JWT字符串，可以用jwt-decode解析
              const jwt = await sessionResponse.text();
              try {
                // 需要安装 jwt-decode: npm i jwt-decode
                // import jwt_decode from "jwt-decode";
                sessionData = jwtDecode(jwt);
              } catch (e) {
                sessionData = null;
              }
            }

            if (sessionData && sessionData.user && sessionData.user.id) {
              console.log("获取到有效会话数据，刷新页面");
              window.location.reload();
            } else {
              console.warn("无法获取有效会话数据，尝试重新登录");
              // 如果仍然无效，可以选择登出
              signOut({ callbackUrl: "/" });
            }
          } catch (error) {
            console.error("刷新会话失败:", error);
          }
        };

        refreshSession();
      }
    }
  }, [session, status]);

  return null;
}

// 会话检查配置
const SESSION_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次

export function AuthProvider({ children }: AuthProviderProps) {
  return (
    <SessionProvider
      refetchInterval={SESSION_CHECK_INTERVAL / 1000} // 转换为秒
      refetchOnWindowFocus={false}
    >
      <SessionMonitor />
      <ExtendedDataAutoFetcher />
      {children}
    </SessionProvider>
  );
}
