"use client";

import { BlurFade } from "./blur-fade";
import { But<PERSON> } from "./ui/button";
import Image from "next/image";
import { useState } from "react";
import { Loader2, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { TEMPLATES, TemplateConfig } from "@/constants/templates";

interface TemplateModalProps {
  template: TemplateConfig;
  onClose: () => void;
  onCreate: (value: string) => void;
  isLoading: boolean;
}

function TemplateModal({
  template,
  onClose,
  onCreate,
  isLoading,
}: TemplateModalProps) {
  return (
    <div className="fixed inset-0 w-full z-50 flex items-end sm:items-center justify-center">
      {/* Backdrop with blur effect */}
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      />

      {/* Modal Container */}
      <div className="absolute left-0 bottom-0 bg-white w-full ">
        <div className="mx-auto w-full max-w-4xl h-[80vh] max-h-[800px] overflow-hidden flex items-center flex-col md:flex-row">
          {/* Left side - Content */}
          <div className="flex-1 p-8 flex flex-col justify-between overflow-y-auto">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-8">
                {template.name}
              </h2>
              <p className="text-gray-700 text-lg mb-12 min-h-[120px]">
                {template.description}
              </p>
            </div>

            <Button
              onClick={() => onCreate(template.value)}
              className=" bg-orange-600 hover:bg-orange-700 text-white py-6 text-lg rounded-xl shadow-lg hover:shadow-orange-500/30 transition-all"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Your Card"
              )}
            </Button>
          </div>

          {/* Right side - Image */}
          <div className="h-full w-auto">
            <Image
              src={template.image}
              alt={template.name}
              className="h-full w-auto"
              priority
            />
          </div>
        </div>
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 rounded-full p-2 bg-white/80 hover:bg-white transition-colors shadow-md cursor-pointer"
          aria-label="Close"
        >
          <X className="h-5 w-5 text-gray-700" />
        </button>
      </div>
    </div>
  );
}

export function TemplateShowcase() {
  const router = useRouter();
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [loadingTemplates, setLoadingTemplates] = useState<
    Record<string, boolean>
  >({});

  const templates = TEMPLATES;

  const handleExploreClick = (index: number) => {
    setSelectedTemplate(index);
  };

  const handleModalClose = () => {
    setSelectedTemplate(null);
  };

  const handleCreateCard = (templateValue: string) => {
    if (loadingTemplates[templateValue]) return;
    setLoadingTemplates((prev) => ({ ...prev, [templateValue]: true }));
    router.push(`/generate?template=${templateValue}`);
  };

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8 max-w-7xl mx-auto">
      {templates.map((template, index) => (
        <BlurFade key={index} delay={200 * (index + 1)}>
          <div className="bg-gray-900 rounded-2xl overflow-hidden relative group">
            <div className="relative w-full pb-[196%] bg-white/90 overflow-hidden">
              <Image
                src={template.image}
                alt={template.name}
                className="absolute inset-0 w-full top-0 h-auto transform group-hover:scale-110 transition-transform duration-900"
              />
              {/* text */}
              <div className="absolute bottom-0 sm:-bottom-[200px] left-0 right-0 p-2 md:p-4 bg-white/90 text-black group-hover:bottom-0 transition-bottom duration-400">
                <h3 className="text-xl font-semibold mb-2">{template.name}</h3>
                <div className="flex space-x-3">
                  <Button
                    className="hidden md:block"
                    variant="outline"
                    size="sm"
                    onClick={() => handleExploreClick(index)}
                  >
                    Explore
                  </Button>
                  <Button
                    className="border-orange-600 bg-orange-600 text-white hover:bg-orange-600 hover:text-white transition-all hover:opacity-70"
                    onClick={() => handleCreateCard(template.value)}
                    variant="outline"
                    size="sm"
                    disabled={loadingTemplates[template.value]}
                  >
                    {loadingTemplates[template.value] ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      "Create Your Card"
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </BlurFade>
      ))}

      {selectedTemplate !== null && (
        <TemplateModal
          template={templates[selectedTemplate]}
          onClose={handleModalClose}
          onCreate={handleCreateCard}
          isLoading={loadingTemplates[templates[selectedTemplate].value]}
        />
      )}
    </div>
  );
}
