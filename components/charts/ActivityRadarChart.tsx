"use client";

import React, { useMemo } from "react";
import {
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ResponsiveContainer,
  Tooltip,
} from "recharts";
import { cn } from "@/utils";

// 开发活跃度数据类型
export interface ActivityScores {
  stars: number;
  forks: number;
  commits: number;
  pullRequests: number;
  issues: number;
  reviews: number;
}

// 雷达图数据点类型
interface ActivityRadarDataPoint {
  dimension: string;
  score: number;
  rawValue: number;
  label: string;
  color: string;
}

// 组件 Props 类型
export interface ActivityRadarChartProps {
  /** 活跃度数据 */
  scores: ActivityScores;
  /** 图表大小 */
  size?: "sm" | "md" | "lg";
  /** 是否显示工具提示 */
  showTooltip?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 是否启用动画 */
  animated?: boolean;
  /** 主题模式 */
  theme?: "light" | "dark";
}

// 六维度配色方案 - 简洁配色
const ACTIVITY_COLORS = {
  stars: "#10B981", // 绿色 - Stars
  forks: "#3B82F6", // 蓝色 - Forks
  commits: "#F59E0B", // 橙色 - Commits
  pullRequests: "#8B5CF6", // 紫色 - PRs
  issues: "#EF4444", // 红色 - Issues
  reviews: "#06B6D4", // 青色 - Reviews
} as const;

// 维度标签映射
const ACTIVITY_LABELS = {
  stars: "Stars",
  forks: "Forks",
  commits: "Commit", // 简化标签避免显示不完整
  pullRequests: "PRs",
  issues: "Issues",
  reviews: "Reviews",
} as const;

// 基准数据配置 - 用于标准化评分
const BASELINE_CONFIG = {
  stars: {
    excellent: 1000, // 优秀基准
    good: 100, // 良好基准
    average: 10, // 平均基准
  },
  forks: {
    excellent: 200,
    good: 20,
    average: 2,
  },
  commits: {
    excellent: 2000,
    good: 500,
    average: 100,
  },
  pullRequests: {
    excellent: 200,
    good: 50,
    average: 10,
  },
  issues: {
    excellent: 100,
    good: 25,
    average: 5,
  },
  reviews: {
    excellent: 150,
    good: 30,
    average: 5,
  },
} as const;

/**
 * 标准化评分算法 - 使用对数函数平滑处理
 */
function normalizeScore(
  rawValue: number,
  dimension: keyof typeof BASELINE_CONFIG
): number {
  const config = BASELINE_CONFIG[dimension];

  if (rawValue === 0) return 0;

  // 使用对数函数进行标准化，确保分布更均匀
  const logValue = Math.log10(rawValue + 1);
  const logExcellent = Math.log10(config.excellent + 1);

  // 标准化到 0-100 范围，超过优秀基准的按100分计算
  const normalizedScore = Math.min(100, (logValue / logExcellent) * 100);

  return Math.round(normalizedScore);
}

/**
 * 格式化数值显示
 */
function formatActivityValue(value: number): string {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + "M";
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + "K";
  }
  return value.toString();
}

/**
 * 将活跃度数据转换为雷达图数据格式
 */
function transformActivityToRadarData(
  scores: ActivityScores
): ActivityRadarDataPoint[] {
  const dimensions: (keyof ActivityScores)[] = [
    "stars",
    "forks",
    "commits",
    "pullRequests",
    "issues",
    "reviews",
  ];

  return dimensions.map((dimension) => ({
    dimension,
    score: normalizeScore(scores[dimension], dimension),
    rawValue: scores[dimension],
    label: ACTIVITY_LABELS[dimension],
    color: ACTIVITY_COLORS[dimension],
  }));
}

/**
 * 获取图表尺寸配置
 */
function getSizeConfig(size: "sm" | "md" | "lg") {
  const configs = {
    sm: {
      height: 180,
      fontSize: 10,
      margin: { top: 25, right: 25, bottom: 25, left: 25 },
    },
    md: {
      height: 240,
      fontSize: 11,
      margin: { top: 30, right: 30, bottom: 30, left: 30 },
    },
    lg: {
      height: 300,
      fontSize: 12,
      margin: { top: 35, right: 35, bottom: 35, left: 35 },
    },
  };
  return configs[size];
}

/**
 * 自定义工具提示组件
 */
function CustomActivityTooltip({ active, payload, theme = "dark" }: any) {
  if (active && payload && payload.length) {
    const data = payload[0].payload;

    return (
      <div
        className={cn(
          "rounded-xl border shadow-lg p-3 backdrop-blur-sm z-50 relative",
          theme === "dark"
            ? "bg-gray-900/90 border-gray-700 text-white"
            : "bg-white/90 border-gray-200 text-gray-900"
        )}
        style={{ zIndex: 9999 }}
      >
        <p className="font-semibold text-sm mb-1">{data.label}</p>
        <div className="flex space-x-2 items-center">
          <div
            className="rounded-full h-3 w-3"
            style={{ backgroundColor: data.color }}
          />
          <span className="font-bold text-lg" style={{ color: data.color }}>
            {formatActivityValue(data.rawValue)}
          </span>
          <span className="text-xs opacity-60">({data.score}/100)</span>
        </div>
      </div>
    );
  }
  return null;
}

/**
 * 开发活跃度雷达图组件
 */
export const ActivityRadarChart = React.memo<ActivityRadarChartProps>(
  ({
    scores,
    size = "md",
    showTooltip = true,
    className,
    animated = true,
    theme = "dark",
  }) => {
    // 转换数据
    const radarData = useMemo(
      () => transformActivityToRadarData(scores),
      [scores]
    );
    const sizeConfig = useMemo(() => getSizeConfig(size), [size]);

    // 计算总体活跃度
    const totalActivity = useMemo(() => {
      const totalScore = radarData.reduce((sum, item) => sum + item.score, 0);
      return Math.round(totalScore / 6);
    }, [radarData]);

    return (
      <div className={cn("relative flex flex-col items-center", className)}>
        {/* 雷达图容器 */}
        <div className="relative">
          <ResponsiveContainer
            width={sizeConfig.height}
            height={sizeConfig.height}
          >
            <RadarChart data={radarData} margin={sizeConfig.margin}>
              {/* 网格 - 简洁样式 */}
              <PolarGrid
                stroke="rgba(255, 255, 255, 0.2)"
                strokeWidth={1}
                gridType="polygon"
              />

              {/* 角度轴 - 维度标签 */}
              <PolarAngleAxis
                dataKey="label"
                tick={{
                  fontSize: sizeConfig.fontSize,
                  fill: "rgba(255, 255, 255, 0.9)",
                  fontWeight: 500,
                }}
              />

              {/* 径向轴 - 隐藏 */}
              <PolarRadiusAxis
                angle={90}
                domain={[0, 100]}
                tick={false}
                axisLine={false}
              />

              {/* 雷达区域 - 渐变填充 */}
              <Radar
                name="Activity"
                dataKey="score"
                stroke="#10B981"
                fill="rgba(16, 185, 129, 0.2)"
                strokeWidth={2}
                dot={{
                  r: 4,
                  fill: "#10B981",
                  stroke: "#059669",
                  strokeWidth: 2,
                }}
                isAnimationActive={animated}
                animationDuration={1200}
                animationEasing="ease-out"
              />

              {/* 工具提示 */}
              {showTooltip && (
                <Tooltip
                  content={<CustomActivityTooltip theme={theme} />}
                  cursor={{
                    stroke: "rgba(255, 255, 255, 0.2)",
                    strokeWidth: 1,
                  }}
                />
              )}
            </RadarChart>
          </ResponsiveContainer>

          {/* 中心总分显示 */}
          {/* <div className="flex inset-0 absolute items-center justify-center pointer-events-none z-10">
            <div className="border rounded-full flex flex-col bg-black/30 border-green-400/30 h-8 text-center w-8 backdrop-blur-sm items-center justify-center">
              <span className="font-bold text-base text-green-400">
                {totalActivity}
              </span>
            </div>
          </div> */}
        </div>

        {/* 数值详情 - 外围显示 */}
        <div className="grid grid-cols-2 gap-2 w-full max-w-sm">
          {radarData.map((item) => (
            <div
              key={item.dimension}
              className="flex items-center justify-between px-2 py-2 rounded-lg backdrop-blur-sm border bg-black/20 border-white/10"
            >
              <div className="flex items-center space-x-2">
                <div
                  className="rounded-full w-2 h-2 flex-shrink-0"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-white/80 text-xs font-medium">
                  {item.label}
                </span>
              </div>
              <div className="text-right">
                <div className="font-bold text-white text-xs">
                  {formatActivityValue(item.rawValue)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
);

ActivityRadarChart.displayName = "ActivityRadarChart";

export default ActivityRadarChart;
