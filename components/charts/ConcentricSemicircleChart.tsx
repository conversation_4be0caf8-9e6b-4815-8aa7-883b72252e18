"use client";

import { DimensionScores } from "@/types/score";
import React from "react";
import { getAllDimensionConfigs } from "@/constants";

// 同心半圆图表组件的Props
export interface ConcentricSemicircleChartProps {
  /** 四个维度的分数数据 */
  scores: DimensionScores;
  /** 图表尺寸 */
  size?: "sm" | "md" | "lg";
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示分值标签 */
  showLabels?: boolean;
  /** 是否显示中心点 */
  showCenterDot?: boolean;
  /** 动画持续时间(ms) */
  animationDuration?: number;
  /** 主题模式 */
  theme?: "light" | "dark";
}

/**
 * 同心半圆图表组件
 * 使用 SVG 绘制四个同心半圆，每个半圆代表一个维度的分值
 */
export function ConcentricSemicircleChart({
  scores,
  size = "md",
  className = "",
  showLabels = true,
  showCenterDot = true,
  animationDuration = 1000,
  theme = "dark",
}: ConcentricSemicircleChartProps) {
  // 基础尺寸配置 - 增加描边宽度
  const sizeConfig = {
    sm: { width: 180, height: 90, strokeWidth: 14, centerDotSize: 1.5 },
    md: { width: 260, height: 130, strokeWidth: 18, centerDotSize: 2 },
    lg: { width: 340, height: 170, strokeWidth: 22, centerDotSize: 2.5 },
  };

  const config = sizeConfig[size];

  // 维度配置：从内到外 - 增强颜色对比度，减小间距
  const dimensions = getAllDimensionConfigs().map((config) => ({
    key: config.key,
    score: scores[config.scoreKey],
    label: config.label,
    color: theme === "dark" ? config.colors.dark : config.colors.light,
    shortName: config.shortName,
  }));

  return (
    <div className={`flex flex-col items-center ${className}`}>
      {/* 同心半圆图表 */}
      <div
        className="relative"
        style={{
          width: config.width,
          height: config.height,
        }}
      >
        <svg
          width={config.width}
          height={config.height * 2}
          className="absolute"
          style={{ bottom: "0", overflow: "hidden" }}
          viewBox={`0 0 ${config.width} ${config.height * 2}`}
        >
          {/* 渐变定义 */}
          <defs>
            {dimensions.map((dimension) => (
              <linearGradient
                key={dimension.key}
                id={`gradient-${dimension.key}`}
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop
                  offset="0%"
                  stopColor={dimension.color}
                  stopOpacity="0.95"
                />
                <stop
                  offset="100%"
                  stopColor={dimension.color}
                  stopOpacity="0.7"
                />
              </linearGradient>
            ))}

            {/* 发光滤镜 */}
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur stdDeviation="4" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
          </defs>

          {/* 绘制同心半圆 - 减小间距 */}
          {dimensions.map((dimension, index) => {
            const radius = 35 + index * 18; // 从内到外递增半径，减小间距
            const circumference = Math.PI * radius; // 半圆周长
            const scoreRatio = Math.max(
              0.05,
              Math.min(1, dimension.score / 100)
            ); // 确保最小可见度，最大100%
            const strokeLength = circumference * scoreRatio;

            return (
              <g key={dimension.key}>
                {/* 背景半圆 */}
                <path
                  d={`M ${config.width / 2 - radius} ${
                    config.height * 2
                  } A ${radius} ${radius} 0 0 1 ${config.width / 2 + radius} ${
                    config.height * 2
                  }`}
                  fill="none"
                  stroke={
                    theme === "dark"
                      ? "rgba(255, 255, 255, 0.08)"
                      : "rgba(0, 0, 0, 0.08)"
                  }
                  strokeWidth={config.strokeWidth}
                  strokeLinecap="round"
                />

                {/* 分值半圆 */}
                <path
                  d={`M ${config.width / 2 - radius} ${
                    config.height * 2
                  } A ${radius} ${radius} 0 0 1 ${config.width / 2 + radius} ${
                    config.height * 2
                  }`}
                  fill="none"
                  stroke={dimension.color}
                  strokeWidth={config.strokeWidth}
                  strokeLinecap="round"
                  strokeDasharray={`${strokeLength} ${circumference}`}
                  filter="url(#glow)"
                  style={{
                    transition: `stroke-dasharray ${animationDuration}ms ease-in-out`,
                    opacity: 0.95,
                  }}
                />
              </g>
            );
          })}
        </svg>

        {/* 中心标识 */}
        {showCenterDot && (
          <div
            className={`absolute rounded-full shadow-lg transition-all duration-300 ${
              theme === "dark" ? "bg-white" : "bg-gray-800"
            }`}
            style={{
              width: `${config.centerDotSize * 8}px`,
              height: `${config.centerDotSize * 8}px`,
              left: "50%",
              bottom: "0",
              transform: "translateX(-50%)",
            }}
          />
        )}
      </div>
    </div>
  );
}

export default ConcentricSemicircleChart;
