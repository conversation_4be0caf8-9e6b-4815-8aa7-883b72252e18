/* eslint-disable @next/next/no-img-element */
"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import { useBackground } from "@/contexts/background-context";
import Loading from "./loading";

export function BingImg({
  className = "",
  backgroundId,
}: {
  className?: string;
  backgroundId?: string;
}) {
  const [bgUrl, setBgUrl] = useState<string | null>(null);
  const [imageId, setImageId] = useState<string | null>(null);
  const { version, setCurrentBackgroundId } = useBackground();
  const controllerRef = useRef<AbortController | null>(null);
  const isMounted = useRef(false);

  const fetchBgUrl = useCallback(async () => {
    // Abort any ongoing request
    if (controllerRef.current) {
      controllerRef.current.abort();
    }

    const controller = new AbortController();
    controllerRef.current = controller;

    try {
      setBgUrl(null);
      const response = await fetch(
        `/api/background${backgroundId ? `?backgroundId=${backgroundId}` : ""}`,
        {
          signal: controller.signal,
          // 允许自动跟随302跳转，获得最佳性能
        }
      );

      if (!response.ok) throw new Error("Failed to fetch background");

      // 从最终URL中提取imageId（通过查询参数传递）
      const finalUrl = new URL(response.url);
      const imageId = finalUrl.searchParams.get("imageId");

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      // Only update state if component is still mounted
      if (isMounted.current) {
        setBgUrl((prevUrl) => {
          if (prevUrl) URL.revokeObjectURL(prevUrl);
          return url;
        });
        setImageId(imageId);

        // 设置当前背景ID到上下文
        if (imageId) {
          setCurrentBackgroundId(imageId);
        }
      } else {
        // If component unmounted, clean up the URL
        URL.revokeObjectURL(url);
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.name === "AbortError") return;
      console.error("Error fetching background URL:", error);
    } finally {
      if (controllerRef.current === controller) {
        controllerRef.current = null;
      }
    }
  }, [backgroundId, setCurrentBackgroundId]);

  // Initial fetch on mount and when version changes
  useEffect(() => {
    isMounted.current = true;
    fetchBgUrl();

    return () => {
      isMounted.current = false;
      if (controllerRef.current) {
        controllerRef.current.abort();
      }
      // Clean up the URL when component unmounts
      if (bgUrl) {
        URL.revokeObjectURL(bgUrl);
      }
    };
  }, [version, fetchBgUrl]);

  return bgUrl ? (
    <img
      src={bgUrl}
      className={className}
      alt="Bing daily background"
      key={bgUrl} // Force re-render when URL changes
      data-bg-id={imageId}
      id="github-card-bg-id"
    />
  ) : (
    <div className={className + " flex items-center justify-center"}>
      <Loading />
    </div>
  );
}
