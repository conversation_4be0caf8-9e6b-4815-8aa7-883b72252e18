:root {
  --color: #f38536;
}

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
}

.svg-loader {
  width: 240px;
  height: 240px;
}

.loader-path {
  stroke-dasharray: 150;
  stroke-dashoffset: 0;
  transform-origin: center;
  stroke: #f38536;
  fill: transparent;
  opacity: 0;
  animation: path-animation 4s ease-in-out infinite;
}

.loader-path:nth-child(1) {
  animation-delay: 0s;
  stroke: #f38536;
}

.loader-path:nth-child(2) {
  animation-delay: 0.5s;
  stroke: #a29bfe;
}

.loader-path:nth-child(3) {
  animation-delay: 1s;
  stroke: #74b9ff;
}

.loader-path:nth-child(4) {
  animation-delay: 1.5s;
  stroke: #81ecec;
}

@keyframes path-animation {
  0% {
    stroke-dashoffset: 150;
    opacity: 0;
    transform: scale(0.8);
  }
  20% {
    opacity: 1;
  }
  40% {
    stroke-dashoffset: 0;
    opacity: 1;
    transform: scale(1);
  }
  60% {
    stroke-dashoffset: 0;
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 0;
  }
  100% {
    stroke-dashoffset: -150;
    opacity: 0;
    transform: scale(0.8);
  }
}
