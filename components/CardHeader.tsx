import { motion } from "framer-motion";
import type { UserData } from "@/types/user-data";
import { formatDate } from "@/utils";

export const CardHeader = ({
  userData,
  style = 1,
  className,
}: {
  userData: UserData;
  style: 1 | 2;
  className?: string;
}) => {
  return (
    <div
      className={
        "flex gap-2 items-center justify-between rounded-lg p-2 md:p-3 " +
        (style === 1 ? "" : "flex-row-reverse") +
        " " +
        className
      }
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center gap-2 min-w-0 flex-1"
      >
        <a
          href={`https://github.com/${userData.login}`}
          target="_blank"
          className={
            style === 1
              ? "rounded-lg h-8 w-8 md:h-10 md:w-10 relative overflow-hidden flex-shrink-0"
              : "rounded-full h-6 w-6 md:h-8 md:w-8 relative overflow-hidden flex-shrink-0"
          }
        >
          <img
            src={userData.avatarUrl}
            alt="Profile"
            className="h-full object-cover w-full"
          />
        </a>
        <h1
          className={
            style === 1
              ? "font-semibold text-base md:text-xl truncate"
              : "text-sm md:text-base truncate"
          }
        >
          {userData.name || userData.login}
        </h1>
      </motion.div>
      <motion.span
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className={
          style === 1
            ? "font-bold text-sm md:text-lg flex-shrink-0"
            : "text-xs md:text-sm flex-shrink-0"
        }
      >
        {formatDate("YYYY-MM-DD", new Date())}
      </motion.span>
    </div>
  );
};
