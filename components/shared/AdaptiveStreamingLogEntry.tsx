/**
 * 流式日志条目组件
 *
 * 简化版本：专注于代码展示和流式输出效果
 */

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Copy, Check, Loader2 } from "lucide-react";
import type { ModuleExecutionLog } from "@/types/debug-logging";
import type { StreamEvent } from "@/hooks/useAIStream";
import { getStatusLabel, formatTime, copyToClipboard } from "./log-utils";
import {
  displayData,
  DataTypeChecker,
  type DisplayResult,
} from "@/lib/ai/core/utils/AdaptiveMetadataDisplayManager";

interface StreamingLogEntryProps {
  log: ModuleExecutionLog | StreamEvent;
  streamingContent?: string;
  isStreaming?: boolean;
}

// 类型守卫
function isModuleExecutionLog(log: any): log is ModuleExecutionLog {
  return (
    log && typeof log === "object" && "moduleType" in log && "status" in log
  );
}

// StreamEvent 适配为 ModuleExecutionLog
function adaptStreamEvent(event: StreamEvent): ModuleExecutionLog {
  return {
    id: `${event.sessionId}-${event.timestamp}`,
    sessionId: event.sessionId,
    moduleType: event.stage as any,
    moduleName: event.stage,
    level: "info" as const,
    status:
      event.type === "error"
        ? "failed"
        : event.type === "complete"
        ? "completed"
        : "running",
    message: event.data.content || `${event.type} - ${event.stage}`,
    timestamp: event.timestamp,
    startTime: event.timestamp,
    processingTime: 0,
    context: {
      stage: event.stage,
      eventType: event.type,
      rawEventData: event.data,
    },
  };
}

export const AdaptiveStreamingLogEntry = ({
  log,
  streamingContent,
  isStreaming = false,
}: StreamingLogEntryProps) => {
  const [displayedContent, setDisplayedContent] = useState("");
  const [showCursor, setShowCursor] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const typewriterRef = useRef<NodeJS.Timeout>();
  const cursorRef = useRef<NodeJS.Timeout>();

  // 统一处理：将 StreamEvent 适配为 ModuleExecutionLog
  const adaptedLog = isModuleExecutionLog(log) ? log : adaptStreamEvent(log);

  // 🚀 自动检测和渲染自描述数据
  const detectAndRenderSelfDescribingData = (
    content: any
  ): DisplayResult | null => {
    try {
      // 1. 检查是否为字符串形式的JSON
      let parsedContent = content;
      if (typeof content === "string") {
        try {
          parsedContent = JSON.parse(content);
        } catch {
          return null; // 不是JSON字符串，使用默认渲染
        }
      }

      // 2. 检查是否为自描述数据
      if (DataTypeChecker.isSelfDescribing(parsedContent)) {
        console.log("🎯 [AdaptiveLogEntry] 检测到自描述数据:", parsedContent);
        const result = displayData(parsedContent);
        console.log("✨ [AdaptiveLogEntry] 渲染结果:", result);
        return result;
      }

      return null;
    } catch (error) {
      console.warn("⚠️ [AdaptiveLogEntry] 自描述数据检测失败:", error);
      return null;
    }
  };

  // 🎨 渲染自描述数据的结果 - 支持精简的DisplayType
  const renderSelfDescribingContent = (result: DisplayResult) => {
    const { type } = result;

    // 根据类型选择不同的渲染策略
    switch (type) {
      case "code":
        return renderCodeContent(result);
      case "json":
        return renderJsonContent(result);
      case "text":
      default:
        return renderTextContent(result);
    }
  };

  // 代码内容渲染器
  const renderCodeContent = (result: DisplayResult) => {
    const { title, content, styling } = result;

    return (
      <div className="relative border border-gray-600 rounded-lg overflow-hidden">
        {/* 代码头部 */}
        <div className="flex items-center justify-between bg-gray-800 text-white px-3 py-2">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{styling?.icon || "💻"}</span>
            <span className="text-sm font-medium">{title}</span>
            <span className="text-xs text-gray-400">代码</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 text-xs"
            onClick={() => handleCopy(content.formatted)}
          >
            {copySuccess ? (
              <Check className="h-3 w-3" />
            ) : (
              <Copy className="h-3 w-3" />
            )}
            复制
          </Button>
        </div>

        {/* 代码内容 */}
        <div className="bg-gray-900 p-3 overflow-x-auto">
          <pre className="text-sm font-mono text-gray-100">
            <code>{content.formatted}</code>
          </pre>
        </div>
      </div>
    );
  };

  // JSON内容渲染器
  const renderJsonContent = (result: DisplayResult) => {
    const { title, content, styling } = result;

    // 确保content.formatted是字符串
    let formattedContent = content.formatted;
    if (typeof formattedContent !== "string") {
      formattedContent = JSON.stringify(formattedContent, null, 2);
    }

    return (
      <div className="relative border border-purple-200 rounded-lg">
        {/* JSON头部 */}
        <div className="flex items-center justify-between bg-purple-50 px-3 py-2 rounded-t-lg">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{styling?.icon || "📊"}</span>
            <span className="text-sm font-medium text-purple-800">{title}</span>
            <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
              JSON
            </span>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs"
              onClick={() => handleCopy(formattedContent)}
            >
              {copySuccess ? (
                <Check className="h-3 w-3" />
              ) : (
                <Copy className="h-3 w-3" />
              )}
              复制
            </Button>
          </div>
        </div>

        {/* JSON内容 */}
        <div className="bg-white max-h-96 overflow-auto">
          <pre className="p-3 text-sm font-mono">
            <code className="text-purple-700">{formattedContent}</code>
          </pre>
        </div>
      </div>
    );
  };

  // 文本内容渲染器
  const renderTextContent = (result: DisplayResult) => {
    const { title, content, styling } = result;

    return (
      <div className="relative bg-[#1c2230] p-3 rounded-lg">
        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-700">
          <span className="text-lg">{styling?.icon || "📄"}</span>
          <span
            className="font-semibold text-sm"
            style={{ color: styling?.color || "#e5e7eb" }}
          >
            {title}
          </span>
          <Button
            variant="ghost"
            size="sm"
            className="ml-auto h-6 text-xs"
            onClick={() => handleCopy(content.formatted)}
          >
            {copySuccess ? (
              <Check className="h-3 w-3" />
            ) : (
              <Copy className="h-3 w-3" />
            )}
          </Button>
        </div>
        <div
          className="text-sm whitespace-pre-wrap"
          style={{ color: styling?.color || "#93c5fd" }}
        >
          {content.formatted}
        </div>
      </div>
    );
  };

  // 获取流式内容信息
  const streamingInfo = adaptedLog.context?.streamingContent;
  const accumulatedContent =
    streamingInfo?.accumulatedContent || streamingContent || "";
  const chunkCount = streamingInfo?.chunkCount || 0;

  // 确定是否为流式日志
  const isStreamingLog = !!accumulatedContent || isStreaming;

  // 判断流式输出是否仍在进行中
  const isLogCompleted =
    adaptedLog.status === "completed" || adaptedLog.status === "failed";
  const hasStreamingInfo = !!streamingInfo;

  let isStreamingActive = false;
  if (isLogCompleted) {
    isStreamingActive = false;
  } else if (hasStreamingInfo) {
    isStreamingActive = streamingInfo.isStreaming === true;
  } else {
    isStreamingActive = isStreaming === true;
  }

  // 打字机效果 - 修复累积显示问题，优化实时性
  useEffect(() => {
    if (!isStreamingActive || !accumulatedContent) {
      setDisplayedContent(accumulatedContent);
      return;
    }

    const targetContent = accumulatedContent;
    const currentDisplayedLength = displayedContent.length;

    // 如果目标内容比当前显示的内容短或相等，直接设置（处理重置情况）
    if (targetContent.length <= currentDisplayedLength) {
      setDisplayedContent(targetContent);
      return;
    }

    // 🚀 如果新内容很少（<5个字符），立即显示，不使用打字机效果
    if (targetContent.length - currentDisplayedLength < 5) {
      setDisplayedContent(targetContent);
      return;
    }

    // 从当前已显示的长度开始继续打字机效果
    let index = currentDisplayedLength;

    if (typewriterRef.current) {
      clearInterval(typewriterRef.current);
    }

    typewriterRef.current = setInterval(() => {
      if (index < targetContent.length) {
        setDisplayedContent(targetContent.slice(0, index + 1));
        index++;
      } else {
        if (typewriterRef.current) {
          clearInterval(typewriterRef.current);
        }
      }
    }, 15); // 🚀 加快打字机速度：15ms间隔，每秒67个字符

    return () => {
      if (typewriterRef.current) {
        clearInterval(typewriterRef.current);
      }
    };
  }, [accumulatedContent, isStreamingActive, displayedContent.length]);

  // 光标闪烁效果
  useEffect(() => {
    if (!isStreamingActive) {
      setShowCursor(false);
      return;
    }

    if (cursorRef.current) {
      clearInterval(cursorRef.current);
    }

    cursorRef.current = setInterval(() => {
      setShowCursor((prev) => !prev);
    }, 500);

    return () => {
      if (cursorRef.current) {
        clearInterval(cursorRef.current);
      }
    };
  }, [isStreamingActive]);

  // 复制处理
  const handleCopy = async (content: string) => {
    const success = await copyToClipboard(content);
    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000); // 2秒后隐藏成功提示
    }
  };

  // 判断内容是否应该以代码格式显示
  const shouldFormatAsCode = (content: any): boolean => {
    if (!content) return false;
    if (typeof content === "object" && content !== null) return true;

    const contentStr = typeof content === "string" ? content : String(content);
    try {
      const parsed = JSON.parse(contentStr);
      return typeof parsed === "object" && parsed !== null;
    } catch {
      const trimmed = contentStr.trim();
      return (
        (trimmed.startsWith("{") && trimmed.includes('"')) ||
        trimmed.includes("selectedStrategyId") ||
        trimmed.includes("keyTalkingPoints") ||
        trimmed.includes("creativeFocus")
      );
    }
  };

  // 获取时间和状态标签
  const timeStr = formatTime(adaptedLog.timestamp);
  const statusLabel = getStatusLabel(adaptedLog.status);

  // 🎯 检测消息中的自描述数据，用于增强头部显示
  const messageDisplayResult = detectAndRenderSelfDescribingData(
    adaptedLog.message
  );
  const enhancedTitle = messageDisplayResult?.title || adaptedLog.message;
  const enhancedIcon = messageDisplayResult?.styling?.icon;

  // 渲染内容
  const renderContent = () => {
    // 🎯 检测特殊的 "🔄" 值，显示加载动画
    if (displayedContent === "🔄") {
      return (
        <div className="flex items-center gap-2 text-blue-400">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-gray-300">思考中...</span>
        </div>
      );
    }

    // 🚀 优先检测自描述数据并应用渲染策略
    const selfDescribingResult =
      detectAndRenderSelfDescribingData(displayedContent);
    if (selfDescribingResult) {
      return renderSelfDescribingContent(selfDescribingResult);
    }

    const isObjectContent = shouldFormatAsCode(displayedContent);

    if (isObjectContent) {
      const formattedContent =
        typeof displayedContent === "object"
          ? JSON.stringify(displayedContent, null, 2)
          : displayedContent;

      return (
        <div className="relative">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-1 right-1 h-6 w-6 opacity-70 hover:opacity-100 z-10 bg-gray-800/90"
            onClick={() => handleCopy(formattedContent)}
            title={copySuccess ? "复制成功!" : "复制内容"}
          >
            {copySuccess ? (
              <Check className="h-3 w-3 text-green-400" />
            ) : (
              <Copy className="h-3 w-3" />
            )}
          </Button>
          <pre className="bg-[#1c2230] p-2 text-sm font-mono overflow-x-auto">
            <code className="text-blue-300 whitespace-pre-wrap">
              {formattedContent}
            </code>
          </pre>
        </div>
      );
    }

    return (
      <span>
        {displayedContent}
        {isStreamingActive && (
          <span
            className={`inline-block w-1 h-[1.1em] ml-1 transition-opacity duration-200 ${
              showCursor ? "opacity-100" : "opacity-0"
            }`}
            style={{
              verticalAlign: "text-top",
              backgroundColor: "#10b981",
            }}
          >
            &nbsp;
          </span>
        )}
      </span>
    );
  };

  return (
    <div className="font-mono text-sm border-l-2 border-gray-600 pl-3 py-2 bg-[#1a202e] rounded-r">
      {/* 日志头部信息 - 增强版 */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-gray-400">[{timeStr}]</span>
          <span
            className={`px-2 py-1 rounded text-xs font-bold ${
              adaptedLog.status === "completed"
                ? "bg-green-900 text-green-300"
                : adaptedLog.status === "failed"
                ? "bg-red-900 text-red-300"
                : adaptedLog.status === "running"
                ? "bg-blue-900 text-blue-300"
                : "bg-gray-700 text-gray-300"
            }`}
          >
            {statusLabel}
          </span>
          <span className="text-gray-300">{adaptedLog.moduleType}</span>

          {/* 🚀 显示自描述数据的图标和标题 */}
          {enhancedIcon && <span className="text-lg">{enhancedIcon}</span>}
          {messageDisplayResult && (
            <span
              className="text-sm font-medium"
              style={{
                color: messageDisplayResult.styling?.color || "#e5e7eb",
              }}
            >
              {enhancedTitle}
            </span>
          )}

          {isStreamingLog && (
            <span className="text-purple-400 text-xs">
              {isStreamingActive ? "🌊 STREAMING" : "📝 STREAMED"}
            </span>
          )}
        </div>
      </div>

      {/* 消息内容 */}
      <div className="mb-2 text-gray-300 text-sm">{adaptedLog.message}</div>

      {/* 流式内容展示 */}
      {isStreamingLog && (
        <div className="mt-3">
          <div className="text-green-400 text-xs mb-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isStreamingActive && (
                <span className="text-yellow-400">{chunkCount} chunks</span>
              )}
            </div>
          </div>

          {/* 内容展示 */}
          <div className="text-gray-300 whitespace-pre-wrap break-words overflow-wrap-anywhere">
            {renderContent()}
          </div>

          {/* 内容统计信息 */}
          {displayedContent && streamingInfo?.processingTime && (
            <div className="mt-3 text-xs text-gray-500 border-t border-gray-700 pt-2">
              <span>
                耗时: {(streamingInfo.processingTime / 1000).toFixed(2)}s
              </span>
            </div>
          )}
        </div>
      )}

      {/* 处理时间信息 */}
      {adaptedLog.processingTime && adaptedLog.processingTime > 0 ? (
        <div className="text-xs text-gray-500 mt-2">
          处理时间: {adaptedLog.processingTime}ms
        </div>
      ) : null}

      {/* 复制成功提示 */}
      {copySuccess && (
        <div className="fixed top-4 right-4 bg-green-600 text-white px-3 py-2 rounded-md shadow-lg z-50 flex items-center gap-2">
          <Check className="h-4 w-4" />
          <span>复制成功!</span>
        </div>
      )}
    </div>
  );
};
