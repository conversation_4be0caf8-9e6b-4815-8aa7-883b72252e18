/**
 * 日志数据处理相关的工具函数
 */

import type { ModuleExecutionLog } from "@/types/debug-logging";

// 获取状态标签
export const getStatusLabel = (status: string) => {
  if (!status) {
    return "UNKNOWN";
  }

  switch (status) {
    case "completed":
      return "SUCCESS";
    case "failed":
      return "ERROR";
    case "running":
      return "RUNNING";
    case "timeout":
      return "TIMEOUT";
    case "pending":
      return "PENDING";
    default:
      return status.toUpperCase();
  }
};

// 格式化时间
export const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString("zh-CN", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    fractionalSecondDigits: 3,
  });
};

// 复制到剪贴板
export const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error("Failed to copy to clipboard:", error);
    return false;
  }
};
