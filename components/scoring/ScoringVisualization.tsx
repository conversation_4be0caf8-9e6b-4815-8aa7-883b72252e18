"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DimensionScoreProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  metrics: Array<{
    name: string;
    value: number;
    weight: number;
    score: number;
    description: string;
  }>;
  totalScore: number;
  color: string;
  className?: string;
}

export const DimensionScore = ({
  title,
  description,
  icon,
  metrics,
  totalScore,
  color,
  className,
}: DimensionScoreProps) => {
  const [showFormula, setShowFormula] = useState<number | null>(null);

  return (
    <div
      className={cn(
        "relative p-6 rounded-xl border border-[#30363d] bg-[#0d1117] hover:bg-[#161b22] transition-colors",
        className
      )}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${color}`}>{icon}</div>
          <div>
            <h3 className="text-lg font-semibold text-[#c9d1d9]">{title}</h3>
            <p className="text-sm text-[#8b949e]">{description}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-[#c9d1d9]">
            {totalScore.toFixed(1)}
          </div>
          <div className="text-xs text-[#8b949e]">/ 100</div>
        </div>
      </div>

      <div className="space-y-3 mt-6">
        {metrics.map((metric, index) => (
          <div
            key={index}
            className="space-y-1 group"
            onMouseEnter={() => setShowFormula(index)}
            onMouseLeave={() => setShowFormula(null)}
          >
            <div className="flex justify-between text-sm">
              <span className="text-[#8b949e] flex items-center">
                {metric.name}
                <span className="ml-1.5 text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                  ⓘ
                </span>
              </span>
              <span className="text-[#c9d1d9] font-medium">
                {metric.score.toFixed(0)}{" "}
                <span className="text-xs text-[#8b949e]">
                  ({metric.weight}%)
                </span>
              </span>
            </div>
            <div className="h-2 bg-[#21262d] rounded-full overflow-hidden">
              <motion.div
                className={`h-full ${color}`}
                initial={{ width: 0 }}
                animate={{ width: `${metric.score}%` }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
              />
            </div>
            {showFormula === index && (
              <div className="text-xs text-[#8b949e] bg-[#161b22] p-2 rounded mt-1 border border-[#30363d]">
                <div className="font-mono">{metric.description}</div>
                <div className="text-[#58a6ff] mt-1">值: {metric.value}</div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Define types for our scoring system
export interface Metric {
  name: string;
  value: number;
  weight: number;
  score: number;
  description: string;
}

export interface DimensionData {
  title: string;
  description: string;
  icon: React.ReactNode;
  metrics: Metric[];
  color: string;
}

// Sample data for demonstration
export const sampleData: { [key: string]: DimensionData } = {
  codeArchitect: {
    title: "代码架构师",
    description: "代码产出与架构能力",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="m12 15 8-8-8-8-8 8z" />
        <path d="M4 15l8 8 8-8" />
      </svg>
    ),
    metrics: [
      {
        name: "提交总数",
        value: 85,
        weight: 35,
        score: 85,
        description: "非线性增长模型: 100 * (1 - 1 / (0.1 * commits + 1))",
      },
      {
        name: "贡献仓库数",
        value: 15,
        weight: 30,
        score: 75,
        description: "Math.min(100, repos * 5)",
      },
      {
        name: "技术影响力",
        value: 82,
        weight: 25,
        score: 82,
        description: "基于贡献仓库的 Star 数加权平均",
      },
      {
        name: "效率与质量",
        value: 85,
        weight: 10,
        score: 85,
        description: "(commits / repos) * 2 (上限100分)",
      },
    ],
    color: "bg-[#1f6feb]",
  },
  communityBuilder: {
    title: "社区建设者",
    description: "协作与沟通能力",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
        <circle cx="9" cy="7" r="4" />
        <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
        <path d="M16 3.13a4 4 0 0 1 0 7.75" />
      </svg>
    ),
    metrics: [
      {
        name: "Pull Requests",
        value: 45,
        weight: 40,
        score: 90,
        description: "100 * (1 - 1 / (0.2 * PRs + 1))",
      },
      {
        name: "代码审查",
        value: 65,
        weight: 30,
        score: 80,
        description: "Math.min(100, (reviews / PRs) * 200)",
      },
      {
        name: "Issue 参与",
        value: 120,
        weight: 20,
        score: 60,
        description: "Math.min(100, issues * 0.5)",
      },
      {
        name: "协作效率",
        value: 85,
        weight: 10,
        score: 85,
        description: "基于 PR 合并率和响应时间",
      },
    ],
    color: "bg-[#8957e5]",
  },
  openSourcePioneer: {
    title: "开源先锋",
    description: "项目影响力与领导力",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
      </svg>
    ),
    metrics: [
      {
        name: "Stars 数量",
        value: 250,
        weight: 40,
        score: 71,
        description: "100 * (1 - 1 / (0.01 * stars + 1))",
      },
      {
        name: "关注者",
        value: 120,
        weight: 30,
        score: 80,
        description: "100 * (1 - 1 / (0.02 * followers + 1))",
      },
      {
        name: "Forks 数量",
        value: 80,
        weight: 20,
        score: 80,
        description: "Math.min(100, forks * 0.2)",
      },
      {
        name: "影响力质量",
        value: 75,
        weight: 10,
        score: 75,
        description: "基于 Star/Forks 比例和项目活跃度",
      },
    ],
    color: "bg-[#1a7f37]",
  },
  innovationExplorer: {
    title: "创新探索者",
    description: "技术广度与学习能力",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M3 3h7v7H3z" />
        <path d="M14 3h7v7h-7z" />
        <path d="M14 14h7v7h-7z" />
        <path d="M3 14h7v7H3z" />
      </svg>
    ),
    metrics: [
      {
        name: "语言多样性",
        value: 8,
        weight: 30,
        score: 80,
        description: "Math.min(100, uniqueLanguages * 10)",
      },
      {
        name: "项目数量",
        value: 25,
        weight: 25,
        score: 85,
        description: "Math.min(100, publicRepos * 0.5)",
      },
      {
        name: "技术广度",
        value: 75,
        weight: 25,
        score: 75,
        description: "基于语言分布的香农熵计算",
      },
      {
        name: "学习活跃度",
        value: 90,
        weight: 20,
        score: 90,
        description: "基于新项目创建频率",
      },
    ],
    color: "bg-[#d29922]",
  },
};

// 五维度配置
const SCORE_DIMENSIONS = [
  {
    id: "creativity",
    name: "代码创造力",
    icon: "💻",
    color: "from-orange-500 to-orange-600",
    metrics: [
      { name: "Commits 提交数", weight: 0.22, baseValue: 50 },
      { name: "Contributed Repos 贡献仓库", weight: 7, baseValue: 5 },
      { name: "Language Stats 语言多样性", weight: 4, baseValue: 3 },
      { name: "Public Repos 公开项目", weight: 6, baseValue: 8 },
    ],
  },
  {
    id: "collaboration",
    name: "协作交流",
    icon: "🤝",
    color: "from-blue-500 to-blue-600",
    metrics: [
      { name: "Pull Requests 代码贡献", weight: 0.14, baseValue: 5 },
      { name: "Reviews 代码审查", weight: 0.05, baseValue: 5 },
      { name: "Issues 问题反馈", weight: 0.05, baseValue: 5 },
    ],
  },
  {
    id: "influence",
    name: "影响力",
    icon: "🌟",
    color: "from-green-500 to-green-600",
    metrics: [
      { name: "Stars 项目认可度", weight: 0.28, baseValue: 1 },
      { name: "Total Forks 项目被复用度", weight: 3, baseValue: 1 },
      { name: "Followers 社区影响力", weight: 0.13, baseValue: 5 },
    ],
  },
  {
    id: "experience",
    name: "经验成长",
    icon: "📅",
    color: "from-purple-500 to-purple-600",
    metrics: [{ name: "Account Age 账号年龄", weight: 7, baseValue: 2 }],
  },
  {
    id: "exploration",
    name: "学习探索",
    icon: "🎓",
    color: "from-indigo-500 to-indigo-600",
    metrics: [{ name: "Following 学习态度", weight: 4, baseValue: 25 }],
  },
];

// 对数增长模型可视化数据
const generateCurveData = (baseValue: number) => {
  const points = [];
  const maxMultiplier = 20;

  for (let i = 0; i <= 100; i += 5) {
    const value = (baseValue * i) / 10; // 从0到baseValue*10
    const score =
      value <= 0
        ? 0
        : (100 * Math.log(1 + value / baseValue)) / Math.log(1 + maxMultiplier);
    points.push({ x: i, y: Math.min(100, Math.max(0, score)) });
  }
  return points;
};

interface ScoringVisualizationProps {
  className?: string;
}

export default function ScoringVisualization({
  className = "",
}: ScoringVisualizationProps) {
  const [selectedDimension, setSelectedDimension] = useState(
    SCORE_DIMENSIONS[0]
  );
  const [hoveredMetric, setHoveredMetric] = useState<string | null>(null);

  const curveData = generateCurveData(
    selectedDimension.metrics[0]?.baseValue || 1
  );

  // 计算总权重
  const totalWeight = SCORE_DIMENSIONS.reduce(
    (sum, dim) =>
      sum +
      dim.metrics.reduce((metricSum, metric) => metricSum + metric.weight, 0),
    0
  );

  return (
    <div className={`space-y-8 ${className}`}>
      {/* 算法概述 */}
      <Card className="bg-gradient-to-br from-gray-900/80 to-gray-800/60 border-gray-700/50">
        <CardHeader>
          <CardTitle className="text-center text-2xl bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 text-transparent">
            综合评分算法 (V4.3)
          </CardTitle>
          <p className="text-center text-gray-400 mt-2">
            基于对数增长模型的五维度加权评分系统，全面评估开发者的综合实力
          </p>
        </CardHeader>
        <CardContent>
          {/* 核心特性展示 */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-3 mb-8">
            <div className="text-center p-4 rounded-lg bg-gradient-to-br from-blue-600/20 to-blue-700/20 border border-blue-500/30">
              <div className="text-3xl mb-2">📈</div>
              <h3 className="font-semibold text-blue-300 mb-1">对数增长模型</h3>
              <p className="text-sm text-gray-400">
                防止数据刷量，重视贡献质量
              </p>
            </div>
            <div className="text-center p-4 rounded-lg bg-gradient-to-br from-green-600/20 to-green-700/20 border border-green-500/30">
              <div className="text-3xl mb-2">⚖️</div>
              <h3 className="font-semibold text-green-300 mb-1">
                智能权重配置
              </h3>
              <p className="text-sm text-gray-400">
                11个核心指标，科学配置权重
              </p>
            </div>
            <div className="text-center p-4 rounded-lg bg-gradient-to-br from-purple-600/20 to-purple-700/20 border border-purple-500/30">
              <div className="text-3xl mb-2">🎯</div>
              <h3 className="font-semibold text-purple-300 mb-1">五维度评估</h3>
              <p className="text-sm text-gray-400">全方位开发者能力画像</p>
            </div>
          </div>

          {/* 维度权重分布可视化 */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4 text-center">
              维度权重分布
            </h3>
            <div className="space-y-3">
              {SCORE_DIMENSIONS.map((dimension) => {
                const dimensionWeight = dimension.metrics.reduce(
                  (sum, metric) => sum + metric.weight,
                  0
                );
                const percentage = (dimensionWeight / totalWeight) * 100;

                return (
                  <div
                    key={dimension.id}
                    className="flex items-center space-x-4"
                  >
                    <div className="flex items-center space-x-2 min-w-[120px]">
                      <span className="text-xl">{dimension.icon}</span>
                      <span className="text-sm text-gray-300">
                        {dimension.name}
                      </span>
                    </div>
                    <div className="flex-1 bg-gray-700 rounded-full h-3 relative overflow-hidden">
                      <div
                        className={`h-full bg-gradient-to-r ${dimension.color} rounded-full transition-all duration-500`}
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-400 min-w-[50px] text-right">
                      {percentage.toFixed(1)}%
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 对数增长曲线可视化 */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4 text-center">
              对数增长模型可视化
            </h3>
            <div className="bg-gray-800/50 rounded-lg p-6">
              {/* 维度选择器 */}
              <div className="flex flex-wrap gap-2 mb-6 justify-center">
                {SCORE_DIMENSIONS.map((dimension) => (
                  <button
                    key={dimension.id}
                    onClick={() => setSelectedDimension(dimension)}
                    className={`px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                      selectedDimension.id === dimension.id
                        ? `bg-gradient-to-r ${dimension.color} text-white`
                        : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                    }`}
                  >
                    {dimension.icon} {dimension.name}
                  </button>
                ))}
              </div>

              {/* SVG 曲线图 */}
              <div className="relative bg-gray-900/50 rounded-lg p-4 mb-4">
                <svg viewBox="0 0 400 200" className="w-full h-48">
                  {/* 网格线 */}
                  <defs>
                    <pattern
                      id="grid"
                      width="40"
                      height="20"
                      patternUnits="userSpaceOnUse"
                    >
                      <path
                        d="M 40 0 L 0 0 0 20"
                        fill="none"
                        stroke="#374151"
                        strokeWidth="0.5"
                      />
                    </pattern>
                  </defs>
                  <rect width="400" height="200" fill="url(#grid)" />
                  {/* 坐标轴 */}
                  <line
                    x1="40"
                    y1="160"
                    x2="360"
                    y2="160"
                    stroke="#6B7280"
                    strokeWidth="2"
                  />
                  <line
                    x1="40"
                    y1="160"
                    x2="40"
                    y2="20"
                    stroke="#6B7280"
                    strokeWidth="2"
                  />
                  {/* 曲线 */}
                  <path
                    d={`M ${curveData
                      .map(
                        (point, index) =>
                          `${index === 0 ? "M" : "L"} ${40 + point.x * 3.2} ${
                            160 - point.y * 1.4
                          }`
                      )
                      .join(" ")}`}
                    fill="none"
                    stroke="url(#gradient)"
                    strokeWidth="3"
                  />
                  {/* 渐变定义 */}
                  <defs>
                    <linearGradient
                      id="gradient"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="0%" stopColor="#F59E0B" />
                      <stop offset="50%" stopColor="#10B981" />
                      <stop offset="100%" stopColor="#8B5CF6" />
                    </linearGradient>
                  </defs>
                  {/* 关键点标记 */}
                  <circle cx="104" cy="90" r="4" fill="#F59E0B" />{" "}
                  {/* 50分点 */}
                  <circle cx="200" cy="45" r="4" fill="#10B981" />{" "}
                  {/* 85分点 */}
                  {/* 标签 */}
                  <text x="50" y="175" fill="#9CA3AF" fontSize="12">
                    0
                  </text>
                  <text x="100" y="175" fill="#9CA3AF" fontSize="12">
                    基准值
                  </text>
                  <text x="195" y="175" fill="#9CA3AF" fontSize="12">
                    10×基准值
                  </text>
                  <text x="25" y="165" fill="#9CA3AF" fontSize="12">
                    0
                  </text>
                  <text x="15" y="95" fill="#9CA3AF" fontSize="12">
                    50
                  </text>
                  <text x="10" y="50" fill="#9CA3AF" fontSize="12">
                    85
                  </text>
                  <text x="5" y="25" fill="#9CA3AF" fontSize="12">
                    100
                  </text>
                </svg>
              </div>

              {/* 公式展示 */}
              <div className="bg-gray-900/60 rounded-lg p-4 text-center">
                <div className="text-yellow-400 font-semibold mb-2">
                  对数增长模型公式
                </div>
                <code className="text-green-300 text-sm">
                  score = 100 × log(1 + value/baseValue) / log(1 + 20)
                </code>
                <div className="grid gap-2 grid-cols-1 md:grid-cols-2 mt-3 text-xs text-gray-400">
                  <div>• 当 value = baseValue 时，score ≈ 50分</div>
                  <div>• 当 value = baseValue × 10 时，score ≈ 85分</div>
                </div>
              </div>
            </div>
          </div>

          {/* 详细指标展示 */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4 text-center">
              指标权重详情
            </h3>
            <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
              {SCORE_DIMENSIONS.map((dimension) => (
                <div
                  key={dimension.id}
                  className="bg-gradient-to-br from-gray-800/40 to-gray-700/30 border border-gray-600/40 rounded-lg p-4"
                >
                  <div className="flex items-center mb-3">
                    <div
                      className={`w-8 h-8 bg-gradient-to-br ${dimension.color} rounded-full flex items-center justify-center text-sm mr-3`}
                    >
                      {dimension.icon}
                    </div>
                    <h4 className="font-medium text-white">{dimension.name}</h4>
                  </div>
                  <div className="space-y-2">
                    {dimension.metrics.map((metric, index) => (
                      <div
                        key={index}
                        className={`flex justify-between items-center p-2 rounded transition-all duration-200 ${
                          hoveredMetric === `${dimension.id}-${index}`
                            ? "bg-gray-600/30"
                            : ""
                        }`}
                        onMouseEnter={() =>
                          setHoveredMetric(`${dimension.id}-${index}`)
                        }
                        onMouseLeave={() => setHoveredMetric(null)}
                      >
                        <span className="text-sm text-gray-300">
                          {metric.name}
                        </span>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            基准: {metric.baseValue}
                          </span>
                          <span className="text-sm font-medium text-yellow-300">
                            权重: {metric.weight}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 算法优势说明 */}
          <div className="mt-8 bg-gradient-to-r from-blue-600/10 to-purple-600/10 border border-blue-500/20 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 text-center">
              算法优势
            </h3>
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              <div>
                <h4 className="font-medium text-blue-300 mb-2">🛡️ 防刷机制</h4>
                <ul className="space-y-1 text-sm text-gray-400">
                  <li>• 对数增长模型自然抑制极值</li>
                  <li>• 新手阶段每个贡献价值更高</li>
                  <li>• 避免单纯数量导向的刷数据行为</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-green-300 mb-2">⚖️ 科学评估</h4>
                <ul className="space-y-1 text-sm text-gray-400">
                  <li>• 多维度全面考量开发者能力</li>
                  <li>• 基于真实工程师价值设计权重</li>
                  <li>• 兼顾代码质量和社区贡献</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
