'use client';

import { motion } from 'framer-motion';
import { DimensionScore, sampleData, DimensionData, Metric } from './ScoringVisualization';

// Calculate total score for a dimension
const calculateTotalScore = (metrics: Metric[]) => {
  const totalWeight = metrics.reduce((sum, metric) => sum + metric.weight, 0);
  const weightedSum = metrics.reduce((sum, metric) => sum + (metric.score * metric.weight), 0);
  return totalWeight > 0 ? Number((weightedSum / totalWeight).toFixed(1)) : 0;
};

interface DimensionWithScore extends Omit<DimensionData, 'metrics'> {
  totalScore: number;
  metrics: Metric[];
}

const FourDimensionsViz = () => {
  const dimensions: DimensionWithScore[] = Object.values(sampleData).map(dimension => ({
    ...dimension,
    totalScore: calculateTotalScore(dimension.metrics)
  }));

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  return (
    <div className="space-y-12">
      <motion.div 
        variants={container}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true, margin: "-100px" }}
        className="grid md:grid-cols-2 gap-6"
      >
        {dimensions.map((dimension) => (
          <motion.div key={dimension.title} variants={item}>
            <DimensionScore
              title={dimension.title}
              description={dimension.description}
              icon={dimension.icon}
              metrics={dimension.metrics}
              totalScore={dimension.totalScore}
              color={dimension.color}
            />
          </motion.div>
        ))}
      </motion.div>
      
      <div className="mt-8 p-6 bg-[#161b22] rounded-xl border border-[#30363d]">
        <h3 className="text-xl font-semibold mb-4 text-[#c9d1d9]">评分算法说明</h3>
        <ul className="space-y-3 text-sm text-[#8b949e]">
          <li>• 每个维度的总分由多个指标加权计算得出，权重反映了该指标在维度中的重要性</li>
          <li>• 评分采用非线性增长模型，随着数值增加，分数增长逐渐放缓</li>
          <li>• 各维度得分范围：0-100 分，分数越高表示在该维度的表现越优秀</li>
          <li>• 最终开发者等级由四个维度的综合表现决定</li>
        </ul>
      </div>
    </div>
  );
};

export default FourDimensionsViz;
