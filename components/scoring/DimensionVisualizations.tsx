"use client";

import { motion } from "framer-motion";
import {
  <PERSON>Chart,
  Pie,
  Cell,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from "recharts";
import { CodeIcon, UsersIcon, StarIcon, BookOpenIcon } from "lucide-react";

// 定义子维度类型
interface SubDimension {
  name: string;
  weight: number;
  description: string;
  baseline?: number;
  maxValue?: number;
  formula?: string;
  optimalRange?: string;
  qualityThreshold?: string;
  bonus?: string;
  criteria?: string;
  note?: string;
  measurement?: string;
  components?: string;
  optimalRatio?: string;
  stages?: Array<{
    name: string;
    range: string;
    growth?: string;
    tier?: string;
    level?: string;
  }>;
  tiers?: Array<{
    name: string;
    range: string;
  }>;
}

// 定义维度类型
interface DimensionData {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  borderColor: string;
  weight: number;
  subDimensions: SubDimension[];
}

// 根据开发备忘文档的准确维度配置
const DIMENSIONS_DATA: DimensionData[] = [
  {
    id: "commit",
    title: "Code Architect",
    description: "Master of code creation and repository contributions",
    icon: <CodeIcon className="h-6 w-6" />,
    color: "#ff6b35",
    bgColor: "rgba(255, 107, 53, 0.1)",
    borderColor: "rgba(255, 107, 53, 0.3)",
    weight: 25, // 四维度各占25%
    subDimensions: [
      {
        name: "代码产出质量",
        weight: 35,
        description: "基于提交数量的非线性增长模型",
        baseline: 100, // commits基准值
        maxValue: 2000,
        stages: [
          { name: "新手期", range: "0-100", growth: "高价值" },
          { name: "成长期", range: "100-500", growth: "稳定增长" },
          { name: "熟练期", range: "500-2000", growth: "渐进增长" },
          { name: "专家期", range: "2000+", growth: "趋于最大值" },
        ],
      },
      {
        name: "贡献一致性",
        weight: 30,
        description: "基于贡献仓库数评估持续贡献能力",
        baseline: 30, // 仓库数基准值
        formula: "基础分(0-25) + 一致性加成(0-5)",
      },
      {
        name: "技术影响力",
        weight: 25,
        description: "基于仓库贡献广度评估技术影响力",
        baseline: 50, // 仓库数基准值
        formula: "影响力分(0-20) + 质量加成(0-5)",
      },
      {
        name: "效率与质量平衡",
        weight: 10,
        description: "每个仓库平均提交数的效率评估",
        optimalRange: "5-50次提交/仓库",
      },
    ],
  },
  {
    id: "collaboration",
    title: "Community Builder",
    description:
      "Expert in fostering collaboration through Pull Requests, Issues, and code reviews",
    icon: <UsersIcon className="h-6 w-6" />,
    color: "#8957e5",
    bgColor: "rgba(137, 87, 229, 0.1)",
    borderColor: "rgba(137, 87, 229, 0.3)",
    weight: 25,
    subDimensions: [
      {
        name: "主动协作能力",
        weight: 40,
        description: "Pull Request主动协作评分",
        baseline: 25, // PRs基准值
        stages: [
          { name: "初学", range: "0-20", growth: "快速增长" },
          { name: "熟练", range: "20-80", growth: "稳定发展" },
          { name: "专家", range: "80+", growth: "持续性加成" },
        ],
      },
      {
        name: "代码审查专业度",
        weight: 30,
        description: "基于reviews与PR比例的质量评估",
        qualityThreshold: "reviews/PR ≥ 0.5",
      },
      {
        name: "社区参与广度",
        weight: 20,
        description: "Issue参与度评估社区贡献",
        baseline: 50, // issues基准值
        bonus: "50+ issues获得高参与度加成",
      },
      {
        name: "协作效率与质量",
        weight: 10,
        description: "协作活动多样性与规模评估",
        criteria: "全面协作者(PR+Review+Issue)获得最高分",
      },
    ],
  },
  {
    id: "influence",
    title: "Open Source Pioneer",
    description:
      "Leader in creating impactful projects that inspire and engage the community",
    icon: <StarIcon className="h-6 w-6" />,
    color: "#06ffa5",
    bgColor: "rgba(6, 255, 165, 0.1)",
    borderColor: "rgba(6, 255, 165, 0.3)",
    weight: 25,
    subDimensions: [
      {
        name: "技术作品影响力",
        weight: 40,
        description: "基于Stars数量的技术认可度",
        baseline: 20, // stars基准值
        stages: [
          { name: "初期影响", range: "0-50", tier: "起步阶段" },
          { name: "成长影响", range: "50-200", tier: "发展阶段" },
          { name: "显著影响", range: "200-1000", tier: "成熟阶段" },
          { name: "顶级影响", range: "1000+", tier: "领先阶段" },
        ],
      },
      {
        name: "社区领导力",
        weight: 30,
        description: "基于关注者数量的社区影响力",
        tiers: [
          { name: "新兴", range: "0-50" },
          { name: "稳定", range: "50-200" },
          { name: "知名", range: "200-500" },
          { name: "顶级", range: "500+" },
        ],
      },
      {
        name: "影响力增长潜力",
        weight: 20,
        description: "Stars和关注者增长潜力评估",
        components: "Stars增长分(0-12) + 关注者增长分(0-8)",
      },
      {
        name: "影响力质量与深度",
        weight: 10,
        description: "基于Star/Fork比例的影响力质量",
        optimalRatio: "3-10:1",
      },
    ],
  },
  {
    id: "exploration",
    title: "Innovation Explorer",
    description:
      "Adventurer in diverse technologies and cutting-edge development practices",
    icon: <BookOpenIcon className="h-6 w-6" />,
    color: "#4d8cff",
    bgColor: "rgba(77, 140, 255, 0.1)",
    borderColor: "rgba(77, 140, 255, 0.3)",
    weight: 25,
    subDimensions: [
      {
        name: "开发生涯深度",
        weight: 25,
        description: "基于仓库数量推断的开发经验",
        baseline: 15, // 基于仓库数量的基准值
        maxValue: 50, // 最大值用于曲线展示
        stages: [
          { name: "新手期", range: "0-5", level: "入门" },
          { name: "成长期", range: "5-15", level: "发展" },
          { name: "熟练期", range: "15-30", level: "成熟" },
          { name: "专家期", range: "30+", level: "专业" },
        ],
      },
      {
        name: "技术多样性",
        weight: 30,
        description: "基于language_stats的语言多样性和项目多样性",
        formula: "语言分(0-20) + 项目多样性分(0-8) + 探索加成(0-2)",
        note: "V4.1升级：使用详细语言统计数据",
      },
      {
        name: "学习活跃度",
        weight: 25,
        description: "基于仓库创建活跃度的学习评估",
        measurement: "仓库创建频率反映学习活跃度",
      },
      {
        name: "成长潜力",
        weight: 20,
        description: "技术广度与创造力增长潜力",
        components: "多样性增长分(0-10) + 创造力分(0-8) + 潜力加成(0-2)",
      },
    ],
  },
];

// 生成对数增长曲线数据
const generateGrowthCurveData = (
  baseline: number,
  maxValue: number = baseline * 10
) => {
  const data = [];
  for (let i = 0; i <= 100; i += 5) {
    const x = (maxValue * i) / 100;
    // 对数增长模型: score = 50 * (1 + log(value/baseline + 1) / log(11))
    const score =
      x === 0
        ? 0
        : Math.min(100, 50 * (1 + Math.log(x / baseline + 1) / Math.log(11)));
    data.push({
      value: x,
      score: Math.round(score * 100) / 100,
      stage:
        x < baseline
          ? "新手"
          : x < baseline * 5
          ? "成长"
          : x < baseline * 20
          ? "熟练"
          : "专家",
    });
  }
  return data;
};

// 环形图组件 - 展示权重分布
const WeightDonutChart = ({
  data,
  centerText,
  color,
}: {
  data: Array<{ name: string; weight: number; color: string }>;
  centerText: string;
  color: string;
}) => {
  return (
    <div className="relative">
      <ResponsiveContainer width="100%" height={200}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={50}
            outerRadius={80}
            paddingAngle={2}
            dataKey="weight"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [`${value}%`, "权重"]}
            labelFormatter={(label) => `${label}`}
            contentStyle={{
              backgroundColor: "#161b22",
              border: "1px solid #30363d",
              borderRadius: "8px",
              color: "#c9d1d9",
            }}
          />
        </PieChart>
      </ResponsiveContainer>
      <div className="flex inset-0 absolute items-center justify-center">
        <div className="text-center">
          <div className="font-medium text-sm text-gray-300">{centerText}</div>
          <div className="text-xs text-gray-500">权重分布</div>
        </div>
      </div>
    </div>
  );
};

// 增长曲线图组件
const GrowthCurveChart = ({
  data,
  color,
  title,
  baseline,
}: {
  data: Array<{ value: number; score: number; stage: string }>;
  color: string;
  title: string;
  baseline: number;
}) => {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <h4 className="font-medium text-sm mb-1 text-gray-300">{title}</h4>
        <p className="text-xs text-gray-500">
          对数增长模型 (基准值: {baseline})
        </p>
      </div>
      <ResponsiveContainer width="100%" height={200}>
        <AreaChart data={data}>
          <defs>
            <linearGradient
              id={`gradient-${color.slice(1)}`}
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              <stop offset="5%" stopColor={color} stopOpacity={0.3} />
              <stop offset="95%" stopColor={color} stopOpacity={0.05} />
            </linearGradient>
          </defs>
          <XAxis
            dataKey="value"
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 10, fill: "#8b949e" }}
            tickFormatter={(value) =>
              value >= 1000 ? `${value / 1000}k` : value.toString()
            }
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 10, fill: "#8b949e" }}
            domain={[0, 100]}
            width={30}
          />
          <Tooltip
            formatter={(value: number) => [`${value.toFixed(1)}分`, "评分"]}
            labelFormatter={(label) => `数量: ${label}`}
            contentStyle={{
              backgroundColor: "#161b22",
              border: "1px solid #30363d",
              borderRadius: "8px",
              color: "#c9d1d9",
            }}
          />
          <Area
            type="monotone"
            dataKey="score"
            stroke={color}
            strokeWidth={2}
            fill={`url(#gradient-${color.slice(1)})`}
          />
          <Line
            type="monotone"
            dataKey="score"
            stroke={color}
            strokeWidth={2}
            dot={{ fill: color, strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5, stroke: color, strokeWidth: 2 }}
          />
        </AreaChart>
      </ResponsiveContainer>

      {/* 阶段标识 */}
      <div className="flex text-xs justify-between">
        <span className="text-orange-400">新手期</span>
        <span className="text-blue-400">成长期</span>
        <span className="text-green-400">熟练期</span>
        <span className="text-purple-400">专家期</span>
      </div>
    </div>
  );
};

// 维度详细信息组件
const DimensionDetailCard = ({ dimension }: { dimension: DimensionData }) => {
  // 生成权重分布数据
  const weightData = dimension.subDimensions.map((sub, index) => ({
    name: sub.name,
    weight: sub.weight,
    color: `hsl(${(index * 90) % 360}, 70%, 60%)`,
  }));

  // 生成增长曲线数据
  // 优先使用第一个有baseline的子维度，如果没有则使用默认值
  const baselineSubDimension =
    dimension.subDimensions.find((sub) => sub.baseline) ||
    dimension.subDimensions[0];
  const defaultBaselines = {
    commit: 100, // commits基准值
    collaboration: 25, // PRs基准值
    influence: 20, // stars基准值
    exploration: 15, // repos基准值
  };
  const baseline =
    baselineSubDimension?.baseline ||
    defaultBaselines[dimension.id as keyof typeof defaultBaselines] ||
    10;
  const maxValue = baselineSubDimension?.maxValue || baseline * 5;
  const growthData = generateGrowthCurveData(baseline, maxValue);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br border rounded-xl from-gray-900/80 to-gray-800/60 border-gray-700/50 p-6 backdrop-blur-sm"
      style={{ borderColor: dimension.borderColor }}
    >
      {/* 维度标题 */}
      <div className="flex space-x-4 mb-6 items-center">
        <div
          className="rounded-lg p-3"
          style={{
            backgroundColor: dimension.bgColor,
            border: `1px solid ${dimension.borderColor}`,
          }}
        >
          {dimension.icon}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-xl text-white mb-1">
            {dimension.title}
          </h3>
          <p className="text-sm text-gray-400">{dimension.description}</p>
          <div className="flex mt-2 items-center">
            <span className="text-xs mr-2 text-gray-500">维度权重:</span>
            <span
              className="font-bold text-sm"
              style={{ color: dimension.color }}
            >
              {dimension.weight}%
            </span>
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="mb-6 grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* 权重分布环形图 */}
        <div className="rounded-lg bg-gray-800/30 p-4">
          <WeightDonutChart
            data={weightData}
            centerText={dimension.title}
            color={dimension.color}
          />
          <div className="space-y-2 mt-4">
            {weightData.map((item, index) => (
              <div
                key={index}
                className="flex text-xs items-center justify-between"
              >
                <div className="flex space-x-2 items-center">
                  <div
                    className="rounded-full h-3 w-3"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-gray-300">{item.name}</span>
                </div>
                <span className="font-medium text-gray-400">
                  {item.weight}%
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* 增长曲线图 */}
        <div className="rounded-lg bg-gray-800/30 p-4">
          <GrowthCurveChart
            data={growthData}
            color={dimension.color}
            title="对数增长模型"
            baseline={baseline}
          />
        </div>
      </div>

      {/* 子维度详情 */}
      <div className="space-y-4">
        <h4 className="font-semibold text-lg text-white mb-3">评分构成详解</h4>
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
          {dimension.subDimensions.map((sub, index) => (
            <div
              key={index}
              className="border rounded-lg bg-gray-800/40 border-gray-700/50 p-4"
            >
              <div className="flex space-x-3 items-start">
                <div
                  className="rounded-full flex font-bold flex-shrink-0 h-8 text-sm text-white w-8 items-center justify-center"
                  style={{ backgroundColor: dimension.color }}
                >
                  {sub.weight}%
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-white mb-2">
                    {sub.name}
                  </div>
                  <div className="text-xs mb-2 text-gray-400">
                    {sub.description}
                  </div>

                  {/* 显示具体配置信息 */}
                  {sub.baseline && (
                    <div className="text-xs mb-1 text-blue-400">
                      基准值: {sub.baseline}
                    </div>
                  )}
                  {sub.formula && (
                    <div className="text-xs mb-1 text-green-400">
                      计算公式: {sub.formula}
                    </div>
                  )}
                  {sub.optimalRange && (
                    <div className="text-xs mb-1 text-purple-400">
                      最佳区间: {sub.optimalRange}
                    </div>
                  )}
                  {sub.qualityThreshold && (
                    <div className="text-xs mb-1 text-yellow-400">
                      质量阈值: {sub.qualityThreshold}
                    </div>
                  )}

                  {/* 显示阶段信息 */}
                  {sub.stages && (
                    <div className="mt-2 text-xs text-gray-500">
                      <div className="font-medium mb-1">评分阶段:</div>
                      <div className="space-y-1">
                        {sub.stages.map((stage, i) => (
                          <div key={i} className="flex justify-between">
                            <span>
                              {stage.name}({stage.range})
                            </span>
                            <span className="text-gray-400">
                              {stage.growth || stage.tier || stage.level}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

const DimensionVisualization = () => {
  return (
    <div className="space-y-8">
      <div className="text-center">
        {/* 核心算法特性 */}
        <div className="mb-8 grid gap-4 grid-cols-1 md:grid-cols-3">
          <div className="bg-gradient-to-br border rounded-lg from-orange-500/20 to-orange-600/10 border-orange-500/30 p-4">
            <h3 className="font-semibold text-sm mb-2 text-orange-300">
              对数增长模型
            </h3>
            <p className="text-xs text-gray-400">
              避免线性增长导致的极值问题，确保评分合理性
            </p>
          </div>
          <div className="bg-gradient-to-br border rounded-lg from-blue-500/20 to-blue-600/10 border-blue-500/30 p-4">
            <h3 className="font-semibold text-sm mb-2 text-blue-300">
              质量指标融合
            </h3>
            <p className="text-xs text-gray-400">
              不仅看数量，更重视效率、平衡性、真实性
            </p>
          </div>
          <div className="bg-gradient-to-br border rounded-lg from-purple-500/20 to-purple-600/10 border-purple-500/30 p-4">
            <h3 className="font-semibold text-sm mb-2 text-purple-300">
              智能分层评分
            </h3>
            <p className="text-xs text-gray-400">
              新手阶段每个贡献价值更高，专家阶段趋于平稳
            </p>
          </div>
        </div>
      </div>

      {/* 维度详细卡片 */}
      <div className="space-y-8">
        {DIMENSIONS_DATA.map((dimension) => (
          <DimensionDetailCard key={dimension.id} dimension={dimension} />
        ))}
      </div>

      {/* 总结说明 */}
      <div className="bg-gradient-to-r border rounded-xl from-gray-800/50 to-gray-700/30 border-gray-600/50 p-6">
        <h3 className="font-semibold text-lg text-white mb-4">
          V4.2 算法核心特性
        </h3>
        <div className="text-sm grid gap-6 grid-cols-1 md:grid-cols-2">
          <div>
            <h4 className="font-medium mb-2 text-orange-400">
              智能分层评分模型
            </h4>
            <ul className="space-y-1 text-gray-400">
              <li>• 非线性增长：新手阶段每个贡献价值更高</li>
              <li>• 时间因素考量：考虑账号年龄、经验年限</li>
              <li>• 质量指标融合：重视效率、平衡性、真实性</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2 text-blue-400">评分精度优化</h4>
            <ul className="space-y-1 text-gray-400">
              <li>• 精确到小数点后2位，确保评分精度</li>
              <li>• 统一配置管理，确保维度标签一致性</li>
              <li>• 防刷数据机制，避免低质量贡献获得高分</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DimensionVisualization;
