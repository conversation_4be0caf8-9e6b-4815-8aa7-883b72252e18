"use client";

import React, { useState } from "react";

// 四维度配置 - V4 统一算法（均衡权重设计）
const SCORE_DIMENSIONS = [
  {
    id: "commit",
    name: "代码提交型",
    icon: "💻",
    color: "from-orange-500 to-orange-600",
    bgColor: "from-orange-600/20 to-orange-700/20",
    borderColor: "border-orange-500/30",
    textColor: "text-orange-300",
    weight: 25, // 均衡权重 25%
    description: "基于代码产出质量、贡献一致性和技术影响力的综合评估",
    metrics: [
      {
        name: "代码产出质量",
        weight: 35,
        description: "基于commit数量的非线性增长评估",
      },
      {
        name: "贡献一致性",
        weight: 30,
        description: "基于贡献仓库数评估持续贡献能力",
      },
      {
        name: "技术影响力",
        weight: 25,
        description: "基于仓库贡献广度评估技术影响力",
      },
      {
        name: "效率与质量平衡",
        weight: 10,
        description: "评估平均每个仓库的commit效率",
      },
    ],
  },
  {
    id: "collaboration",
    name: "协作交流型",
    icon: "🤝",
    color: "from-blue-500 to-blue-600",
    bgColor: "from-blue-600/20 to-blue-700/20",
    borderColor: "border-blue-500/30",
    textColor: "text-blue-300",
    weight: 25, // 均衡权重 25%
    description: "基于团队协作能力、代码审查专业度和社区参与的综合评估",
    metrics: [
      {
        name: "主动协作能力",
        weight: 40,
        description: "Pull Request主动协作评分",
      },
      {
        name: "代码审查专业度",
        weight: 30,
        description: "基于reviews与PR比例的质量评估",
      },
      {
        name: "社区参与广度",
        weight: 20,
        description: "Issue参与度和社区活跃度",
      },
      {
        name: "协作效率与质量",
        weight: 10,
        description: "协作多样性和规模评估",
      },
    ],
  },
  {
    id: "influence",
    name: "开源影响型",
    icon: "🌟",
    color: "from-green-500 to-green-600",
    bgColor: "from-green-600/20 to-green-700/20",
    borderColor: "border-green-500/30",
    textColor: "text-green-300",
    weight: 25, // 均衡权重 25%
    description: "基于技术作品影响力、社区领导力和影响力质量的综合评估",
    metrics: [
      {
        name: "技术作品影响力",
        weight: 40,
        description: "Stars技术认可度的非线性增长",
      },
      {
        name: "社区领导力",
        weight: 30,
        description: "基于followers的社区影响力评估",
      },
      {
        name: "影响力增长潜力",
        weight: 20,
        description: "Stars和followers的增长效率",
      },
      {
        name: "影响力质量与深度",
        weight: 10,
        description: "影响力集中度、真实性和可持续性",
      },
    ],
  },
  {
    id: "exploration",
    name: "学习探索型",
    icon: "🎓",
    color: "from-indigo-500 to-indigo-600",
    bgColor: "from-indigo-600/20 to-indigo-700/20",
    borderColor: "border-indigo-500/30",
    textColor: "text-indigo-300",
    weight: 25, // 均衡权重 25%
    description: "基于技术多样性、学习活跃度和成长潜力的综合评估",
    metrics: [
      {
        name: "开发生涯深度",
        weight: 25,
        description: "基于仓库数量推断的经验深度",
      },
      {
        name: "技术多样性",
        weight: 30,
        description: "语言多样性和项目多样性评估",
      },
      {
        name: "学习活跃度",
        weight: 25,
        description: "基于仓库创建活跃度的学习评估",
      },
      {
        name: "成长潜力",
        weight: 20,
        description: "技术广度和项目创造力的增长潜力",
      },
    ],
  },
];

// 对数增长模型可视化数据（使用固定基准值进行演示）
const generateCurveData = () => {
  const points = [];
  const baseValue = 50; // 使用固定的演示基准值
  const maxMultiplier = 20;

  for (let i = 0; i <= 100; i += 5) {
    const value = (baseValue * i) / 10; // 从0到baseValue*10
    const score =
      value <= 0
        ? 0
        : (100 * Math.log(1 + value / baseValue)) / Math.log(1 + maxMultiplier);
    points.push({ x: i, y: Math.min(100, Math.max(0, score)) });
  }
  return points;
};

interface OverallScoringVisualizationProps {
  className?: string;
}

export default function OverallScoringVisualization({
  className = "",
}: OverallScoringVisualizationProps) {
  const [selectedDimension, setSelectedDimension] = useState(
    SCORE_DIMENSIONS[0]
  );
  const [hoveredMetric, setHoveredMetric] = useState<string | null>(null);

  const curveData = generateCurveData();

  return (
    <div className={`space-y-8 ${className}`}>
      {/* 算法概述 */}
      <div className="text-center mb-8">
        <h2 className="font-bold mb-4 text-3xl">
          <span className="bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 text-transparent">
            V4 统一评分算法
          </span>
        </h2>
        <p className="mx-auto text-lg max-w-3xl text-gray-400">
          基于四维度均衡设计的多维度评分系统，每个维度权重25%，全面评估开发者综合实力
        </p>
        <div className="mx-auto mt-4 max-w-4xl">
          <div className="border rounded-lg bg-green-500/10 border-green-500/30 p-4">
            <p className="text-sm text-green-300">
              <span className="font-semibold">✅ 算法重构说明：</span>
              已统一为 V4 多维度评分算法，采用四维度均衡设计（每维度25%权重）。
              移除了原有权重不均衡的 V4.3
              算法，现在系统中只有一套统一的计算逻辑，
              确保各维度权重平衡，更准确地反映开发者的全面能力。
            </p>
          </div>
        </div>
      </div>

      {/* 核心特性展示 */}
      <div className="mb-12 grid gap-6 grid-cols-1 md:grid-cols-3">
        <div className="bg-gradient-to-br border rounded-xl from-blue-600/20 to-blue-700/20 border-blue-500/30 text-center p-6 transition-transform duration-300 hover:scale-105">
          <div className="mb-3 text-4xl">⚖️</div>
          <h3 className="font-semibold text-lg mb-2 text-blue-300">
            四维度均衡
          </h3>
          <p className="text-sm text-gray-400">每个维度25%权重，全面平衡评估</p>
        </div>
        <div className="bg-gradient-to-br border rounded-xl from-green-600/20 to-green-700/20 border-green-500/30 text-center p-6 transition-transform duration-300 hover:scale-105">
          <div className="mb-3 text-4xl">🔄</div>
          <h3 className="font-semibold text-lg mb-2 text-green-300">
            统一算法逻辑
          </h3>
          <p className="text-sm text-gray-400">
            系统中仅有一套计算逻辑，易于维护
          </p>
        </div>
        <div className="bg-gradient-to-br border rounded-xl from-purple-600/20 to-purple-700/20 border-purple-500/30 text-center p-6 transition-transform duration-300 hover:scale-105">
          <div className="mb-3 text-4xl">🎯</div>
          <h3 className="font-semibold text-lg mb-2 text-purple-300">
            智能分层评估
          </h3>
          <p className="text-sm text-gray-400">
            每个维度内部采用多层次评估机制
          </p>
        </div>
      </div>

      {/* 维度权重分布可视化 */}
      <div className="bg-gradient-to-br border rounded-xl from-gray-800/50 to-gray-700/30 border-gray-600/50 mb-8 p-8">
        <h3 className="font-semibold text-xl text-white text-center mb-6">
          四维度权重分布（均衡设计）
        </h3>
        <div className="space-y-4">
          {SCORE_DIMENSIONS.map((dimension) => (
            <div key={dimension.id} className="flex space-x-4 items-center">
              <div className="flex space-x-3 min-w-[140px] items-center">
                <span className="text-2xl">{dimension.icon}</span>
                <span className="font-medium text-white">{dimension.name}</span>
              </div>
              <div className="rounded-full bg-gray-700 flex-1 h-4 relative overflow-hidden">
                <div
                  className={`h-full bg-gradient-to-r ${dimension.color} rounded-full transition-all duration-700 ease-out`}
                  style={{ width: `${dimension.weight}%` }}
                />
              </div>
              <span className="font-medium text-right min-w-[60px] text-gray-300">
                {dimension.weight}%
              </span>
            </div>
          ))}
        </div>
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-400">
            V4 统一算法采用严格的四维度均衡设计，每个维度权重完全相等（25%），
            避免了权重偏向问题，确保全面客观的开发者能力评估。
          </p>
        </div>
      </div>

      {/* 对数增长曲线可视化 */}
      <div className="bg-gradient-to-br border rounded-xl from-gray-800/50 to-gray-700/30 border-gray-600/50 mb-8 p-8">
        <h3 className="font-semibold text-xl text-white text-center mb-6">
          多维度评分模型可视化
        </h3>

        {/* 维度选择器 */}
        <div className="flex flex-wrap mb-8 gap-3 justify-center">
          {SCORE_DIMENSIONS.map((dimension) => (
            <button
              key={dimension.id}
              onClick={() => setSelectedDimension(dimension)}
              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedDimension.id === dimension.id
                  ? `bg-gradient-to-r ${dimension.color} text-white shadow-lg`
                  : `bg-gray-800/50 border ${dimension.borderColor} ${dimension.textColor} hover:bg-gray-700/50`
              }`}
            >
              <span className="mr-2">{dimension.icon}</span>
              {dimension.name}
            </button>
          ))}
        </div>

        {/* 选中维度的详细说明 */}
        <div className="text-center mb-6">
          <h4
            className={`text-lg font-semibold mb-2 ${selectedDimension.textColor}`}
          >
            {selectedDimension.name} - 权重 {selectedDimension.weight}%
          </h4>
          <p className="mx-auto text-sm max-w-2xl text-gray-400">
            {selectedDimension.description}
          </p>
        </div>

        {/* SVG 曲线图 */}
        <div className="rounded-lg bg-gray-900/50 mb-6 p-6 relative">
          <svg viewBox="0 0 400 200" className="h-64 w-full">
            {/* 网格线 */}
            <defs>
              <pattern
                id="grid"
                width="40"
                height="20"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 40 0 L 0 0 0 20"
                  fill="none"
                  stroke="rgba(255,255,255,0.1)"
                  strokeWidth="0.5"
                />
              </pattern>
            </defs>
            <rect width="400" height="200" fill="url(#grid)" />

            {/* 坐标轴 */}
            <line
              x1="40"
              y1="180"
              x2="380"
              y2="180"
              stroke="rgba(255,255,255,0.3)"
              strokeWidth="1"
            />
            <line
              x1="40"
              y1="20"
              x2="40"
              y2="180"
              stroke="rgba(255,255,255,0.3)"
              strokeWidth="1"
            />

            {/* 标签 */}
            <text
              x="200"
              y="195"
              textAnchor="middle"
              className="text-xs fill-gray-400"
            >
              输入值倍数
            </text>
            <text
              x="20"
              y="100"
              textAnchor="middle"
              className="text-xs fill-gray-400"
            >
              得分
            </text>

            {/* 对数增长曲线 */}
            <path
              d={`M ${curveData
                .map((point, index) => {
                  const x = 40 + (point.x / 100) * 340;
                  const y = 180 - (point.y / 100) * 160;
                  return `${index === 0 ? "M" : "L"} ${x} ${y}`;
                })
                .join(" ")}`}
              fill="none"
              stroke={`url(#gradient-${selectedDimension.id})`}
              strokeWidth="2"
            />

            {/* 关键点标记 */}
            <circle cx="140" cy="100" r="3" fill="#fbbf24" />
            <circle cx="280" cy="68" r="3" fill="#10b981" />

            {/* 渐变定义 */}
            <defs>
              <linearGradient
                id={`gradient-${selectedDimension.id}`}
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop
                  offset="0%"
                  className={selectedDimension.textColor.replace(
                    "text-",
                    "stop-"
                  )}
                />
                <stop
                  offset="100%"
                  className={selectedDimension.textColor.replace(
                    "text-",
                    "stop-"
                  )}
                />
              </linearGradient>
            </defs>
          </svg>

          {/* 关键点说明 */}
          <div className="space-y-1 text-xs top-2 right-2 absolute">
            <div className="flex space-x-2 items-center">
              <div className="rounded-full bg-yellow-500 h-2 w-2"></div>
              <span className="text-yellow-300">基准值: ~50分</span>
            </div>
            <div className="flex space-x-2 items-center">
              <div className="rounded-full bg-green-500 h-2 w-2"></div>
              <span className="text-green-300">10倍值: ~85分</span>
            </div>
          </div>
        </div>

        {/* 算法公式展示 */}
        <div className="rounded-lg bg-gray-900/60 text-center p-6">
          <div className="font-semibold text-lg mb-3 text-yellow-400">
            V4 统一评分公式
          </div>
          <div className="rounded-lg bg-black/30 mb-4 p-4">
            <code className="font-mono text-base text-green-300">
              综合得分 = (代码提交型 + 协作交流型 + 开源影响型 + 学习探索型) ÷ 4
            </code>
          </div>
          <div className="text-sm grid text-gray-300 gap-3 grid-cols-1 md:grid-cols-2">
            <div className="flex space-x-2 items-center justify-center">
              <span className="rounded-full bg-blue-500 h-3 w-3"></span>
              <span>四维度权重完全均衡（各25%）</span>
            </div>
            <div className="flex space-x-2 items-center justify-center">
              <span className="rounded-full bg-purple-500 h-3 w-3"></span>
              <span>每个维度内部采用智能分层评估</span>
            </div>
          </div>
        </div>
      </div>

      {/* 详细指标展示 */}
      <div className="bg-gradient-to-br border rounded-xl from-gray-800/50 to-gray-700/30 border-gray-600/50 p-8">
        <h3 className="font-semibold text-xl text-white text-center mb-6">
          维度内部评估指标
        </h3>
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
          {SCORE_DIMENSIONS.map((dimension) => (
            <div
              key={dimension.id}
              className="bg-gradient-to-br border rounded-lg from-gray-900/40 to-gray-800/30 border-gray-600/40 p-6 transition-transform duration-200 hover:scale-105"
            >
              <div className="flex mb-4 items-center">
                <div
                  className={`w-10 h-10 bg-gradient-to-br ${dimension.color} rounded-full flex items-center justify-center text-lg mr-4`}
                >
                  {dimension.icon}
                </div>
                <div>
                  <h4 className="font-semibold text-white text-sm">
                    {dimension.name}
                  </h4>
                  <p className="text-xs text-gray-400">
                    权重: {dimension.weight}%
                  </p>
                </div>
              </div>
              <div className="space-y-3">
                {dimension.metrics.map((metric, index) => (
                  <div
                    key={index}
                    className="rounded-lg cursor-pointer flex bg-gray-800/30 p-3 transition-colors duration-200 justify-between items-center hover:bg-gray-700/30"
                    onMouseEnter={() =>
                      setHoveredMetric(`${dimension.id}-${index}`)
                    }
                    onMouseLeave={() => setHoveredMetric(null)}
                  >
                    <div className="flex-1">
                      <span className="font-medium text-sm text-gray-200">
                        {metric.name}
                      </span>
                      <div className="flex space-x-3 mt-1 items-center">
                        <span className="rounded bg-gray-600/30 text-xs py-1 px-2 text-gray-400">
                          权重: {metric.weight}%
                        </span>
                      </div>
                      {hoveredMetric === `${dimension.id}-${index}` && (
                        <p className="mt-2 text-xs text-gray-400">
                          {metric.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 算法优势说明 */}
      <div className="bg-gradient-to-r border rounded-xl from-blue-600/10 to-purple-600/10 border-blue-500/20 p-8">
        <h3 className="font-semibold text-xl text-white text-center mb-6">
          V4 统一算法优势
        </h3>
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
          <div className="border rounded-lg bg-blue-600/10 border-blue-500/20 p-6">
            <h4 className="flex font-semibold text-lg mb-3 text-blue-300 items-center">
              <span className="mr-2">⚖️</span>权重均衡
            </h4>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="mr-2 text-blue-400">•</span>
                四维度严格均衡，各占25%权重
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-blue-400">•</span>
                避免单一维度过度影响综合评分
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-blue-400">•</span>
                协作交流能力得到应有重视
              </li>
            </ul>
          </div>
          <div className="border rounded-lg bg-green-600/10 border-green-500/20 p-6">
            <h4 className="flex font-semibold text-lg mb-3 text-green-300 items-center">
              <span className="mr-2">🔧</span>系统统一
            </h4>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="mr-2 text-green-400">•</span>
                全系统统一算法逻辑，避免冲突
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-green-400">•</span>
                易于维护和升级的代码架构
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-green-400">•</span>
                确保评分一致性和可预测性
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
