"use client";

import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { cn } from "@/utils";

export interface MultiDimensionCardSkeletonProps {
  showViewToggle?: boolean;
  showUserStats?: boolean;
  showBestDimension?: boolean;
  className?: string;
}

export function MultiDimensionCardSkeleton({
  showViewToggle = true,
  showUserStats = true,
  showBestDimension = true,
  className,
}: MultiDimensionCardSkeletonProps) {
  // 尺寸配置
  const sizeConfig = {
    sm: {
      container: "max-w-sm",
      avatar: "w-10 h-10",
      spacing: "space-y-3",
      padding: "p-3",
    },
    md: {
      container: "max-w-md",
      avatar: "w-12 h-12",
      spacing: "space-y-4",
      padding: "p-4",
    },
    lg: {
      container: "max-w-lg",
      avatar: "w-16 h-16",
      spacing: "space-y-5",
      padding: "p-6",
    },
  };

  const config = sizeConfig["md"];

  return (
    <Card
      className={cn(
        "overflow-hidden border border-gray-200 dark:border-gray-700 rounded-lg p-6",
        config.container,
        className
      )}
    >
      <CardHeader className={cn("pb-4", config.spacing)}>
        {/* 用户基本信息骨架 */}
        <div className="flex space-x-4 items-start">
          <div className="relative">
            {/* 头像骨架 */}
            <div
              className={cn(
                "rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse",
                config.avatar
              )}
            />
            {/* 等级徽章骨架 */}
            <div className="rounded bg-gray-200 h-4 -right-1 -bottom-1 animate-pulse w-6 absolute dark:bg-gray-700" />
          </div>

          <div className="space-y-2 flex-1 min-w-0">
            <div className="flex items-center justify-between">
              {/* 用户名骨架 */}
              <div className="rounded bg-gray-200 h-5 animate-pulse w-32 dark:bg-gray-700" />

              {/* 擅长标签骨架 */}
              {showBestDimension && (
                <div className="rounded bg-gray-200 h-5 animate-pulse w-16 dark:bg-gray-700" />
              )}
            </div>

            {/* GitHub用户名骨架 */}
            <div className="rounded bg-gray-200 h-4 animate-pulse w-24 dark:bg-gray-700" />

            {/* 位置和公司信息骨架 */}
            <div className="flex space-x-3 items-center">
              <div className="rounded bg-gray-200 h-3 animate-pulse w-20 dark:bg-gray-700" />
              <div className="rounded bg-gray-200 h-3 animate-pulse w-16 dark:bg-gray-700" />
            </div>
          </div>
        </div>

        {/* 用户简介骨架 */}
        <div className="space-y-2">
          <div className="rounded bg-gray-200 h-4 w-full animate-pulse dark:bg-gray-700" />
          <div className="rounded bg-gray-200 h-4 animate-pulse w-3/4 dark:bg-gray-700" />
        </div>

        {/* 统计信息骨架 */}
        {showUserStats && (
          <div className="border-t border-gray-100 grid pt-2 gap-2 grid-cols-4 dark:border-gray-800">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="space-y-1 text-center">
                <div className="rounded mx-auto bg-gray-200 h-4 animate-pulse w-8 dark:bg-gray-700" />
                <div className="rounded mx-auto bg-gray-200 h-3 animate-pulse w-12 dark:bg-gray-700" />
              </div>
            ))}
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {showViewToggle ? (
          <div className="w-full">
            {/* Tabs 导航骨架 */}
            <div className="rounded-lg bg-gray-100 mb-4 w-full grid p-1 grid-cols-2 dark:bg-gray-800">
              <div className="rounded bg-gray-200 h-8 animate-pulse dark:bg-gray-700" />
              <div className="rounded bg-gray-200 h-8 animate-pulse dark:bg-gray-700" />
            </div>

            {/* 图表内容骨架 */}
            <div className="mt-2">
              <div className="space-y-3">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between items-center">
                      <div className="rounded bg-gray-200 h-3 animate-pulse w-16 dark:bg-gray-700" />
                      <div className="rounded bg-gray-200 h-3 animate-pulse w-8 dark:bg-gray-700" />
                    </div>
                    <div className="rounded-full bg-gray-200 h-2 w-full animate-pulse dark:bg-gray-700" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          // 无切换时的图表骨架
          <div className="mt-2">
            <div className="rounded bg-gray-200 h-32 w-full animate-pulse dark:bg-gray-700" />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default MultiDimensionCardSkeleton;
