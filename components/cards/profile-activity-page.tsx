"use client";

import { Footer } from "@/components/footer";
import { useEffect, useState } from "react";
import { ShareButton } from "@/components/share-button";
import {
  GradeSection,
  ActivityChartSection,
  LiquidGlassBlock,
} from "@/components/MultiDimension";
import {
  CompleteUserResponse,
  ApiResponse,
  ApiErrorResponse,
} from "@/types/user-data";
import Loading from "@/components/loading";
import { BingImg } from "@/components/bing-img";
import { BlurFade } from "../blur-fade";
import { CardHeader } from "@/components/CardHeader";

interface ProfileActivityPageProps {
  username: string;
  hideMenu?: boolean;
  sharedData?: CompleteUserResponse;
  onDownloadStateChange?: (downloading: boolean) => void;
  backgroundId?: string;
}

export function ProfileActivityPage({
  username,
  hideMenu = false,
  sharedData,
  onDownloadStateChange,
  backgroundId,
}: ProfileActivityPageProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [data, setData] = useState<CompleteUserResponse | null>(null);
  const [isLoading, setIsLoading] = useState(!sharedData);
  const [error, setError] = useState<string | null>(null);

  // Forward downloading state to parent component if the prop exists
  useEffect(() => {
    onDownloadStateChange?.(isDownloading);
  }, [isDownloading, onDownloadStateChange]);

  // 获取用户数据
  useEffect(() => {
    if (!sharedData && username) {
      const fetchUserData = async () => {
        try {
          setIsLoading(true);
          setError(null);

          const res = await fetch(`/api/github-data`);
          const result: ApiResponse<CompleteUserResponse> = await res.json();

          if (result.success && result.data) {
            // 直接使用新的统一API响应格式
            setData(result.data);
          } else {
            setError(
              (result as ApiErrorResponse).error || "Failed to fetch user data"
            );
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
          setError("Network error occurred");
        } finally {
          setIsLoading(false);
        }
      };

      fetchUserData();
    } else if (sharedData) {
      // 如果有共享数据，直接构建数据结构
      setData(sharedData);
    }
  }, [username, sharedData]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return <Loading />;
  }

  // 如果有错误，显示错误状态
  if (error || !data) {
    return (
      <div className="flex min-h-screen bg-[#0d1117] text-white items-center justify-center">
        <div className="text-center">
          <h2 className="font-bold mb-4 text-2xl">Failed to Load Data</h2>
          <p className="mb-4 text-gray-400">
            {error || "Unable to retrieve user data"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-lg bg-[#fa7b19] text-white py-2 px-4 hover:bg-[#e76b0a]"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // 直接使用统一数据结构
  const { userData, multiDimensionScore } = data;

  return (
    <div className="bg-linear-to-b flex flex-col min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white py-4 px-4 relative justify-center sm:py-8">
      {/* 背景图片 */}
      <BingImg
        className={
          "h-full object-cover w-full top-0 left-0 " +
          (isDownloading ? "absolute" : "fixed")
        }
        backgroundId={backgroundId}
      />

      {/* 主要内容 */}
      <div className="mx-auto w-content max-w-[100%] z-10 relative">
        {/* 导航栏 */}
        {!hideMenu && !isDownloading && (
          <div className="flex h-10 relative overflow-hidden justify-end">
            <ShareButton
              templateType="activity"
              setIsDownloading={setIsDownloading}
            />
          </div>
        )}

        {/* 卡片展示区域 */}
        <div className={"space-y-8 text-white mb-10 mt-2"}>
          <LiquidGlassBlock isDownloading={isDownloading}>
            {/* Header */}
            <CardHeader userData={userData} style={2} className={"mb-12"} />
            <GradeSection
              multiDimensionScore={multiDimensionScore}
              className="mb-12"
            />

            <ActivityChartSection
              userProfile={userData}
              isDownloading={isDownloading}
              showTitle={false}
              className="mb-2"
            />
          </LiquidGlassBlock>
        </div>

        {/* Footer */}
        <BlurFade delay={1300}>
          <Footer showQrcode showStyle={2} />
        </BlurFade>
      </div>
    </div>
  );
}
