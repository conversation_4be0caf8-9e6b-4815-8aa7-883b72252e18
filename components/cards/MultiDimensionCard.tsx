"use client";

import React from "react";
import MultiDimensionCardSkeleton from "./MultiDimensionCardSkeleton";
import { CardHeader } from "@/components/CardHeader";
import {
  GradeSection,
  StatisticsSection,
  DimensionBreakdownSection,
  ActivityChartSection,
  LiquidGlassBlock,
} from "@/components/MultiDimension";
import { UserData } from "@/types/user-data";
import { MultiDimensionScore } from "@/types/multi-dimension";

// Multi-dimension card Props
export interface MultiDimensionCardProps {
  userProfile: UserData;
  multiDimensionScore: MultiDimensionScore;
  showUserStats?: boolean;
  showDimensionBreakdown?: boolean;
  isLoading?: boolean;
  className?: string;
  isDownloading?: boolean;
  showActivityChart?: boolean;
}

/**
 * Multi-dimension card component with liquid glass effect
 */
export function MultiDimensionCard({
  userProfile,
  multiDimensionScore,
  showUserStats,
  showDimensionBreakdown,
  isLoading = false,
  className,
  isDownloading,
  showActivityChart,
}: MultiDimensionCardProps) {
  // Show skeleton if loading
  if (isLoading) {
    return (
      <MultiDimensionCardSkeleton
        showUserStats={showUserStats}
        className={className}
      />
    );
  }

  return (
    <div className={"space-y-8 text-white " + className}>
      {/* Header */}
      <CardHeader
        userData={userProfile}
        style={2}
        className={isDownloading ? "bg-black/20" : ""}
      />

      {/* Grade Section */}
      <LiquidGlassBlock className={isDownloading ? "bg-black/20" : ""}>
        <GradeSection multiDimensionScore={multiDimensionScore} />
      </LiquidGlassBlock>

      {/* Statistics Block */}
      {showUserStats && (
        <LiquidGlassBlock className={isDownloading ? "bg-black/20" : ""}>
          <StatisticsSection
            multiDimensionScore={multiDimensionScore}
            userProfile={userProfile}
            isDownloading={isDownloading}
            showTitle
          />
        </LiquidGlassBlock>
      )}

      {/* Dimension Breakdown Block */}
      {showDimensionBreakdown && (
        <LiquidGlassBlock className={isDownloading ? "bg-black/20" : ""}>
          <DimensionBreakdownSection
            multiDimensionScore={multiDimensionScore}
            isDownloading={isDownloading}
          />
        </LiquidGlassBlock>
      )}

      {/* Charts Block */}
      {showActivityChart && (
        <LiquidGlassBlock className={isDownloading ? "bg-black/20" : ""}>
          <ActivityChartSection
            userProfile={userProfile}
            isDownloading={isDownloading}
          />
        </LiquidGlassBlock>
      )}
    </div>
  );
}

export default MultiDimensionCard;
