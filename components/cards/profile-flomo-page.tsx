/* eslint-disable @next/next/no-img-element */
"use client";
import { BlurFade } from "@/components/blur-fade";
import { Footer } from "@/components/footer";
import { useEffect, useState } from "react";
import { ShareButton } from "@/components/share-button";
import { ProfileContribute } from "@/components/profile-contribute";
import { BingImg } from "@/components/bing-img";
import NumberTicker from "@/components/ui/number-ticker";
import AnimatedGradientText from "@/components/ui/animated-gradient-text";
import { cn } from "@/utils";
import { CompleteUserResponse } from "@/types/user-data";
import Loading from "@/components/loading";
import { CardHeader } from "@/components/CardHeader";
import { calculateMultiDimensionScores } from "@/lib/github/score";

interface ProfileFlomoPageProps {
  username: string;
  hideMenu?: boolean;
  sharedData?: CompleteUserResponse;
  onDownloadStateChange?: (downloading: boolean) => void;
  backgroundId?: string;
}

export function ProfileFlomoPage({
  username,
  hideMenu = false,
  sharedData,
  onDownloadStateChange,
  backgroundId,
}: ProfileFlomoPageProps) {
  const [data, setData] = useState<CompleteUserResponse | null>(null);
  const [loading, setLoading] = useState(!sharedData);

  useEffect(() => {
    // If sharedData is provided, use it directly
    if (sharedData) {
      setData(sharedData);
      setLoading(false);
      return;
    }

    if (!username) return;
  }, [sharedData]);

  const [isDownloading, setIsDownloading] = useState(false);

  // Forward downloading state to parent component if the prop exists
  useEffect(() => {
    onDownloadStateChange?.(isDownloading);
  }, [isDownloading, onDownloadStateChange]);

  if (loading) return <Loading />;
  if (!data)
    return (
      <div className="bg-linear-to-b flex min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white items-center justify-center">
        User not found
      </div>
    );

  const multiDimensionScore = calculateMultiDimensionScores({
    commits: data.userData.commits,
    contributedRepos: data.userData.contributedRepos,
    pullRequests: data.userData.pullRequests,
    reviews: data.userData.reviews,
    issues: data.userData.issues,
    totalStars: data.userData.totalStars,
    totalForks: data.userData.totalForks,
    followers: data.userData.followers,
    languageDiversity: data.userData.languageStats?.totalLanguages || 0,
    publicRepos: data.userData.publicRepos,
    following: data.userData.following,
    createdAt: data.userData.createdAt,
  });

  return (
    <div className="bg-linear-to-b flex flex-col min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white py-4 px-4 relative justify-center sm:py-8">
      <BingImg
        className="h-full object-cover w-full top-0 left-0 absolute"
        backgroundId={backgroundId}
      />

      <div
        className={`relative z-10 w-content max-w-[100%] mx-auto ${
          isDownloading ? "bg-gray-900/70" : "bg-gray-900/20"
        } backdrop-blur-lg rounded-lg p-4 pt-8`}
      >
        {/* Settings button - only shown if hideMenu is false */}
        {!hideMenu && !isDownloading && (
          <BlurFade delay={100}>
            <div className="flex h-10 relative overflow-hidden justify-end">
              {!isDownloading && (
                <ShareButton
                  templateType="flomo"
                  setIsDownloading={setIsDownloading}
                />
              )}
            </div>
          </BlurFade>
        )}
        {/* header */}
        <BlurFade delay={200}>
          <CardHeader userData={data.userData} style={1} />
        </BlurFade>

        <ProfileContribute username={username} />

        <div
          className="border-t border-b border-t-gray-300/30 border-b-gray-800/60 my-10 grid py-4 gap-4 grid-cols-2 relative
        ''] before:absolute before:top-0 before:left-0 before:right-0 before:h-[1px] before:bg-gray-800/60
        after:content-[''] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[1px] after:bg-gray-300/30"
        >
          {[
            {
              label: "Public Repos",
              value: data.userData.publicRepos,
            },
            {
              label: "Followers",
              value: data.userData.followers,
            },
            {
              label: "Total Stars",
              value: data.userData.totalStars,
            },
            {
              label: "Total Commits",
              value: data.userData.commits,
            },
            {
              label: multiDimensionScore.bestDimension,
              value: multiDimensionScore.overallGrade,
            },
          ].map((item) => (
            <div key={item.label} className="pl-2">
              {typeof item.value === "number" ? (
                <NumberTicker
                  value={item.value}
                  className="font-bold text-white text-2xl"
                />
              ) : (
                <AnimatedGradientText className="bg-black h-10 mb-2 w-10 float-left">
                  <span
                    className={cn(
                      ` animate-gradient text-xl font-bold bg-linear-to-r from-[#ffaa40] via-[#9c40ff] to-[#ffaa40] bg-[length:var(--bg-size)_100%] bg-clip-text text-transparent`
                    )}
                  >
                    {item.value}
                  </span>
                </AnimatedGradientText>
              )}
              <div className="font-normal flex-1 text-sm text-left text-gray-300 clear-both">
                {item.label}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        {
          <BlurFade delay={1300}>
            <Footer showQrcode showStyle={2} />
          </BlurFade>
        }
      </div>
    </div>
  );
}
