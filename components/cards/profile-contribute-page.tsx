"use client";
import { BlurFade } from "@/components/blur-fade";
import { Footer } from "@/components/footer";
import { useEffect, useState } from "react";
import { ShareButton } from "@/components/share-button";
import { ProfileContribute } from "@/components/profile-contribute";
import { ProfileTotal } from "@/components/profile-total";
import { BingImg } from "@/components/bing-img";
import {
  CompleteUserResponse,
  ApiResponse,
  ApiErrorResponse,
} from "@/types/user-data";
import Loading from "@/components/loading";

interface ProfileContributePageProps {
  username: string;
  hideMenu?: boolean;
  sharedData?: CompleteUserResponse;
  onDownloadStateChange?: (downloading: boolean) => void;
  backgroundId?: string;
}

export function ProfileContributePage({
  username,
  hideMenu = false,
  sharedData,
  onDownloadStateChange,
  backgroundId,
}: ProfileContributePageProps) {
  const [data, setData] = useState<CompleteUserResponse | null>(null);
  const [loading, setLoading] = useState(!sharedData);

  // Use an AbortController to handle cancelling fetch requests when component unmounts
  useEffect(() => {
    // If sharedData is provided, use it directly
    if (sharedData) {
      setData(sharedData);
      setLoading(false);
      return;
    }

    if (!username) return;

    const fetchUserData = async () => {
      try {
        setLoading(true);
        const res = await fetch(`/api/github-data`);
        const result: ApiResponse<CompleteUserResponse> = await res.json();

        if (result.success && result.data && result.data.userData) {
          setData(result.data);
        } else {
          console.error("Error in API response:", result);
        }
      } catch (error: unknown) {
        console.error("Error fetching user data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [username, sharedData]);

  const [isDownloading, setIsDownloading] = useState(false);

  // Forward downloading state to parent component if the prop exists
  useEffect(() => {
    onDownloadStateChange?.(isDownloading);
  }, [isDownloading, onDownloadStateChange]);

  if (loading) return <Loading />;
  if (!data)
    return (
      <div className="bg-linear-to-b flex min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white items-center justify-center">
        User not found
      </div>
    );

  return (
    <div className="bg-linear-to-b min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white py-4 px-4 relative sm:py-8">
      <BingImg
        className="h-full object-cover w-full top-0 left-0 absolute"
        backgroundId={backgroundId}
      />

      <div
        className={`relative z-10 w-content max-w-[100%] mx-auto ${
          isDownloading ? "bg-gray-900/70" : "bg-gray-900/20"
        } backdrop-blur-lg rounded-lg p-4 pt-8`}
      >
        {/* Settings button - only shown if hideMenu is false */}
        {!hideMenu && !isDownloading && (
          <BlurFade delay={100}>
            <div className="flex h-10 relative overflow-hidden justify-end">
              {!isDownloading && (
                <ShareButton
                  templateType="contribute"
                  setIsDownloading={setIsDownloading}
                />
              )}
            </div>
          </BlurFade>
        )}

        <BlurFade delay={200}>
          <ProfileTotal userData={data.userData} />
        </BlurFade>

        <ProfileContribute username={username} years={3} />

        {/* Footer */}
        {
          <BlurFade delay={1300}>
            <Footer showQrcode />
          </BlurFade>
        }
      </div>
    </div>
  );
}
