"use client";

import { Footer } from "@/components/footer";
import { useEffect, useState } from "react";
import { ShareButton } from "@/components/share-button";
import { MultiDimensionCard } from "@/components/cards/MultiDimensionCard";
import {
  CompleteUserResponse,
  ApiResponse,
  ApiErrorResponse,
} from "@/types/user-data";
import { BingImg } from "@/components/bing-img";
import { BlurFade } from "../blur-fade";

interface ProfileMultiDimensionPageProps {
  username: string;
  hideMenu?: boolean;
  sharedData?: CompleteUserResponse;
  onDownloadStateChange?: (downloading: boolean) => void;
  backgroundId?: string;
}

export function ProfileMultiDimensionPage({
  username,
  hideMenu = false,
  sharedData,
  onDownloadStateChange,
  backgroundId,
}: ProfileMultiDimensionPageProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  // CompleteUserResponse
  const [data, setData] = useState<CompleteUserResponse | null>(null);
  const [isLoading, setIsLoading] = useState(!sharedData);
  const [loadingStage, setLoadingStage] = useState<string>("初始化...");
  const [error, setError] = useState<string | null>(null);
  const [performanceInfo, setPerformanceInfo] = useState<{
    responseTime: number;
    cached: boolean;
    version: string;
  } | null>(null);

  // Forward downloading state to parent component if the prop exists
  useEffect(() => {
    onDownloadStateChange?.(isDownloading);
  }, [isDownloading, onDownloadStateChange]);

  // 增强用户体验的数据获取
  useEffect(() => {
    if (!sharedData && username) {
      const fetchData = async () => {
        const startTime = Date.now();
        try {
          setIsLoading(true);
          setError(null);
          setLoadingStage("连接服务器...");

          const res = await fetch(`/api/github-data`);

          setLoadingStage("获取数据中...");
          const result: ApiResponse<CompleteUserResponse> = await res.json();

          if (result.success && result.data) {
            setLoadingStage("处理数据...");

            // 收集性能信息
            const clientResponseTime = Date.now() - startTime;
            const serverResponseTime = result.data.metadata?.responseTime || 0;
            const cached = result.data.metadata?.cached || false;
            const version = result.data.metadata?.version || "unknown";

            setPerformanceInfo({
              responseTime: Math.max(clientResponseTime, serverResponseTime),
              cached,
              version,
            });

            setLoadingStage("渲染界面...");

            // 短暂延迟让用户看到"渲染界面"状态
            await new Promise((resolve) => setTimeout(resolve, 200));

            // 直接使用最优结构的返回数据
            setData(result.data);

            // 性能提示（仅在开发模式下显示）
            if (process.env.NODE_ENV === "development") {
              console.log(
                `🚀 页面加载完成: ${clientResponseTime}ms (${
                  cached ? "缓存命中" : "新数据"
                })`
              );
            }
          } else {
            setError(
              (result as ApiErrorResponse).error ||
                "Failed to fetch GitHub data"
            );
          }
        } catch (error) {
          console.error("Error fetching GitHub data:", error);
          setError("网络连接错误，请检查网络连接");
        } finally {
          setIsLoading(false);
          setLoadingStage("初始化...");
        }
      };

      fetchData();
    } else if (sharedData) {
      // 如果有共享数据，直接构建数据结构（无需适配器转换）
      setData(sharedData);
    }
  }, [username, sharedData]);

  // 增强的加载体验
  if (isLoading) {
    return (
      <div className="bg-linear-to-b flex flex-col min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white py-4 px-4 relative justify-center items-center">
        {/* 背景图片 */}
        <BingImg
          className="h-full object-cover w-full top-0 left-0 fixed"
          backgroundId={backgroundId}
        />

        {/* 增强的加载界面 */}
        <div className="text-center z-10 relative">
          <div className="mb-8">
            {/* 加载动画 */}
            <div className="border-t-white rounded-full mx-auto border-4 border-white/20 h-16 mb-4 animate-spin w-16"></div>

            {/* 加载阶段显示 */}
            <h2 className="font-semibold text-xl mb-2">正在生成多维度卡片</h2>
            <p className="text-sm mb-4 text-white/80">{loadingStage}</p>

            {/* 技术提示 */}
            <div className="space-y-1 text-xs text-white/60">
              <p>🚀 使用智能缓存技术，大幅提升加载速度</p>
              <p>📊 实时计算四维度评分数据</p>
              <p>✨ 统一数据类型优化版本</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 增强的错误处理体验
  if (error || !data) {
    const handleRetry = () => {
      setError(null);
      setIsLoading(true);
      setLoadingStage("重新连接...");

      // 触发重新获取数据
      setTimeout(() => {
        window.location.reload();
      }, 300);
    };

    const handleForceRefresh = async () => {
      setError(null);
      setIsLoading(true);
      setLoadingStage("强制刷新缓存...");

      try {
        // 强制刷新缓存
        const res = await fetch(`/api/github-data?invalidateCache=true`);
        const result: ApiResponse<CompleteUserResponse> = await res.json();

        if (result.success && result.data) {
          setData(result.data);
          setIsLoading(false);
        } else {
          throw new Error(
            (result as ApiErrorResponse).error || "Failed to refresh data"
          );
        }
      } catch (error) {
        console.error("Force refresh failed:", error);
        setError("强制刷新失败，请稍后再试");
        setIsLoading(false);
      }
      setLoadingStage("初始化...");
    };

    return (
      <div className="bg-linear-to-b flex flex-col min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white py-4 px-4 relative justify-center items-center">
        {/* 背景图片 */}
        <BingImg
          className="h-full object-cover w-full top-0 left-0 fixed"
          backgroundId={backgroundId}
        />

        {/* 增强的错误界面 */}
        <div className="max-w-md text-center z-10 relative">
          <div className="border rounded-lg bg-black/30 border-white/20 mb-8 p-6 backdrop-blur-sm">
            {/* 错误图标 */}
            <div className="mb-4 text-red-400 text-4xl">⚠️</div>

            <h2 className="font-bold text-xl mb-4">数据加载失败</h2>
            <p className="text-sm mb-6 leading-relaxed text-white/80">
              {error || "无法获取用户数据，可能是网络连接问题或服务暂时不可用"}
            </p>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <button
                onClick={handleRetry}
                className="rounded-lg font-medium bg-[#fa7b19] text-white w-full py-3 px-4 transition-colors hover:bg-[#e76b0a]"
                disabled={isLoading}
              >
                {isLoading ? "重试中..." : "🔄 重新加载"}
              </button>

              <button
                onClick={handleForceRefresh}
                className="border rounded-lg bg-white/10 border-white/20 text-white text-sm w-full py-2 px-4 transition-colors hover:bg-white/20"
                disabled={isLoading}
              >
                ⚡ 强制刷新缓存
              </button>
            </div>

            {/* 帮助提示 */}
            <div className="space-y-1 mt-6 text-xs text-white/60">
              <p>💡 建议：检查网络连接或稍后再试</p>
              <p>🔧 如果问题持续，请尝试强制刷新缓存</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 直接使用统一数据结构
  const { userData, multiDimensionScore } = data;

  return (
    <div className="bg-linear-to-b flex flex-col min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white py-4 px-4 relative justify-center sm:py-8">
      {/* 背景图片 */}
      <BingImg
        className={
          "h-full object-cover w-full top-0 left-0 " +
          (isDownloading ? "absolute" : "fixed")
        }
        backgroundId={backgroundId}
      />

      {/* 主要内容 */}
      <div className="mx-auto w-content max-w-[100%] z-10 relative">
        {/* 导航栏 */}
        {!hideMenu && !isDownloading && (
          <div className="flex h-10 relative overflow-hidden justify-end">
            <ShareButton
              templateType="multidimension"
              setIsDownloading={setIsDownloading}
            />
          </div>
        )}

        {/* 性能反馈提示 */}
        {performanceInfo && performanceInfo.responseTime > 0 && (
          <BlurFade delay={200}>
            <div className="border rounded-lg bg-black/20 border-white/10 mb-4 p-3 backdrop-blur-sm">
              <div className="flex text-sm items-center justify-between">
                <div className="flex gap-2 items-center">
                  {performanceInfo.cached ? (
                    <span className="text-green-400">⚡ 缓存命中</span>
                  ) : (
                    <span className="text-blue-400">🔄 实时数据</span>
                  )}
                  <span className="text-white/70">
                    加载时间: {performanceInfo.responseTime}ms
                  </span>
                </div>
                <div className="text-xs text-white/50">
                  {performanceInfo.version}
                </div>
              </div>
            </div>
          </BlurFade>
        )}

        {/* 卡片展示区域 */}
        <MultiDimensionCard
          userProfile={userData}
          multiDimensionScore={multiDimensionScore}
          showDimensionBreakdown={true}
          showActivityChart={true}
          showUserStats={true}
          isLoading={isLoading}
          className="mt-4 mb-10"
          isDownloading={isDownloading}
        />

        {/* Footer */}
        <BlurFade delay={1300}>
          <Footer showQrcode showStyle={2} />
        </BlurFade>
      </div>
    </div>
  );
}
