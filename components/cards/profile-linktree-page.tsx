"use client";
import { BlurFade } from "@/components/blur-fade";
import { Footer } from "@/components/footer";
import { CompleteUserResponse, UserData } from "@/types/user-data";
import { useState, useEffect } from "react";
import { BingImg } from "@/components/bing-img";
import NumberTicker from "@/components/ui/number-ticker";
import HyperText from "@/components/ui/hyper-text";
import { ProfileTotal } from "@/components/profile-total";
import { ShareButton } from "@/components/share-button";
import { BookOpen, Users, Star, GitBranch } from "lucide-react";
import Loading from "@/components/loading";

interface ProfileLinktreePageProps {
  username: string;
  hideMenu?: boolean;
  sharedData?: CompleteUserResponse;
  onDownloadStateChange?: (downloading: boolean) => void;
  backgroundId?: string;
}

export function ProfileLinktreePage({
  username,
  hideMenu = false,
  sharedData,
  onDownloadStateChange,
  backgroundId,
}: ProfileLinktreePageProps) {
  const [data, setData] = useState<CompleteUserResponse | null>(null);
  const [loading, setLoading] = useState(!sharedData);

  useEffect(() => {
    // If sharedData is provided, use it directly
    if (sharedData) {
      setData(sharedData);
      setLoading(false);
      return;
    }

    if (!username) return;
  }, [username, sharedData]);

  const [isDownloading, setIsDownloading] = useState(false);

  // Forward downloading state to parent component if the prop exists
  useEffect(() => {
    onDownloadStateChange?.(isDownloading);
  }, [isDownloading, onDownloadStateChange]);

  if (loading) return <Loading />;
  if (!data)
    return (
      <div className="bg-linear-to-b flex min-h-screen from-orange-600 via-orange-800 to-gray-900 text-white items-center justify-center">
        User not found
      </div>
    );

  return (
    <div className="bg-linear-to-b flex flex-col min-h-screen from-orange-600  via-orange-800 to-gray-900 text-white py-4 px-4 relative justify-center sm:py-8">
      <BingImg
        className="h-full object-cover w-full top-0 left-0 absolute"
        backgroundId={backgroundId}
      />

      <div className={`relative z-10 w-content max-w-[100%] mx-auto`}>
        {/* Settings button */}
        {!isDownloading && !hideMenu && (
          <BlurFade delay={100}>
            <div className="flex h-10 relative overflow-hidden justify-end">
              {!isDownloading && (
                <ShareButton
                  templateType="linktree"
                  setIsDownloading={setIsDownloading}
                />
              )}
            </div>
          </BlurFade>
        )}
        {/* Profile section */}
        <BlurFade delay={300}>
          <ProfileTotal userData={data.userData} />
        </BlurFade>
        {/* Navigation buttons - updated with real data */}
        <div className="mx-auto max-w-md space-y-4 my-10">
          {[
            {
              label: "Total Stars",
              value: data.userData.totalStars,
              delay: 500,
              icon: Star,
            },
            {
              label: "Total Commits",
              value: data.userData.commits,
              delay: 600,
              icon: GitBranch,
            },
            {
              label: "Public Repos",
              value: data.userData.publicRepos,
              delay: 700,
              icon: BookOpen,
            },
            {
              label: "Followers",
              value: data.userData.followers,
              delay: 800,
              icon: Users,
            },
          ].map((item) => (
            <BlurFade key={item.label} delay={item.delay}>
              <button className="rounded-full flex bg-white/90 text-black w-full py-4 px-6 gap-3 items-center">
                <HyperText className="font-normal flex-1 text-sm text-left ">
                  {item.label}
                </HyperText>
                <span className="text-sm ">
                  {typeof item.value === "number" ? (
                    <NumberTicker value={item.value} />
                  ) : (
                    item.value
                  )}
                </span>
                <item.icon size={22} />
              </button>
            </BlurFade>
          ))}
        </div>
        {/* Footer */}
        <BlurFade delay={1300}>
          <Footer showQrcode />
        </BlurFade>
      </div>
    </div>
  );
}
