/* eslint-disable @next/next/no-img-element */
// import Image from 'next/image'
import { ContributionGrade } from "@/components/contribution-grade";
import { UserData } from "@/types/user-data";
import { calculateMultiDimensionScores } from "@/lib/github/score";

export function ProfileTotal({ userData }: { userData: UserData }) {
  // 直接计算多维度评分，无需适配器
  const multiDimensionScore = calculateMultiDimensionScores({
    commits: userData.commits,
    contributedRepos: userData.contributedRepos,
    pullRequests: userData.pullRequests,
    reviews: userData.reviews,
    issues: userData.issues,
    totalStars: userData.totalStars,
    totalForks: userData.totalForks,
    followers: userData.followers,
    languageDiversity: userData.languageStats?.totalLanguages || 0,
    publicRepos: userData.publicRepos,
    following: userData.following,
    createdAt: userData.createdAt,
  });

  return (
    <div className="flex flex-col pt-6 items-center">
      <a
        href={`https://github.com/${userData.login}`}
        target="_blank"
        className="rounded-full h-28 mb-6 w-28 relative sm:h-32 sm:w-32"
      >
        <img
          src={userData.avatarUrl}
          alt="Profile"
          className="rounded-full h-full object-cover w-full"
        />
        <div className="right-0 bottom-0 absolute">
          <ContributionGrade grade={multiDimensionScore.overallGrade} />
        </div>
      </a>
      <h1 className="font-bold mb-2 text-2xl">
        {userData.name || userData.login}
      </h1>
      <p className="text-gray-200">{userData.bio || "No bio available"}</p>
    </div>
  );
}
