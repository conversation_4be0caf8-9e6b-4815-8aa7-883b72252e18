import React from "react";
import {
  ActivityRadarChart,
  ActivityScores,
} from "@/components/charts/ActivityRadarChart";
import type { UserData } from "@/types/user-data";

interface ActivityChartSectionProps {
  userProfile: UserData;
  isDownloading?: boolean;
  showTitle?: boolean;
  className?: string;
}

export function ActivityChartSection({
  userProfile,
  isDownloading,
  showTitle = true,
  className,
}: ActivityChartSectionProps) {
  // 从userProfile数据生成活跃度评分
  const activityData: ActivityScores = {
    stars: userProfile.totalStars || 0,
    forks: userProfile.totalForks || 0,
    commits: userProfile.commits || 0,
    pullRequests: userProfile.pullRequests || 0,
    issues: userProfile.issues || 0,
    reviews: userProfile.reviews || 0,
  };

  return (
    <div className={"space-y-4 " + (className || "")}>
      {showTitle && (
        <h4 className="font-semibold text-base text-center mb-4">
          Development Activity Analysis
        </h4>
      )}

      {/* Activity Radar Chart */}
      <ActivityRadarChart
        scores={activityData}
        size="md"
        showTooltip={true}
        animated={true}
        theme="dark"
        className="mt-2"
      />
    </div>
  );
}
