import { DimensionScores } from "@/types/score";
import { getAllDimensionConfigs } from "@/constants";

// Get best dimension
export function getBestDimension(scores: DimensionScores): string {
  const dimensions = getAllDimensionConfigs().map((config) => ({
    key: config.key,
    name: config.label,
    value: scores[config.scoreKey],
  }));

  const best = dimensions.reduce((prev, current) =>
    current.value > prev.value ? current : prev
  );

  return best.name;
}

// Format numbers with K, M units
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
}
