import React from "react";
import { ConcentricSemicircleChart } from "@/components/charts/ConcentricSemicircleChart";
import { MultiDimensionScore } from "@/types/multi-dimension";
import { getBestDimension } from "./utils";
import { getAllDimensionConfigs } from "@/constants";

interface DimensionBreakdownSectionProps {
  multiDimensionScore: MultiDimensionScore;
  isDownloading?: boolean;
  showTitle?: boolean;
  className?: string
}

export function DimensionBreakdownSection({
  multiDimensionScore,
  isDownloading,
  showTitle = true,
  className
}: DimensionBreakdownSectionProps) {
  const dimensionData = getAllDimensionConfigs().map((config) => ({
    key: config.scoreKey,
    label: config.label,
    score: multiDimensionScore[config.scoreKey],
    bgColor: config.colors.bg,
    bgColorEnd: config.colors.bgEnd,
    borderColor: config.colors.border,
    textColor: config.colors.text,
    scoreColor: config.colors.score,
  }));

  const bestDimension = getBestDimension(multiDimensionScore);

  return (
    <div className={className || ''}>
      {showTitle && (
        <h4 className="font-semibold text-base text-center mb-4">
          Dimension Breakdown
        </h4>
      )}

      {/* 同心半圆部分 */}
      <ConcentricSemicircleChart
        scores={multiDimensionScore}
        size="md"
        className="-mt-4 mb-4"
        theme="dark"
        showLabels={false}
        showCenterDot={true}
      />

      {/* 四组分值展示 */}
      <div className="grid gap-3 grid-cols-2">
        {dimensionData.map((dimension) => (
          <div
            key={dimension.key}
            className="border rounded-lg text-sm p-3"
            style={{
              background: `linear-gradient(135deg, ${dimension.bgColor} 0%, ${dimension.bgColorEnd} 100%)`,
              borderColor: dimension.borderColor,
            }}
          >
            <div className="flex space-x-2 items-center">
              <span className={`${dimension.textColor} font-medium text-xs`}>
                {dimension.label}
              </span>
            </div>
            <span className={`font-bold ${dimension.scoreColor} text-lg`}>
              {dimension.score}
            </span>
          </div>
        ))}
      </div>

      <div className="border-t border-white/30 mt-4 pt-4 overflow-hidden">
        <div className="rounded-xl text-xs text-center p-3 liquidGlass-wrapper overflow-hidden">
          <div className="liquidGlass-shine"> </div>
          🏆 Specialties: {bestDimension}
        </div>
      </div>
    </div>
  );
}
