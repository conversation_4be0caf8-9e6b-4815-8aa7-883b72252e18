import React from "react";
import { MultiDimensionScore } from "@/types/multi-dimension";

interface GradeSectionProps {
  multiDimensionScore: MultiDimensionScore;
  className?: string;
}

export function GradeSection({
  multiDimensionScore,
  className,
}: GradeSectionProps) {
  return (
    <>
      <div className={"space-y-2 text-center " + className}>
        <div className="font-bold text-shadow-sm leading-none text-[60px] sm:text-[80px] md:text-[110px] text-white/60 text-shadow-white/20">
          {multiDimensionScore.overallGrade}
        </div>
        <div className="font-semibold text-xs sm:text-sm opacity-80">
          <span className="font-normal text-xs">Total Score:</span>{" "}
          {multiDimensionScore.totalScore} pts
        </div>

        <div>
          {/* <div className="text-xs opacity-70">Specialties</div> */}
          <span className="font-semibold text-base sm:text-lg break-words">
            {multiDimensionScore.bestDimension}
          </span>
        </div>
        {/* <div className="text-sm opacity-80">{gradeInfo.description}</div> */}
      </div>
    </>
  );
}
