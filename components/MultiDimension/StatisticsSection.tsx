import React from "react";
import { cn } from "@/utils";
import {
  Users,
  Star,
  GitFork,
  Calendar,
  Code,
  GitPullRequest,
  Eye,
  BookOpen,
  BookMarked,
} from "lucide-react";
import type { UserData } from "@/types/user-data";
import { formatNumber } from "./utils";
import { MultiDimensionScore } from "@/types/multi-dimension";
import { getAllDimensionConfigs } from "@/constants";

interface StatisticsSectionProps {
  multiDimensionScore: MultiDimensionScore;
  userProfile: UserData;
  isDownloading?: boolean;
  showTitle?: boolean;
  className?: string;
}

export function StatisticsSection({
  multiDimensionScore,
  userProfile,
  isDownloading,
  showTitle,
  className,
}: StatisticsSectionProps) {
  // Get the best dimension configuration
  const bestDimensionLabel = multiDimensionScore.bestDimension;
  const bestDimensionConfig = getAllDimensionConfigs().find(
    (config) => config.label === bestDimensionLabel
  );

  // Define dimension-specific metrics
  const getDimensionMetrics = () => {
    if (!bestDimensionConfig) {
      // Fallback to default metrics if no best dimension found
      return [
        {
          value: formatNumber(userProfile.followers),
          label: "Followers",
          icon: Users,
          hasBg: true,
        },
        {
          value: formatNumber(userProfile.publicRepos),
          label: "Repos",
          icon: BookMarked,
          hasBg: true,
        },
        {
          value: formatNumber(userProfile.totalStars),
          label: "Stars",
          icon: Star,
          hasBg: true,
        },
        {
          value: `${
            new Date().getFullYear() -
            new Date(userProfile.createdAt).getFullYear()
          } Years`,
          label: "Account Age",
          icon: Calendar,
          hasBg: true,
        },
      ];
    }

    switch (bestDimensionConfig.key) {
      case "commit":
        // Code Architect - focus on code creation metrics
        return [
          {
            value: formatNumber(userProfile.commits || 0),
            label: "Commits",
            icon: Code,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.contributedRepos || 0),
            label: "Contributed Repos",
            icon: GitFork,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.publicRepos),
            label: "Public Repos",
            icon: BookMarked,
            hasBg: true,
          },
          {
            value: `${
              new Date().getFullYear() -
              new Date(userProfile.createdAt).getFullYear()
            } Years`,
            label: "Coding Experience",
            icon: Calendar,
            hasBg: true,
          },
        ];

      case "collaboration":
        // Community Builder - focus on collaboration metrics
        return [
          {
            value: formatNumber(userProfile.pullRequests || 0),
            label: "Pull Requests",
            icon: GitPullRequest,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.reviews || 0),
            label: "Code Reviews",
            icon: Eye,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.issues || 0),
            label: "Issues",
            icon: GitFork,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.followers),
            label: "Followers",
            icon: Users,
            hasBg: true,
          },
        ];

      case "influence":
        // Open Source Influencer - focus on impact metrics
        return [
          {
            value: formatNumber(userProfile.totalStars),
            label: "Total Stars",
            icon: Star,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.totalForks || 0),
            label: "Total Forks",
            icon: GitFork,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.followers),
            label: "Followers",
            icon: Users,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.publicRepos),
            label: "Public Repos",
            icon: BookMarked,
            hasBg: true,
          },
        ];

      case "exploration":
        // Innovation Explorer - focus on learning and diversity metrics
        return [
          {
            value: formatNumber(userProfile.publicRepos),
            label: "Repositories",
            icon: BookOpen,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.following),
            label: "Following",
            icon: Users,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.languageStats?.totalLanguages || 0),
            label: "Languages",
            icon: Code,
            hasBg: true,
          },
          {
            value: `${
              new Date().getFullYear() -
              new Date(userProfile.createdAt).getFullYear()
            } Years`,
            label: "Learning Journey",
            icon: Calendar,
            hasBg: true,
          },
        ];

      default:
        // Default fallback
        return [
          {
            value: formatNumber(userProfile.followers),
            label: "Followers",
            icon: Users,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.publicRepos),
            label: "Repos",
            icon: GitFork,
            hasBg: true,
          },
          {
            value: formatNumber(userProfile.totalStars),
            label: "Stars",
            icon: Star,
            hasBg: true,
          },
          {
            value: `${
              new Date().getFullYear() -
              new Date(userProfile.createdAt).getFullYear()
            } Years`,
            label: "Account Age",
            icon: Calendar,
            hasBg: true,
          },
        ];
    }
  };

  const statisticsData = getDimensionMetrics();

  // Generate dynamic title based on best dimension
  const getDynamicTitle = () => {
    if (!bestDimensionConfig) return "GitHub Statistics";

    switch (bestDimensionConfig.key) {
      case "commit":
        return "Code Creation Metrics";
      case "collaboration":
        return "Collaboration Metrics";
      case "influence":
        return "Impact & Influence";
      case "exploration":
        return "Learning & Exploration";
      default:
        return "GitHub Statistics";
    }
  };

  return (
    <div className={"space-y-3 " + (className || "")}>
      {showTitle && (
        <h4 className="font-semibold text-base text-center mb-3">
          {getDynamicTitle()}
        </h4>
      )}
      <div className="grid gap-3 grid-cols-2">
        {statisticsData.map((stat, index) => (
          <div
            key={index}
            className={cn(
              "border border-white/30 p-4 !rounded-2xl",
              stat.hasBg && "bg-white/10 backdrop-blur-sm",
              isDownloading && "bg-black/30"
            )}
          >
            <div className={cn("font-semibold text-xl")}>{stat.value}</div>
            <div className="flex space-x-1 mt-1 text-sm opacity-80 items-center">
              <stat.icon className="h-4 w-4" />
              <span>{stat.label}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
