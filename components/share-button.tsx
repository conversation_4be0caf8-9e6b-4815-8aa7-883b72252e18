"use client";

import {
  <PERSON><PERSON>sisVertical,
  Image as ImageIcon,
  House,
  Copy as CopyIcon,
  RefreshCcw as RefreshIcon,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Drawer,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
  DrawerClose,
} from "./ui/drawer";
import { toCanvas } from "html-to-image";
import { downloadImage } from "@/utils";
import toast from "react-hot-toast";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { useBackground } from "@/contexts/background-context";
import { useShareLinks } from "@/contexts/sharelinks-context";

export function ShareButton({
  setIsDownloading,
  templateType,
}: {
  setIsDownloading: (isDownloading: boolean) => void;
  templateType: string;
}) {
  const { shareLink, generateShareLink } = useShareLinks();

  const { status } = useSession();
  const { refreshBackground } = useBackground();

  const saveAsImage = () => {
    setIsDownloading(true);
    setTimeout(() => {
      const node = document.body;
      if (node) {
        toCanvas(node, {
          canvasHeight: node.clientHeight * window.devicePixelRatio,
          canvasWidth: node.clientWidth * window.devicePixelRatio,
        })
          .then(function (canvas: HTMLCanvasElement) {
            downloadImage(canvas);
          })
          .catch(function (error) {
            console.error("Error saving image:", error);
          })
          .finally(() => {
            setIsDownloading(false);
          });
      } else {
        throw new Error("Node not found");
      }
    }, 50);
  };

  const generateAndCopyLink = (needUpdate = false) => {
    // Check if user is authenticated first
    if (status !== "authenticated") {
      toast.error("You need to sign in to generate a share link");
      return;
    }

    // If we already have a share URL, just copy it
    if (shareLink?.shareLink) {
      const fullUrl = `${shareLink.shareLink}`;
      navigator.clipboard.writeText(fullUrl);
      toast.success("Share link copied to clipboard");
      // update background
      if (needUpdate) {
        generateShareLink(templateType, true);
      }
    }
  };

  return (
    <Drawer>
      <DrawerTrigger asChild>
        <button
          className="p-2 rounded-full bg-white/40 hover:bg-white/10"
          title="Share"
        >
          <EllipsisVertical size={24} strokeWidth={2.5} />
        </button>
      </DrawerTrigger>

      <DrawerContent>
        <div className="mx-auto w-full max-w-sm">
          <DrawerHeader>
            <DrawerTitle className="text-center">Share GitHub Card</DrawerTitle>
          </DrawerHeader>

          <div className="flex flex-col gap-4 p-4 items-center">
            <DrawerClose asChild>
              <Button
                onClick={saveAsImage}
                className="w-full flex items-center justify-center gap-2 h-10"
              >
                <ImageIcon size={20} />
                Save as Image
              </Button>
            </DrawerClose>

            <DrawerClose asChild>
              <div className="flex items-center w-full">
                <Button
                  variant="secondary"
                  onClick={() => generateAndCopyLink(false)}
                  className={
                    "flex-1 flex items-center justify-center gap-2 h-10" +
                    (shareLink?.exists ? " rounded-r-none" : "")
                  }
                  disabled={!shareLink}
                >
                  Copy link
                </Button>
                {shareLink?.exists && (
                  <>
                    <Tooltip defaultOpen>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={() => generateAndCopyLink(true)}
                          className="rounded-l-none"
                          size="icon"
                          disabled={!shareLink}
                        >
                          <RefreshIcon size={20} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Use this background</p>
                      </TooltipContent>
                    </Tooltip>
                  </>
                )}
              </div>
            </DrawerClose>

            <DrawerClose asChild>
              <Button
                variant="outline"
                className="w-full flex items-center justify-center gap-2 h-10"
                onClick={() => refreshBackground()}
              >
                Change Background
              </Button>
            </DrawerClose>

            <DrawerClose asChild>
              <Link href="/" className="w-full">
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2 h-10"
                >
                  <House size={20} />
                  Back to Home
                </Button>
              </Link>
            </DrawerClose>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
