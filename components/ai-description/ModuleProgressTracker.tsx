"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle2,
  Clock,
  Loader2,
  AlertCircle,
  Zap,
  Target,
  Bot,
  Eye,
  ChevronRight,
  Timer,
  Activity,
} from "lucide-react";
import { cn } from "@/lib/utils";

// 模块状态类型
export type ModuleStatus =
  | "pending"
  | "running"
  | "completed"
  | "error"
  | "skipped";

// 模块信息接口
export interface ModuleInfo {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  status: ModuleStatus;
  startTime?: number;
  endTime?: number;
  progress: number;
  error?: string;
  metadata?: Record<string, any>;
}

// 组件属性
interface ModuleProgressTrackerProps {
  modules: ModuleInfo[];
  currentModuleId?: string;
  overallProgress: number;
  isRunning: boolean;
  showTimings?: boolean;
  showMetadata?: boolean;
  compact?: boolean;
  className?: string;
}

// 默认模块配置
export const DEFAULT_MODULES: Omit<ModuleInfo, "status" | "progress">[] = [
  {
    id: "analyzer",
    name: "Data Analyzer",
    description: "Processing GitHub data patterns and extracting insights",
    icon: Zap,
  },
  {
    id: "strategist",
    name: "Comedy Strategist",
    description: "Selecting optimal humor strategy and creative approach",
    icon: Target,
  },
  {
    id: "writer",
    name: "Text Writer",
    description: "Generating personalized description with selected style",
    icon: Bot,
  },
  {
    id: "critic",
    name: "Quality Critic",
    description:
      "Evaluating output quality and providing optimization feedback",
    icon: Eye,
  },
];

export function ModuleProgressTracker({
  modules,
  currentModuleId,
  overallProgress,
  isRunning,
  showTimings = true,
  showMetadata = false,
  compact = false,
  className,
}: ModuleProgressTrackerProps) {
  const [animatedProgress, setAnimatedProgress] = useState(0);

  // 动画化进度条
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(overallProgress);
    }, 100);
    return () => clearTimeout(timer);
  }, [overallProgress]);

  // 获取状态样式
  const getStatusStyles = (status: ModuleStatus) => {
    switch (status) {
      case "completed":
        return {
          bg: "bg-green-50 dark:bg-green-950",
          border: "border-green-200 dark:border-green-800",
          text: "text-green-900 dark:text-green-100",
          icon: "text-green-600 dark:text-green-400",
        };
      case "running":
        return {
          bg: "bg-blue-50 dark:bg-blue-950",
          border: "border-blue-200 dark:border-blue-800",
          text: "text-blue-900 dark:text-blue-100",
          icon: "text-blue-600 dark:text-blue-400",
        };
      case "error":
        return {
          bg: "bg-red-50 dark:bg-red-950",
          border: "border-red-200 dark:border-red-800",
          text: "text-red-900 dark:text-red-100",
          icon: "text-red-600 dark:text-red-400",
        };
      case "pending":
        return {
          bg: "bg-gray-50 dark:bg-gray-900",
          border: "border-gray-200 dark:border-gray-700",
          text: "text-gray-900 dark:text-gray-100",
          icon: "text-gray-500 dark:text-gray-400",
        };
      default:
        return {
          bg: "bg-gray-50 dark:bg-gray-900",
          border: "border-gray-200 dark:border-gray-700",
          text: "text-gray-900 dark:text-gray-100",
          icon: "text-gray-500 dark:text-gray-400",
        };
    }
  };

  // 获取状态图标
  const getStatusIcon = (module: ModuleInfo) => {
    switch (module.status) {
      case "completed":
        return <CheckCircle2 className="w-5 h-5" />;
      case "running":
        return <Loader2 className="w-5 h-5 animate-spin" />;
      case "error":
        return <AlertCircle className="w-5 h-5" />;
      default:
        const ModuleIcon = module.icon;
        return <ModuleIcon className="w-5 h-5" />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: ModuleStatus) => {
    switch (status) {
      case "completed":
        return "Completed";
      case "running":
        return "Running";
      case "error":
        return "Error";
      case "pending":
        return "Pending";
      case "skipped":
        return "Skipped";
      default:
        return "Unknown";
    }
  };

  // 计算执行时间
  const getExecutionTime = (module: ModuleInfo) => {
    if (module.startTime && module.endTime) {
      return module.endTime - module.startTime;
    }
    if (module.startTime && module.status === "running") {
      return Date.now() - module.startTime;
    }
    return null;
  };

  // 格式化时间
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  // 渲染紧凑模式
  if (compact) {
    return (
      <div className={cn("space-y-3", className)}>
        <div className="flex items-center justify-between text-sm">
          <span className="font-medium">Generation Progress</span>
          <span className="text-muted-foreground">
            {Math.round(animatedProgress)}%
          </span>
        </div>

        <Progress value={animatedProgress} className="h-2" />

        <div className="flex items-center gap-2">
          {modules.map((module, index) => {
            const styles = getStatusStyles(module.status);
            const isActive = module.id === currentModuleId;

            return (
              <div key={module.id} className="flex items-center gap-1">
                <div
                  className={cn(
                    "w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all",
                    styles.bg,
                    styles.border,
                    isActive && "ring-2 ring-blue-500 ring-offset-2"
                  )}
                >
                  <div className={cn("w-4 h-4", styles.icon)}>
                    {getStatusIcon(module)}
                  </div>
                </div>

                {index < modules.length - 1 && (
                  <ChevronRight className="w-3 h-3 text-gray-400" />
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // 渲染完整模式
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-3">
          <Activity className="w-5 h-5 text-blue-600" />
          <span>Generation Progress</span>
          <Badge variant="outline" className="ml-auto">
            {isRunning ? "Running" : "Idle"}
          </Badge>
        </CardTitle>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Overall Progress</span>
            <span className="font-medium">{Math.round(animatedProgress)}%</span>
          </div>
          <Progress value={animatedProgress} className="h-3" />
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {modules.map((module, index) => {
          const styles = getStatusStyles(module.status);
          const isActive = module.id === currentModuleId;
          const executionTime = getExecutionTime(module);

          return (
            <div
              key={module.id}
              className={cn(
                "p-4 rounded-lg border transition-all",
                styles.bg,
                styles.border,
                isActive && "ring-2 ring-blue-500 ring-offset-2"
              )}
            >
              <div className="flex items-start gap-3">
                {/* 状态图标 */}
                <div className={cn("mt-0.5", styles.icon)}>
                  {getStatusIcon(module)}
                </div>

                {/* 模块信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={cn("font-medium text-sm", styles.text)}>
                      {module.name}
                    </h4>

                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {getStatusText(module.status)}
                      </Badge>

                      {showTimings && executionTime && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Timer className="w-3 h-3" />
                          {formatTime(executionTime)}
                        </div>
                      )}
                    </div>
                  </div>

                  <p
                    className={cn(
                      "text-xs leading-relaxed",
                      styles.text,
                      "opacity-80"
                    )}
                  >
                    {module.description}
                  </p>

                  {/* 模块进度条 */}
                  {module.status === "running" && (
                    <div className="mt-2">
                      <Progress value={module.progress} className="h-1" />
                    </div>
                  )}

                  {/* 错误信息 */}
                  {module.status === "error" && module.error && (
                    <div className="mt-2 p-2 bg-red-100 dark:bg-red-900 rounded text-xs text-red-800 dark:text-red-200">
                      {module.error}
                    </div>
                  )}

                  {/* 元数据 */}
                  {showMetadata &&
                    module.metadata &&
                    Object.keys(module.metadata).length > 0 && (
                      <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                        {Object.entries(module.metadata).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              {key}:
                            </span>
                            <span>{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    )}
                </div>
              </div>

              {/* 连接线 */}
              {index < modules.length - 1 && (
                <div className="flex justify-center mt-2">
                  <ChevronRight className="w-4 h-4 text-gray-400 rotate-90" />
                </div>
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}

// 辅助Hook：管理模块状态
export function useModuleProgress(
  initialModules: Omit<ModuleInfo, "status" | "progress">[]
) {
  const [modules, setModules] = useState<ModuleInfo[]>(
    initialModules.map((module) => ({
      ...module,
      status: "pending" as ModuleStatus,
      progress: 0,
    }))
  );

  const [currentModuleId, setCurrentModuleId] = useState<string | null>(null);
  const [overallProgress, setOverallProgress] = useState(0);

  const updateModule = (id: string, updates: Partial<ModuleInfo>) => {
    setModules((prev) =>
      prev.map((module) =>
        module.id === id ? { ...module, ...updates } : module
      )
    );
  };

  const startModule = (id: string) => {
    setCurrentModuleId(id);
    updateModule(id, {
      status: "running",
      startTime: Date.now(),
      progress: 0,
    });
  };

  const completeModule = (id: string, metadata?: Record<string, any>) => {
    updateModule(id, {
      status: "completed",
      endTime: Date.now(),
      progress: 100,
      metadata,
    });
  };

  const errorModule = (id: string, error: string) => {
    updateModule(id, {
      status: "error",
      endTime: Date.now(),
      error,
    });
  };

  const updateProgress = (id: string, progress: number) => {
    updateModule(id, { progress });
  };

  const reset = () => {
    setModules((prev) =>
      prev.map((module) => ({
        ...module,
        status: "pending" as ModuleStatus,
        progress: 0,
        startTime: undefined,
        endTime: undefined,
        error: undefined,
        metadata: undefined,
      }))
    );
    setCurrentModuleId(null);
    setOverallProgress(0);
  };

  // 计算总体进度
  useEffect(() => {
    const totalProgress = modules.reduce((sum, module) => {
      if (module.status === "completed") return sum + 100;
      if (module.status === "running") return sum + module.progress;
      return sum;
    }, 0);

    setOverallProgress(totalProgress / modules.length);
  }, [modules]);

  return {
    modules,
    currentModuleId,
    overallProgress,
    isRunning: currentModuleId !== null,
    startModule,
    completeModule,
    errorModule,
    updateProgress,
    reset,
  };
}
