"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Lightbulb,
  MessageSquare,
  Zap,
  Award,
  Target,
} from "lucide-react";

// 质量评估维度定义
export interface QualityDimension {
  id: string;
  name: string;
  description: string;
  score: number; // 0-100
  status: "excellent" | "good" | "fair" | "poor";
  icon: React.ComponentType<{ className?: string }>;
}

// 质量评估结果
export interface QualityAssessmentResult {
  overallScore: number; // 0-100
  confidence: number; // 0-1
  dimensions: QualityDimension[];
  strengths: string[];
  improvements: string[];
  suggestions: string[];
  analysisTime: number; // ms
  modelUsed: string;
}

interface QualityAssessmentProps {
  result: QualityAssessmentResult | null;
  isAnalyzing?: boolean;
  generatedText?: string;
  onRegenerate?: () => void;
  onFeedback?: (rating: number, comment: string) => void;
  className?: string;
}

// 获取评分等级
const getScoreLevel = (
  score: number
): {
  label: string;
  color: string;
  bgColor: string;
} => {
  if (score >= 90)
    return {
      label: "Excellent",
      color: "text-green-700",
      bgColor: "bg-green-100",
    };
  if (score >= 75)
    return {
      label: "Good",
      color: "text-blue-700",
      bgColor: "bg-blue-100",
    };
  if (score >= 60)
    return {
      label: "Fair",
      color: "text-yellow-700",
      bgColor: "bg-yellow-100",
    };
  return {
    label: "Needs Improvement",
    color: "text-red-700",
    bgColor: "bg-red-100",
  };
};

// 维度图标映射
const getDimensionIcon = (dimensionId: string) => {
  const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
    humor: Star,
    compliance: CheckCircle,
    originality: Lightbulb,
    relevance: Target,
    language_quality: MessageSquare,
  };
  return iconMap[dimensionId] || AlertCircle;
};

export function QualityAssessment({
  result,
  isAnalyzing = false,
  generatedText,
  onRegenerate,
  onFeedback,
  className,
}: QualityAssessmentProps) {
  const [userRating, setUserRating] = React.useState<number>(0);
  const [showFeedback, setShowFeedback] = React.useState<boolean>(false);

  if (isAnalyzing) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5 animate-spin" />
            Quality Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center text-muted-foreground">
              Analyzing generated content quality...
            </div>
            <div className="space-y-2">
              {[
                "Humor Assessment",
                "Compliance Check",
                "Originality Analysis",
                "Relevance Score",
                "Language Quality",
              ].map((dimension, index) => (
                <div key={dimension} className="flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full bg-muted animate-pulse" />
                  <span className="text-sm text-muted-foreground">
                    {dimension}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5" />
            Quality Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            Generate content to see quality assessment
          </div>
        </CardContent>
      </Card>
    );
  }

  const overallLevel = getScoreLevel(result.overallScore);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5" />
            Quality Assessment
          </CardTitle>
          <Badge className={`${overallLevel.bgColor} ${overallLevel.color}`}>
            {overallLevel.label}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 总体评分 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Quality Score</span>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold">{result.overallScore}</span>
              <span className="text-muted-foreground">/100</span>
            </div>
          </div>
          <Progress value={result.overallScore} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Poor</span>
            <span>Excellent</span>
          </div>
        </div>

        <Separator />

        {/* 维度评估 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Quality Dimensions</h4>
          <div className="space-y-3">
            {result.dimensions.map((dimension) => {
              const IconComponent = getDimensionIcon(dimension.id);
              const dimensionLevel = getScoreLevel(dimension.score);

              return (
                <div key={dimension.id} className="flex items-center gap-3">
                  <IconComponent className="w-4 h-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">
                        {dimension.name}
                      </span>
                      <span className="text-sm font-mono">
                        {dimension.score}
                      </span>
                    </div>
                    <Progress value={dimension.score} className="h-1" />
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <Separator />

        {/* 优点与改进建议 */}
        <div className="grid gap-4 md:grid-cols-2">
          {/* 优点 */}
          {result.strengths.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                Strengths
              </h4>
              <ul className="space-y-1">
                {result.strengths.map((strength, index) => (
                  <li
                    key={index}
                    className="text-sm text-muted-foreground flex items-start gap-2"
                  >
                    <span className="w-1 h-1 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                    {strength}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* 改进建议 */}
          {result.improvements.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Lightbulb className="w-4 h-4 text-orange-600" />
                Improvements
              </h4>
              <ul className="space-y-1">
                {result.improvements.map((improvement, index) => (
                  <li
                    key={index}
                    className="text-sm text-muted-foreground flex items-start gap-2"
                  >
                    <span className="w-1 h-1 rounded-full bg-orange-500 mt-2 flex-shrink-0" />
                    {improvement}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* AI建议 */}
        {result.suggestions.length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Zap className="w-4 h-4 text-blue-600" />
                AI Suggestions
              </h4>
              <div className="space-y-2">
                {result.suggestions.map((suggestion, index) => (
                  <div
                    key={index}
                    className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg"
                  >
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      {suggestion}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        <Separator />

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* 用户评分 */}
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map((rating) => (
                <Button
                  key={rating}
                  variant="ghost"
                  size="sm"
                  className="p-1"
                  onClick={() => setUserRating(rating)}
                >
                  <Star
                    className={`w-4 h-4 ${
                      rating <= userRating
                        ? "text-yellow-500 fill-current"
                        : "text-muted-foreground"
                    }`}
                  />
                </Button>
              ))}
            </div>
            {userRating > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFeedback(!showFeedback)}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Feedback
              </Button>
            )}
          </div>

          {onRegenerate && (
            <Button variant="outline" size="sm" onClick={onRegenerate}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Regenerate
            </Button>
          )}
        </div>

        {/* 反馈表单 */}
        {showFeedback && userRating > 0 && (
          <div className="p-4 bg-muted/50 rounded-lg space-y-3">
            <h5 className="text-sm font-medium">Your Feedback</h5>
            <textarea
              className="w-full p-2 text-sm border rounded-md resize-none"
              rows={3}
              placeholder="Share your thoughts on this generation..."
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFeedback(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  // Handle feedback submission
                  setShowFeedback(false);
                }}
              >
                Submit
              </Button>
            </div>
          </div>
        )}

        {/* 分析信息 */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Analysis completed in {result.analysisTime}ms</span>
            <span>Confidence: {Math.round(result.confidence * 100)}%</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
