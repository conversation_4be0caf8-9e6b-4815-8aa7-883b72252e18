/**
 * AI 描述生成系统 - 组件导出
 */

// 核心组件
export { AIDescriptionEngine } from "./AIDescriptionEngine";
export {
  ModuleProgressTracker,
  useModuleProgress,
  DEFAULT_MODULES,
} from "./ModuleProgressTracker";
export { QualityAssessment } from "./QualityAssessment";

// 类型导出
export type { UserPreferences } from "@/lib/ai/types";
export type { ModuleStatus, ModuleInfo } from "./ModuleProgressTracker";
export type { QualityAssessmentResult } from "./QualityAssessment";

/**
 * AI 系统类型定义
 */
export interface GenerationOptions {
  forceRefresh?: boolean;
  quickMode?: boolean;
}

export interface ApiResponse {
  status: "success" | "error";
  message?: string;
  data?: GenerationResult;
  debug_info?: any;
}

/**
 * 模块输出的统一接口
 */
export interface ModuleOutput {
  status: "success" | "error";
  content: any;
  error_details?: any;
  debug_info?: any;
}

/**
 * 最终生成结果
 */
export interface GenerationResult {
  final_description: string;
  debug_info: {
    analyzer: ModuleOutput;
    strategist: ModuleOutput;
    writer: ModuleOutput;
    critic: ModuleOutput;
  };
}

// API Response Types
export interface GenerationResponse {
  success: boolean;
  data?: GenerationResult;
  error?: string;
  requestId: string;
}

// Stream Events
export type StreamEvent =
  | { type: "module_start"; module: string }
  | { type: "module_progress"; module: string; progress: number }
  | { type: "module_complete"; module: string; result: any }
  | { type: "generation_complete"; result: GenerationResult }
  | { type: "error"; error: string };

// 工具函数
export const formatExecutionTime = (ms: number): string => {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(1)}s`;
};

export const getConfidenceLevel = (
  confidence: number
): "low" | "medium" | "high" => {
  if (confidence < 0.4) return "low";
  if (confidence < 0.7) return "medium";
  return "high";
};

export const getConfidenceColor = (confidence: number): string => {
  if (confidence < 0.4) return "text-red-600";
  if (confidence < 0.7) return "text-yellow-600";
  return "text-green-600";
};

export type AIAction =
  | { type: "start_generation"; options: GenerationOptions }
  | { type: "generation_complete"; result: GenerationResult }
  | { type: "error"; error: string };
