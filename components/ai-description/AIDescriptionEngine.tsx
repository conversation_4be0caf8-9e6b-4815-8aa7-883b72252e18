"use client";

import { useState, useCallback, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, AlertCircle } from "lucide-react";
import toast from "react-hot-toast";
import {
  QualityAssessment,
  type QualityAssessmentResult,
} from "./QualityAssessment";
import { useAIStream } from "@/hooks/useAIStream";
import { AdaptiveStreamingLogEntry } from "@/components/shared";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { UserData } from "@/types/user-data";

interface AIDescriptionEngineProps {
  userId: string;
  username: string;
  currentDescription?: string;
  isProUser: boolean;
  onDescriptionUpdate?: (description: string) => void;
}

export function AIDescriptionEngine({
  userId,
  currentDescription,
  isProUser,
  onDescriptionUpdate,
}: AIDescriptionEngineProps) {
  // GitHub数据状态
  const [githubData, setGithubData] = useState<UserData | null>(null);
  const [isLoadingGithubData, setIsLoadingGithubData] = useState(false);

  // 质量评估状态 - 仅Pro用户
  const [qualityResult, setQualityResult] =
    useState<QualityAssessmentResult | null>(null);
  const [isAnalyzingQuality, setIsAnalyzingQuality] = useState(false);

  // 流式输出Hook
  const {
    isStreaming,
    currentStage,
    accumulatedContent,
    finalResult,
    error,
    events,
    startStream,
    stopStream,
    resetState,
  } = useAIStream({
    onComplete: (result: string) => {
      onDescriptionUpdate?.(result);

      // 自动质量评估（Pro功能）
      if (isProUser && result) {
        setTimeout(() => {
          performQualityAssessment(result, null);
        }, 500);
      }

      toast.success("AI描述生成成功！");
    },
    onError: (errorMsg: string) => {
      toast.error(`描述生成失败: ${errorMsg}`);
    },
  });

  // 获取GitHub数据
  const fetchGitHubData = useCallback(async () => {
    if (!userId) {
      toast.error("用户ID不存在");
      return;
    }

    setIsLoadingGithubData(true);
    try {
      const response = await fetch("/api/github-data", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      const result = (await response.json()) as any;

      if (result.success && result.data?.userData) {
        setGithubData(result.data.userData);
        toast.success("GitHub数据获取成功");
      } else {
        throw new Error(result.error || "获取GitHub数据失败");
      }
    } catch (error) {
      console.error("GitHub data fetch error:", error);
      toast.error("获取GitHub数据失败，请稍后重试");
    } finally {
      setIsLoadingGithubData(false);
    }
  }, [userId]);

  // 质量评估 - 简化版
  const performQualityAssessment = useCallback(
    async (generatedText: string, _debugInfo?: any) => {
      if (!isProUser || !generatedText) return;

      setIsAnalyzingQuality(true);

      try {
        // 模拟质量评估 - 实际应该调用CriticModule
        await new Promise((resolve) => setTimeout(resolve, 2000));

        const mockResult: QualityAssessmentResult = {
          overallScore: Math.floor(Math.random() * 30) + 70, // 70-100
          confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
          dimensions: [
            {
              id: "humor",
              name: "幽默度",
              description: "内容趣味性",
              score: 85,
              status: "excellent",
              icon: Sparkles,
            },
            {
              id: "compliance",
              name: "符合度",
              description: "风格匹配",
              score: 78,
              status: "good",
              icon: Bot,
            },
          ],
          strengths: ["创意独特", "语言自然"],
          improvements: ["可以更幽默一些"],
          suggestions: ["增加具体数据引用"],
          analysisTime: 1500,
          modelUsed: "doubao-pro-4k",
        };

        setQualityResult(mockResult);
      } catch (error) {
        console.error("Quality assessment failed:", error);
      } finally {
        setIsAnalyzingQuality(false);
      }
    },
    [isProUser]
  );

  // 主生成函数 - 使用流式输出
  const generateDescription = useCallback(
    async (_forceRegenerate = false) => {
      if (!githubData) {
        toast.error("请先获取GitHub数据");
        return;
      }

      try {
        // 启动流式生成，使用默认配置
        await startStream({
          sessionId: `ai-desc-${Date.now()}-${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          githubData,
          userPreferences: {
            perspective: "first_person",
            targetEmotion: "humble",
            style: "auto comedy, humor intensity: 70%",
            includeWords: [],
            excludeWords: [],
          },
          options: {
            optimizationEnabled: isProUser,
            enableStreaming: true,
          },
        });
      } catch (error) {
        console.error("Generation failed:", error);
        toast.error("描述生成失败");
      }
    },
    [githubData, isProUser, startStream]
  );

  // 自动获取GitHub数据
  useEffect(() => {
    if (userId && !githubData && !isLoadingGithubData) {
      fetchGitHubData();
    }
  }, [userId, githubData, isLoadingGithubData, fetchGitHubData]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Bot className="w-5 h-5" />
            <span>AI 描述生成</span>
            {isProUser && <Badge variant="outline">Pro</Badge>}
          </CardTitle>
          <div className="flex items-center space-x-2">
            {isStreaming && (
              <Badge variant="outline" className="animate-pulse">
                生成中...
              </Badge>
            )}
            {finalResult && (
              <Badge variant="outline" className="bg-green-50 text-green-700">
                已完成
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 错误显示 */}
        {error && (
          <div className="flex items-center space-x-2 p-3 bg-destructive/10 rounded-lg">
            <AlertCircle className="w-5 h-5 text-destructive" />
            <span className="text-sm text-destructive">{error}</span>
          </div>
        )}

        {/* AI 生成过程实时日志展示 - 优化版本 */}
        {(isStreaming ||
          finalResult ||
          accumulatedContent ||
          events.length > 0) && (
          <div className="space-y-4">
            <div className="border rounded-lg bg-gray-900 text-gray-100">
              <div className="p-3 border-b border-gray-700 bg-gray-800">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-200">
                    🤖 AI 生成过程
                    {events.length > 0 && (
                      <span className="ml-2 text-xs text-gray-400">
                        ({events.length} 条事件)
                      </span>
                    )}
                  </h3>
                  <div className="flex items-center space-x-2">
                    {isStreaming && (
                      <div className="flex items-center space-x-2 text-xs text-blue-400">
                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-blue-400 border-t-transparent" />
                        <span>正在生成...</span>
                      </div>
                    )}
                    {currentStage && (
                      <Badge
                        variant="outline"
                        className="text-xs bg-purple-900/30 border-purple-500/30 text-purple-300"
                      >
                        当前阶段: {currentStage}
                      </Badge>
                    )}
                    {finalResult && (
                      <Badge
                        variant="outline"
                        className="text-xs bg-green-900/30 border-green-500/30 text-green-300"
                      >
                        ✅ 已完成
                      </Badge>
                    )}
                  </div>
                </div>

                {/* 进度条 - 基于新统一日志系统的智能进度 */}
                {isStreaming && (
                  <div className="mt-2">
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>整体进度</span>
                      <span>
                        {Math.round(
                          events.length > 0
                            ? (events.filter((e) => e.type === "stage_complete")
                                .length /
                                4) *
                                100
                            : 0
                        )}
                        %
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-1.5">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full transition-all duration-300"
                        style={{
                          width: `${Math.round(
                            events.length > 0
                              ? (events.filter(
                                  (e) => e.type === "stage_complete"
                                ).length /
                                  4) *
                                  100
                              : 0
                          )}%`,
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              <ScrollArea className="h-[400px]">
                <div className="p-4 space-y-3">
                  {/* 显示所有事件的完整日志 - 优化渲染 */}
                  {events.length > 0
                    ? events
                        // 🎯 优化排序：先按时间戳，再按序列号，确保严格顺序
                        .sort((a, b) => {
                          // 首先按时间戳排序
                          const timestampDiff = a.timestamp - b.timestamp;
                          if (timestampDiff !== 0) {
                            return timestampDiff;
                          }

                          // 时间戳相同时，按序列号排序（确保顺序稳定）
                          const aSequence = a.sequenceId ?? 0;
                          const bSequence = b.sequenceId ?? 0;
                          return aSequence - bSequence;
                        })
                        .slice(-50)
                        .map((event, index) => {
                          return (
                            <AdaptiveStreamingLogEntry
                              key={`${event.sessionId}-${event.timestamp}-${
                                event.sequenceId ?? index
                              }`}
                              log={event}
                              streamingContent={
                                // 如果是内容块且是最新的且正在流式传输，显示累积内容
                                event.type === "content_chunk" &&
                                index === events.slice(-50).length - 1 &&
                                isStreaming
                                  ? accumulatedContent
                                  : event.data.content
                              }
                              isStreaming={
                                event.type === "content_chunk" &&
                                index === events.slice(-50).length - 1 &&
                                isStreaming
                              }
                            />
                          );
                        })
                    : // 如果没有事件但有内容，创建一个临时的流式显示
                      accumulatedContent && (
                        <AdaptiveStreamingLogEntry
                          log={{
                            type: "content_chunk",
                            stage: (currentStage || "writer") as any,
                            data: { content: accumulatedContent },
                            timestamp: Date.now(),
                            sessionId: "temp",
                          }}
                          streamingContent={accumulatedContent}
                          isStreaming={isStreaming}
                        />
                      )}

                  {/* 如果没有任何内容，显示等待状态 */}
                  {events.length === 0 &&
                    !accumulatedContent &&
                    isStreaming && (
                      <div className="text-center text-gray-500 py-8">
                        <div className="animate-spin rounded-full h-6 w-6 border-2 border-gray-400 border-t-transparent mx-auto mb-2" />
                        <p className="text-sm">🚀 启动 AI 模块管道...</p>
                        <p className="text-xs text-gray-600 mt-1">
                          使用统一日志系统进行智能监控
                        </p>
                      </div>
                    )}

                  {/* 性能统计信息 - 如果有最终结果 */}
                  {finalResult && events.length > 0 && (
                    <div className="mt-4 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                      <div className="text-xs text-gray-400 space-y-1">
                        <div className="flex justify-between">
                          <span>📊 生成统计:</span>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>事件总数: {events.length}</div>
                          <div>
                            已完成阶段:{" "}
                            {
                              events.filter((e) => e.type === "stage_complete")
                                .length
                            }
                            /4
                          </div>
                          <div>内容长度: {finalResult.length} 字符</div>
                          <div>
                            平均响应:{" "}
                            {events.length > 1
                              ? Math.round(
                                  (events[events.length - 1].timestamp -
                                    events[0].timestamp) /
                                    events.length
                                )
                              : 0}
                            ms
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Pro用户质量评估 */}
            {isProUser && qualityResult && (
              <QualityAssessment
                result={qualityResult}
                isAnalyzing={isAnalyzingQuality}
              />
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2">
          {!githubData ? (
            <Button
              onClick={fetchGitHubData}
              disabled={isLoadingGithubData}
              className="flex-1"
            >
              {isLoadingGithubData ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  获取数据中...
                </>
              ) : (
                <>
                  <Bot className="w-4 h-4 mr-2" />
                  获取GitHub数据
                </>
              )}
            </Button>
          ) : (
            <>
              <Button
                onClick={() => generateDescription(false)}
                disabled={isStreaming}
                className="flex-1"
              >
                {isStreaming ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    生成描述
                  </>
                )}
              </Button>

              {(finalResult || accumulatedContent) && (
                <Button
                  variant="outline"
                  onClick={() => generateDescription(true)}
                  disabled={isStreaming}
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              )}

              {isStreaming && (
                <Button variant="destructive" onClick={stopStream} size="sm">
                  停止
                </Button>
              )}

              {(finalResult || error) && (
                <Button variant="outline" onClick={resetState} size="sm">
                  重置
                </Button>
              )}
            </>
          )}
        </div>

        {/* 当前描述显示 */}
        {currentDescription && (
          <div className="text-xs text-muted-foreground">
            <p>当前描述: {currentDescription}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
