"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { motion } from "framer-motion";

type LeaderboardItemProps = {
  item: {
    rank: number;
    userId: string;
    username: string;
    displayName?: string;
    avatarUrl: string;
    contributionScore: number;
    contributionGrade: string;
  };
  isCurrentUser: boolean;
};

export function LeaderboardItem({ item, isCurrentUser }: LeaderboardItemProps) {
  const [prevRank, setPrevRank] = useState(item.rank);
  const [rankChange, setRankChange] = useState(0);

  // Ensure contributionScore is always treated as a number
  const score =
    typeof item.contributionScore === "number" ? item.contributionScore : 0;

  useEffect(() => {
    if (prevRank !== item.rank) {
      setRankChange(prevRank - item.rank);
      setPrevRank(item.rank);
    }
  }, [item.rank, prevRank]);

  // 判断排名变化
  const getRankChangeIcon = () => {
    if (rankChange > 0) {
      return <span className="text-xs text-green-500">↑{rankChange}</span>;
    } else if (rankChange < 0) {
      return (
        <span className="text-xs text-red-500">↓{Math.abs(rankChange)}</span>
      );
    }
    return null;
  };

  // 排名颜色
  const getRankStyle = (rank: number) => {
    if (rank === 1) return "text-yellow-400 font-bold";
    if (rank === 2) return "text-slate-300 font-bold";
    if (rank === 3) return "text-amber-600 font-bold";
    return "text-[#8b949e]";
  };

  return (
    <motion.div
      key={item.userId}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`grid grid-cols-12 px-4 py-3 items-center ${
        isCurrentUser ? "bg-[#1d2431]" : ""
      } hover:bg-[#1d2431] transition-colors`}
    >
      <div className="flex flex-col text-center col-span-1 items-center">
        <span className={getRankStyle(item.rank)}>{item.rank}</span>
        {rankChange !== 0 && getRankChangeIcon()}
      </div>

      <div className="flex gap-2 col-span-5 items-center">
        <div className="rounded-full h-8 w-8 relative overflow-hidden">
          <Image
            src={item.avatarUrl}
            alt={item.username}
            fill
            className="object-cover"
          />
        </div>
        <div className="flex flex-col">
          <span className="font-medium truncate">{item.username}</span>
          {item.displayName && (
            <span className="text-xs text-[#8b949e] truncate">
              {item.displayName}
            </span>
          )}
        </div>
      </div>

      <div className="font-mono font-medium text-right pr-4 col-span-3">
        {score.toLocaleString()}
      </div>

      <div className="font-mono font-medium text-right pr-4 col-span-3">
        {item.contributionGrade}
      </div>
    </motion.div>
  );
}
