"use client";

import { motion } from "framer-motion";
import Image from "next/image";

type CurrentUserRankProps = {
  currentUser: {
    rank: number;
    userId: string;
    username: string;
    displayName?: string;
    avatarUrl: string;
    contributionScore: number;
    contributionGrade: string;
  };
};

export function CurrentUserRank({ currentUser }: CurrentUserRankProps) {
  // Ensure contributionScore is always treated as a number
  const score =
    typeof currentUser.contributionScore === "number"
      ? currentUser.contributionScore
      : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.5 }}
      className="bg-[#1d2431] border-t-4 border-blue-500 grid py-3 px-4 grid-cols-12 items-center"
    >
      <div className="text-center col-span-1">
        <span className="font-bold text-blue-400">{currentUser.rank}</span>
      </div>

      <div className="flex gap-2 col-span-5 items-center">
        <div className="rounded-full border-2 border-blue-500 h-8 w-8 relative overflow-hidden">
          <Image
            src={currentUser.avatarUrl}
            alt={currentUser.username}
            fill
            className="object-cover"
          />
        </div>
        <div className="flex flex-col">
          <span className="font-medium truncate">{currentUser.username}</span>
          {currentUser.displayName && (
            <span className="text-xs text-[#8b949e] truncate">
              {currentUser.displayName}
            </span>
          )}
        </div>
      </div>

      <div className="font-mono font-medium text-right pr-4 col-span-3">
        {score.toLocaleString()}
      </div>

      <div className="font-mono font-medium text-right pr-4 col-span-3">
        {currentUser.contributionGrade}
      </div>
    </motion.div>
  );
}
