import { getLeaderboardAction } from "@/app/actions/getLeaderboard";
import { LeaderboardItem } from "./LeaderboardItem";
import { CurrentUserRank } from "./CurrentUserRank";

export default async function LeaderboardList() {
  // 这里 env 会自动注入（open-next/app router/Cloudflare Workers 环境下）
  // 但如果没有自动注入，可以在页面 Server Component 里传递
  const data = await getLeaderboardAction();

  const { leaderboard, currentUser, totalUsers, lastUpdated } = data;

  const formattedLastUpdated = new Date(lastUpdated).toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <div>
      <div className="border-b flex bg-[#0d1117] border-[#30363d] py-3 px-4 justify-between items-center">
        <div className="font-semibold text-lg">
          Total participants: {totalUsers}
        </div>
        <div className="text-sm text-[#8b949e]">
          Updated at: {formattedLastUpdated}
        </div>
      </div>

      <div className="divide-y divide-[#21262d]">
        <div className="font-medium grid py-3 px-4 text-[#8b949e] grid-cols-12">
          <div className="text-center col-span-1">#</div>
          <div className="col-span-5">User</div>
          <div className="text-right pr-4 col-span-3">Grade</div>
        </div>

        {leaderboard.length > 0 ? (
          <>
            {leaderboard.map((item: any) => (
              <LeaderboardItem
                key={item.userId}
                item={item}
                isCurrentUser={item.userId === data.currentUser?.userId}
              />
            ))}

            {currentUser &&
              !leaderboard.some(
                (item: any) => item.userId === currentUser.userId
              ) && (
                <>
                  <div className="text-center py-2 px-4 text-[#8b949e] italic">
                    ・・・
                  </div>
                  <CurrentUserRank currentUser={currentUser} />
                </>
              )}
          </>
        ) : (
          <div className="text-center py-10 text-[#8b949e]">No data yet</div>
        )}
      </div>
    </div>
  );
}
