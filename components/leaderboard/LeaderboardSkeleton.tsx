"use client";

export function LeaderboardSkeleton() {
  return (
    <div>
      <div className="border-b flex bg-[#0d1117] border-[#30363d] py-3 px-4 justify-between items-center">
        <div className="rounded bg-[#21262d] h-6 animate-pulse w-32"></div>
        <div className="rounded bg-[#21262d] h-4 animate-pulse w-48"></div>
      </div>

      <div className="divide-y divide-[#21262d]">
        <div className="font-medium grid py-3 px-4 text-[#8b949e] grid-cols-12">
          <div className="text-center col-span-1">#</div>
          <div className="col-span-3">User</div>
          <div className="text-right pr-4 col-span-8">Contribution Score</div>
        </div>

        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} className="grid py-3 px-4 grid-cols-12 items-center">
            <div className="flex col-span-1 justify-center">
              <div className="rounded bg-[#21262d] h-5 animate-pulse w-5"></div>
            </div>
            <div className="flex gap-2 col-span-3 items-center">
              <div className="rounded-full bg-[#21262d] h-8 animate-pulse w-8"></div>
              <div className="flex flex-col gap-1">
                <div className="rounded bg-[#21262d] h-4 animate-pulse w-20"></div>
                <div className="rounded bg-[#21262d] h-3 animate-pulse w-16"></div>
              </div>
            </div>
            <div className="flex pr-4 col-span-8 justify-end">
              <div className="rounded bg-[#21262d] h-5 animate-pulse w-16"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
