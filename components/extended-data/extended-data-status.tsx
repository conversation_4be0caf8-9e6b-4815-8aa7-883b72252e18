"use client";

import { useExtendedData } from "@/hooks/use-extended-data";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, RefreshCw, CheckCircle, XCircle, Clock } from "lucide-react";
import { useSession } from "next-auth/react";

interface ExtendedDataStatusProps {
  className?: string;
  showDetails?: boolean;
}

/**
 * 扩展数据状态显示组件
 * 
 * 功能：
 * - 显示扩展数据的当前状态
 * - 提供手动刷新按钮
 * - 显示最后更新时间
 */
export function ExtendedDataStatus({ 
  className = "", 
  showDetails = true 
}: ExtendedDataStatusProps) {
  const { data: session } = useSession();
  const { status, isLoading, error, triggerUpdate, checkStatus } = useExtendedData();

  // 如果用户未登录，不显示组件
  if (!session?.user) {
    return null;
  }

  const getStatusBadge = () => {
    if (!status) {
      return <Badge variant="secondary">Unknown</Badge>;
    }

    if (status.updateStatus === "updating") {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Loader2 className="h-3 w-3 animate-spin" />
          Updating
        </Badge>
      );
    }

    if (status.updateStatus === "failed") {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Failed
        </Badge>
      );
    }

    if (status.exists && !status.expired) {
      return (
        <Badge variant="default" className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Up to date
        </Badge>
      );
    }

    if (status.expired || !status.exists) {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          Needs update
        </Badge>
      );
    }

    return <Badge variant="secondary">Unknown</Badge>;
  };

  const handleRefresh = async () => {
    try {
      await triggerUpdate();
    } catch (err) {
      console.error("Failed to trigger update:", err);
    }
  };

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "Never";
    return new Date(timestamp).toLocaleString();
  };

  if (!showDetails) {
    // 简化版本，只显示状态和刷新按钮
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {getStatusBadge()}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading}
          className="h-6 px-2"
        >
          {isLoading ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <RefreshCw className="h-3 w-3" />
          )}
        </Button>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm">Extended Data Status</CardTitle>
            <CardDescription className="text-xs">
              GitHub extended data for AI features
            </CardDescription>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 text-xs">
          {status && (
            <>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last updated:</span>
                <span>{formatDate(status.lastFetchedAt)}</span>
              </div>
              {status.expiresAt && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Expires:</span>
                  <span>{formatDate(status.expiresAt)}</span>
                </div>
              )}
              {status.errorMessage && (
                <div className="text-destructive text-xs">
                  Error: {status.errorMessage}
                </div>
              )}
            </>
          )}
          {error && (
            <div className="text-destructive text-xs">
              Error: {error}
            </div>
          )}
        </div>
        
        <div className="flex gap-2 mt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh
              </>
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={checkStatus}
            disabled={isLoading}
          >
            Check Status
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 简化的状态指示器，适合放在导航栏或工具栏
 */
export function ExtendedDataIndicator({ className = "" }: { className?: string }) {
  return (
    <ExtendedDataStatus 
      className={className} 
      showDetails={false} 
    />
  );
}
