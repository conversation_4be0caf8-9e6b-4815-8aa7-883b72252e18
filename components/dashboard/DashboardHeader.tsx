"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { GithubButton } from "@/components/auth/github-button";
import { Menu } from "lucide-react";
import Logo from "@/public/logo.png";

interface DashboardHeaderProps {
  onMobileMenuToggle: () => void;
}

export function DashboardHeader({ onMobileMenuToggle }: DashboardHeaderProps) {
  return (
    <header className="bg-[#0d1117]/95 backdrop-blur-sm border-b border-[#21262d] px-3 py-3 sm:px-4 sm:py-4 md:px-6">
      <div className="flex items-center justify-between">
        {/* 左侧：移动端菜单按钮和Logo */}
        <div className="flex items-center gap-3 md:gap-4 min-w-0 flex-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={onMobileMenuToggle}
            className="lg:hidden text-muted-foreground hover:text-foreground p-2 h-8 w-8"
          >
            <Menu className="w-4 h-4" />
          </Button>

          {/* Logo (移动端显示) */}
          <Link
            href="/"
            className="lg:hidden flex items-center gap-2 text-white hover:text-[#c9d1d9] transition min-w-0"
          >
            <Image
              src={Logo}
              alt="GitHub Card Logo"
              width={20}
              height={20}
              className="rounded-full flex-shrink-0"
            />
            <span className="font-semibold text-sm sm:text-base truncate">
              GitHub Card
            </span>
          </Link>
        </div>

        {/* 右侧：用户菜单 */}
        <div className="flex-shrink-0">
          <GithubButton />
        </div>
      </div>
    </header>
  );
}
