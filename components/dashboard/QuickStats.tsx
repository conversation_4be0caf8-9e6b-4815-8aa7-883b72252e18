// V5.1 Dashboard Feature - QuickStats Component
// Created: 2025-01-30
// Purpose: 展示GitHub关键数据指标的快速统计卡片

"use client";

import React from "react";
import { DashboardCard } from "./DashboardLayout";
import NumberTicker from "@/components/ui/number-ticker";
import {
  Star,
  GitFork,
  Users,
  BookOpen,
  GitCommit,
  Eye,
  TrendingUp,
  Calendar,
} from "lucide-react";
import { cn } from "@/utils";

interface QuickStatsProps {
  stats: {
    totalStars: number;
    totalForks: number;
    followers: number;
    publicRepos: number;
    totalCommits: number;
    accountAge: number;
  };
  className?: string;
  loading?: boolean;
  error?: string;
}

interface StatItemProps {
  icon: React.ReactNode;
  value: number | string;
  label: string;
  color?: string;
  animated?: boolean;
}

function StatItem({
  icon,
  value,
  label,
  color = "text-primary",
  animated = true,
}: StatItemProps) {
  return (
    <div className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-br from-background/50 to-muted/20 border border-border/50">
      <div className={cn("flex-shrink-0", color)}>{icon}</div>
      <div className="min-w-0 flex-1">
        <div className="text-lg font-bold">
          {animated && typeof value === "number" ? (
            <NumberTicker value={value} />
          ) : (
            value
          )}
        </div>
        <div className="text-xs text-muted-foreground truncate">{label}</div>
      </div>
    </div>
  );
}

export function QuickStats({
  stats,
  className,
  loading = false,
  error,
}: QuickStatsProps) {
  const statsItems = [
    {
      icon: <Star className="h-4 w-4" />,
      value: stats.totalStars,
      label: "Total Stars",
      color: "text-yellow-500",
    },
    {
      icon: <GitFork className="h-4 w-4" />,
      value: stats.totalForks,
      label: "Total Forks",
      color: "text-blue-500",
    },
    {
      icon: <Users className="h-4 w-4" />,
      value: stats.followers,
      label: "Followers",
      color: "text-green-500",
    },
    {
      icon: <BookOpen className="h-4 w-4" />,
      value: stats.publicRepos,
      label: "Public Repos",
      color: "text-purple-500",
    },
    {
      icon: <GitCommit className="h-4 w-4" />,
      value: stats.totalCommits,
      label: "Total Commits",
      color: "text-orange-500",
    },
    {
      icon: <Calendar className="h-4 w-4" />,
      value: `${stats.accountAge} Years`,
      label: "Account Age",
      color: "text-cyan-500",
      animated: false,
    },
  ];

  return (
    <DashboardCard
      title="GitHub Statistics"
      description="Key metrics from your GitHub profile"
      icon={<TrendingUp className="h-4 w-4" />}
      loading={loading}
      error={error}
      className={className}
    >
      {!loading && !error && (
        <div className="space-y-3">
          {statsItems.map((item, index) => (
            <StatItem
              key={index}
              icon={item.icon}
              value={item.value}
              label={item.label}
              color={item.color}
              animated={item.animated}
            />
          ))}
        </div>
      )}
    </DashboardCard>
  );
}
