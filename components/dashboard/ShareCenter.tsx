// V5.1 Dashboard Feature - Share Center Component
// Created: 2025-01-30
// Purpose: 分享管理中心，整合分享功能、链接管理和统计

"use client";

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSubscription } from "@/hooks/use-subscription";
import useSWR from "swr";
import { useSession } from "next-auth/react";
import toast from "react-hot-toast";
import {
  Share2,
  ExternalLink,
  Copy,
  Trash2,
  RefreshCw,
  Settings,
  Clock,
  Infinity,
  Eye,
  Calendar,
} from "lucide-react";
import { getTemplateConfig } from "@/constants/templates";
import { formatDate } from "@/utils/common";
import Loading from "@/components/loading";
import {
  ErrorResponse,
  ApiResponse as ShareLinksResponse,
  LinkData,
} from "@/app/api/share-links/user/[userId]/route";

// 分享统计数据类型
interface ShareStatsData {
  totalLinks: number;
  activeLinks: number;
  expiredLinks: number;
  mostUsedTemplate: string | null;
  oldestLink: number | null;
  newestLink: number | null;
}

interface ShareCenterProps {
  data: { sharing: ShareStatsData } | null;
  loading?: boolean;
  error?: boolean;
  className?: string;
  onRefresh?: () => void;
}

// 模拟分享链接数据类型
interface ShareLink {
  id: string;
  templateType: string;
  url: string;
  createdAt: number;
  expiresAt: number;
  isActive: boolean;
  viewCount: number;
  title: string;
}

// 格式化相对时间的简单函数
function formatTimeAgo(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor(diff / (1000 * 60));

  if (days > 0) return `${days} day${days > 1 ? "s" : ""} ago`;
  if (hours > 0) return `${hours} hour${hours > 1 ? "s" : ""} ago`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
  return "Just now";
}

export function ShareCenter({
  data,
  loading = false,
  error = false,
  className = "",
  onRefresh,
}: ShareCenterProps) {
  const { data: session } = useSession();
  const { isPro } = useSubscription();

  // 数据获取器函数
  const fetcher = (url: string) => fetch(url).then((res) => res.json());

  // 获取用户的分享链接列表
  const {
    data: shareLinksResponse,
    mutate: refreshShareLinks,
    isLoading: shareLinksLoading,
  } = useSWR<ShareLinksResponse>(
    session?.user?.id ? `/api/share-links/user/${session.user.id}` : null,
    async (url: string) => {
      const response = await fetch(url);
      return response.json() as Promise<ShareLinksResponse>;
    },
    {
      refreshInterval: 30 * 1000, // 30秒自动刷新
      revalidateOnFocus: true, // 允许页面切换时重新验证
      revalidateOnMount: true, // 组件挂载时重新验证
      dedupingInterval: 0, // 禁用去重，确保每次都能获取数据
    }
  );

  // 获取真实的分享链接数据
  const shareLinks = shareLinksResponse?.success
    ? shareLinksResponse.data.map((link: LinkData) => ({
        id: link.id,
        templateType: link.templateType,
        url: link.shareLink,
        createdAt: link.createdAt,
        expiresAt: link.expiresAt,
        isActive: link.isActive,
        viewCount: 0, // 暂时设为0，后续可以添加访问统计
        title: `${
          getTemplateConfig(link.templateType)?.name || "Unknown"
        } Analysis`,
      }))
    : [];

  const handleCopyLink = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success("Share link copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy link:", err);
      toast.error("Failed to copy link");
    }
  };

  const handleDeleteLink = async (linkId: string) => {
    try {
      const response = await fetch("/api/share-links", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ linkId }),
      });

      if (response.ok) {
        toast.success("Share link deleted successfully!");
        refreshShareLinks(); // 刷新列表
      } else {
        const errorData = (await response.json()) as ErrorResponse;
        throw new Error(errorData.error || "Failed to delete link");
      }
    } catch (err) {
      console.error("Failed to delete link:", err);
      toast.error("Failed to delete link");
    }
  };

  // 格式化具体过期日期
  const formatExpirationDate = (expiresAt: number) => {
    const date = new Date(expiresAt);
    return formatDate("M/D/YYYY h:mm A", date);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            Share Center
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <Loading />
              <p className="text-sm text-muted-foreground">
                Loading share links...
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            Share Center
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Unable to load sharing data</p>
            {onRefresh && (
              <Button
                variant="outline"
                onClick={onRefresh}
                className="mt-4"
                disabled={loading}
              >
                <RefreshCw
                  className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
                />
                Retry
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  const sharingStats = data?.sharing || {
    totalLinks: 0,
    activeLinks: 0,
    expiredLinks: 0,
    mostUsedTemplate: null,
    oldestLink: null,
    newestLink: null,
  };

  return (
    <Card className={className}>
      <CardContent className="space-y-6">
        {/* 分享统计概览 */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 p-4 rounded-lg">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {sharingStats.totalLinks}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Total Links
              </p>
            </div>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 p-4 rounded-lg">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                {sharingStats.activeLinks}
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">
                Active Links
              </p>
            </div>
          </div>
          <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 p-4 rounded-lg">
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                {sharingStats.expiredLinks}
              </p>
              <p className="text-sm text-orange-700 dark:text-orange-300">
                Expired Links
              </p>
            </div>
          </div>
        </div>

        {/* 现有分享链接列表 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-base">Your Share Links</h4>
            <Badge variant="secondary">{shareLinks.length} links</Badge>
          </div>

          <div className="space-y-3">
            {shareLinksLoading ? (
              <div className="text-center py-8">
                <div className="space-y-4">
                  <Loading />
                  <p className="text-sm text-muted-foreground">
                    Loading your share links...
                  </p>
                </div>
              </div>
            ) : shareLinks.length === 0 ? (
              <div className="text-center py-8">
                <Share2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No share links yet</p>
                <p className="text-sm text-muted-foreground">
                  Create your first share link to get started
                </p>
              </div>
            ) : (
              shareLinks.map((link) => {
                const templateConfig = getTemplateConfig(link.templateType);
                const Icon = templateConfig?.icon || Share2;

                return (
                  <div
                    key={link.id}
                    className={`p-4 rounded-lg border ${
                      link.isActive
                        ? "border-border bg-card"
                        : "border-border bg-muted/50 opacity-75"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <Icon className="w-5 h-5 text-primary mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium truncate">{link.title}</p>
                            <Badge
                              variant={link.isActive ? "default" : "secondary"}
                              className={templateConfig?.color}
                            >
                              {templateConfig?.name || "Unknown"}
                            </Badge>
                            {!link.isActive && (
                              <Badge variant="destructive">Expired</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Eye className="w-3 h-3" />
                              {link.viewCount} views
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {formatTimeAgo(link.createdAt)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              <div className="flex items-center gap-2">
                                <span
                                  className={`${
                                    isPro
                                      ? "line-through text-muted-foreground"
                                      : ""
                                  } ${
                                    link.isActive
                                      ? "text-green-600 dark:text-green-400"
                                      : "text-red-600 dark:text-red-400"
                                  }`}
                                >
                                  {formatExpirationDate(link.expiresAt)}
                                </span>
                                {isPro && (
                                  <div className="flex items-center gap-1">
                                    <Infinity className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                                    <span className="text-blue-600 dark:text-blue-400">
                                      Never expires
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-1 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(link.url, "_blank")}
                          disabled={!link.isActive}
                          title={link.isActive ? "Open link" : "Link expired"}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyLink(link.url)}
                          title="Copy link"
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteLink(link.id)}
                          className="text-destructive hover:text-destructive"
                          title="Delete link"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>

        {/* 分享提示 */}
        <div className="bg-muted/50 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <Settings className="w-5 h-5 text-muted-foreground mt-0.5" />
            <div className="text-sm">
              <p className="font-medium mb-1">Share Link Tips</p>
              <ul className="text-muted-foreground space-y-1">
                <li>• Share links expire after 30 days for security</li>
                <li>
                  • You can create multiple links with different templates
                </li>
                <li>• Track views and engagement for each shared link</li>
                <li>• Delete unused links to keep your list organized</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
