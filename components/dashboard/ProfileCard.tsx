// V5.1 Dashboard Feature - ProfileCard Component
// Created: 2025-01-30
// Purpose: 用户基本信息和多维度评分展示卡片

"use client";

import React from "react";
import { DashboardCard } from "./DashboardLayout";
import { CardHeader } from "@/components/CardHeader";
import { GradeSection } from "@/components/MultiDimension";
import { Button } from "@/components/ui/button";
import { ExternalLink, Settings, Github } from "lucide-react";
import { cn } from "@/utils";
import { MultiDimensionScore } from "@/types/multi-dimension";
import type { UserData } from "@/types/user-data";

interface ProfileCardProps {
  userProfile: UserData;
  multiDimensionScore: MultiDimensionScore;
  className?: string;
  loading?: boolean;
  error?: string;
}

export function ProfileCard({
  userProfile,
  multiDimensionScore,
  className,
  loading = false,
  error,
}: ProfileCardProps) {
  const quickActions = (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          window.open(`https://github.com/${userProfile.login}`, "_blank")
        }
        className="h-8 px-2 min-h-[44px] md:h-8 md:min-h-0"
      >
        <Github className="h-3 w-3 md:h-3 md:w-3" />
        <span className="sr-only md:not-sr-only md:ml-1">GitHub</span>
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => {
          // TODO: Navigate to settings page
          console.log("Navigate to settings");
        }}
        className="h-8 px-2 min-h-[44px] md:h-8 md:min-h-0"
      >
        <Settings className="h-3 w-3 md:h-3 md:w-3" />
        <span className="sr-only md:not-sr-only md:ml-1">Settings</span>
      </Button>
    </div>
  );

  return (
    <DashboardCard
      title="Profile Overview"
      description="Your GitHub profile and contribution scores"
      actions={quickActions}
      loading={loading}
      error={error}
      className={cn("space-y-4", className)}
    >
      {!loading && !error && (
        <div className="space-y-4">
          {/* 用户基本信息 - 复用CardHeader */}
          <div className="rounded-lg bg-gradient-to-br from-background/50 to-muted/50 p-3 md:p-4">
            <CardHeader
              userData={userProfile}
              style={1} // 使用简洁样式
              className="bg-transparent"
            />
          </div>

          {/* 多维度评分 - 复用GradeSection */}
          <div className="rounded-lg bg-gradient-to-br from-primary/5 to-secondary/5 p-3 md:p-4">
            <GradeSection
              multiDimensionScore={multiDimensionScore}
              className="text-foreground"
            />
          </div>

          {/* 快速统计 */}
          <div className="grid grid-cols-2 gap-3 pt-2">
            <div className="text-center p-3 rounded-lg bg-background/50 border border-border/50">
              <div className="text-xl md:text-2xl font-bold text-primary">
                {userProfile.totalStars.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Total Stars
              </div>
            </div>
            <div className="text-center p-3 rounded-lg bg-background/50 border border-border/50">
              <div className="text-xl md:text-2xl font-bold text-primary">
                {userProfile.publicRepos.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Public Repos
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardCard>
  );
}
