"use client";

import React, { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/utils";
import Logo from "@/public/logo.png";
import { X } from "lucide-react";
import { DASHBOARD_NAV_ITEMS } from "@/constants/dashboard-navigation";

interface DashboardMobileMenuProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentPath: string;
}

export function DashboardMobileMenu({
  open,
  onOpenChange,
  currentPath,
}: DashboardMobileMenuProps) {
  // 处理 ESC 键关闭菜单
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && open) {
        onOpenChange(false);
      }
    };

    if (open) {
      document.addEventListener("keydown", handleEscape);
      // 防止背景滚动
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [open, onOpenChange]);

  if (!open) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className={cn(
          "fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity duration-300",
          open ? "opacity-100" : "opacity-0"
        )}
        onClick={() => onOpenChange(false)}
      />

      {/* 左侧弹出菜单 */}
      <div
        className={cn(
          "fixed left-0 top-0 z-50 h-full bg-[#0d1117]/95 backdrop-blur-sm border-r border-[#21262d] shadow-xl transition-transform duration-300 ease-in-out",
          "w-fit min-w-[280px] max-w-[85vw] sm:max-w-[400px]",
          open ? "translate-x-0" : "-translate-x-full"
        )}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-[#21262d]">
          <div className="flex items-center gap-2">
            <Image
              src={Logo}
              alt="GitHub Card Logo"
              width={24}
              height={24}
              className="rounded-full"
            />
            <span className="font-semibold text-white">Dashboard Navigation</span>
          </div>
          <button
            onClick={() => onOpenChange(false)}
            className="p-1 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted/20 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 导航菜单 */}
        <div className="flex-1 overflow-auto p-4">
          <nav className="space-y-2">
            {DASHBOARD_NAV_ITEMS.map((item) => {
              const Icon = item.icon;
              const isActive = currentPath === item.href;

              return (
                <Link
                  key={item.id}
                  href={item.href}
                  onClick={() => onOpenChange(false)}
                  className={cn(
                    "flex items-center gap-3 p-3 rounded-lg transition-colors whitespace-nowrap",
                    isActive
                      ? "bg-blue-500/20 text-blue-300 border-r-2 border-blue-400 shadow-sm"
                      : "hover:bg-gray-700/50 hover:text-gray-100 text-gray-300"
                  )}
                >
                  <Icon className={cn(
                    "w-5 h-5 flex-shrink-0",
                    isActive ? "text-blue-300" : "text-gray-400 hover:text-gray-200"
                  )} />
                  <div className="flex-1 min-w-0">
                    <div className={cn(
                      "font-medium",
                      isActive ? "text-blue-200" : "text-gray-200 hover:text-white"
                    )}>
                      {item.label}
                    </div>
                    <div className={cn(
                      "text-xs transition-colors",
                      isActive
                        ? "text-blue-300/80"
                        : "text-gray-400 hover:text-gray-300"
                    )}>
                      {item.description}
                    </div>
                  </div>
                  {item.badge && (
                    <Badge variant="secondary" className={cn(
                      "text-xs ml-2 flex-shrink-0",
                      isActive ? "bg-blue-400/20 text-blue-200" : "bg-gray-600 text-gray-200"
                    )}>
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </>
  );
}
