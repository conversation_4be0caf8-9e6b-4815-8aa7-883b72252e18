// V5.1 Dashboard Feature - AIPanel Component (V5 Reconstruction)
// Created: 2025-01-30
// Purpose: AI 功能面板 - V5 重构占位符

"use client";

import React from "react";
import { DashboardCard } from "./DashboardLayout";
import {
  Construction,
  Sparkles,
  Crown,
  Brain,
  Zap,
  Bot,
  Rocket,
} from "lucide-react";
import { cn } from "@/utils";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface AIPanelProps {
  userId: string;
  username: string;
  className?: string;
  loading?: boolean;
  error?: string;
  isMobile?: boolean;
  isPro?: boolean; // 从父组件传递subscription状态
}

export function AIPanel({
  userId,
  username,
  className,
  loading = false,
  error,
  isMobile = false,
  isPro = false,
}: AIPanelProps) {
  const isProUser = isPro;

  return (
    <DashboardCard
      title="AI Profile Description"
      description="V5 Architecture Under Development"
      icon={
        <div className="flex items-center gap-1">
          <Construction className="h-4 w-4 text-amber-500" />
          {isProUser && <Crown className="h-3 w-3 text-amber-500" />}
        </div>
      }
      loading={loading}
      error={error}
      className={cn("lg:col-span-2", className)}
    >
      {!loading && !error && (
        <div className="space-y-4">
          {/* V5 重构通知 */}
          <Card className="border border-dashed border-amber-300 bg-amber-50 dark:bg-amber-950">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Construction className="w-5 h-5 text-amber-600" />
                <h3 className="font-medium text-amber-800 dark:text-amber-200">
                  V5 Four-Module Architecture
                </h3>
                <Badge
                  variant="outline"
                  className="text-xs text-amber-600 border-amber-300"
                >
                  In Development
                </Badge>
              </div>

              <p className="text-sm text-amber-700 dark:text-amber-300 mb-4">
                We're rebuilding the AI system with advanced modular
                architecture
              </p>

              {/* 四模块架构展示 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                <div className="flex flex-col items-center gap-1 p-2 bg-white dark:bg-gray-800 rounded border">
                  <Brain className="w-5 h-5 text-blue-500" />
                  <span className="text-xs font-medium">Analyzer</span>
                </div>
                <div className="flex flex-col items-center gap-1 p-2 bg-white dark:bg-gray-800 rounded border">
                  <Zap className="w-5 h-5 text-purple-500" />
                  <span className="text-xs font-medium">Strategist</span>
                </div>
                <div className="flex flex-col items-center gap-1 p-2 bg-white dark:bg-gray-800 rounded border">
                  <Bot className="w-5 h-5 text-green-500" />
                  <span className="text-xs font-medium">Writer</span>
                </div>
                <div className="flex flex-col items-center gap-1 p-2 bg-white dark:bg-gray-800 rounded border">
                  <Rocket className="w-5 h-5 text-orange-500" />
                  <span className="text-xs font-medium">Critic</span>
                </div>
              </div>

              {/* 进度条 */}
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span className="text-amber-700 dark:text-amber-300">
                    Phase 1: Cleanup
                  </span>
                  <span className="text-amber-600">60%</span>
                </div>
                <div className="w-full bg-amber-200 dark:bg-amber-900 rounded-full h-1.5">
                  <div
                    className="bg-amber-500 h-1.5 rounded-full"
                    style={{ width: "60%" }}
                  ></div>
                </div>
              </div>

              {/* Pro用户特殊说明 */}
              {isProUser && (
                <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-950 rounded border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-2">
                    <Crown className="w-4 h-4 text-blue-600" />
                    <span className="text-xs font-medium text-blue-800 dark:text-blue-200">
                      Pro Features Coming First
                    </span>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                    Advanced customization and debugging tools will be available
                    for Pro users
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 临时功能状态 */}
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded border">
              <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                Ready
              </div>
              <div className="text-xs text-blue-700 dark:text-blue-300">
                System Status
              </div>
            </div>
            <div className="p-3 bg-purple-50 dark:bg-purple-950 rounded border">
              <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
                {isProUser ? "Pro" : "Free"}
              </div>
              <div className="text-xs text-purple-700 dark:text-purple-300">
                Plan Type
              </div>
            </div>
            <div className="p-3 bg-green-50 dark:bg-green-950 rounded border">
              <div className="text-lg font-bold text-green-600 dark:text-green-400">
                16d
              </div>
              <div className="text-xs text-green-700 dark:text-green-300">
                Est. Completion
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardCard>
  );
}
