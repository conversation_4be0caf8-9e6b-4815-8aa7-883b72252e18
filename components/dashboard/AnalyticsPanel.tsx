// V5.1 Dashboard Feature - Analytics Panel Component
// Created: 2025-01-30
// Purpose: 高级数据分析面板，展示GitHub多维度分析和趋势数据

"use client";

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ActivityRadarChart } from "@/components/charts/ActivityRadarChart";
import { ConcentricSemicircleChart } from "@/components/charts/ConcentricSemicircleChart";
import { DimensionProgressBar } from "@/components/ui/DimensionProgressBar";
import { Badge } from "@/components/ui/badge";
import NumberTicker from "@/components/ui/number-ticker";
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Target,
  Award,
  Users,
  GitBranch,
  Star,
  GitFork,
  Eye,
  Calendar,
  Activity,
} from "lucide-react";
import { DashboardAnalyticsData } from "@/hooks/use-dashboard-data";

interface AnalyticsPanelProps {
  data: DashboardAnalyticsData | null;
  loading?: boolean;
  error?: boolean;
  className?: string;
}

export function AnalyticsPanel({
  data,
  loading = false,
  error = false,
  className = "",
}: AnalyticsPanelProps) {
  if (loading) {
    return (
      <Card className={`col-span-full ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Advanced Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-64 bg-muted rounded-lg"></div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-20 bg-muted rounded-lg"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className={`col-span-full ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Advanced Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Unable to load analytics data
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 准备雷达图数据
  const radarData = {
    stars: data.influenceMetrics.totalStars,
    forks: data.influenceMetrics.totalForks,
    commits: data.activityMetrics.commits,
    pullRequests: data.activityMetrics.pullRequests,
    issues: data.activityMetrics.issues,
    reviews: data.activityMetrics.reviews,
  };

  // 准备同心圆图数据（使用正确的scores格式）
  const concentricScores = {
    commitScore: Math.min(100, (data.activityMetrics.commits / 20) * 100), // 标准化到0-100
    collaborationScore: Math.min(
      100,
      ((data.activityMetrics.pullRequests + data.activityMetrics.reviews) /
        50) *
        100
    ),
    influenceScore: Math.min(
      100,
      (data.influenceMetrics.totalStars / 100) * 100
    ),
    explorationScore: Math.min(
      100,
      (data.diversityMetrics.languageCount / 20) * 100
    ),
  };

  // 计算趋势指标
  const getTrendIcon = (value: number, threshold: number = 0) => {
    return value > threshold ? (
      <TrendingUp className="w-4 h-4 text-green-500" />
    ) : (
      <TrendingDown className="w-4 h-4 text-red-500" />
    );
  };

  return (
    <Card className={`col-span-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          Advanced Analytics
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Deep insights into your GitHub development patterns
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 顶部关键指标 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  Contribution Score
                </p>
                <div className="flex items-center gap-2">
                  <NumberTicker
                    value={data.overallMetrics.contributionScore}
                    className="text-2xl font-bold text-blue-900 dark:text-blue-100"
                  />
                  {getTrendIcon(data.overallMetrics.contributionScore, 500)}
                </div>
              </div>
              <Award className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700 dark:text-green-300">
                  Percentile Rank
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {data.overallMetrics.percentileRank}%
                  </span>
                  {getTrendIcon(data.overallMetrics.percentileRank, 50)}
                </div>
              </div>
              <Target className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700 dark:text-purple-300">
                  Languages
                </p>
                <div className="flex items-center gap-2">
                  <NumberTicker
                    value={data.diversityMetrics.languageCount}
                    className="text-2xl font-bold text-purple-900 dark:text-purple-100"
                  />
                  <GitBranch className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <Activity className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700 dark:text-orange-300">
                  Activity Level
                </p>
                <p className="text-lg font-bold text-orange-900 dark:text-orange-100">
                  {data.timeMetrics.activityLevel}
                </p>
              </div>
              <Calendar className="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 活跃度雷达图 */}
          <div className="space-y-4">
            <h4 className="font-semibold text-base">
              Development Activity Radar
            </h4>
            <div className="bg-card border rounded-lg p-4">
              <ActivityRadarChart
                scores={radarData}
                size="lg"
                showTooltip={true}
                animated={true}
                theme="dark"
              />
            </div>
          </div>

          {/* 多维度同心圆图 */}
          <div className="space-y-4">
            <h4 className="font-semibold text-base">
              Multi-Dimensional Analysis
            </h4>
            <div className="bg-card border rounded-lg p-4">
              <ConcentricSemicircleChart
                scores={concentricScores}
                size="lg"
                showLabels={true}
                animationDuration={1000}
              />
            </div>
          </div>
        </div>

        {/* 维度分析详情 */}
        <div className="space-y-4">
          <h4 className="font-semibold text-base">Dimension Breakdown</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 活动维度 */}
            <div className="space-y-3">
              <h5 className="font-medium text-sm text-muted-foreground flex items-center gap-2">
                <GitBranch className="w-4 h-4" />
                Activity Metrics
              </h5>
              <div className="space-y-2">
                <DimensionProgressBar
                  dimension="commit"
                  label="Commits"
                  score={Math.min(
                    100,
                    (data.activityMetrics.commits / 20) * 100
                  )}
                />
                <DimensionProgressBar
                  dimension="collaboration"
                  label="Pull Requests"
                  score={Math.min(
                    100,
                    (data.activityMetrics.pullRequests / 10) * 100
                  )}
                />
                <DimensionProgressBar
                  dimension="collaboration"
                  label="Reviews"
                  score={Math.min(
                    100,
                    (data.activityMetrics.reviews / 10) * 100
                  )}
                />
              </div>
            </div>

            {/* 影响力维度 */}
            <div className="space-y-3">
              <h5 className="font-medium text-sm text-muted-foreground flex items-center gap-2">
                <Star className="w-4 h-4" />
                Influence Metrics
              </h5>
              <div className="space-y-2">
                <DimensionProgressBar
                  dimension="influence"
                  label="Total Stars"
                  score={Math.min(
                    100,
                    (data.influenceMetrics.totalStars / 100) * 100
                  )}
                />
                <DimensionProgressBar
                  dimension="influence"
                  label="Followers"
                  score={Math.min(
                    100,
                    (data.influenceMetrics.followers / 50) * 100
                  )}
                />
                <DimensionProgressBar
                  dimension="exploration"
                  label="Public Repos"
                  score={Math.min(
                    100,
                    (data.influenceMetrics.publicRepos / 20) * 100
                  )}
                />
              </div>
            </div>
          </div>
        </div>

        {/* 优势领域和改进建议 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 优势领域 */}
          <div className="space-y-3">
            <h5 className="font-medium text-sm text-muted-foreground flex items-center gap-2">
              <Award className="w-4 h-4" />
              Strength Areas
            </h5>
            <div className="flex flex-wrap gap-2">
              {data.overallMetrics.strengthAreas.map((area, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                >
                  {area}
                </Badge>
              ))}
            </div>
          </div>

          {/* 改进建议 */}
          <div className="space-y-3">
            <h5 className="font-medium text-sm text-muted-foreground flex items-center gap-2">
              <Target className="w-4 h-4" />
              Improvement Areas
            </h5>
            <div className="flex flex-wrap gap-2">
              {data.overallMetrics.improvementAreas.map((area, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="border-orange-200 text-orange-700 dark:border-orange-800 dark:text-orange-300"
                >
                  {area}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* 下一步里程碑 */}
        <div className="space-y-3">
          <h5 className="font-medium text-sm text-muted-foreground flex items-center gap-2">
            <Target className="w-4 h-4" />
            Next Milestones
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            {data.overallMetrics.nextMilestones.map((milestone, index) => (
              <div key={index} className="bg-muted/50 p-3 rounded-lg text-sm">
                {milestone}
              </div>
            ))}
          </div>
        </div>

        {/* 与平均用户对比 */}
        <div className="space-y-3">
          <h5 className="font-medium text-sm text-muted-foreground flex items-center gap-2">
            <Users className="w-4 h-4" />
            Comparison vs Average User
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-card border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Commits</p>
                  <p className="text-lg font-semibold">
                    {data.comparisonMetrics.vsAverageUser.commits.ratio.toFixed(
                      1
                    )}
                    x
                  </p>
                </div>
                <GitBranch className="w-5 h-5 text-blue-500" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                You: {data.comparisonMetrics.vsAverageUser.commits.user} | Avg:{" "}
                {data.comparisonMetrics.vsAverageUser.commits.average}
              </p>
            </div>

            <div className="bg-card border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Stars</p>
                  <p className="text-lg font-semibold">
                    {data.comparisonMetrics.vsAverageUser.stars.ratio.toFixed(
                      1
                    )}
                    x
                  </p>
                </div>
                <Star className="w-5 h-5 text-yellow-500" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                You: {data.comparisonMetrics.vsAverageUser.stars.user} | Avg:{" "}
                {data.comparisonMetrics.vsAverageUser.stars.average}
              </p>
            </div>

            <div className="bg-card border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Followers</p>
                  <p className="text-lg font-semibold">
                    {data.comparisonMetrics.vsAverageUser.followers.ratio.toFixed(
                      1
                    )}
                    x
                  </p>
                </div>
                <Users className="w-5 h-5 text-green-500" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                You: {data.comparisonMetrics.vsAverageUser.followers.user} |
                Avg: {data.comparisonMetrics.vsAverageUser.followers.average}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
