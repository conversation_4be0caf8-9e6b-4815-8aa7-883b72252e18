"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/utils";
import Logo from "@/public/logo.png";
import { PanelLeftClose, PanelLeftOpen } from "lucide-react";
import { DASHBOARD_NAV_ITEMS, type NavItem } from "@/constants/dashboard-navigation";

interface DashboardSidebarProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
  currentPath: string;
}

interface DashboardNavItemProps {
  item: NavItem;
  collapsed: boolean;
  active: boolean;
}

function DashboardNavItem({ item, collapsed, active }: DashboardNavItemProps) {
  const Icon = item.icon;

  return (
    <Link
      href={item.href}
      className={cn(
        "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 group",
        active
          ? "bg-blue-500/20 text-blue-300 border-r-2 border-blue-400 shadow-sm"
          : "hover:bg-gray-700/50 hover:text-gray-100 text-gray-300"
      )}
    >
      <Icon className={cn(
        "w-5 h-5 flex-shrink-0",
        active ? "text-blue-300" : "text-gray-400 group-hover:text-gray-200"
      )} />

      {!collapsed && (
        <>
          <div className="flex-1 min-w-0">
            <div className={cn(
              "font-medium truncate",
              active ? "text-blue-200" : "text-gray-200 group-hover:text-white"
            )}>
              {item.label}
            </div>
            <div className={cn(
              "text-xs truncate transition-all duration-200",
              active
                ? "text-blue-300/80"
                : "text-gray-400 opacity-70 group-hover:opacity-100 group-hover:text-gray-300"
            )}>
              {item.description}
            </div>
          </div>

          {item.badge && (
            <Badge variant="secondary" className={cn(
              "text-xs",
              active ? "bg-blue-400/20 text-blue-200" : "bg-gray-600 text-gray-200"
            )}>
              {item.badge}
            </Badge>
          )}
        </>
      )}
    </Link>
  );
}

export function DashboardSidebar({
  collapsed,
  onToggleCollapse,
  currentPath,
}: DashboardSidebarProps) {
  return (
    <aside
      className={cn(
        "hidden lg:flex flex-col bg-[#0d1117]/95 backdrop-blur-sm border-r border-[#21262d] transition-all duration-300",
        collapsed ? "w-16" : "w-60"
      )}
    >
      {/* 侧边栏头部 */}
      <div className="p-4 border-b border-[#21262d]">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <Link href="/" className="flex items-center gap-2">
              <Image
                src={Logo}
                alt="GitHub Card Logo"
                width={24}
                height={24}
                className="rounded-full"
              />
              <span className="font-semibold text-white">Github Card</span>
            </Link>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="text-muted-foreground hover:text-foreground"
          >
            {collapsed ? (
              <PanelLeftOpen className="w-4 h-4" />
            ) : (
              <PanelLeftClose className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-2">
        <div className="space-y-1">
          {DASHBOARD_NAV_ITEMS.map((item) => (
            <DashboardNavItem
              key={item.id}
              item={item}
              collapsed={collapsed}
              active={currentPath === item.href}
            />
          ))}
        </div>
      </nav>

      {/* 侧边栏底部 */}
      {!collapsed && (
        <div className="p-4 border-t border-[#21262d]">
          <div className="text-xs text-muted-foreground text-center">
            GitHub Card Dashboard
          </div>
        </div>
      )}
    </aside>
  );
}
