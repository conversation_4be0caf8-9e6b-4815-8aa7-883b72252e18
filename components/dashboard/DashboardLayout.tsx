// V5.1 Dashboard Feature - Layout Component
// Created: 2025-01-30
// Purpose: 提供Dashboard的响应式网格布局系统

import React from "react";
import Link from "next/link";
import Image from "next/image";
import Logo from "@/public/logo.png";
import { cn } from "@/utils";
import { GithubButton } from "@/components/auth/github-button";

interface DashboardLayoutProps {
  children: React.ReactNode;
  className?: string;
}

interface DashboardGridProps {
  children: React.ReactNode;
  className?: string;
}

interface DashboardCardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  loading?: boolean;
  error?: string;
  actions?: React.ReactNode;
}

// Dashboard导航栏组件
function DashboardNavbar() {
  return (
    <nav className="sticky top-0 w-full bg-[#0d1117]/95 backdrop-blur-sm z-50 border-b border-[#21262d]">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        {/* 左侧：Logo */}
        <Link
          href="/"
          className="text-xl font-bold flex items-center gap-2 text-white hover:text-[#c9d1d9] transition"
        >
          <Image
            src={Logo}
            alt="GitHub Card Logo"
            width={30}
            height={30}
            className="object-cover rounded-full"
          />
          GitHub Card
        </Link>

        {/* 右侧：用户组件 - 直接复用GithubButton */}
        <GithubButton />
      </div>
    </nav>
  );
}

// 主布局容器
export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Dashboard导航栏 */}
      <DashboardNavbar />

      {/* 主内容区域 */}
      <div className={cn("px-4 py-6 md:px-6 lg:px-8", className)}>
        <div className="mx-auto max-w-7xl">{children}</div>
      </div>
    </div>
  );
}

// 响应式网格容器
export function DashboardGrid({ children, className }: DashboardGridProps) {
  return (
    <div
      className={cn(
        // 移动端：单列
        "grid grid-cols-1 gap-4",
        // 平板端：2列
        "md:grid-cols-2 md:gap-6",
        // 桌面端：3列
        "lg:grid-cols-3 lg:gap-8",
        // 超大屏：保持3列但增加间距
        "xl:gap-10",
        className
      )}
    >
      {children}
    </div>
  );
}

// Dashboard卡片组件
export function DashboardCard({
  children,
  className,
  title,
  description,
  icon,
  loading = false,
  error,
  actions,
}: DashboardCardProps) {
  return (
    <div
      className={cn(
        // 基础样式
        "relative overflow-hidden rounded-xl border",
        "bg-card text-card-foreground shadow-sm",
        // 液态玻璃效果
        "backdrop-blur-sm bg-opacity-80",
        // 悬停效果
        "transition-all duration-200 hover:shadow-md",
        // 响应式调整
        "p-4 md:p-6",
        className
      )}
    >
      {/* 卡片头部 */}
      {(title || icon || actions) && (
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            {icon && (
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10 text-primary">
                {icon}
              </div>
            )}
            <div>
              {title && (
                <h3 className="text-lg font-semibold leading-none tracking-tight">
                  {title}
                </h3>
              )}
              {description && (
                <p className="mt-1 text-sm text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
          </div>
          {actions && <div className="flex items-center gap-2">{actions}</div>}
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-3">
          <div className="flex items-center gap-2 text-sm text-destructive">
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            {error}
          </div>
        </div>
      )}

      {/* 加载状态 */}
      {loading && !error && (
        <div className="space-y-3">
          <div className="animate-pulse">
            <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </div>
      )}

      {/* 卡片内容 */}
      {!loading && !error && children}
    </div>
  );
}

// 特殊布局组件

// 全宽卡片（跨越所有列）
export function DashboardFullWidthCard({
  children,
  className,
  ...props
}: DashboardCardProps) {
  return (
    <div className="md:col-span-2 lg:col-span-3">
      <DashboardCard className={className} {...props}>
        {children}
      </DashboardCard>
    </div>
  );
}

// 半宽卡片（桌面端占2列）
export function DashboardHalfWidthCard({
  children,
  className,
  ...props
}: DashboardCardProps) {
  return (
    <div className="lg:col-span-2">
      <DashboardCard className={className} {...props}>
        {children}
      </DashboardCard>
    </div>
  );
}

// 统计数字卡片
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    trend: "up" | "down" | "neutral";
  };
  icon?: React.ReactNode;
  description?: string;
  loading?: boolean;
}

export function StatsCard({
  title,
  value,
  change,
  icon,
  description,
  loading = false,
}: StatsCardProps) {
  if (loading) {
    return (
      <DashboardCard loading={true}>
        <div></div>
      </DashboardCard>
    );
  }

  return (
    <DashboardCard>
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">
            {typeof value === "number" ? value.toLocaleString() : value}
          </p>
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
        {icon && (
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
            {icon}
          </div>
        )}
      </div>

      {change && (
        <div className="mt-4 flex items-center gap-1">
          <div
            className={cn(
              "flex items-center gap-1 text-xs font-medium",
              change.trend === "up" && "text-green-600",
              change.trend === "down" && "text-red-600",
              change.trend === "neutral" && "text-muted-foreground"
            )}
          >
            {change.trend === "up" && (
              <svg
                className="h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 17l10-10M17 7l-10 10"
                />
              </svg>
            )}
            {change.trend === "down" && (
              <svg
                className="h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 17l-10-10M7 7l10 10"
                />
              </svg>
            )}
            {Math.abs(change.value)}%
          </div>
          <span className="text-xs text-muted-foreground">vs last period</span>
        </div>
      )}
    </DashboardCard>
  );
}

// Dashboard头部
interface DashboardHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

export function DashboardHeader({
  title,
  description,
  actions,
}: DashboardHeaderProps) {
  return (
    <div className="mb-8 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && <p className="text-muted-foreground">{description}</p>}
      </div>
      {actions && <div className="flex items-center gap-2">{actions}</div>}
    </div>
  );
}

// 移动端底部导航（可选）
interface MobileNavProps {
  items: Array<{
    id: string;
    label: string;
    icon: React.ReactNode;
    active?: boolean;
    onClick?: () => void;
  }>;
}

export function MobileNav({ items }: MobileNavProps) {
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t md:hidden">
      <div className="flex items-center justify-around py-2">
        {items.map((item) => (
          <button
            key={item.id}
            onClick={item.onClick}
            className={cn(
              "flex flex-col items-center gap-1 px-3 py-2 text-xs",
              "transition-colors duration-200",
              item.active
                ? "text-primary"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            <div className="h-5 w-5">{item.icon}</div>
            <span>{item.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
