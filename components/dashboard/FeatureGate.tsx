// V5.1 Dashboard Feature - FeatureGate Component
// Created: 2025-01-30
// Purpose: Pro功能权限控制组件，提供统一的功能访问控制

"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Crown, Lock, Sparkles, ArrowRight, CheckCircle } from "lucide-react";
import { cn } from "@/utils";
import { useSubscription } from "@/hooks/use-subscription";

interface FeatureGateProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  showUpgradePrompt?: boolean;
  upgradePromptProps?: {
    title?: string;
    description?: string;
    features?: string[];
    ctaText?: string;
  };
}

interface UpgradePromptProps {
  title?: string;
  description?: string;
  features?: string[];
  ctaText?: string;
  className?: string;
}

// 默认升级提示组件
function DefaultUpgradePrompt({
  title = "Upgrade to Pro",
  description = "Unlock advanced features and enhance your GitHub experience",
  features = [
    "AI-powered profile descriptions",
    "Advanced analytics and insights",
    "Priority support",
    "Exclusive templates and themes",
  ],
  ctaText = "Upgrade Now",
  className,
}: UpgradePromptProps) {
  const handleUpgrade = () => {
    window.location.href = "/subscription";
  };

  return (
    <Card
      className={cn(
        "border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50",
        className
      )}
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center text-lg">
            <Lock className="w-5 h-5 mr-2 text-amber-600" />
            {title}
          </CardTitle>
          <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white">
            <Crown className="w-3 h-3 mr-1" />
            Pro
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground mt-2">{description}</p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 功能列表 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-amber-800">
            Pro Features Include:
          </h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li
                key={index}
                className="flex items-center text-xs text-muted-foreground"
              >
                <CheckCircle className="w-3 h-3 mr-2 text-green-500 flex-shrink-0" />
                {feature}
              </li>
            ))}
          </ul>
        </div>

        {/* 升级按钮 */}
        <Button
          onClick={handleUpgrade}
          className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600"
        >
          <Sparkles className="w-4 h-4 mr-2" />
          {ctaText}
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>

        {/* 次要信息 */}
        <p className="text-xs text-center text-muted-foreground">
          30-day money-back guarantee • Cancel anytime
        </p>
      </CardContent>
    </Card>
  );
}

// 主要的功能门控组件
export function FeatureGate({
  feature,
  children,
  fallback,
  className,
  showUpgradePrompt = true,
  upgradePromptProps = {},
}: FeatureGateProps) {
  const { canUseFeature, isLoading, isPro } = useSubscription();

  // 加载状态
  if (isLoading) {
    return (
      <div className={cn("animate-pulse", className)}>
        <div className="h-32 bg-muted rounded-lg"></div>
      </div>
    );
  }

  // 检查权限
  const hasAccess = canUseFeature(feature);

  // 有权限，显示实际内容
  if (hasAccess) {
    return <div className={className}>{children}</div>;
  }

  // 无权限，显示回退内容或升级提示
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  if (showUpgradePrompt) {
    return (
      <div className={className}>
        <DefaultUpgradePrompt {...upgradePromptProps} />
      </div>
    );
  }

  // 默认的锁定状态
  return (
    <div
      className={cn(
        "flex items-center justify-center p-8 text-center",
        className
      )}
    >
      <div className="space-y-2">
        <Lock className="w-8 h-8 mx-auto text-muted-foreground" />
        <p className="text-sm text-muted-foreground">
          This feature requires a Pro subscription
        </p>
      </div>
    </div>
  );
}

// 便捷的Pro功能包装器
export function ProFeature({
  children,
  className,
  ...upgradeProps
}: {
  children: React.ReactNode;
  className?: string;
} & UpgradePromptProps) {
  return (
    <FeatureGate
      feature="advanced_analytics"
      className={className}
      upgradePromptProps={upgradeProps}
    >
      {children}
    </FeatureGate>
  );
}

// 检查用户是否为Pro用户的Hook
export function useIsProUser() {
  const { isPro, isLoading } = useSubscription();
  return { isPro, isLoading };
}
