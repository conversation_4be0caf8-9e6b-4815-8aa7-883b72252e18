/**
 * 维度类型定义
 */
export type DimensionType =
  | "commit"
  | "collaboration"
  | "influence"
  | "exploration";

/**
 * 维度键值映射
 */
export type DimensionKey =
  | "commitScore"
  | "collaborationScore"
  | "influenceScore"
  | "explorationScore";

/**
 * 维度配置接口
 */
export interface DimensionConfig {
  key: DimensionType;
  scoreKey: DimensionKey;
  label: string;
  description: string;
  shortName: string;
  colors: {
    light: string;
    dark: string;
    bg: string;
    bgEnd: string;
    border: string;
    text: string;
    score: string;
  };
}

/**
 * 统一的维度配置
 */
export const DIMENSION_CONFIGS: Record<DimensionType, DimensionConfig> = {
  commit: {
    key: "commit",
    scoreKey: "commitScore",
    label: "Code Architect",
    description: "Master of code creation and repository contributions",
    shortName: "CA",
    colors: {
      light: "#ea580c",
      dark: "#ff6b35",
      bg: "rgba(255, 107, 53, 0.5)",
      bgEnd: "rgba(255, 107, 53, 0.3)",
      border: "rgba(255, 107, 53, 0.4)",
      text: "text-orange-100",
      score: "text-orange-200",
    },
  },
  collaboration: {
    key: "collaboration",
    scoreKey: "collaborationScore",
    label: "Community Builder",
    description:
      "Expert in fostering collaboration through Pull Requests, Issues, and code reviews",
    shortName: "CB",
    colors: {
      light: "#d97706",
      dark: "#ffd23f",
      bg: "rgba(255, 210, 63, 0.5)",
      bgEnd: "rgba(255, 210, 63, 0.3)",
      border: "rgba(255, 210, 63, 0.4)",
      text: "text-amber-100",
      score: "text-amber-200",
    },
  },
  influence: {
    key: "influence",
    scoreKey: "influenceScore",
    label: "Open Source Pioneer",
    description:
      "Leader in creating impactful projects that inspire and engage the community",
    shortName: "OSP",
    colors: {
      light: "#0891b2",
      dark: "#06ffa5",
      bg: "rgba(6, 255, 165, 0.5)",
      bgEnd: "rgba(6, 255, 165, 0.3)",
      border: "rgba(6, 255, 165, 0.4)",
      text: "text-cyan-100",
      score: "text-cyan-200",
    },
  },
  exploration: {
    key: "exploration",
    scoreKey: "explorationScore",
    label: "Innovation Explorer",
    description:
      "Adventurer in diverse technologies and cutting-edge development practices",
    shortName: "IE",
    colors: {
      light: "#2563eb",
      dark: "#4d8cff",
      bg: "rgba(77, 140, 255, 0.5)",
      bgEnd: "rgba(77, 140, 255, 0.3)",
      border: "rgba(77, 140, 255, 0.4)",
      text: "text-blue-100",
      score: "text-blue-200",
    },
  },
} as const;

/**
 * 获取所有维度配置
 */
export function getAllDimensionConfigs(): DimensionConfig[] {
  return Object.values(DIMENSION_CONFIGS);
}

/**
 * 获取指定维度的配置
 */
export function getDimensionConfig(dimension: DimensionType): DimensionConfig {
  return DIMENSION_CONFIGS[dimension];
}

/**
 * 获取维度标签
 */
export function getDimensionLabel(dimension: DimensionType): string {
  return DIMENSION_CONFIGS[dimension].label;
}

/**
 * 获取维度描述
 */
export function getDimensionDescription(dimension: DimensionType): string {
  return DIMENSION_CONFIGS[dimension].description;
}

/**
 * 获取维度颜色配置
 */
export function getDimensionColors(dimension: DimensionType) {
  return DIMENSION_CONFIGS[dimension].colors;
}
