import { LucideIcon, Share2, Bar<PERSON><PERSON><PERSON>, <PERSON>, Eye } from "lucide-react";
import PreviewLinktree from "@/public/preview/linktree.png";
import PreviewContribute from "@/public/preview/contribute.png";
import PreviewFlomo from "@/public/preview/flomo.jpg";
import PreviewMultiDimension from "@/public/preview/multidimension.jpg";
import PreviewActivity from "@/public/preview/activity.png";
import PreviewDimension from "@/public/preview/dimension.jpg";
import PreviewStatistics from "@/public/preview/statistics.jpg";

// 模板类型定义
export type TemplateType =
  | "linktree"
  | "contribute"
  | "flomo"
  | "multidimension"
  | "activity"
  | "dimension"
  | "statistics";

// 模板配置接口
export interface TemplateConfig {
  name: string;
  description: string;
  icon: LucideIcon;
  color: string;
  image: any;
  value: TemplateType;
  tag?: string;
}

// 统一的模板配置数据
export const TEMPLATES: TemplateConfig[] = [
  {
    name: "Linktree",
    description: "A beautiful card showcasing your social links",
    image: PreviewLinktree,
    value: "linktree",
    icon: Share2,
    color:
      "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
  },
  {
    name: "Contribution",
    description: "Highlight your GitHub contributions and stats",
    image: PreviewContribute,
    value: "contribute",
    icon: BarChart3,
    color:
      "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200",
  },
  {
    name: "Flomo",
    description: "A beautiful card showcasing your social links",
    image: PreviewFlomo,
    tag: "New",
    value: "flomo",
    icon: Share2,
    color: "bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-200",
  },
  {
    name: "Multi-Dimension",
    description:
      "Comprehensive developer scoring across four dimensions to showcase your technical expertise",
    image: PreviewMultiDimension,
    tag: "V4",
    value: "multidimension",
    icon: BarChart3,
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  },
  {
    name: "Activity",
    description: "Showcase your GitHub activity and contributions",
    image: PreviewActivity,
    value: "activity",
    icon: Calendar,
    color:
      "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
  },
  {
    name: "Dimension",
    description: "Showcase your GitHub dimension and contributions",
    image: PreviewDimension,
    value: "dimension",
    icon: Eye,
    color: "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
  },
  {
    name: "Statistics",
    description: "Showcase your GitHub statistics and contributions",
    image: PreviewStatistics,
    value: "statistics",
    icon: BarChart3,
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  },
];

// 从数组生成的映射对象，用于快速查找
export const TEMPLATE_CONFIGS: Record<TemplateType, TemplateConfig> =
  TEMPLATES.reduce((acc, template) => {
    acc[template.value] = template;
    return acc;
  }, {} as Record<TemplateType, TemplateConfig>);

// 工具函数
export function getTemplateConfig(templateType: string): TemplateConfig | null {
  return TEMPLATE_CONFIGS[templateType as TemplateType] || null;
}

export function getTemplateName(templateType: string): string {
  return TEMPLATE_CONFIGS[templateType as TemplateType]?.name || "Unknown";
}

export function isValidTemplateType(
  templateType: string
): templateType is TemplateType {
  return templateType in TEMPLATE_CONFIGS;
}
