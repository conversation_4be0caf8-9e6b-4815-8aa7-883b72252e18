import { ImageSource } from "../types/image-source";

// 新的图片源配置系统
export const IMAGE_SOURCES: ImageSource[] = [
  {
    id: "bimg-cc",
    name: "Bimg.cc Random",
    type: "direct",
    url: "https://api.bimg.cc/random?w=1920&h=1080&mkt=zh-CN",
    weight: 3, // 降低权重，优先使用API源
    enabled: true,
  },
  {
    id: "pexels-nature",
    name: "Pexels Nature",
    type: "pexels",
    url: "https://api.pexels.com/v1/curated",
    weight: 6,
    config: {
      orientation: "portrait",
      size: "large",
      per_page: 25,
      keywords: [
        "nature",
        "landscape",
        "wildlife",
        "forest",
        "ocean",
        "mountains",
      ],
      exclude_keywords: ["person", "people", "human", "face", "portrait"],
    },
    enabled: !!process.env.PEXELS_API_KEY,
  },
  {
    id: "pixabay-nature",
    name: "Pixabay Nature",
    type: "pixabay",
    url: "https://pixabay.com/api/",
    weight: 8, // 最高权重，内容质量好且过滤严格
    config: {
      orientation: "portrait",
      size: "large",
      image_type: "photo",
      category: "nature",
      min_width: 1080,
      min_height: 1920,
      per_page: 20,
      safesearch: true,
      order: "popular",
      keywords: [
        "forest",
        "trees",
        "flowers",
        "wildlife",
        "birds",
        "mountains",
        "ocean",
        "lake",
        "sunset",
        "galaxy",
        "stars",
        "aurora",
      ],
      exclude_keywords: [
        "person",
        "people",
        "human",
        "face",
        "portrait",
        "man",
        "woman",
        "child",
        "family",
        "business",
        "fashion",
        "sport",
      ],
    },
    enabled: !!process.env.PIXABAY_API_KEY,
  },
  {
    id: "pixabay-space",
    name: "Pixabay Space",
    type: "pixabay",
    url: "https://pixabay.com/api/",
    weight: 7,
    config: {
      orientation: "portrait",
      size: "large",
      image_type: "photo",
      category: "backgrounds",
      min_width: 1080,
      min_height: 1920,
      per_page: 15,
      safesearch: true,
      order: "popular",
      keywords: [
        "galaxy",
        "stars",
        "nebula",
        "space",
        "astronomy",
        "milky way",
        "constellation",
        "aurora",
        "night sky",
        "cosmos",
      ],
      exclude_keywords: ["person", "people", "human"],
    },
    enabled: !!process.env.PIXABAY_API_KEY,
  },
  // Unsplash 图片源配置
  {
    id: "unsplash-nature",
    name: "Unsplash Nature",
    type: "unsplash",
    url: "https://api.unsplash.com/search/photos",
    weight: 8,
    config: {
      orientation: "portrait",
      size: "large",
      per_page: 25,
      order: "relevant",
      keywords: [
        "nature",
        "landscape",
        "forest",
        "mountain",
        "ocean",
        "sky",
        "trees",
        "flowers",
        "sunset",
        "sunrise",
      ],
      exclude_keywords: ["person", "people", "human", "face", "portrait"],
    },
    enabled: !!process.env.UNSPLASH_ACCESS_KEY,
  },
  {
    id: "unsplash-space",
    name: "Unsplash Space",
    type: "unsplash",
    url: "https://api.unsplash.com/search/photos",
    weight: 7,
    config: {
      orientation: "portrait",
      size: "large",
      per_page: 20,
      order: "relevant",
      keywords: [
        "galaxy",
        "stars",
        "nebula",
        "space",
        "astronomy",
        "milky way",
        "constellation",
        "aurora",
        "night sky",
        "cosmos",
      ],
      exclude_keywords: ["person", "people", "human"],
    },
    enabled: !!process.env.UNSPLASH_ACCESS_KEY,
  },
  {
    id: "unsplash-abstract",
    name: "Unsplash Abstract",
    type: "unsplash",
    url: "https://api.unsplash.com/search/photos",
    weight: 6,
    config: {
      orientation: "portrait",
      size: "large",
      per_page: 20,
      order: "relevant",
      keywords: [
        "abstract",
        "patterns",
        "textures",
        "geometric",
        "minimalist",
        "colors",
        "gradient",
        "artistic",
      ],
      exclude_keywords: ["person", "people", "human", "face"],
    },
    enabled: !!process.env.UNSPLASH_ACCESS_KEY,
  },
];
