// 清理效率优化配置 - Phase 4.3
// 基于Research和Plan阶段的分析结果

export const CLEANUP_OPTIMIZATION = {
  // Phase 1: 并发控制策略
  CONCURRENCY: 5, // 最优并发数 (基于Cloudflare Workers限制)
  TIMEOUT_MS: 30000, // 30秒超时
  RETRY_ATTEMPTS: 3, // 失败重试次数
  RETRY_DELAY: 1000, // 重试间隔 (毫秒)

  // Phase 2: 批量操作配置
  BATCH_SIZE: 10, // 批量操作大小
  MAX_BATCH_SIZE: 50, // 最大批量大小
  BATCH_TIMEOUT: 45000, // 批量操作超时

  // Phase 3: 监控配置
  ENABLE_METRICS: true, // 性能监控开关
  LOG_PERFORMANCE: true, // 性能日志记录
  METRICS_INTERVAL: 1000, // 监控数据收集间隔

  // 错误处理策略
  FAIL_FAST: false, // 单点失败不中断整体
  MAX_ERRORS: 5, // 最大允许错误数
  ERROR_THRESHOLD: 0.1, // 10%错误率阈值

  // 性能优化参数
  CHUNK_SIZE: 5, // 并发块大小
  PARALLEL_LIMIT: 10, // 并行操作限制
  MEMORY_THRESHOLD: 0.8, // 内存使用阈值
} as const;

// 清理操作结果类型扩展
export interface EnhancedCleanupResult {
  // 基础清理结果
  removedFromQueue: number;
  removedFromR2: number;
  freedSpace: number;
  errors: string[];

  // 性能监控数据
  totalDuration: number; // 总耗时
  parallelDuration: number; // 并行处理耗时
  batchOperations: number; // 批量操作次数
  concurrencyUtilization: number; // 并发利用率

  // 错误分析
  r2Errors: number; // R2删除错误数
  kvErrors: number; // KV删除错误数
  timeoutErrors: number; // 超时错误数
  retryCount: number; // 重试次数

  // 优化指标
  performanceGain: number; // 性能提升倍数
  apiCallReduction: number; // API调用减少数量
}

// 并发控制配置接口
export interface ConcurrencyConfig {
  concurrency: number;
  batchSize: number;
  timeoutMs: number;
  retryAttempts: number;
  retryDelay: number;
  enableMetrics: boolean;
}

// 默认并发配置
export const DEFAULT_CONCURRENCY_CONFIG: ConcurrencyConfig = {
  concurrency: CLEANUP_OPTIMIZATION.CONCURRENCY,
  batchSize: CLEANUP_OPTIMIZATION.BATCH_SIZE,
  timeoutMs: CLEANUP_OPTIMIZATION.TIMEOUT_MS,
  retryAttempts: CLEANUP_OPTIMIZATION.RETRY_ATTEMPTS,
  retryDelay: CLEANUP_OPTIMIZATION.RETRY_DELAY,
  enableMetrics: CLEANUP_OPTIMIZATION.ENABLE_METRICS,
};
