// V5.1 Dashboard Feature - Centralized Navigation Configuration
// Created: 2025-06-22
// Purpose: 统一的Dashboard导航配置，避免重复定义

import { Bot, BarChart3, Share2, User, Settings, TestTube } from "lucide-react";

export interface NavItem {
  id: string;
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  badge?: string;
}

export const DASHBOARD_NAV_ITEMS: NavItem[] = [
  {
    id: "overview",
    label: "Profile",
    href: "/dashboard",
    icon: User,
    description: "Manage your profile settings and GitHub information",
  },
  {
    id: "ai",
    label: "AI Features",
    href: "/dashboard/ai",
    icon: Bot,
    description: "AI description generation and customization",
    badge: "Pro",
  },
  {
    id: "test-4",
    label: "AI Debug",
    href: "/dashboard/test-4",
    icon: TestTube,
    description: "V5 AI module debugging and optimization platform",
    badge: "Beta",
  },
  {
    id: "analytics",
    label: "Analytics",
    href: "/dashboard/analytics",
    icon: BarChart3,
    description: "Advanced GitHub data analysis",
  },
  {
    id: "share",
    label: "Share Center",
    href: "/dashboard/share",
    icon: Share2,
    description: "Manage sharing links and templates",
  },
  {
    id: "settings",
    label: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
    description: "Account and app settings",
  },
];

/**
 * 根据路径获取当前导航项
 */
export function getCurrentNavItem(pathname: string): NavItem | undefined {
  return DASHBOARD_NAV_ITEMS.find((item) => item.href === pathname);
}

/**
 * 检查路径是否匹配导航项
 */
export function isNavItemActive(item: NavItem, pathname: string): boolean {
  return item.href === pathname;
}
