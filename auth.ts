import NextAuth from "next-auth";
import GitHub<PERSON>rovider from "next-auth/providers/github";
import { getDb } from "./lib/db";
import * as schema from "./lib/db/schema";
import { users, userSubscriptions } from "./lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import type { SQL } from "drizzle-orm";
import { eq } from "drizzle-orm";

// 配置 Auth.js - 使用纯JWT策略
export const { auth, handlers, signIn, signOut } = NextAuth({
  providers: [
    GitHubProvider({
      clientId: process.env.GITHUB_ID as string,
      clientSecret: process.env.GITHUB_SECRET as string,
      profile(profile) {
        return {
          id: profile.id.toString(),
          name: profile.name ?? profile.login,
          email: profile.email,
          image: profile.avatar_url,
          username: profile.login,
          githubId: profile.id.toString(),
          avatarUrl: profile.avatar_url,
        };
      },
    }),
  ],
  // 禁用数据库适配器，纯使用JWT
  adapter: undefined,
  // 使用JWT策略
  session: {
    strategy: "jwt",
    maxAge: 3 * 24 * 60 * 60, // 3天
  },
  callbacks: {
    async jwt({ token, user, account, profile }) {
      // console.log("jwt callback", { token, user, account, profile });

      // 确保 token 有必要的属性
      const jwtToken = token as any;

      // 如果不是登录流程，直接返回现有 token
      if (!account || !profile) {
        return jwtToken;
      }

      try {
        // 1. 首先尝试从数据库获取用户
        const db = await getDb();
        console.log("Database connection established successfully");
        const githubId = (profile as any).id?.toString();

        // 2. 查找或创建用户 - 优化版本，防止竞态条件
        let dbUser = null;

        // 先尝试通过 GitHub ID 查找
        if (githubId) {
          dbUser = await db.query.users.findFirst({
            where: (users, { eq }) => eq(users.githubId, githubId),
          });
        }

        // 如果没找到用户，创建一个新用户
        if (!dbUser && githubId) {
          console.log("Creating new user for GitHub ID:", githubId);
          try {
            const userId = uuidv4();
            const now = new Date().getTime();
            const profileName = (profile as any).name || (profile as any).login;

            // 创建新用户
            const [newUser] = await db
              .insert(users)
              .values({
                id: userId,
                name: profileName,
                email: (profile as any).email || null,
                emailVerified: now,
                image: (profile as any).avatar_url || null,
                githubId: githubId,
                username: (profile as any).login,
                displayName: profileName,
                avatarUrl: (profile as any).avatar_url,
              })
              .returning();

            if (newUser) {
              dbUser = newUser;
              console.log("New user created with ID:", dbUser.id);

              // 限时特惠：新用户自动送一年 pro 订阅
              try {
                const planPro = await db.query.subscriptionPlans.findFirst({
                  where: (plans, { eq, and }) =>
                    and(
                      eq(plans.id, "price_1RLyWiHFsKiAaOeKsUiYwzra" as string)
                    ),
                });
                if (!planPro) {
                  console.error("Failed to find Pro plan");
                } else if (!planPro.stripePriceId) {
                  console.error("Pro plan is missing stripePriceId");
                } else {
                  const subscriptionData = {
                    id: uuidv4(),
                    userId: dbUser.id,
                    planId: planPro.id,
                    status: "active" as const,
                    priceId: planPro.stripePriceId,
                    currentPeriodStart: now,
                    currentPeriodEnd: now + 365 * 24 * 60 * 60 * 1000, // 一年后
                    cancelAtPeriodEnd: true,
                    createdAt: now,
                    updatedAt: now,
                  };

                  await db.insert(userSubscriptions).values(subscriptionData);

                  console.log(
                    "Pro subscription created:",
                    JSON.stringify(subscriptionData)
                  );
                }
              } catch (subscriptionError) {
                console.error(
                  "Error creating subscription:",
                  subscriptionError
                );
                // 不要抛出错误，让用户创建继续
              }
            }
          } catch (error) {
            console.error("Error creating user:", error);
            // 如果创建失败，再次尝试查找用户（可能是并发创建导致的错误）
            if (githubId) {
              dbUser = await db.query.users.findFirst({
                where: (users, { eq }) => eq(users.githubId, githubId),
              });
            }
          }
        }

        // 3. 使用数据库中的用户ID
        if (dbUser) {
          // 确保使用数据库中的用户ID
          const dbUserId = dbUser.id;
          console.log("Using database user ID in JWT:", dbUserId);

          // 使用类型断言确保类型安全
          const dbUserAny = dbUser as Record<string, any>;
          const profileAny = profile as Record<string, any>;

          // 更新 JWT token 中的用户信息
          const updatedToken = {
            ...jwtToken,
            userId: dbUserId,
            sub: dbUserId, // 确保 sub 和 id 一致
            username: String(dbUserAny.username || profileAny?.login || ""),
            githubId: String(
              dbUserAny.githubId || dbUserAny.github_id || githubId || ""
            ),
            avatarUrl: String(
              dbUserAny.avatarUrl ||
                dbUserAny.avatar_url ||
                profileAny?.avatar_url ||
                ""
            ),
            name: String(
              dbUserAny.name || profileAny?.name || profileAny?.login || ""
            ),
            email: String(dbUserAny.email || profileAny?.email || ""),
            picture: String(
              dbUserAny.image ||
                dbUserAny.avatar_url ||
                profileAny?.avatar_url ||
                ""
            ),
            createdAt: Number(dbUserAny.createdAt || dbUserAny.created_at || 0),
          };

          console.log("JWT token updated with DB user ID:", dbUserId);
          return updatedToken;
        } else {
          console.error("Failed to find or create user in database");
          throw new Error("Unable to find or create user in database");
        }
      } catch (error) {
        console.error("Error in JWT callback:", error);
        throw error;
      }
    },

    async session({ session, token }) {
      try {
        if (token && session.user) {
          // 确保所有 token 信息传递到 session
          session.user.id = (token.userId as string) || (token.sub as string);
          session.user.name = (token.name as string) || session.user.name;
          session.user.email = (token.email as string) || session.user.email;
          session.user.image = (token.picture as string) || session.user.image;
          session.user.username = token.username as string;
          session.user.githubId = token.githubId as string;
          session.user.avatarUrl = token.avatarUrl as string;
          session.user.createdAt = token.createdAt as number;
        }
        return session;
      } catch (e) {
        console.error("session callback error", e);
        throw e;
      }
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV !== "production",
  trustHost: true,
  // 修改 cookie 配置以适应 Cloudflare Workers
  cookies: {
    sessionToken: {
      name: "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    csrfToken: {
      name: "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    callbackUrl: {
      name: "next-auth.callback-url",
      options: {
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
});
