# .env.local
# Next.js 环境配置
NEXTJS_ENV=development

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=2wRxxrhdtW4tvZLZM9VEc36bh+rfwTf4uRxgFeEiF/I=

# GitHub OAuth
GITHUB_ID=********************
GITHUB_SECRET=****************************************
GITHUB_TOKENS=*********************************************************************************************

# Stripe配置
STRIPE_SECRET_KEY=sk_test_51RLyT2HFsKiAaOeKjMuEnYHGQKrfBtVMv9HUtwbf6omOto7ZzomeUtRDk1roMNHxkw2cvktB3yOc6AzjW5fGmkS400T3eQzHbY
STRIPE_WEBHOOK_SECRET=whsec_9HYszJrgrYGhepM0m2YAA4Drx4kkLgKH
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RLyT2HFsKiAaOeKxuQLQlEiINfi6qxP7DD71a19RfoUOTdvf2TDOp54lSoh7SyAc99Vz0Xwcmuk3DnFwl7P496v001Jcvz36H

# 多维度评分 - 语言多样性分析
ENABLE_DETAILED_LANGUAGE_ANALYSIS=true
MAX_LANGUAGE_ANALYSIS_REPOS=50

# Pexels API 设置
PEXELS_API_KEY=sdCKHrhpHihjmJodJGB0ZDdreTEeR4RvGIJoRWtiq27SEsf3w4cJdTG8

# PIXABAY 设置
PIXABAY_API_KEY=**********************************

# UNSPLASH 设置
UNSPLASH_ACCESS_KEY=lqn-C5Aar2v1lr0sitPWqO8BhxtO0ewDfnqFz28oPYw

# admin 设置
REFRESH_CACHE_TOKEN=123

# Cloudflare R2 Storage
R2_PUBLIC_URL=https://pub-xxxxx.r2.dev

# AI 配置 - 动态多提供商支持
# 🎯 现在支持每次调用时动态指定provider和model，不再需要LLM_PROVIDER全局配置

# Doubao AI 配置 - Doubao 1.6 大模型
DOUBAO_API_KEY=2842976b-8294-4e0d-bf4e-36f271099168

# OpenRouter AI 配置 - 智能路由和多模型支持
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
OPENROUTER_APP_NAME=GitHub-Card
OPENROUTER_DEFAULT_MODEL=openai/gpt-4o
OPENROUTER_SITE_URL=https://github-card.refined-x.workers.dev

# OPENROUTER
OPENROUTER_API_KEY=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

# Vercel AI Gateway 配置 - 统一多模型接入平台
AI_GATEWAY_API_KEY=IRstAQECtInKRlCculN87K0B
AI_GATEWAY_DEFAULT_MODEL=openai/gpt-4o
AI_GATEWAY_APP_NAME=GitHub-Card
# AI_GATEWAY_BASE_URL=https://ai-gateway.vercel.sh/v1  # 可选，使用自定义网关地址

GOOGLE_CLOUD_PROJECT=gen-lang-client-**********
GEMINI_API_KEY=AIzaSyC-7eLOsZuAluYTdVDhIFEE1OzVCVFgI2I

ANTHROPIC_AUTH_TOKEN=sk-d3xUc6HPX1APzkD47z6U0IexECQcz8ujbvvyPYwEOusmvJOy
ANTHROPIC_BASE_URL=https://anyrouter.top