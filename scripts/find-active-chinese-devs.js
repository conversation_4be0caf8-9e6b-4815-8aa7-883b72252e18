// scripts/find-active-chinese-devs.js
import dotenv from "dotenv";
import { writeFile } from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";

// 获取当前模块的目录名
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, "../.env") });

const GITHUB_TOKEN = process.env.GITHUB_TOKENS.split(",")[0];

if (!GITHUB_TOKEN) {
  console.error("错误: 未找到 GITHUB_TOKENS 环境变量");
  process.exit(1);
}

function getDateRange() {
  const date = new Date();
  date.setFullYear(date.getFullYear() - 1); // 搜索一年内创建的用户
  return date.toISOString().split("T")[0];
}

// 获取用户详细信息
async function getUserDetails(login) {
  try {
    const response = await fetch(`https://api.github.com/users/${login}`, {
      headers: {
        Authorization: `token ${GITHUB_TOKEN}`,
        Accept: "application/vnd.github.v3+json",
      },
    });

    if (!response.ok) {
      console.error(`获取用户 ${login} 详情失败: ${response.status}`);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error(`获取用户 ${login} 详情时出错:`, error.message);
    return null;
  }
}

// 搜索活跃的中国开发者
async function findActiveChineseDevelopers() {
  const since = getDateRange();
  const url = new URL("https://api.github.com/search/users");

  // 更精确的搜索条件
  url.searchParams.append(
    "q",
    `location:China type:user followers:>100 repos:>5 sort:followers`
  );
  url.searchParams.append("per_page", "30"); // 减少每页数量以避免速率限制
  url.searchParams.append("page", "1");

  try {
    // 1. 搜索用户
    console.log("正在搜索符合条件的用户...");
    const response = await fetch(url.toString(), {
      headers: {
        Authorization: `token ${GITHUB_TOKEN}`,
        Accept: "application/vnd.github.v3+json",
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "搜索用户失败");
    }

    const data = await response.json();
    console.log(`初步找到 ${data.items.length} 位符合条件的用户`);

    // 2. 获取用户详情
    console.log("正在获取用户详情...");
    const developers = [];

    for (const item of data.items) {
      const details = await getUserDetails(item.login);
      if (!details) continue;

      // 计算活跃度得分
      const activityScore =
        (details.followers || 0) * 0.5 +
        (details.public_repos || 0) * 0.3 +
        (details.blog ? 10 : 0) +
        (details.bio ? 5 : 0);

      developers.push({
        login: item.login,
        html_url: item.html_url,
        name: details.name,
        bio: details.bio,
        blog: details.blog,
        location: details.location,
        public_repos: details.public_repos,
        followers: details.followers,
        following: details.following,
        created_at: details.created_at,
        updated_at: details.updated_at,
        activity_score: Math.round(activityScore * 10) / 10, // 保留一位小数
      });

      // 添加延迟以避免触发 GitHub API 速率限制
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    // 3. 按活跃度得分排序
    developers.sort((a, b) => b.activity_score - a.activity_score);

    console.log(`\n成功获取 ${developers.length} 位活跃开发者的详细信息`);

    // 4. 保存结果到文件
    const resultPath = path.join(__dirname, "active_chinese_devs.json");
    await writeFile(resultPath, JSON.stringify(developers, null, 2));
    console.log(`结果已保存到: ${resultPath}`);

    // 5. 打印前20位开发者信息
    console.log("\n活跃中国开发者排名前20:");
    developers.slice(0, 20).forEach((dev, index) => {
      console.log(`${index + 1}. ${dev.login} (${dev.name || "未设置名称"})`);
      console.log(
        `   关注者: ${dev.followers} | 仓库: ${dev.public_repos} | 活跃度: ${dev.activity_score}`
      );
      console.log(`   个人主页: ${dev.blog || "未设置"}`);
      console.log(`   GitHub: ${dev.html_url}\n`);
    });

    return developers;
  } catch (error) {
    console.error("发生错误:", error instanceof Error ? error.message : error);
    return [];
  }
}

// 执行搜索
findActiveChineseDevelopers().catch(console.error);
