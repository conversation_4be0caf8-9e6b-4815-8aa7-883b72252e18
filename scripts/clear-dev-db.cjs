#!/usr/bin/env node

/**
 * 🗑️ 清空本地开发数据库脚本
 *
 * 用途：清空本地开发环境的数据库数据，用于开发调试
 * 使用：yarn clear-dev-db 或 node scripts/clear-dev-db.js
 *
 * ⚠️ 警告：此脚本仅用于开发环境，不要在生产环境使用！
 */

const fs = require("fs");
const path = require("path");
const readline = require("readline");

// 颜色输出函数
const colors = {
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`,
};

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// 询问用户确认的函数
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// 检查是否为开发环境
function checkEnvironment() {
  const nodeEnv = process.env.NODE_ENV;
  const isProduction = nodeEnv === "production";

  console.log(colors.blue("🔍 环境检查..."));
  console.log(`   NODE_ENV: ${nodeEnv || "未设置"}`);

  if (isProduction) {
    console.log(colors.red("❌ 错误：检测到生产环境，脚本已终止！"));
    console.log(colors.yellow("   此脚本仅允许在开发环境使用。"));
    process.exit(1);
  }

  console.log(colors.green("✅ 环境检查通过：开发环境"));
}

// 清空数据库文件
function clearDatabaseFiles() {
  const dbPaths = [
    // SQLite 数据库文件
    "database.db",
    "database.db-journal",
    "database.db-wal",
    "database.db-shm",
    // 本地开发数据库文件
    "lib/db/dev/dev-database.db",
    "lib/db/dev/dev-database.db-journal",
    "lib/db/dev/dev-database.db-wal",
    "lib/db/dev/dev-database.db-shm",
    // Cloudflare Workers 本地数据
    ".wrangler/state/v3/d1/miniflare-D1DatabaseObject",
    ".wrangler/state/v3/d1",
    ".wrangler/state/d1",
    // 其他可能的数据库文件
    "dev.db",
    "local.db",
  ];

  const deletedFiles = [];
  const notFoundFiles = [];

  console.log(colors.blue("\n🗑️ 清理数据库文件..."));

  dbPaths.forEach((dbPath) => {
    const fullPath = path.resolve(dbPath);

    try {
      if (fs.existsSync(fullPath)) {
        const stats = fs.statSync(fullPath);

        if (stats.isDirectory()) {
          // 删除目录
          fs.rmSync(fullPath, { recursive: true, force: true });
          deletedFiles.push(`${dbPath}/ (目录)`);
        } else {
          // 删除文件
          fs.unlinkSync(fullPath);
          deletedFiles.push(dbPath);
        }
      } else {
        notFoundFiles.push(dbPath);
      }
    } catch (error) {
      console.log(colors.yellow(`   ⚠️ 无法删除 ${dbPath}: ${error.message}`));
    }
  });

  // 输出结果
  if (deletedFiles.length > 0) {
    console.log(
      colors.green(`   ✅ 已删除 ${deletedFiles.length} 个文件/目录:`)
    );
    deletedFiles.forEach((file) => {
      console.log(colors.green(`      - ${file}`));
    });
  }

  if (notFoundFiles.length > 0) {
    console.log(
      colors.cyan(
        `   ℹ️ 未找到 ${notFoundFiles.length} 个文件/目录 (可能已不存在):`
      )
    );
    notFoundFiles.forEach((file) => {
      console.log(colors.cyan(`      - ${file}`));
    });
  }
}

// 清空缓存
function clearCache() {
  const cachePaths = [
    // Next.js 缓存
    ".next",
    // Node modules 缓存
    "node_modules/.cache",
    // Yarn 缓存 (项目级)
    ".yarn/cache",
    // 其他可能的缓存目录
    ".cache",
    "dist",
    "build",
  ];

  const deletedCaches = [];
  const notFoundCaches = [];

  console.log(colors.blue("\n🧹 清理缓存文件..."));

  cachePaths.forEach((cachePath) => {
    const fullPath = path.resolve(cachePath);

    try {
      if (fs.existsSync(fullPath)) {
        fs.rmSync(fullPath, { recursive: true, force: true });
        deletedCaches.push(cachePath);
      } else {
        notFoundCaches.push(cachePath);
      }
    } catch (error) {
      console.log(
        colors.yellow(`   ⚠️ 无法删除缓存 ${cachePath}: ${error.message}`)
      );
    }
  });

  // 输出结果
  if (deletedCaches.length > 0) {
    console.log(
      colors.green(`   ✅ 已清理 ${deletedCaches.length} 个缓存目录:`)
    );
    deletedCaches.forEach((cache) => {
      console.log(colors.green(`      - ${cache}/`));
    });
  }

  if (notFoundCaches.length > 0) {
    console.log(
      colors.cyan(
        `   ℹ️ 未找到 ${notFoundCaches.length} 个缓存目录 (可能已不存在):`
      )
    );
    notFoundCaches.forEach((cache) => {
      console.log(colors.cyan(`      - ${cache}/`));
    });
  }
}

// 显示重建数据库的提示
function showRebuildInstructions() {
  console.log(colors.blue("\n📋 重建数据库步骤:"));
  console.log(colors.cyan("   1. 生成数据库迁移: yarn db:generate"));
  console.log(colors.cyan("   2. 应用数据库迁移: yarn db:push"));
  console.log(
    colors.cyan("   3. 初始化开发数据: yarn db:init-dev (如果有的话)")
  );
  console.log(colors.cyan("   4. 启动开发服务器: yarn dev"));
}

// 主函数
async function main() {
  console.log(colors.bold(colors.blue("🗑️ GitHub Card 开发数据库清理工具")));
  console.log(
    colors.yellow("⚠️ 此操作将清空所有本地开发数据，请确保已备份重要数据！\n")
  );

  try {
    // 1. 检查环境
    checkEnvironment();

    // 2. 用户确认
    const confirm1 = await askQuestion(
      colors.yellow('❓ 确定要清空本地开发数据库吗？(输入 "yes" 确认): ')
    );

    if (confirm1.toLowerCase() !== "yes") {
      console.log(colors.cyan("ℹ️ 操作已取消。"));
      rl.close();
      return;
    }

    console.log(colors.green("\n🚀 开始清理..."));

    // 3. 清空数据库文件
    clearDatabaseFiles();

    // 4. 清空缓存
    clearCache();

    // 5. 显示重建指令
    showRebuildInstructions();

    console.log(colors.bold(colors.green("\n✅ 清理完成！")));
    console.log(
      colors.cyan("💡 提示：现在可以重新初始化数据库并启动开发服务器。")
    );
  } catch (error) {
    console.log(colors.red(`\n❌ 清理过程中发生错误: ${error.message}`));
    console.log(colors.yellow("请检查文件权限或手动删除相关文件。"));
  } finally {
    rl.close();
  }
}

// 处理未捕获的异常
process.on("uncaughtException", (error) => {
  console.log(colors.red(`\n❌ 未捕获的异常: ${error.message}`));
  rl.close();
  process.exit(1);
});

process.on("SIGINT", () => {
  console.log(colors.yellow("\n\n⏹️ 用户中断操作"));
  rl.close();
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main();
}
