#!/usr/bin/env tsx

/**
 * Doubao 迁移性能测试脚本
 * 验证四个 AI 模块的性能表现
 */

import { AnalyzerModule } from "../lib/ai/core/modules/AnalyzerModule";
import { StrategistModule } from "../lib/ai/core/modules/StrategistModule";
import { WriterModule } from "../lib/ai/core/modules/WriterModule";
import { CriticModule } from "../lib/ai/core/modules/CriticModule";

// 模拟 GitHub 数据 - 使用正确的 UserData 接口
const MOCK_USER_DATA = {
  username: "test-user",
  login: "test-user",
  name: "Test User",
  avatarUrl: "https://github.com/avatar.jpg",
  bio: "Full-stack developer passionate about open source",
  blog: "https://blog.example.com",
  location: "San Francisco, CA",
  twitterUsername: "testuser",
  publicRepos: 45,
  followers: 1250,
  following: 200,
  createdAt: Date.now() - 365 * 24 * 60 * 60 * 1000,
  updatedAt: Date.now(),
  totalStars: 3500,
  commits: 2800,
  pullRequests: 180,
  issues: 95,
  reviews: 220,
  totalForks: 450,
  contributedRepos: 25,
  contributionScore: 85,
};

async function testModulePerformance() {
  console.log("🚀 开始 Doubao 迁移性能测试...\n");

  const results = {
    analyzer: { duration: 0, success: false, output: null as any },
    strategist: { duration: 0, success: false, output: null as any },
    writer: { duration: 0, success: false, output: null as any },
    critic: { duration: 0, success: false, output: null as any },
  };

  // Test 1: AnalyzerModule (算法化，预期 < 5ms)
  console.log("🔍 测试 AnalyzerModule (算法模式)...");
  try {
    const startTime = performance.now();
    const analyzerModule = new AnalyzerModule();
    const analysisResult = await analyzerModule.analyzeWithStreaming(
      MOCK_USER_DATA,
      {
        analysisDepth: "detailed",
      }
    );
    const endTime = performance.now();

    results.analyzer.duration = endTime - startTime;
    results.analyzer.success = true;
    results.analyzer.output = analysisResult;

    console.log(`  ✅ 完成时间: ${results.analyzer.duration.toFixed(2)}ms`);
    console.log(
      `  📊 分析结果: ${analysisResult?.status} - ${
        Object.keys(analysisResult?.content || {}).length
      } 个维度`
    );
  } catch (error) {
    results.analyzer.success = false;
    console.log(`  ❌ 失败: ${error}`);
  }

  // Test 2: StrategistModule (混合模式，预期 < 200ms)
  console.log("\n💡 测试 StrategistModule (混合模式)...");
  if (results.analyzer.success) {
    try {
      const startTime = performance.now();
      const strategistModule = new StrategistModule();
      const strategyResult = await strategistModule.selectStrategyWithStreaming(
        results.analyzer.output,
        {
          perspective: "first_person",
          targetEmotion: "self_deprecating",
        },
        () => {} // 测试脚本中提供空的onProgress回调
      );
      const endTime = performance.now();

      results.strategist.duration = endTime - startTime;
      results.strategist.success = true;
      results.strategist.output = strategyResult;

      console.log(`  ✅ 完成时间: ${results.strategist.duration.toFixed(2)}ms`);
      console.log(
        `  🎯 策略选择: ${
          strategyResult?.content?.selectedStrategyId || "unknown"
        }`
      );
    } catch (error) {
      results.strategist.success = false;
      console.log(`  ❌ 失败: ${error}`);
    }
  } else {
    console.log("  ⏭️  跳过 (依赖 AnalyzerModule)");
  }

  // Test 3: WriterModule (AI 模式，预期 < 3000ms)
  console.log("\n✍️  测试 WriterModule (Doubao AI 模式)...");
  if (results.strategist.success) {
    try {
      const startTime = performance.now();
      const writerModule = new WriterModule();
      const writerResult = await writerModule.generateTextWithStreaming(
        MOCK_USER_DATA,
        results.analyzer.output,
        results.strategist.output,
        {
          perspective: "first_person",
          targetEmotion: "self_deprecating",
        },
        () => {} // 空的onProgress回调，仅用于性能测试
      );
      const endTime = performance.now();

      results.writer.duration = endTime - startTime;
      results.writer.success = true;
      results.writer.output = writerResult;

      console.log(`  ✅ 完成时间: ${results.writer.duration.toFixed(2)}ms`);
      console.log(
        `  📝 生成长度: ${
          writerResult?.content?.generatedText?.length || 0
        } 字符`
      );
    } catch (error) {
      results.writer.success = false;
      console.log(`  ❌ 失败: ${error}`);
    }
  } else {
    console.log("  ⏭️  跳过 (依赖 StrategistModule)");
  }

  // Test 4: CriticModule (智能评估，预期 < 500ms)
  console.log("\n🔎 测试 CriticModule (智能评估模式)...");
  if (results.writer.success) {
    try {
      const startTime = performance.now();
      const criticModule = new CriticModule();
      const criticResult = await criticModule.evaluateTextWithStreaming(
        results.writer.output?.content?.generatedText || "test text",
        results.strategist.output,
        MOCK_USER_DATA,
        () => {} // 空的onProgress回调，仅用于性能测试
      );
      const endTime = performance.now();

      results.critic.duration = endTime - startTime;
      results.critic.success = true;
      results.critic.output = criticResult;

      console.log(`  ✅ 完成时间: ${results.critic.duration.toFixed(2)}ms`);
      console.log(
        `  📊 质量评分: ${criticResult?.content?.overallScore || 0}/100`
      );
    } catch (error) {
      results.critic.success = false;
      console.log(`  ❌ 失败: ${error}`);
    }
  } else {
    console.log("  ⏭️  跳过 (依赖 WriterModule)");
  }

  // 性能总结
  console.log("\n📈 性能测试总结:");
  console.log("=====================================");

  const totalDuration = Object.values(results).reduce(
    (sum, result) => sum + result.duration,
    0
  );
  const successCount = Object.values(results).filter((r) => r.success).length;

  console.log(`总执行时间: ${totalDuration.toFixed(2)}ms`);
  console.log(`成功模块数: ${successCount}/4`);
  console.log(`平均模块耗时: ${(totalDuration / 4).toFixed(2)}ms`);

  console.log("\n各模块详情:");
  Object.entries(results).forEach(([module, result]) => {
    const status = result.success ? "✅" : "❌";
    const benchmark = getBenchmark(module);
    const performance =
      result.duration <= benchmark
        ? "🚀 优秀"
        : result.duration <= benchmark * 2
        ? "⚡ 良好"
        : "⚠️  需优化";

    console.log(
      `  ${status} ${module.padEnd(12)}: ${result.duration
        .toFixed(2)
        .padStart(8)}ms (${performance})`
    );
  });

  // 架构评价
  console.log("\n🏆 Doubao 迁移架构评价:");
  console.log("=====================================");

  if (successCount === 4) {
    console.log("✅ 迁移成功: 所有模块正常运行");
    if (totalDuration < 4000) {
      console.log("🚀 性能优秀: 总耗时 < 4秒");
    } else if (totalDuration < 8000) {
      console.log("⚡ 性能良好: 总耗时 < 8秒");
    } else {
      console.log("⚠️  性能待优化: 总耗时过长");
    }
  } else {
    console.log("❌ 迁移存在问题: 部分模块失败");
  }

  return results;
}

function getBenchmark(module: string): number {
  const benchmarks: Record<string, number> = {
    analyzer: 5, // 算法化，预期 < 5ms
    strategist: 200, // 混合模式，预期 < 200ms
    writer: 3000, // AI 模式，预期 < 3s
    critic: 500, // 智能评估，预期 < 500ms
  };
  return benchmarks[module] || 1000;
}

// 执行测试
if (require.main === module) {
  testModulePerformance()
    .then(() => {
      console.log("\n🎉 性能测试完成!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 测试失败:", error);
      process.exit(1);
    });
}

export { testModulePerformance };
