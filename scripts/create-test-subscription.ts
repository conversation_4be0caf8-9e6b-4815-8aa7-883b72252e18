#!/usr/bin/env tsx

/**
 * 为测试用户创建 Pro 订阅
 */

import Database from "better-sqlite3";
import { drizzle } from "drizzle-orm/better-sqlite3";
import * as schema from "../lib/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import path from "path";

async function createTestSubscription() {
  console.log("🚀 创建测试 Pro 订阅...\n");

  const dbPath = path.join(process.cwd(), "lib/db/dev/dev-database.db");
  const sqlite = new Database(dbPath);
  const db = drizzle(sqlite, { schema });

  try {
    const userId = "790a5bd1-7fa5-4a64-af53-917688d5fa68";
    const planId = "price_1RLyWiHFsKiAaOeKsUiYwzra"; // 年付 Pro 计划

    // 1. 检查用户是否存在
    const user = await db.query.users.findFirst({
      where: eq(schema.users.id, userId),
    });

    if (!user) {
      console.log(`❌ 用户 ${userId} 不存在`);
      return;
    }

    console.log(`✅ 找到用户: ${user.username} (${user.name})`);

    // 2. 检查是否已有活跃订阅
    const existingSubscription = await db.query.userSubscriptions.findFirst({
      where: (sub, { eq, and }) =>
        and(eq(sub.userId, userId), eq(sub.status, "active")),
    });

    if (existingSubscription) {
      console.log(`⚠️  用户已有活跃订阅: ${existingSubscription.planId}`);
      console.log(`   状态: ${existingSubscription.status}`);
      console.log(`   到期时间: ${new Date(existingSubscription.currentPeriodEnd).toISOString()}`);
      
      // 删除现有订阅
      await db.delete(schema.userSubscriptions)
        .where(eq(schema.userSubscriptions.id, existingSubscription.id));
      console.log(`🗑️  已删除现有订阅`);
    }

    // 3. 检查计划是否存在
    const plan = await db.query.subscriptionPlans.findFirst({
      where: eq(schema.subscriptionPlans.id, planId),
    });

    if (!plan) {
      console.log(`❌ 计划 ${planId} 不存在`);
      return;
    }

    console.log(`✅ 找到计划: ${plan.name} ($${plan.price / 100}/${plan.interval})`);

    // 4. 创建 Pro 订阅
    const now = Date.now();
    const oneYearLater = now + 365 * 24 * 60 * 60 * 1000; // 一年后

    const subscriptionData = {
      id: uuidv4(),
      userId: userId,
      planId: planId,
      status: "active" as const,
      priceId: planId,
      currentPeriodStart: now,
      currentPeriodEnd: oneYearLater,
      cancelAtPeriodEnd: true,
      createdAt: now,
      updatedAt: now,
    };

    await db.insert(schema.userSubscriptions).values(subscriptionData);

    console.log(`\n✅ Pro 订阅创建成功！`);
    console.log(`   订阅ID: ${subscriptionData.id}`);
    console.log(`   用户: ${user.username}`);
    console.log(`   计划: ${plan.name}`);
    console.log(`   状态: ${subscriptionData.status}`);
    console.log(`   开始时间: ${new Date(subscriptionData.currentPeriodStart).toISOString()}`);
    console.log(`   到期时间: ${new Date(subscriptionData.currentPeriodEnd).toISOString()}`);
    console.log(`   自动取消: ${subscriptionData.cancelAtPeriodEnd}`);

    // 5. 验证订阅
    const createdSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(schema.userSubscriptions.id, subscriptionData.id),
      with: {
        plan: true,
      },
    });

    if (createdSubscription) {
      console.log(`\n🔍 验证订阅:`);
      console.log(`   订阅状态: ${createdSubscription.status}`);
      console.log(`   计划名称: ${createdSubscription.plan?.name}`);
      console.log(`   是否包含Pro: ${createdSubscription.plan?.name?.toLowerCase().includes("pro") ? "✅ 是" : "❌ 否"}`);
    }

  } catch (error) {
    console.error("❌ 创建订阅时发生错误:", error);
  } finally {
    sqlite.close();
  }
}

createTestSubscription()
  .then(() => {
    console.log("\n✅ 操作完成");
    process.exit(0);
  })
  .catch((error) => {
    console.error("\n❌ 操作失败:", error);
    process.exit(1);
  });
