-- 修复 contribute_datas 表结构
PRAGMA foreign_keys=OFF;

-- 创建新的表结构
CREATE TABLE contribute_datas_new (
  id TEXT PRIMARY KEY NOT NULL,
  user_id TEXT NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
  login TEXT NOT NULL,
  name TEXT,
  avatar_url TEXT NOT NULL,
  bio TEXT,
  blog TEXT,
  location TEXT,
  twitter_username TEXT,
  public_repos INTEGER NOT NULL,
  followers INTEGER NOT NULL,
  following INTEGER NOT NULL,
  user_created_at INTEGER NOT NULL,
  total_stars INTEGER NOT NULL,
  contribution_score INTEGER NOT NULL,
  commits INTEGER NOT NULL,
  pull_requests INTEGER NOT NULL,
  issues INTEGER NOT NULL,
  reviews INTEGER NOT NULL,
  total_forks INTEGER NOT NULL DEFAULT 0,
  contributed_repos INTEGER NOT NULL DEFAULT 0,
  language_stats TEXT,
  data_version INTEGER NOT NULL DEFAULT 1,
  last_full_update INTEGER NOT NULL DEFAULT 0,
  update_status TEXT NOT NULL DEFAULT 'completed',
  last_updated INTEGER NOT NULL DEFAULT ((unixepoch('now', 'subsec') * 1000)),
  record_created_at INTEGER NOT NULL DEFAULT ((unixepoch('now', 'subsec') * 1000))
);

-- 复制数据（排除不需要的列）
INSERT INTO contribute_datas_new (
  id, user_id, login, name, avatar_url, bio, blog, location, twitter_username,
  public_repos, followers, following, user_created_at, total_stars, contribution_score,
  commits, pull_requests, issues, reviews, total_forks, contributed_repos,
  language_stats, data_version, last_full_update, update_status, last_updated, record_created_at
)
SELECT 
  id, user_id, login, name, avatar_url, bio, blog, location, twitter_username,
  public_repos, followers, following, user_created_at, total_stars, contribution_score,
  commits, pull_requests, issues, reviews, total_forks, contributed_repos,
  language_stats, data_version, last_full_update, update_status, last_updated, record_created_at
FROM contribute_datas;

-- 删除旧表
DROP TABLE contribute_datas;

-- 重命名新表
ALTER TABLE contribute_datas_new RENAME TO contribute_datas;

-- 创建索引
CREATE UNIQUE INDEX contribute_datas_user_id_unique ON contribute_datas (user_id);

PRAGMA foreign_keys=ON;
