#!/usr/bin/env tsx

/**
 * 为指定用户添加 Pro 订阅（用于测试）
 */

import { getDb } from "@/lib/db";
import { userSubscriptions, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

async function addProSubscription(userId: string) {
  console.log(`🔍 为用户 ${userId} 添加 Pro 订阅...\n`);

  try {
    const db = await getDb();

    // 1. 检查用户是否存在
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user) {
      console.log(`❌ 用户 ${userId} 不存在`);
      return;
    }

    console.log(`✅ 找到用户: ${user.username} (${user.name})`);

    // 2. 检查是否已有活跃订阅
    const existingSubscription = await db.query.userSubscriptions.findFirst({
      where: (sub, { eq, and }) =>
        and(eq(sub.userId, userId), eq(sub.status, "active")),
    });

    if (existingSubscription) {
      console.log(`⚠️  用户已有活跃订阅: ${existingSubscription.planId}`);
      console.log(`   状态: ${existingSubscription.status}`);
      console.log(`   到期时间: ${new Date(existingSubscription.currentPeriodEnd).toISOString()}`);
      return;
    }

    // 3. 创建 Pro 订阅
    const now = Date.now();
    const oneYearLater = now + 365 * 24 * 60 * 60 * 1000; // 一年后

    const subscriptionData = {
      id: uuidv4(),
      userId: userId,
      planId: "price_1RLyWiHFsKiAaOeKsUiYwzra", // 年付 Pro 计划
      status: "active" as const,
      priceId: "price_1RLyWiHFsKiAaOeKsUiYwzra",
      currentPeriodStart: now,
      currentPeriodEnd: oneYearLater,
      cancelAtPeriodEnd: true,
      createdAt: now,
      updatedAt: now,
    };

    await db.insert(userSubscriptions).values(subscriptionData);

    console.log(`✅ Pro 订阅创建成功！`);
    console.log(`   订阅ID: ${subscriptionData.id}`);
    console.log(`   计划ID: ${subscriptionData.planId}`);
    console.log(`   状态: ${subscriptionData.status}`);
    console.log(`   开始时间: ${new Date(subscriptionData.currentPeriodStart).toISOString()}`);
    console.log(`   到期时间: ${new Date(subscriptionData.currentPeriodEnd).toISOString()}`);
    console.log(`   自动取消: ${subscriptionData.cancelAtPeriodEnd}`);

  } catch (error) {
    console.error("❌ 添加订阅时发生错误:", error);
  }
}

// 从命令行参数获取用户ID，或使用默认的测试用户ID
const userId = process.argv[2] || "790a5bd1-7fa5-4a64-af53-917688d5fa68";

console.log("🚀 开始添加 Pro 订阅...");
console.log(`📝 目标用户ID: ${userId}\n`);

addProSubscription(userId)
  .then(() => {
    console.log("\n✅ 操作完成");
    process.exit(0);
  })
  .catch((error) => {
    console.error("\n❌ 操作失败:", error);
    process.exit(1);
  });
