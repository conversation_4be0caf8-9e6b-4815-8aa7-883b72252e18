import Database from "better-sqlite3";
import { drizzle } from "drizzle-orm/better-sqlite3";
import * as schema from "../lib/db/schema";
import path from "path";
import fs from "fs";

// 订阅计划配置 - 请根据生产环境的实际数据更新这些值
const SUBSCRIPTION_PLANS_CONFIG = [
  // 年付 Pro 计划 - 与生产环境保持一致
  {
    id: "price_1RLyWiHFsKiAaOeKsUiYwzra", // 生产环境年付Pro计划ID
    name: "GitHub Card Pro",
    description: "Unlock all premium features and unlimited access",
    price: 3600, // $36.00 in cents (年付)
    currency: "USD",
    interval: "year",
    stripePriceId: "price_1RLyWiHFsKiAaOeKsUiYwzra",
    stripeProductId: "prod_github_card_pro",
    features: [
      "All templates available",
      "Unlimited share-links",
      "Share-links that never expire",
      "Priority support",
      "Professional analytics",
      "AI-powered descriptions",
      "Custom styling options",
    ],
    isActive: true,
  },
  // 月付 Pro 计划 - 与生产环境保持一致
  {
    id: "price_1RLyWiHFsKiAaOeKsUiYwzrb", // 生产环境月付Pro计划ID
    name: "GitHub Card Pro",
    description: "Unlock all premium features and unlimited access",
    price: 900, // $9.00 in cents (月付)
    currency: "USD",
    interval: "month",
    stripePriceId: "price_1RLyWiHFsKiAaOeKsUiYwzrb",
    stripeProductId: "prod_github_card_pro",
    features: [
      "All templates available",
      "Unlimited share-links",
      "Share-links that never expire",
      "Priority support",
      "Professional analytics",
      "AI-powered descriptions",
      "Custom styling options",
    ],
    isActive: true,
  },
];

// 从 schema 定义生成 CREATE TABLE 语句
function generateCreateTableSQL() {
  const createStatements: string[] = [];

  // 用户表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY NOT NULL,
      name TEXT,
      email TEXT,
      email_verified INTEGER,
      image TEXT,
      github_id TEXT NOT NULL UNIQUE,
      username TEXT NOT NULL,
      display_name TEXT,
      avatar_url TEXT NOT NULL,
      created_at INTEGER DEFAULT (unixepoch()) NOT NULL,
      updated_at INTEGER DEFAULT (unixepoch()) NOT NULL
    )
  `);

  // 贡献数据表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS contribute_datas (
      id TEXT PRIMARY KEY NOT NULL,
      user_id TEXT NOT NULL UNIQUE,
      login TEXT NOT NULL,
      name TEXT,
      avatar_url TEXT NOT NULL,
      bio TEXT,
      blog TEXT,
      location TEXT,
      twitter_username TEXT,
      public_repos INTEGER NOT NULL,
      followers INTEGER NOT NULL,
      following INTEGER NOT NULL,
      user_created_at INTEGER NOT NULL,
      total_stars INTEGER NOT NULL,
      contribution_score INTEGER NOT NULL,
      commits INTEGER NOT NULL,
      pull_requests INTEGER NOT NULL,
      issues INTEGER NOT NULL,
      reviews INTEGER NOT NULL,
      total_forks INTEGER DEFAULT 0 NOT NULL,
      contributed_repos INTEGER DEFAULT 0 NOT NULL,
      language_stats TEXT,
      data_version INTEGER DEFAULT 1 NOT NULL,
      last_full_update INTEGER DEFAULT 0 NOT NULL,
      update_status TEXT DEFAULT 'completed' NOT NULL,
      last_updated INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      record_created_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // 用户行为表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS user_behaviors (
      id TEXT PRIMARY KEY NOT NULL,
      userId TEXT NOT NULL,
      action_type TEXT NOT NULL,
      action_data BLOB,
      performed_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // 分享链接表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS share_links (
      id TEXT PRIMARY KEY NOT NULL,
      userId TEXT NOT NULL,
      link_token TEXT NOT NULL UNIQUE,
      created_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      expires_at INTEGER NOT NULL,
      is_active INTEGER DEFAULT 1 NOT NULL,
      template_type TEXT DEFAULT 'contribute' NOT NULL,
      background_image_id TEXT,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // 订阅计划表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS subscription_plans (
      id TEXT PRIMARY KEY NOT NULL,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      price INTEGER NOT NULL,
      currency TEXT DEFAULT 'USD' NOT NULL,
      interval TEXT NOT NULL,
      features TEXT NOT NULL,
      is_active INTEGER DEFAULT 1 NOT NULL,
      stripe_price_id TEXT,
      stripe_product_id TEXT,
      created_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      updated_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL
    )
  `);

  // 用户订阅表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS user_subscriptions (
      id TEXT PRIMARY KEY NOT NULL,
      user_id TEXT NOT NULL,
      plan_id TEXT NOT NULL,
      status TEXT NOT NULL,
      price_id TEXT NOT NULL,
      current_period_start INTEGER NOT NULL,
      current_period_end INTEGER NOT NULL,
      cancel_at INTEGER,
      canceled_at INTEGER,
      cancel_at_period_end INTEGER DEFAULT 1 NOT NULL,
      created_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      updated_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT
    )
  `);

  // 支付记录表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS payments (
      id TEXT PRIMARY KEY NOT NULL,
      user_id TEXT NOT NULL,
      subscription_id TEXT,
      amount INTEGER NOT NULL,
      currency TEXT DEFAULT 'USD' NOT NULL,
      status TEXT NOT NULL,
      payment_method TEXT,
      receipt_url TEXT,
      created_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL
    )
  `);

  // 订阅日志表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS subscription_logs (
      id TEXT PRIMARY KEY NOT NULL,
      subscription_id TEXT NOT NULL,
      user_id TEXT NOT NULL,
      from_status TEXT NOT NULL,
      to_status TEXT NOT NULL,
      reason TEXT,
      metadata TEXT,
      timestamp INTEGER NOT NULL,
      created_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // AI描述表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS ai_descriptions (
      id TEXT PRIMARY KEY NOT NULL,
      user_id TEXT NOT NULL UNIQUE,
      generated_description TEXT NOT NULL,
      description_style TEXT NOT NULL,
      data_snapshot TEXT NOT NULL,
      prompt_used TEXT NOT NULL,
              ai_model_version TEXT DEFAULT 'doubao-pro-128k' NOT NULL,
      custom_description TEXT,
      is_custom_applied INTEGER DEFAULT 0 NOT NULL,
      generated_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      updated_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      expires_at INTEGER NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // 描述生成历史表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS description_history (
      id TEXT PRIMARY KEY NOT NULL,
      user_id TEXT NOT NULL,
      description_content TEXT NOT NULL,
      generation_type TEXT NOT NULL,
      style TEXT,
      created_at INTEGER DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // =================================================================
  // V5 AI 描述生成系统 - 新增表
  // =================================================================

  // 1. AI描述生成请求表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS ai_generation_requests (
      id TEXT PRIMARY KEY NOT NULL,
      user_id TEXT NOT NULL,
      preferred_style TEXT,
      context_type TEXT,
      stream_response INTEGER DEFAULT 0,
      force_regenerate INTEGER DEFAULT 0,
      status TEXT NOT NULL DEFAULT 'pending',
      current_step TEXT,
      progress_percentage INTEGER DEFAULT 0,
      step_timings TEXT,
      started_at INTEGER NOT NULL,
      completed_at INTEGER,
      error_message TEXT,
      retry_count INTEGER DEFAULT 0,
      created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // 2. 模块处理日志表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS ai_module_logs (
      id TEXT PRIMARY KEY NOT NULL,
      request_id TEXT NOT NULL,
      module_name TEXT NOT NULL,
      input_data TEXT NOT NULL,
      output_data TEXT,
      prompt_used TEXT,
      status TEXT NOT NULL,
      processing_time INTEGER,
      token_usage TEXT,
      model_version TEXT,
      error_code TEXT,
      error_message TEXT,
      quality_score INTEGER,
      confidence_score INTEGER,
      created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
      FOREIGN KEY (request_id) REFERENCES ai_generation_requests(id) ON DELETE CASCADE
    )
  `);

  // =================================================================
  // V5 扩展数据表 - GitHub扩展数据独立存储
  // =================================================================

  // 3. GitHub扩展数据表
  createStatements.push(`
    CREATE TABLE IF NOT EXISTS github_extended_datas (
      id TEXT PRIMARY KEY NOT NULL,
      user_id TEXT NOT NULL,
      repositories_data TEXT,
      commits_data TEXT,
      readme_data TEXT,
      tech_stack_files_data TEXT,
      time_stats_data TEXT,
      fetch_config TEXT,
      data_version TEXT NOT NULL DEFAULT '1.0.0',
      fetched_at INTEGER NOT NULL,
      expires_at INTEGER NOT NULL,
      update_status TEXT NOT NULL DEFAULT 'completed',
      error_message TEXT,
      repositories_count INTEGER NOT NULL DEFAULT 0,
      commits_count INTEGER NOT NULL DEFAULT 0,
      readmes_count INTEGER NOT NULL DEFAULT 0,
      tech_stack_files_count INTEGER NOT NULL DEFAULT 0,
      time_stats_count INTEGER NOT NULL DEFAULT 0,
      created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
      updated_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  return createStatements;
}

// 生成索引创建语句
function generateCreateIndexSQL() {
  const indexStatements: string[] = [];

  // 用户表索引
  indexStatements.push(
    `CREATE UNIQUE INDEX IF NOT EXISTS users_github_id_unique ON users (github_id)`
  );

  // 贡献数据表索引
  indexStatements.push(
    `CREATE UNIQUE INDEX IF NOT EXISTS contribute_datas_user_id_unique ON contribute_datas (user_id)`
  );

  // 分享链接表索引
  indexStatements.push(
    `CREATE UNIQUE INDEX IF NOT EXISTS share_links_link_token_unique ON share_links (link_token)`
  );

  // AI描述表索引
  indexStatements.push(
    `CREATE UNIQUE INDEX IF NOT EXISTS ai_descriptions_user_id_unique ON ai_descriptions (user_id)`
  );

  // GitHub扩展数据表索引
  indexStatements.push(
    `CREATE UNIQUE INDEX IF NOT EXISTS github_extended_datas_user_id_unique ON github_extended_datas (user_id)`
  );
  indexStatements.push(
    `CREATE INDEX IF NOT EXISTS github_extended_datas_update_status_idx ON github_extended_datas (update_status)`
  );
  indexStatements.push(
    `CREATE INDEX IF NOT EXISTS github_extended_datas_expires_at_idx ON github_extended_datas (expires_at)`
  );

  return indexStatements;
}

async function resetDevDatabaseFromSchema() {
  const dbPath = path.join(process.cwd(), "lib/db/dev/dev-database.db");

  console.log("🚀 Starting database reset from schema...");
  console.log("Database path:", dbPath);

  // 确保目录存在
  const dbDir = path.dirname(dbPath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
    console.log("Created database directory:", dbDir);
  }

  // 如果数据库文件已存在，先备份
  if (fs.existsSync(dbPath)) {
    const backupPath = `${dbPath}.backup.${Date.now()}`;
    fs.copyFileSync(dbPath, backupPath);
    console.log("Existing database backed up to:", backupPath);

    // 删除现有数据库文件
    fs.unlinkSync(dbPath);
    console.log("Removed existing database file");
  }

  // 创建新的数据库连接
  const sqlite = new Database(dbPath);
  sqlite.pragma("journal_mode = WAL");

  const db = drizzle(sqlite, { schema });

  try {
    console.log("📝 Creating tables from schema...");

    // 创建所有表
    const createStatements = generateCreateTableSQL();
    for (const statement of createStatements) {
      sqlite.exec(statement.trim());
    }
    console.log("✅ All tables created successfully");

    // 创建索引
    console.log("📝 Creating indexes...");
    const indexStatements = generateCreateIndexSQL();
    for (const statement of indexStatements) {
      sqlite.exec(statement.trim());
    }
    console.log("✅ All indexes created successfully");

    // 插入订阅计划数据
    console.log("📝 Inserting subscription plans...");
    for (const planConfig of SUBSCRIPTION_PLANS_CONFIG) {
      await db.insert(schema.subscriptionPlans).values({
        id: planConfig.id,
        name: planConfig.name,
        description: planConfig.description,
        price: planConfig.price,
        currency: planConfig.currency,
        interval: planConfig.interval,
        stripePriceId: planConfig.stripePriceId,
        stripeProductId: planConfig.stripeProductId,
        features: JSON.stringify(planConfig.features),
        isActive: planConfig.isActive,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      console.log(`✅ ${planConfig.name} plan inserted (ID: ${planConfig.id})`);
    }

    console.log("✅ Database reset completed successfully!");

    // 显示所有计划
    console.log("📊 Available subscription plans:");
    const allPlans = await db.query.subscriptionPlans.findMany({
      where: (plans, { eq }) => eq(plans.isActive, true),
    });

    if (allPlans.length > 0) {
      allPlans.forEach((plan) => {
        console.log(
          `   - ${plan.name}: $${plan.price / 100}/${plan.interval} (ID: ${
            plan.id
          })`
        );
      });
    } else {
      console.log("   (No subscription plans found)");
    }

    console.log("\n🎉 Database is ready for development!");
    console.log("💡 You can now start the development server with: yarn dev");
  } catch (error) {
    console.error("❌ Error resetting database:", error);
    throw error;
  } finally {
    sqlite.close();
  }
}

// 直接运行脚本
console.log("🚀 Starting database reset from schema...");
console.log(
  "⚠️  This will completely recreate the database from schema definitions"
);
console.log("");
resetDevDatabaseFromSchema().catch(console.error);

export { resetDevDatabaseFromSchema };
