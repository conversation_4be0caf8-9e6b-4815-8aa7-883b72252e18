[{"login": "ruanyf", "html_url": "https://github.com/ruanyf", "name": "<PERSON><PERSON>", "bio": null, "blog": "https://twitter.com/ruanyf", "location": "Shanghai, China", "public_repos": 73, "followers": 82630, "following": 0, "created_at": "2011-07-10T01:07:17Z", "updated_at": "2025-01-11T16:45:48Z", "activity_score": 41346.9}, {"login": "<PERSON><PERSON><PERSON><PERSON>", "html_url": "https://github.com/michaelliao", "name": "Crypto Michael", "bio": "Crypto developer.", "blog": "https://liaoxuefeng.com", "location": "Beijing, China", "public_repos": 104, "followers": 37833, "following": 3, "created_at": "2010-11-06T12:21:35Z", "updated_at": "2025-05-20T15:41:25Z", "activity_score": 18962.7}, {"login": "<PERSON><PERSON><PERSON><PERSON>", "html_url": "https://github.com/daimajia", "name": "代码家", "bio": "Zhenfund VP of Investment.", "blog": "daimajia.com", "location": "Beijing, China", "public_repos": 91, "followers": 24831, "following": 272, "created_at": "2012-10-07T02:40:06Z", "updated_at": "2025-05-14T12:37:14Z", "activity_score": 12457.8}, {"login": "<PERSON><PERSON><PERSON>", "html_url": "https://github.com/JacksonTian", "name": "<PERSON>", "bio": null, "blog": "", "location": "Hangzhou, China", "public_repos": 270, "followers": 21302, "following": 207, "created_at": "2010-07-09T02:46:57Z", "updated_at": "2025-03-13T03:02:27Z", "activity_score": 10732}, {"login": "cloudwu", "html_url": "https://github.com/cloudwu", "name": "云风", "bio": "coder ( c , lua , open source )", "blog": "http://blog.codingnow.com", "location": "China", "public_repos": 137, "followers": 21338, "following": 2, "created_at": "2011-04-14T15:57:13Z", "updated_at": "2025-04-27T08:55:39Z", "activity_score": 10725.1}, {"login": "phodal", "html_url": "https://github.com/phodal", "name": "<PERSON><PERSON>", "bio": "I'm digging holes.", "blog": "https://www.phodal.com/", "location": "Shanghai / Hangzhou, China", "public_repos": 370, "followers": 20335, "following": 9, "created_at": "2010-11-08T11:46:51Z", "updated_at": "2025-05-16T14:33:39Z", "activity_score": 10293.5}, {"login": "liyu<PERSON>", "html_url": "https://github.com/liyupi", "name": "程序员鱼皮", "bio": "speak less do more！前腾讯全栈开发，现科技公司创始人", "blog": "https://www.codefather.cn", "location": "China Shanghai", "public_repos": 89, "followers": 19086, "following": 29, "created_at": "2017-02-26T08:44:22Z", "updated_at": "2025-05-08T05:27:08Z", "activity_score": 9584.7}, {"login": "<PERSON><PERSON><PERSON><PERSON>", "html_url": "https://github.com/justjavac", "name": "迷渡", "bio": "Creator of vscode-deno. Currently a contributor to Deno.", "blog": "https://twitter.com/justjavac", "location": "Tianjin, China", "public_repos": 397, "followers": 17048, "following": 87, "created_at": "2010-08-10T05:24:31Z", "updated_at": "2025-05-24T11:21:36Z", "activity_score": 8658.1}, {"login": "halfrost", "html_url": "https://github.com/halfrost", "name": "halfrost", "bio": "CS master @Stanford 💪 天道酬勤，勤能补拙。博观而约取，厚积而薄发。Gopher / Rustacean / Kubernetes / Cloud Native / Machine Learning / DeFi Smart Contract", "blog": "https://halfrost.com", "location": "[California, Singapore, China]", "public_repos": 32, "followers": 17255, "following": 361, "created_at": "2015-02-03T07:01:48Z", "updated_at": "2025-04-25T20:53:55Z", "activity_score": 8652.1}, {"login": "jackfrued", "html_url": "https://github.com/jackfrued", "name": "骆昊", "bio": "只有非常努力，才能看起来毫不费力！", "blog": "http://blog.csdn.net/jackfrued", "location": "Chengdu Sichuan, China", "public_repos": 139, "followers": 16329, "following": 6, "created_at": "2014-05-03T15:04:08Z", "updated_at": "2025-05-27T12:23:07Z", "activity_score": 8221.2}, {"login": "<PERSON><PERSON><PERSON>", "html_url": "https://github.com/Ovilia", "name": "<PERSON><PERSON>", "bio": "GitHub Star; Apache Member; Apache ECharts VP;\r\nPassionate Dataviz Developer & Creative Designer", "blog": "http://zhangwenli.com", "location": "Shanghai, China", "public_repos": 65, "followers": 15752, "following": 249, "created_at": "2011-05-10T12:43:24Z", "updated_at": "2025-03-05T02:35:21Z", "activity_score": 7910.5}, {"login": "onevcat", "html_url": "https://github.com/onevcat", "name": "<PERSON>", "bio": "<PERSON><PERSON><PERSON>, creator, proud father.", "blog": "https://onev.cat", "location": "Yokohama, Japan / China", "public_repos": 217, "followers": 15564, "following": 138, "created_at": "2011-09-01T16:27:21Z", "updated_at": "2025-05-22T00:29:34Z", "activity_score": 7862.1}, {"login": "eust-w", "html_url": "https://github.com/eust-w", "name": "longtao", "bio": "Technology for good", "blog": "https://longtao.fun", "location": "China", "public_repos": 135, "followers": 15402, "following": 42, "created_at": "2018-05-09T07:07:42Z", "updated_at": "2025-05-27T06:42:59Z", "activity_score": 7756.5}, {"login": "astaxie", "html_url": "https://github.com/astaxie", "name": "astaxie", "bio": "Write the testable code", "blog": "http://beego.vip", "location": "Shanghai, China", "public_repos": 83, "followers": 15035, "following": 23, "created_at": "2010-03-31T06:07:58Z", "updated_at": "2025-05-27T06:42:57Z", "activity_score": 7557.4}, {"login": "RubyLouvre", "html_url": "https://github.com/RubyLouvre", "name": "司徒正美", "bio": "穿梭于二进制与二次元的JS魔术师", "blog": "http://www.cnblogs.com/rubylouvre/", "location": "China", "public_repos": 129, "followers": 14764, "following": 34, "created_at": "2010-01-27T12:26:59Z", "updated_at": "2024-02-22T09:10:00Z", "activity_score": 7435.7}, {"login": "sorrycc", "html_url": "https://github.com/sorrycc", "name": "chencheng (云谦)", "bio": "Front-end developer at alipay, creator of @umijs, @dvajs, mako, father, roadhog, babel-plugin-import, awesome-javascript...", "blog": "https://sorrycc.com/", "location": "HangZhou, China", "public_repos": 237, "followers": 14527, "following": 76, "created_at": "2008-11-18T05:04:27Z", "updated_at": "2025-03-17T05:14:02Z", "activity_score": 7349.6}, {"login": "CyC2018", "html_url": "https://github.com/CyC2018", "name": null, "bio": null, "blog": "https://dwz.cn/H5R7WDSN", "location": "Guangzhou, China", "public_repos": 7, "followers": 14670, "following": 5, "created_at": "2018-02-08T09:03:31Z", "updated_at": "2025-04-27T14:49:05Z", "activity_score": 7347.1}, {"login": "bailicangdu", "html_url": "https://github.com/bailicangdu", "name": "cangdu", "bio": "水光潋滟晴方好，山色空蒙雨亦奇。", "blog": "https://github.com/bailicangdu", "location": "Shanghai, China", "public_repos": 22, "followers": 13750, "following": 20, "created_at": "2016-07-05T09:14:13Z", "updated_at": "2025-05-27T06:18:02Z", "activity_score": 6896.6}, {"login": "fengdu78", "html_url": "https://github.com/fengdu78", "name": "<PERSON>", "bio": "黄海广，计算机博士，微信公众号：机器学习初学者；知乎：黄海广；知识星球ID:92416895", "blog": "http://www.ai-start.com", "location": "Qingdao,China", "public_repos": 17, "followers": 13511, "following": 16, "created_at": "2017-03-01T15:57:52Z", "updated_at": "2025-04-14T00:30:18Z", "activity_score": 6775.6}, {"login": "egoist", "html_url": "https://github.com/egoist", "name": "EGOIST", "bio": "Indie Hacker", "blog": "https://egoist.dev", "location": "China", "public_repos": 798, "followers": 12909, "following": 25, "created_at": "2014-09-15T22:08:21Z", "updated_at": "2025-05-21T18:29:49Z", "activity_score": 6708.9}, {"login": "PanJiaChen", "html_url": "https://github.com/PanJiaChen", "name": "花裤衩", "bio": null, "blog": "", "location": "Shanghai, China", "public_repos": 72, "followers": 13306, "following": 84, "created_at": "2014-07-10T06:45:26Z", "updated_at": "2025-03-13T10:53:52Z", "activity_score": 6674.6}, {"login": "breakwa11", "html_url": "https://github.com/breakwa11", "name": "破娃酱", "bio": "二次元|喵星人|技術宅|學生|膜法少女|沒有威嚴的大小姐|黑長直，偶爾写写程式_(•̀ω•́ 」∠)__。最近壓力山大，因為天天被罵人品差和智障。喜歡玩遊戲、聊天。歡迎來撩，喜歡我请fo然后。。。", "blog": "", "location": "喵嗷污, China", "public_repos": 8, "followers": 13196, "following": 0, "created_at": "2014-08-13T09:46:15Z", "updated_at": "2022-06-22T05:40:37Z", "activity_score": 6605.4}, {"login": "draveness", "html_url": "https://github.com/draveness", "name": "Draven", "bio": "HFT / C++ / Go", "blog": "https://draven.co/", "location": "Beijing, China", "public_repos": 50, "followers": 13128, "following": 28, "created_at": "2014-01-24T16:22:01Z", "updated_at": "2025-05-08T02:38:53Z", "activity_score": 6594}, {"login": "hongyangAndroid", "html_url": "https://github.com/hongyangAndroid", "name": "张鸿洋", "bio": "学习ing", "blog": "http://www.wanandroid.com", "location": "Beijing,China", "public_repos": 102, "followers": 13023, "following": 35, "created_at": "2015-01-26T07:05:45Z", "updated_at": "2025-02-28T12:33:04Z", "activity_score": 6557.1}, {"login": "YunaiV", "html_url": "https://github.com/YunaiV", "name": "芋道源码", "bio": "愿半生编码，如一生老友！", "blog": "http://www.iocoder.cn?github", "location": "Shanghai, China", "public_repos": 83, "followers": 12104, "following": 74, "created_at": "2012-07-21T02:30:00Z", "updated_at": "2025-05-17T06:05:41Z", "activity_score": 6091.9}, {"login": "mqy<PERSON><PERSON>", "html_url": "https://github.com/mqyqingfeng", "name": "冴羽", "bio": "前端工程师", "blog": "https://yayujs.com", "location": "Hangzhou, China", "public_repos": 34, "followers": 11462, "following": 2, "created_at": "2015-03-13T11:08:31Z", "updated_at": "2025-02-13T11:41:15Z", "activity_score": 5756.2}, {"login": "fouber", "html_url": "https://github.com/fouber", "name": "张云龙", "bio": null, "blog": "https://github.com/fouber/blog", "location": "China", "public_repos": 171, "followers": 11289, "following": 76, "created_at": "2010-12-25T15:03:59Z", "updated_at": "2025-05-21T06:35:57Z", "activity_score": 5705.8}, {"login": "<PERSON><PERSON><PERSON><PERSON>", "html_url": "https://github.com/QianMo", "name": "浅墨（毛星云）", "bio": "Game Programmer", "blog": "https://zhuanlan.zhihu.com/game-programming", "location": "Shenzhen, China", "public_repos": 37, "followers": 11245, "following": 9, "created_at": "2014-06-08T12:25:21Z", "updated_at": "2021-11-28T12:46:43Z", "activity_score": 5648.6}, {"login": "mi<PERSON><PERSON>", "html_url": "https://github.com/miloyip", "name": "<PERSON>", "bio": null, "blog": "", "location": "Hong Kong, China", "public_repos": 29, "followers": 10695, "following": 32, "created_at": "2011-11-15T06:34:33Z", "updated_at": "2025-03-21T13:19:20Z", "activity_score": 5356.2}, {"login": "easychen", "html_url": "https://github.com/easychen", "name": "Easy", "bio": null, "blog": "http://ftqq.com", "location": "Chongqing, China", "public_repos": 231, "followers": 10530, "following": 31, "created_at": "2011-12-30T09:41:54Z", "updated_at": "2025-03-07T16:21:36Z", "activity_score": 5344.3}]