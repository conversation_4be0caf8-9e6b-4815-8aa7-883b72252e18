# 订阅计划配置说明

## 📋 概述

本文档说明如何正确配置开发环境的订阅计划 ID，确保与生产环境保持一致。

## ⚠️ 重要提醒

**订阅计划 ID 必须与生产环境 D1 数据库中的实际值保持一致！**

## 🔧 配置文件位置

需要更新的文件：

- `scripts/init-dev-db.ts` - 数据库初始化脚本中的 `SUBSCRIPTION_PLANS_CONFIG`
- `auth.ts` - 新用户注册时的免费计划分配

## 📝 获取生产环境订阅计划 ID

### 方法 1: 查询 D1 数据库

```sql
-- 在Cloudflare Dashboard的D1数据库控制台中执行
SELECT id, name, description, price, interval, stripe_price_id
FROM subscription_plans
WHERE is_active = 1;
```

### 方法 2: 使用管理 API

访问生产环境的管理 API 端点：

```
GET /api/admin/sync-plans
```

### 方法 3: 查看 Stripe Dashboard

1. 登录 [Stripe Dashboard](https://dashboard.stripe.com)
2. 导航到 "产品" → "价格"
3. 复制相应的价格 ID（格式：`price_xxxxxxxxxx`）

## 🛠️ 配置步骤

### 1. 更新初始化脚本

编辑 `scripts/init-dev-db.ts` 文件中的 `SUBSCRIPTION_PLANS_CONFIG`：

```typescript
const SUBSCRIPTION_PLANS_CONFIG = [
  {
    id: "price_1RLyWiHFsKiAaOeKsUiYwzra", // 替换为实际的免费计划ID
    name: "Free",
    description: "Perfect for getting started with GitHub Card",
    price: 0,
    currency: "USD",
    interval: "month",
    stripePriceId: null,
    stripeProductId: null,
    features: [
      "All templates available",
      "One share-link at a time",
      "Share-link 3 days validity",
      "Community support",
      "Basic analytics",
    ],
    isActive: true,
  },
  {
    id: "price_xxxxxxxxxx", // 替换为实际的Pro计划ID
    name: "Pro",
    description: "Unlock all premium features and unlimited access",
    price: 999, // 根据实际价格调整
    currency: "USD",
    interval: "month",
    stripePriceId: "price_xxxxxxxxxx", // 替换为实际的Stripe价格ID
    stripeProductId: "prod_xxxxxxxxxx", // 替换为实际的Stripe产品ID
    features: [
      "All templates available",
      "Unlimited share-links",
      "Share-links that never expire",
      "Priority support",
      "Professional analytics",
      "AI-powered descriptions",
      "Custom styling options",
    ],
    isActive: true,
  },
];
```

### 2. 更新认证配置

编辑 `auth.ts` 文件，更新新用户注册时使用的免费计划 ID：

```typescript
// 找到这一行
const planFree = await db.query.subscriptionPlans.findFirst({
  where: (plans, { eq }) => eq(plans.id, "price_1RLyWiHFsKiAaOeKsUiYwzra"), // 替换为实际ID
});
```

## 🔍 验证配置

### 1. 运行初始化脚本

```bash
yarn db:init-dev
```

### 2. 检查输出

确保看到类似以下的输出：

```
✅ Free plan inserted (ID: price_1RLyWiHFsKiAaOeKsUiYwzra)
✅ Pro plan inserted (ID: price_xxxxxxxxxx)
```

### 3. 运行测试脚本

```bash
yarn test:init-db
```

## 📚 相关文档

- [Stripe 价格配置文档](https://stripe.com/docs/api/prices)
- [Cloudflare D1 数据库文档](https://developers.cloudflare.com/d1/)
- [项目数据库 Schema](../lib/db/schema.ts)

## 🆘 故障排除

### 问题 1: 订阅计划 ID 不匹配

**症状**: 用户注册后没有订阅记录，或者订阅页面显示异常

**解决方案**:

1. 检查生产环境 D1 数据库中的实际 ID
2. 更新配置文件中的 ID
3. 重新运行初始化脚本

### 问题 2: Stripe 集成异常

**症状**: 支付流程失败，无法创建订阅

**解决方案**:

1. 确认 Stripe 价格 ID 和产品 ID 正确
2. 检查 Stripe webhook 配置
3. 验证环境变量设置

### 问题 3: 免费计划分配失败

**症状**: 新用户注册后没有免费订阅

**解决方案**:

1. 检查 `auth.ts` 中的免费计划 ID
2. 确认免费计划在数据库中存在
3. 查看服务器日志获取详细错误信息

## 📞 联系支持

如果遇到配置问题，请：

1. 检查服务器日志
2. 验证数据库连接
3. 确认所有环境变量正确设置
4. 提供详细的错误信息和步骤重现
