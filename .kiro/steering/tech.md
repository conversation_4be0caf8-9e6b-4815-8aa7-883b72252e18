# Tech Stack & Build System

## Core Technologies

- **Next.js 14** with App Router and TypeScript
- **React 18.2.0** for UI components
- **Tailwind CSS** for styling with custom animations
- **Drizzle ORM** with SQLite for database operations
- **NextAuth.js v5** for authentication
- **Stripe** for payment processing

## Deployment & Infrastructure

- **Cloudflare Workers** as the primary deployment target
- **OpenNext Cloudflare** adapter for serverless deployment
- **Cloudflare D1** for SQLite database
- **Cloudflare KV** for caching
- **Cloudflare R2** for object storage
- **Wrangler** for deployment and local development

## Key Libraries

- **Framer Motion** for animations
- **Recharts** for data visualizations
- **Monaco Editor** for code editing interfaces
- **Radix UI** for accessible component primitives
- **SWR** for data fetching and caching
- **React Hot Toast** for notifications

## Package Management

- **Yarn** (v1.22.22) as the package manager
- Node.js 20 required

## Common Commands

### Development

```bash
yarn dev                    # Start Next.js development server
yarn worker:dev            # Start Cloudflare Workers development
yarn build                 # Build for production
yarn worker:build          # Build for Cloudflare Workers
```

### Database Operations

```bash
yarn db:generate           # Generate Drizzle migrations
yarn db:studio             # Open Drizzle Studio
yarn db:push              # Apply migrations to local DB
yarn db:push-remote       # Apply migrations to remote DB
yarn db:init-dev          # Initialize development database
yarn db:reset-dev         # Reset and reinitialize dev database
```

### Testing

```bash
yarn test                  # Run Jest tests
yarn test:watch           # Run tests in watch mode
yarn test:coverage        # Run tests with coverage
yarn test:integration     # Run integration tests
```

### Deployment

```bash
yarn deploy               # Deploy to Cloudflare Workers
yarn worker:tail          # View Cloudflare Workers logs
```

## Build Configuration

- **TypeScript** with strict mode enabled
- **ESLint** with Next.js configuration
- **PostCSS** for CSS processing
- **Webpack** optimizations for production builds
- **OpenNext** configuration for Cloudflare compatibility
