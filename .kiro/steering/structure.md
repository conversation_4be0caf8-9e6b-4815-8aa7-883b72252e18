# Project Structure & Organization

## Root Directory Layout

```
├── app/                    # Next.js App Router pages and API routes
├── components/             # Reusable React components
├── lib/                    # Core business logic and utilities
├── types/                  # TypeScript type definitions
├── constants/              # Application constants and configuration
├── hooks/                  # Custom React hooks
├── contexts/               # React context providers
├── utils/                  # General utility functions
├── docs/                   # Project documentation
├── scripts/                # Database and utility scripts
├── __tests__/              # Test files and test utilities
└── drizzle/                # Database migrations
```

## App Directory Structure (Next.js App Router)

```
app/
├── api/                    # API route handlers
│   ├── auth/              # Authentication endpoints
│   ├── dashboard/         # Dashboard-related APIs
│   ├── ai/                # AI description generation
│   ├── stripe/            # Payment processing
│   └── leaderboard/       # Ranking and statistics
├── dashboard/             # Protected dashboard pages
├── auth/                  # Authentication pages
├── leaderboard/           # Public leaderboard
├── subscription/          # Subscription management
└── shared/                # Shared profile pages
```

## Component Organization

```
components/
├── ui/                    # Base UI components (buttons, inputs, etc.)
├── auth/                  # Authentication-related components
├── dashboard/             # Dashboard-specific components
├── cards/                 # Profile card components
├── charts/                # Data visualization components
├── ai-description/        # AI description generation UI
├── debug-platform/        # Development and debugging tools
├── leaderboard/           # Leaderboard components
└── shared/                # Shared utility components
```

## Library Structure

```
lib/
├── db/                    # Database schema and utilities
├── github/                # GitHub API integration
├── ai/                    # AI service integrations
├── cloudflare/            # Cloudflare services (KV, R2)
├── subscription/          # Stripe and subscription logic
├── services/              # Business logic services
└── adapters/              # Data transformation utilities
```

## Key Conventions

### File Naming

- **Components**: PascalCase (e.g., `ProfileCard.tsx`)
- **Pages**: kebab-case for routes (e.g., `dashboard/ai/page.tsx`)
- **Utilities**: camelCase (e.g., `userDataService.ts`)
- **Types**: kebab-case with `.ts` extension (e.g., `github.ts`)

### Import Patterns

- Use `@/` alias for absolute imports from project root
- Group imports: external libraries, internal modules, relative imports
- Prefer named exports over default exports for utilities

### Component Structure

- Co-locate related components in feature directories
- Separate UI primitives in `components/ui/`
- Use index files for clean imports from component directories

### API Route Organization

- Group related endpoints in subdirectories
- Use descriptive route names matching the feature
- Implement proper error handling and type safety

### Database & Types

- Database schema in `lib/db/schema.ts`
- Type definitions in dedicated `types/` directory
- Use Drizzle ORM relations for data modeling

### Testing Structure

```
__tests__/
├── components/            # Component unit tests
├── integration/           # Integration tests
├── e2e/                   # End-to-end tests
└── pages/                 # Page-level tests
```

## Configuration Files

- `next.config.mjs` - Next.js configuration with Cloudflare optimizations
- `wrangler.jsonc` - Cloudflare Workers deployment configuration
- `drizzle.config.json` - Database migration configuration
- `tailwind.config.js` - Tailwind CSS customization
- `tsconfig.json` - TypeScript compiler options
