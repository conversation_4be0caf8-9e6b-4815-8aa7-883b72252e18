# 🔣 V5 AI 重构项目符号系统

_v3.0 | Created: 2025-01-30 | Updated: 2025-01-30_

## 📁 文件和目录符号

- 📂 = /memory-bank/ (项目记忆库)
- 📦 = /memory-bank/back-up/ (备份目录)
- 🗑️ = 待删除文件
- 🆕 = 新创建文件
- 🔧 = 重构中的文件

## 📚 文档符号系统

### 核心文档

- σ₁ = 📂projectbrief.md (项目简介)
- σ₂ = 📂systemPatterns.md (技术架构)
- σ₃ = 📂techContext.md (技术上下文)
- σ₄ = 📂activeContext.md (当前活跃上下文)
- σ₅ = 📂progress.md (进度追踪)

### V5 重构专用文档

- σ₆ = 📂v5-technical-design.md (V5 技术设计)
- σ₇ = 📂v5-module-specs.md (四模块详细规格)
- σ₈ = 📂v5-testing-framework.md (V5 测试框架)

## 🎯 项目阶段和模式符号

### Π 项目阶段 (Project Phases)

- Π₁ = 🌱UNINITIATED (未初始化)
- Π₂ = 🚧INITIALIZING (初始化中)
- Π₃ = 🏗️DEVELOPMENT (开发阶段)
- Π₄ = 🔧MAINTENANCE (维护阶段)
- **Π₅ = 🔄REFACTORING (重构阶段)** - 新增 V5 专用

### Ω RIPER 模式 (工作模式)

- Ω₁ = 🔍R (RESEARCH) - 调研模式
- Ω₂ = 💡I (INNOVATE) - 创新模式
- Ω₃ = 📝P (PLAN) - 规划模式
- Ω₄ = ⚙️E (EXECUTE) - 执行模式
- Ω₅ = 🔎RV (REVIEW) - 审查模式

## 🤖 V5 四模块 AI 架构符号

### 核心模块

- 🧠 = Analyzer (分析器模块)
- 🎭 = Strategist (策略师模块)
- ✍️ = Writer (写手模块)
- 🎯 = Critic (评论家模块)

### 数据流符号

- 🧠 → 🎭 → ✍️ → 🎯 (四模块处理链)
- 📊 = 原始 GitHub 数据
- 🏷️ = 情感价位标记
- 📋 = 创意简报
- 📝 = 最终生成文本
- ⭐ = 质量评估分数

### 技术组件

- 🔧 = ModularAIGenerator (主引擎)
- 📚 = ComedyStrategyLibrary (策略库)
- 📖 = FewShotExampleLibrary (示例库)
- 🎛️ = PromptTemplateEngine (提示词引擎)
- 🛡️ = JSONValidator (数据验证器)

## 📊 重构进度符号

### 阶段状态

- 📋 = 准备和清理阶段
- 🏗️ = 核心模块开发阶段
- 🎨 = 用户界面重构阶段
- 🧪 = 测试和优化阶段

### 任务状态

- ✅ = 已完成
- 🔄 = 进行中
- ⏳ = 待开始
- 🚫 = 已取消/删除
- 🔥 = 高优先级
- ⭐ = 中优先级
- 📅 = 计划中

### 风险级别

- 🔴 = 高风险
- 🟡 = 中风险
- 🟢 = 低风险
- ⚠️ = 需要关注

## 🎯 质量指标符号

### 技术指标

- ⏱️ = 响应时间
- 🏃 = 性能表现
- 🛡️ = 错误处理
- 📐 = 测试覆盖率
- 🔍 = 调试能力

### 业务指标

- 😊 = 用户满意度
- 💼 = 转化率
- 📈 = 使用率增长
- 💰 = 成本控制
- 🎨 = 生成质量

## 🔧 开发工具符号

### 前端组件

- 🎛️ = AIDescriptionEngine
- 📊 = ModuleProgressTracker
- 🎨 = StyleControlPanel
- ⭐ = QualityAssessment
- 📋 = GenerationHistory

### 后端服务

- 🌐 = API 端点
- 🗄️ = 数据库表
- 🔑 = 认证和权限
- 📡 = 外部 API 调用
- 🔄 = 数据同步

## 💾 数据库符号

### 表类型

- 🗄️ = 主数据表
- 📊 = 分析数据表
- 📚 = 策略库表
- 📖 = 示例库表
- 📝 = 生成记录表
- 🔍 = 日志追踪表

### 数据状态

- 🆕 = 新增数据
- 🔄 = 更新中
- ✅ = 已确认
- 🗑️ = 待删除
- 📦 = 已归档

## 🌐 API 和集成符号

### API 类型

- 🤖 = AI 模块 API
- 📊 = 数据分析 API
- 🎨 = 前端组件 API
- 🔧 = 管理工具 API
- 📈 = 监控统计 API

### 集成状态

- 🔗 = 已连接
- ⚡ = 实时同步
- 📤 = 输出数据
- 📥 = 输入数据
- 🔄 = 双向通信

## 🎨 用户体验符号

### 界面状态

- 💻 = 桌面端
- 📱 = 移动端
- 📲 = 响应式
- 🎯 = 交互反馈
- ✨ = 动画效果

### 用户类型

- 🆓 = 免费用户
- 👑 = Pro 用户
- 🔧 = 开发者
- 📊 = 分析师
- 🎨 = 设计师

## 📚 交叉引用符号

### 标准引用格式

- [↗️σ₁:R₁] = 标准文档交叉引用
- [↗️🧠:M₁] = 模块功能引用
- [↗️📊:T₁] = 技术指标引用
- [↗️🎯:Q₁] = 质量标准引用

### 版本控制

- v1.0 = 初始版本
- v2.0 = 重大重构版本
- v3.0 = V5 重构版本
- α = 内测版本
- β = 公测版本
- RC = 发布候选版本

## 🔄 变更追踪符号

### 变更类型

- ➕ = 新增功能
- ➖ = 删除功能
- 🔧 = 修改功能
- 🐛 = 问题修复
- ⚡ = 性能优化
- 📚 = 文档更新

### 影响范围

- 🌍 = 全局影响
- 📦 = 模块级影响
- 📁 = 文件级影响
- 🔧 = 配置级影响
- 🎨 = 界面级影响

---

## 使用示例

**项目状态表示：**

```
当前状态: Π₅🔄 (重构阶段-进行中)
工作模式: Ω₃📝 (规划模式)
核心任务: 🧠→🎭→✍️→🎯 (四模块架构实现)
```

**进度表示：**

```
📋阶段一: ✅20% 完成
🏗️阶段二: ⏳待开始
🎨阶段三: ⏳待开始
🧪阶段四: ⏳待开始
```

**质量指标：**

```
⏱️API响应: < 3秒 (目标)
😊用户满意度: > 4.3/5 (目标)
🛡️错误处理: > 95% (目标)
```

这个符号系统为 V5 重构项目提供了清晰、一致的视觉语言，帮助快速识别项目状态、技术组件和进度信息。
