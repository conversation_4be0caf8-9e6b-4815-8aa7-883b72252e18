# σ₂: System Patterns

_v1.1 | Created: {DATE} | Updated: {DATE}_
_Π: DEVELOPMENT | Ω: RESEARCH_

## 🏛️ Architecture Overview

Modern full-stack web application built with Next.js 14 App Router, following serverless-first architecture patterns optimized for Cloudflare Workers deployment. **V4 升级将在保持现有架构基础上，扩展多维度评分系统、增强 API 限流机制、优化数据一致性管理。**

### Core Patterns

- **Component-Driven Development**: Reusable React components with TypeScript
- **Liquid Glass Design System**: Consistent glassmorphism UI patterns
- **Data-Driven Scoring**: Algorithm-based multi-dimensional evaluation
- **Responsive-First**: Mobile-optimized with progressive enhancement

## 🌍 Content & Language Standards

### English-First Approach

**All user-facing content MUST be in fluent, natural English:**

```typescript
// ✅ Good - Natural English
const DIMENSION_LABELS = {
  commit: "Code Architect",
  collaboration: "Community Builder",
  influence: "Open Source Pioneer",
  exploration: "Innovation Explorer",
};

// ❌ Avoid - Direct translations or Chinese text
const DIMENSION_LABELS = {
  commit: "代码提交型",
  collaboration: "协作交流型",
};
```

### Content Guidelines

1. **UI Text**: Clear, concise, action-oriented
2. **Error Messages**: Helpful and user-friendly
3. **Descriptions**: Professional yet accessible
4. **Technical Terms**: Industry-standard terminology
5. **Tone**: Professional, encouraging, inclusive

## 🏗️ Core Components

### Frontend Layer

- **Next.js 14 App Router**: Modern React framework with file-based routing
- **TypeScript**: Type-safe development environment
- **Tailwind CSS**: Utility-first CSS framework
- **Framer Motion**: Animation and interaction library
- **Radix UI**: Accessible component primitives

### Authentication Layer

- **NextAuth.js v5**: OAuth authentication with GitHub provider
- **JWT tokens**: Secure session management
- **D1 Adapter**: Database session storage

### Data Layer

- **Drizzle ORM**: Type-safe database operations
- **SQLite/D1**: Serverless database on Cloudflare
- **GitHub API**: Real-time profile and contribution data
- **🆕 数据版本控制**: 向后兼容性处理、数据迁移工具
- **🆕 多维度数据扩展**: Fork 数、贡献仓库数、语言多样性统计

### Payment Layer

- **Stripe**: Subscription and payment processing
- **Webhook handling**: Secure payment event processing

### Deployment Layer

- **Cloudflare Workers**: Serverless edge computing
- **OpenNext**: Next.js to Workers adapter
- **Wrangler**: Development and deployment tooling

## 🔄 Data Flow Patterns

### User Authentication Flow

1. GitHub OAuth initiation
2. Token exchange and validation
3. User profile creation/update
4. Session establishment

### 🆕 V4 多维度评分流程

1. **数据获取阶段**: GitHub API 数据抓取（带限流控制）
2. **评分计算阶段**: 四维度算法计算（代码提交、协作交流、开源影响、学习探索）
3. **数据存储阶段**: 版本化数据持久化
4. **缓存更新阶段**: 多层缓存策略更新

### Card Generation Flow

1. GitHub API data fetching (🆕 with rate limiting)
2. Profile analytics computation (🆕 multi-dimension scoring)
3. Card template rendering (🆕 radar chart, progress bars)
4. Image generation and caching

### 🆕 异步数据更新流程

1. **优先级队列管理**: 活跃用户优先处理
2. **批量数据更新**: 后台定时任务执行
3. **错误处理和重试**: 智能重试机制
4. **数据一致性验证**: 完整性检查和修复

### Subscription Flow

1. Stripe checkout session creation
2. Payment processing
3. Webhook event handling
4. User tier upgrade

## 🛡️ Security Patterns

- OAuth 2.0 authentication
- JWT token validation
- CSRF protection
- Rate limiting (🆕 enhanced with token bucket algorithm)
- Input sanitization
- Secure environment variable handling
- **🆕 API Token 安全管理**: 多 Token 轮换、健康检查、故障转移
- **🆕 数据访问控制**: 用户数据隔离、权限验证、审计日志

## 🎨 UI Component Patterns

### Liquid Glass Components

```typescript
// Reusable glassmorphism wrapper
function LiquidGlassBlock({ children, className }) {
  return (
    <div className="rounded-2xl p-7 liquidGlass-wrapper hover:p-6">
      <div className="liquidGlass-effect"></div>
      <div className="liquidGlass-tint"></div>
      <div className="liquidGlass-shine"></div>
      <div className="z-[3] relative liquidGlass-text">{children}</div>
    </div>
  );
}
```

### Chart Components

- **ActivityRadarChart**: Six-dimensional development activity
- **ScoreRadarChart**: Four-dimensional performance scoring
- **ConcentricSemicircleChart**: Nested dimension visualization
- **ContributionGauge**: Overall capability assessment

## 📊 Data Flow Patterns

### GitHub Data Processing

1. **Fetch** → GitHub API v4 (GraphQL)
2. **Transform** → Multi-dimension adapters
3. **Score** → Algorithm-based evaluation
4. **Cache** → Redis/CloudflareKV
5. **Render** → React components

### Scoring Algorithm Pattern (V4 Direct Score)

```typescript
// V4 核心：直接分数计算模型（替代百分位排名）
function calculateDirectScore(
  value: number,
  baseValue: number,
  maxScore: number = 100
): number {
  if (value <= 0) return 0;

  // 对数增长模型：更直观的分数映射
  // 当 value = baseValue 时，得分约为 maxScore * 0.5
  // 当 value = baseValue * 10 时，得分约为 maxScore * 0.85
  const maxMultiplier = 20;
  const score =
    (maxScore * Math.log(1 + value / baseValue)) / Math.log(1 + maxMultiplier);

  return Math.min(maxScore, Math.max(0, score));
}

function calculateDimensionScore(data: GitHubData): MultiDimensionScore {
  const dimensionScores = {
    commitScore: calculateCommitScore(data),
    collaborationScore: calculateCollaborationScore(data),
    influenceScore: calculateInfluenceScore(data),
    explorationScore: calculateExplorationScore(data),
  };

  // V4 优化：使用基于领域突出度的等级判定算法
  const overallGrade = getOptimizedContributionGrade(dimensionScores);

  return {
    ...dimensionScores,
    overallGrade,
  };
}

// V4 核心：基于领域突出度的智能等级判定
function getOptimizedContributionGrade(scores: DimensionScores): string {
  const EXCELLENT_THRESHOLD = 80; // 突出领域标准
  const GOOD_THRESHOLD = 60; // 基础合格标准

  const allScores = Object.values(scores);
  const excellentDomains = allScores.filter(
    (score) => score >= EXCELLENT_THRESHOLD
  ).length;
  const maxScore = Math.max(...allScores);

  // 智能等级判定：D(最高<60) → C(最高<80) → B(1个突出) → A(2个突出) → S(3-4个突出)
  if (maxScore < GOOD_THRESHOLD) return "D";
  if (maxScore < EXCELLENT_THRESHOLD) return "C";
  if (excellentDomains >= 3) return "S";
  if (excellentDomains === 2) return "A";
  if (excellentDomains === 1) return "B";
  return "C";
}
```

## 🔧 Development Patterns

### File Organization

```
components/
├── charts/          # Data visualization
├── cards/           # Profile display
├── ui/             # Reusable elements
└── auth/           # Authentication

lib/
├── adapters/       # Data transformation
├── github/         # API integration
└── utils/          # Helper functions
```

### Component Standards

- TypeScript interfaces for all props
- Responsive design with Tailwind CSS
- Error boundaries and loading states
- Accessibility compliance (ARIA labels)
- Performance optimization (memo, useMemo)
