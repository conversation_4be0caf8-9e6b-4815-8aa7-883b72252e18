# V5 AI 架构迁移前数据库 Schema 备份

_备份时间: 2025-01-30_
_备份原因: V5 四模块 AI 架构迁移前的完整数据库结构保存_

## 当前 AI 相关表结构

### 1. AI 描述主表 (aiDescriptions)

```sql
CREATE TABLE ai_descriptions (
  id TEXT PRIMARY KEY NOT NULL,
  user_id TEXT NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,

  -- AI生成的描述内容
  generated_description TEXT NOT NULL,
  description_style TEXT NOT NULL, -- technical-expert, community-builder, innovation-pioneer, learning-enthusiast

  -- 生成时的数据快照
  data_snapshot TEXT NOT NULL, -- JSON格式的GitHub数据
  prompt_used TEXT NOT NULL,
  ai_model_version TEXT NOT NULL DEFAULT 'deepseek-reasoner',

  -- 用户自定义内容
  custom_description TEXT, -- 用户编辑后的版本
  is_custom_applied BOOLEAN NOT NULL DEFAULT FALSE,

  -- 元数据
  generated_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
  updated_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000),
  expires_at INTEGER NOT NULL -- 描述过期时间，需要重新生成
);
```

### 2. 描述历史表 (descriptionHistory)

```sql
CREATE TABLE description_history (
  id TEXT PRIMARY KEY NOT NULL,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  description_content TEXT NOT NULL,
  generation_type TEXT NOT NULL, -- 'ai-generated', 'user-customized', 'fallback'
  style TEXT,
  created_at INTEGER NOT NULL DEFAULT (unixepoch('now', 'subsec') * 1000)
);
```

### 3. 相关索引

```sql
-- ai_descriptions 表索引
CREATE INDEX idx_ai_descriptions_user_id ON ai_descriptions(user_id);
CREATE INDEX idx_ai_descriptions_expires_at ON ai_descriptions(expires_at);
CREATE INDEX idx_ai_descriptions_style ON ai_descriptions(description_style);
CREATE INDEX idx_ai_descriptions_generated_at ON ai_descriptions(generated_at);

-- description_history 表索引
CREATE INDEX idx_description_history_user_id ON description_history(user_id);
CREATE INDEX idx_description_history_created_at ON description_history(created_at);
CREATE INDEX idx_description_history_type ON description_history(generation_type);
```

## 当前数据库迁移文件

**最新迁移**: `0015_ai_descriptions.sql` - 创建 AI 描述功能的基础表结构

## V5 迁移需求概述

V5 四模块架构需要：

1. **扩展现有 ai_descriptions 表**：添加四模块处理字段
2. **新增 ai_generation_requests 表**：请求追踪和流式生成支持
3. **新增 ai_module_logs 表**：四模块处理详细日志
4. **新增 comedy_strategies 表**：喜剧策略配置库
5. **新增 few_shot_examples 表**：少样本学习示例库

## 数据保护策略

- 保留所有现有用户数据
- 保持向后兼容性
- 提供回滚机制
- 渐进式迁移方案

## 备份验证

✅ 当前 schema 结构已完整备份
✅ 迁移文件历史已记录
✅ 数据保护策略已制定
✅ 准备开始 V5 架构升级
