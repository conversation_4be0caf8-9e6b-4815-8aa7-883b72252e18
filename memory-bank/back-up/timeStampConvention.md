# 🕒 时间戳统一约定

_v1.0 | Created: 2024-12-19 | Updated: 2024-12-19_
_Π: DEVELOPMENT | Ω: RESEARCH_

## 📋 核心约定

### ⚡ 统一标准

- **所有时间数据必须使用毫秒时间戳格式存储和传递**
- **数据库中所有时间字段均为 INTEGER 类型，存储毫秒时间戳**
- **API 交互中的时间字段统一转换为毫秒时间戳**

### 🔄 数据转换规则

#### GitHub API 数据处理

```typescript
// GitHub API 返回 ISO 8601 字符串格式
const created_at = "2013-03-31T02:26:44Z";

// 必须转换为毫秒时间戳
const timestamp = new Date(created_at).getTime(); // 1364697204000
```

#### 数据库存储

```sql
-- 所有时间字段都使用毫秒时间戳
userCreatedAt: integer("user_created_at").notNull(),
lastUpdated: integer("last_updated").notNull().default(sql`(unixepoch('now', 'subsec') * 1000)`),
recordCreatedAt: integer("record_created_at").notNull().default(sql`(unixepoch('now', 'subsec') * 1000)`)
```

### 🛠️ 实用函数

#### 时间戳确保函数

```typescript
const ensureTimestamp = (value: any): number => {
  if (typeof value === "string") {
    return new Date(value).getTime();
  } else if (typeof value === "number") {
    // 检查是否是秒级时间戳（2100年之前）
    if (value < 4102444800) {
      return value * 1000;
    }
    return value;
  }
  return Date.now();
};
```

## 🚨 关键注意事项

### ❌ 禁止的做法

- 不要使用字符串格式存储时间
- 不要使用秒级时间戳（除非明确需要转换）
- 不要在计算中混用不同精度的时间戳

### ✅ 推荐做法

- 统一使用 `Date.now()` 获取当前毫秒时间戳
- API 数据接收后立即转换为毫秒时间戳
- 数据库查询时确保时间字段格式一致性

## 🗑️ 数据清理策略

### 旧数据处理

- 当发现时间格式不一致时，清除相关旧数据
- 重新从 API 获取数据，确保格式统一
- 使用 `lib/db/clear-legacy-data.sql` 脚本清理

### 迁移策略

```sql
-- 清理包含字符串时间戳的旧数据
DELETE FROM contribute_datas;
```

## 📊 影响范围

### 核心文件

- `lib/github/fetch.ts` - API 数据转换
- `lib/server-github.ts` - 数据库交互
- `lib/adapters/multidimension-adapter.ts` - 评分计算
- `lib/db/schema.ts` - 数据库结构

### 相关类型

- `GitHubData.created_at: number`
- `GitHubUserData.created_at: number`
- 数据库所有时间字段均为 `integer`

## 🎯 未来维护

### 开发检查清单

- [ ] 新增时间字段时使用毫秒时间戳
- [ ] API 集成时立即转换时间格式
- [ ] 数据库迁移时保持时间戳一致性
- [ ] 时间计算时确保精度统一

**⚠️ 重要提醒：严格遵循此约定，避免在时间格式问题上浪费开发时间**
