# GitHub Card V5.1 Dashboard 产品需求文档

_创建日期: 2025-01-30 | 版本: V5.1 | 状态: 需求设计_
_Π: 🏗️DEVELOPMENT | Ω: 📝P_

## 🎯 项目概述

基于 V5 AI 描述功能的成功部署，V5.1 版本将为用户提供一个务实的 Dashboard 界面，专注于平台功能的便捷使用和管理，包括 AI 功能控制、分享链接管理、订阅状态查看等核心平台功能，让用户更高效地使用本平台服务。

## 📋 需求背景

### 当前痛点

1. **功能入口分散**: 用户需要在多个页面间切换使用平台功能
2. **管理不便**: AI 功能、分享链接、订阅状态缺乏统一管理界面
3. **使用门槛高**: 新用户难以快速上手平台核心功能
4. **Pro 价值不明确**: 付费功能的实际价值和使用方式不够清晰

### 业务目标

- **简化操作流程**: 提供一站式的平台功能管理中心
- **提高使用效率**: 减少页面跳转，提升功能使用便利性
- **促进功能发现**: 帮助用户更好地了解和使用平台功能
- **优化 Pro 体验**: 清晰展示 Pro 功能价值和使用状态

## 🏗️ Dashboard 架构设计

### 整体布局

```
┌─────────────────────────────────────────────────────────────────┐
│                        Dashboard Header                         │
│         User Info | Subscription Status | Quick Actions        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   AI Control    │  │  Share Manager  │  │ Account Status  │  │
│  │  - Generate     │  │  - Link List    │  │  - Subscription │  │
│  │  - Customize    │  │  - Templates    │  │  - Usage Stats  │  │
│  │  - History      │  │  - Quick Share  │  │  - Limits       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│                                                                 │
│  ┌─────────────────────────────────────────┐  ┌─────────────────┐  │
│  │            Recent Activity              │  │  Quick Actions  │  │
│  │  - Platform Operations                 │  │  - New Share    │  │
│  │  - AI Generations                      │  │  - Refresh Data │  │
│  │  - Share Activities                    │  │  - Settings     │  │
│  └─────────────────────────────────────────┘  └─────────────────┘  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 响应式设计

**桌面端 (≥1024px)**:

- 3 列网格布局
- 侧边栏导航
- 卡片式模块化设计

**平板端 (768px-1023px)**:

- 2 列网格布局
- 顶部导航
- 可折叠侧边栏

**移动端 (<768px)**:

- 单列垂直布局
- 底部导航栏
- 卡片堆叠显示

## 🧩 核心功能模块

### 1. AI 功能控制面板 (AI Control Panel)

**功能描述**: 集成所有 AI 描述相关功能的控制中心

**Free 用户功能**:

- AI 描述生成按钮
- 4 种风格选择
- 生成结果预览
- Pro 升级提示

**Pro 用户功能**:

- 自定义编辑器
- 描述历史记录
- 草稿保存/应用
- 高级风格混合

**技术实现**:

```typescript
interface AIControlPanelProps {
  userId: string;
  isPro: boolean;
  aiStatus: AIStatus;
  onDescriptionUpdate?: (description: string) => void;
}

interface AIStatus {
  lastGenerated?: number;
  generationCount: number;
  monthlyLimit: number;
  currentDescription?: string;
  availableStyles: string[];
}
```

**复用组件**:

- `AIDescriptionSection` - 主要 AI 功能
- `AIDescriptionUpgradeCard` - 升级提示
- `DescriptionEditor` - Pro 编辑器

### 2. 分享链接管理器 (Share Manager)

**功能描述**: 统一管理用户的分享链接，提供便捷的创建、管理和使用功能

**核心功能**:

- **活跃链接列表**: 显示用户当前有效的分享链接
- **一键创建**: 快速创建不同模板类型的分享链接
- **链接状态**: 显示链接有效期、访问次数、创建时间
- **快速操作**: 复制链接、社交分享、删除过期链接

**Pro 功能差异**:

- Free: 最多 3 个活跃链接，3 天有效期
- Pro: 无限链接数量，永久有效期

**技术实现**:

```typescript
interface ShareManagerProps {
  userId: string;
  shareLinks: ShareLink[];
  isPro: boolean;
  linkLimits: LinkLimits;
  onCreateLink: (templateType: string) => Promise<void>;
  onDeleteLink: (linkId: string) => Promise<void>;
}

interface LinkLimits {
  maxLinks: number;
  currentCount: number;
  defaultExpiry: number; // days
}
```

**复用组件**:

- `ShareButton` - 分享按钮组件
- `TemplateShowcase` - 模板选择

### 3. 账户状态面板 (Account Status)

**功能描述**: 显示用户在平台上的订阅状态、使用统计和功能限制

**核心信息展示**:

- **订阅状态**: 当前计划类型（Free/Pro）、到期时间、续费状态
- **使用统计**: AI 生成次数、分享链接数量、本月使用情况
- **功能限制**: 当前计划的功能限制和使用配额
- **升级提示**: Pro 功能预览和升级引导

**统计数据**:

- AI 描述生成次数（本月/总计）
- 分享链接创建数量（活跃/总计）
- 账户创建时间和使用天数
- 最近登录时间

**技术实现**:

```typescript
interface AccountStatusProps {
  userId: string;
  subscription: UserSubscription;
  usageStats: UsageStatistics;
  isPro: boolean;
}

interface UsageStatistics {
  aiGenerations: {
    thisMonth: number;
    total: number;
    monthlyLimit: number;
  };
  shareLinks: {
    active: number;
    total: number;
    maxActive: number;
  };
  accountAge: number; // days
  lastLogin: number;
}
```

**复用组件**:

- 现有的订阅状态组件
- `NumberTicker` - 数字动画

### 4. 最近活动 (Recent Activity)

**功能描述**: 展示用户在平台上的最近操作记录，帮助用户了解使用历史

**核心活动类型**:

- AI 描述生成记录（生成时间、风格类型）
- 分享链接创建（模板类型、创建时间）
- 订阅状态变更（升级/降级时间、计划类型）
- 登录记录（登录时间、设备信息）

**展示设计**:

- 简洁的时间线布局
- 活动类型图标区分
- 相对时间显示（"2 小时前"）
- 最近 10 条活动记录

**技术实现**:

```typescript
interface RecentActivityProps {
  userId: string;
  activities: UserActivity[];
  limit?: number;
}

interface UserActivity {
  id: string;
  userId: string;
  actionType: ActivityType;
  actionData: Record<string, any>;
  performedAt: number;
  status: "success" | "failed";
}

type ActivityType =
  | "ai_description_generated"
  | "share_link_created"
  | "subscription_changed"
  | "user_login";
```

**复用组件**:

- 现有的`userBehaviors`数据表结构

### 5. 快速操作面板 (Quick Actions)

**功能描述**: 提供常用操作的快捷入口

**核心操作**:

- 创建新分享链接
- 生成 AI 描述
- 刷新 GitHub 数据
- 查看订阅状态

**技术实现**:

```typescript
interface QuickActionsProps {
  userId: string;
  isPro: boolean;
  onAction: (actionType: string) => Promise<void>;
}
```

## 🎨 设计规范

### 视觉风格

**色彩方案**:

- 主色调: 继承现有橙色系 (#fa7b19)
- 背景: 深色主题 (#0d1117)
- 卡片: 液态玻璃效果 (LiquidGlassBlock)
- 文字: 白色/灰色层次

**组件风格**:

- 圆角: 统一使用 12px 圆角
- 阴影: 柔和阴影效果
- 间距: 8px 基础间距单位
- 动画: 流畅的过渡动画

### 交互设计

**加载状态**:

- 骨架屏加载
- 渐进式内容展示
- 错误状态处理

**响应反馈**:

- Toast 通知
- 按钮状态变化
- 数据更新提示

## 🛠️ 技术实现方案

### 路由设计

```
/dashboard
├── /dashboard/profile     # 个人资料管理
├── /dashboard/ai          # AI功能详情
├── /dashboard/analytics   # 数据分析详情
├── /dashboard/share       # 分享管理
└── /dashboard/settings    # 设置页面
```

### 组件架构

```typescript
// 主Dashboard组件
export function Dashboard({ userId }: { userId: string }) {
  return (
    <div className="dashboard-container">
      <DashboardHeader />
      <div className="dashboard-grid">
        <ProfileCard />
        <AIPanel />
        <QuickStats />
        <AnalyticsPanel />
        <ShareCenter />
        <RecentActivity />
      </div>
    </div>
  );
}

// 响应式网格布局
const dashboardGrid = {
  desktop: "grid-cols-3 gap-6",
  tablet: "grid-cols-2 gap-4",
  mobile: "grid-cols-1 gap-3",
};
```

### 数据管理

**状态管理**:

- React Query 用于服务端状态
- Zustand 用于客户端状态
- Context 用于全局状态

**数据获取**:

```typescript
// Dashboard数据获取
export async function getDashboardData(userId: string) {
  const [userProfile, aiDescriptions, shareLinks, analytics] =
    await Promise.all([
      getUserProfile(userId),
      getAIDescriptions(userId),
      getShareLinks(userId),
      getAnalytics(userId),
    ]);

  return {
    userProfile,
    aiDescriptions,
    shareLinks,
    analytics,
  };
}
```

## 📱 移动端适配

### 移动端特殊考虑

**导航设计**:

- 底部 Tab 导航
- 顶部标题栏
- 侧滑菜单

**内容优化**:

- 卡片垂直堆叠
- 关键信息优先显示
- 手势操作支持

**性能优化**:

- 懒加载非关键内容
- 图片压缩和优化
- 减少不必要的动画

## 🔐 权限控制

### Free 用户限制

- 基本信息展示 ✅
- AI 描述生成 ✅ (有限次数)
- 简单统计图表 ✅
- 基础分享功能 ✅ (3 天有效期)
- 最近活动查看 ✅

### Pro 用户权限

- 所有 Free 功能 ✅
- AI 描述自定义编辑 ✅
- 高级数据分析 ✅
- 无限分享链接 ✅
- 永久有效期 ✅
- 优先技术支持 ✅

## 📊 性能指标

### 加载性能

- 首屏加载时间 < 2 秒
- 组件渲染时间 < 500ms
- API 响应时间 < 1 秒
- 图表渲染时间 < 800ms

### 用户体验

- 交互响应时间 < 100ms
- 页面切换动画 < 300ms
- 错误恢复时间 < 3 秒
- 移动端适配评分 > 95 分

## 🚀 开发计划

### Phase 1: 基础架构 (1 周)

- [ ] Dashboard 路由设计
- [ ] 基础布局组件
- [ ] 响应式网格系统
- [ ] 数据获取架构

### Phase 2: 核心模块 (2 周)

- [ ] ProfileCard 组件
- [ ] AIPanel 集成
- [ ] QuickStats 实现
- [ ] 基础交互逻辑

### Phase 3: 高级功能 (2 周)

- [ ] AnalyticsPanel
- [ ] ShareCenter
- [ ] RecentActivity
- [ ] 权限控制完善

### Phase 4: 优化完善 (1 周)

- [ ] 移动端适配
- [ ] 性能优化
- [ ] 测试覆盖
- [ ] 文档完善

## 📈 成功指标

### 用户参与度

- Dashboard 访问率 > 80%
- 平均停留时间 > 3 分钟
- 功能使用率提升 > 40%
- 用户满意度 > 4.5/5

### 业务指标

- Pro 转化率提升 > 20%
- 用户留存率提升 > 15%
- 功能发现率提升 > 50%
- 支持工单减少 > 30%

## 🔄 后续迭代

### V5.2 计划功能

- 个性化 Dashboard 布局
- 更多数据可视化选项
- 团队协作功能
- API 开放接口

### 长期规划

- AI 驱动的个性化推荐
- 社区互动功能
- 企业级 Dashboard
- 数据导出和集成

---

**总结**: V5.1 Dashboard 将为用户提供一个统一、高效、美观的信息管理中心，通过整合现有功能和优化用户体验，显著提升产品价值和用户满意度。
