# 🔣 Symbol Reference Guide

_v1.0 | Created: 2025-01-27 | Updated: 2025-01-27_

## 📁 File Symbols

- 📂 = `/memory-bank/`
- 📦 = `/memory-bank/backups/`
- 𝕄 = Memory file array [σ₁, σ₂, σ₃, σ₄, σ₅]

## 📋 Memory Files (𝕄)

- σ₁ = 📋 Project Brief (`projectbrief.md`)
- σ₂ = 🏛️ System Patterns (`systemPatterns.md`)
- σ₃ = 💻 Technical Context (`techContext.md`)
- σ₄ = 🔮 Active Context (`activeContext.md`)
- σ₅ = 📊 Progress Tracker (`progress.md`)

## 🔄 RIPER Modes (Ω)

- Ω₁ = 🔍 RESEARCH - Analysis and investigation
- Ω₂ = 💡 INNOVATE - Creative problem solving
- Ω₃ = 📝 PLAN - Strategic planning and design
- Ω₄ = ⚙️ EXECUTE - Implementation and development
- Ω₅ = 🔎 REVIEW - Validation and quality assurance

## 🏗️ Project Phases (Π)

- Π₁ = 🌱 UNINITIATED - Framework not yet started
- Π₂ = 🚧 INITIALIZING - START phase active
- Π₃ = 🏗️ DEVELOPMENT - Main development phase
- Π₄ = 🔧 MAINTENANCE - Long-term support phase

## 🎯 Task Categories (𝕋)

- 𝕋[0:3] = Research tasks (read, ask, observe, document)
- 𝕋[4:6] = Innovation tasks (suggest, explore, evaluate)
- 𝕋[7:9] = Planning tasks (create plan, detail specs, sequence)
- 𝕋[10:12] = Execution tasks (implement, follow, test)
- 𝕋[13:15] = Review tasks (validate, verify, report)

## 🔗 Cross-Reference Format

- Standard: `[↗️σ₁:R₁]` - Links to requirement R₁ in Project Brief
- Mode: `[↗️Ω₃]` - References PLAN mode
- Phase: `[↗️Π₃]` - References DEVELOPMENT phase

## ⚠️ Safety Protocols (Δ)

- Δ₁ = Destructive operation warning
- Δ₂ = Phase transition verification
- Δ₃ = Re-initialization confirmation
- Δ₄ = Error reporting and recovery

## 🛠️ Operations (Φ)

- Φ_memory = Memory management functions
- Φ_file = File system operations
- Φ_mode_transition = Mode switching logic

## 📊 Status Indicators

- ✅ = Completed
- 🔄 = In Progress
- 📋 = Planned
- ⚠️ = Needs Attention
- 🐛 = Issue/Bug
- 🎯 = Goal/Target
